import { Grid, Typography, CircularProgress, Box } from "@mui/material";
import { memo, useRef, useCallback, useState, useEffect, forwardRef } from "react";
import VirtualizedList from "./VirtualizedList";
import { SentimentVeryDissatisfied } from "@mui/icons-material";
import VirtualizedCardListRow from "./VirtualizedCardListRow";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";

const VirtualizedCardList = forwardRef(
    (
        {
            events,
            setShowDetailModal,
            setSelectedCard,
            isLoading,
            onLoadMore,
            hasMore,
            containerRef,
            CustomCard,
            onFlaggedByClick,
            buttonsToShow,
            signedUrls,
        },
        ref,
    ) => {
        const { screenSize } = useApp();
        const listRef = useRef();
        const [isScrolling, setIsScrolling] = useState(false);
        const [containerHeight, setContainerHeight] = useState(0);
        const [containerWidth, setContainerWidth] = useState(0);

        useEffect(() => {
            if (ref) {
                ref.current = {
                    scrollToTop: () => {
                        if (listRef.current) {
                            listRef.current.scrollTo(0);
                        }
                    },
                };
            }
        }, [ref]);

        useEffect(() => {
            const target = containerRef?.current;
            if (!target) return;

            const updateSize = () => {
                const newHeight = target.clientHeight;
                const newWidth = target.clientWidth;
                setContainerHeight(isLoading ? newHeight - 70 : newHeight);
                setContainerWidth(newWidth);
            };

            updateSize();
            const resizeObserver = new ResizeObserver(updateSize);
            resizeObserver.observe(target);

            return () => {
                resizeObserver.unobserve(target);
            };
        }, [containerRef, isLoading]);

        useEffect(() => {
            if (listRef.current) {
                listRef.current.resetAfterIndex(0);
            }
        }, [events, screenSize, containerWidth]);

        useEffect(() => {
            if (!isLoading && onLoadMore && hasMore && listRef.current) {
                const containerElement = containerRef?.current;
                if (containerElement) {
                    // If the container is hidden (e.g., its parent tab has display: none), it will have no client rects
                    const isHidden = containerElement.getClientRects().length === 0;
                    if (isHidden) {
                        return; // Do not auto-load when not visible
                    }
                    const computedStyle = window.getComputedStyle(containerElement);
                    const isVisible = computedStyle.display !== "none" && computedStyle.visibility !== "hidden";
                    if (!isVisible) {
                        return;
                    }
                }

                const { scrollHeight, clientHeight } = listRef.current._outerRef;
                if (scrollHeight <= clientHeight && hasMore) {
                    onLoadMore();
                }
            }
        }, [events, isLoading, onLoadMore, hasMore, containerRef]);

        const getColumnCount = useCallback(() => {
            // Prefer dynamic calculation by container width to avoid overly stretched cards on large screens.
            const minCardWidth = 300; // px
            const gap = 16; // approximate horizontal gap per card (spacing=2 ~ 16px)
            const width = containerWidth || 0;
            if (width <= 0) {
                // Fallback to screen size if width not yet measured
                if (screenSize.xs) return 1;
                if (screenSize.sm) return 2;
                if (screenSize.md) return 3;
                if (screenSize.lg) return 4;
                return 5;
            }
            const columns = Math.max(1, Math.floor((width + gap) / (minCardWidth + gap)));
            // Put an upper bound if needed to avoid too many columns
            return Math.min(columns, 12);
        }, [containerWidth, screenSize]);

        const getItemSize = (index) => {
            const columnCount = getColumnCount();
            const rowIndex = Math.floor(index / columnCount);
            return rowIndex === 0 ? 350 : 350;
        };

        const checkScrollPosition = useCallback((scrollOffset, clientHeight, scrollHeight) => {
            const scrollPosition = scrollOffset + clientHeight;
            return scrollPosition >= scrollHeight * 0.8;
        }, []);

        const handleScroll = useCallback(
            ({ scrollOffset, scrollUpdateWasRequested }) => {
                if (!isLoading && onLoadMore && !scrollUpdateWasRequested && listRef.current) {
                    const { scrollHeight, clientHeight } = listRef.current._outerRef;

                    if (checkScrollPosition(scrollOffset, clientHeight, scrollHeight) && hasMore && !isLoading && !isScrolling) {
                        setIsScrolling(true);
                        onLoadMore();
                        setTimeout(() => setIsScrolling(false), 1000);
                    }
                }
            },
            [hasMore, isLoading, onLoadMore, isScrolling, checkScrollPosition],
        );

        if (isLoading && events.length === 0) {
            return (
                <Grid display={"flex"} justifyContent={"center"} alignItems={"center"} size={12}>
                    <CircularProgress />
                </Grid>
            );
        }

        if (events.length === 0) {
            return (
                <Grid display={"flex"} justifyContent={"center"} alignItems={"center"} size={12}>
                    <Grid display={"flex"} flexDirection={"column"} alignItems={"center"} justifyContent={"center"}>
                        <SentimentVeryDissatisfied sx={{ fontSize: "100px", color: theme.palette.custom.borderColor }} />
                        <Typography variant="h6" component="div" gutterBottom color={theme.palette.custom.borderColor}>
                            No data available
                        </Typography>
                    </Grid>
                </Grid>
            );
        }

        const columnCount = getColumnCount();

        return (
            <>
                <VirtualizedList
                    listRef={listRef}
                    containerHeight={containerHeight}
                    getItemSize={getItemSize}
                    handleScroll={handleScroll}
                    Row={VirtualizedCardListRow}
                    items={events}
                    columnCount={columnCount}
                    rowData={{
                        CustomCard,
                        setShowDetailModal,
                        setSelectedCard,
                        onFlaggedByClick,
                        buttonsToShow,
                        signedUrls,
                    }}
                />
                {isLoading && events.length > 0 && (
                    <Box display="flex" justifyContent="center" width={"100%"} padding={2}>
                        <CircularProgress />
                    </Box>
                )}
            </>
        );
    },
);

VirtualizedCardList.displayName = "VirtualizedCardList";
export default memo(VirtualizedCardList);

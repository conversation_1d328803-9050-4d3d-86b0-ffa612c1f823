import{e as t,i as e}from"./index-Cmi2ob6r.js";const a=new class{constructor(){this.cacheStore="s3_url_cache",this.s3Config={buckets:{assets:{name:"quartermaster-assets",region:"us-east-1"},compressedItems:{name:"webapp-artifacts-compressed",region:"us-east-2"}}}}async getSignedUrl({region:e,bucket_name:a,key:r}){try{const s=await t.get("/s3/signedUrl",{params:{key:r,bucket_name:a,region:e}});return s.data.signedUrl}catch(s){throw s}}async getCachedUrl(t){try{const a=await e.getItem(this.cacheStore,t);if(!a)return null;const r=Date.now();return a.expiresAt&&r>=a.expiresAt?(await e.deleteItem(this.cacheStore,t),null):a.url}catch(a){return null}}getExpiryFromUrl(t){try{const e=new URL(t),a=e.searchParams.get("Expires")||e.searchParams.get("X-Amz-Expires");return a?1e3*parseInt(a):null}catch(e){return null}}async fetchCloudfrontSignedUrl(a,r,s){try{const n=`signed_url_${a}`,c=await this.getCachedUrl(n);if(c)return c;const i=(await t.get("/s3/cloudfront/signedUrl",{params:{key:a,bucketName:r,region:s}})).data.signedUrl;if(i){const t=this.getExpiryFromUrl(i);t&&await e.addItems(this.cacheStore,[{_id:n,url:i,expiresAt:t,timestamp:Date.now()}])}return i}catch(n){throw n}}async fetchCloudfrontSignedUrlBatch(e){try{return(await t.post("/s3/cloudfront/signedUrl/batch",{batch:e})).data.signedUrls}catch(a){throw a}}async fetchSignedUrlsBatch(t){try{const e=[],a=new Map;if(t.forEach((t=>{if(t.thumbnail_image_path){const r=`webapp-artifacts-compressed:${t.thumbnail_image_path}`;a.has(r)||(e.push({bucketName:"webapp-artifacts-compressed",key:t.thumbnail_image_path,region:"us-east-1"}),a.set(r,[])),a.get(r).push({artifactId:t._id,type:"thumbnail"})}if(t.image_path){const r=`${t.bucket_name}:${t.image_path}`;a.has(r)||(e.push({bucketName:t.bucket_name,key:t.image_path,region:t.aws_region}),a.set(r,[])),a.get(r).push({artifactId:t._id,type:"image"})}if(t.video_path){const r=`${t.bucket_name}:${t.video_path}`;a.has(r)||(e.push({bucketName:t.bucket_name,key:t.video_path,region:t.aws_region}),a.set(r,[])),a.get(r).push({artifactId:t._id,type:"video"})}})),e.length>0){const t=await this.fetchCloudfrontSignedUrlBatch(e),r=new Map;return t.forEach(((t,s)=>{const n=e[s],c=`${n.bucketName}:${n.key}`;(a.get(c)||[]).forEach((({artifactId:e,type:a})=>{const s=`${e}:${a}`;r.set(s,t.signedUrl)}))})),r}return new Map}catch(e){return new Map}}};export{a as s};

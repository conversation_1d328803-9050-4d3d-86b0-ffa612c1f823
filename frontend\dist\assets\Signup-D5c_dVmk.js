import{ay as e,V as a,r as s,j as r,G as n,T as t,W as i,X as o,Y as l,Z as d,$ as u,L as c,a0 as m,a1 as p}from"./vendor-B98I-pgv.js";import{e as h}from"./index-Cmi2ob6r.js";import{F as x,a as g,b as f}from"./utils-D3r61PVZ.js";import{s as j}from"./validation-schemas-CuwDAm9M.js";import"./maps--fsV2DPB.js";import"./charts-gTQAinvd.js";function w(){const w=e(),N=new URLSearchParams(w.search),v=N.get("email"),F=parseInt(N.get("role_id")),y=N.get("role"),b=N.get("organization_id"),S=N.get("organization_name"),C=N.get("token"),P=a(),[W,T]=s.useState(!1),[q,z]=s.useState(!1),[_,k]=s.useState(""),[D,A]=s.useState(!1),B=s.useRef();s.useEffect((()=>{v&&F&&C&&S&&b||P("/login")}),[v,F,C,S,b]),s.useEffect((()=>{_&&(clearTimeout(B.current),B.current=setTimeout((()=>k("")),3e3))}),[_]);return r.jsxs(n,{container:!0,flexDirection:"column",gap:1,children:[r.jsx(n,{flexDirection:"column",color:"#FFFFFF",children:r.jsx(n,{children:r.jsx(t,{variant:"h3",fontWeight:"600",textAlign:"center",sx:{whiteSpace:"pre-line"},children:"SmartMast \n Dashboard Signup"})})}),r.jsx(x,{initialValues:{firstName:"",lastName:"",username:"",password:"",confirmPassword:"",role_id:F,organization_id:b},validationSchema:j,onSubmit:async e=>{const a={name:`${e.firstName} ${e.lastName}`,username:e.username,email:v,password:e.password,role_id:F,organization_id:b,token:C};A(!0);try{await h.post("/users",a,{meta:{showSnackbar:!0}}).then((()=>{A(!1),P("/login")}))}catch(s){k("SignUp failed: "+(s.response?.data?.message||s.message||JSON.stringify(s))),A(!1)}},children:({errors:e,touched:a,values:s,handleChange:p,isSubmitting:h})=>r.jsx(g,{children:r.jsxs(n,{container:!0,flexDirection:"column",gap:4,children:[r.jsx(n,{children:r.jsx(f,{name:"email",as:i,className:"input-signup",type:"text",inputProps:{autoComplete:"on"},autoComplete:"on",placeholder:"Email",variant:"outlined",fullWidth:!0,value:v,required:!0,disabled:!0})}),r.jsx(n,{children:r.jsx(f,{name:"role",as:i,className:"input-signup",type:"text",placeholder:"Role",variant:"outlined",fullWidth:!0,value:y,disabled:!0})}),r.jsx(n,{children:r.jsx(f,{name:"organization",as:i,className:"input-signup",type:"text",placeholder:"Organization",variant:"outlined",fullWidth:!0,value:S,disabled:!0})}),r.jsxs(n,{container:!0,gap:2,alignItems:"center",justifyContent:"center",children:[r.jsx(n,{size:"grow",children:r.jsx(f,{name:"firstName",as:i,fullWidth:!0,className:"input-signup",placeholder:"First Name",variant:"outlined",onChange:p,value:s.firstName,required:!0,error:a.firstName&&Boolean(e.firstName),helperText:a.firstName&&e.firstName})}),r.jsx(n,{size:"grow",children:r.jsx(f,{name:"lastName",as:i,fullWidth:!0,className:"input-signup",placeholder:"Last Name",variant:"outlined",onChange:p,value:s.lastName,required:!0,error:a.lastName&&Boolean(e.lastName),helperText:a.lastName&&e.lastName})})]}),r.jsx(n,{children:r.jsx(f,{name:"username",as:i,fullWidth:!0,className:"input-signup",type:"text",inputProps:{autoComplete:"on"},autoComplete:"on",placeholder:"Username",variant:"outlined",onChange:p,value:s.username,required:!s.username,error:a.username&&Boolean(e.username),helperText:a.username&&e.username})}),r.jsx(n,{children:r.jsx(f,{name:"password",as:i,fullWidth:!0,className:"input-signup",placeholder:"Password",variant:"outlined",value:s.password,onChange:p,required:!0,type:W?"text":"password",InputProps:{endAdornment:r.jsx(o,{position:"end",children:r.jsx(l,{onClick:()=>T((e=>!e)),sx:{color:"primary.contrastText"},"aria-label":"toggle password visibility",children:W?r.jsx(d,{}):r.jsx(u,{})})})},error:a.password&&Boolean(e.password),helperText:a.password&&e.password})}),r.jsx(n,{children:r.jsx(f,{name:"confirmPassword",as:i,fullWidth:!0,className:"input-signup",placeholder:"Confirm Password",variant:"outlined",value:s.confirmPassword,onChange:p,required:!0,type:q?"text":"password",InputProps:{endAdornment:r.jsx(o,{position:"end",children:r.jsx(l,{onClick:()=>z((e=>!e)),sx:{color:"primary.contrastText"},"aria-label":"toggle confirm password visibility","data-testid":"toggle-password-visibility",children:q?r.jsx(d,{}):r.jsx(u,{})})})},error:a.confirmPassword&&Boolean(e.confirmPassword),helperText:a.confirmPassword&&e.confirmPassword})}),r.jsx(n,{display:_?"block":"none",children:r.jsx(t,{color:"error",children:_})}),r.jsx(n,{children:r.jsx(c,{className:"btn-login",type:"submit",variant:"contained",color:"primary",fullWidth:!0,disabled:D||h,endIcon:D&&r.jsx(m,{}),children:"Signup"})})]})})}),r.jsx(n,{color:"#FFFFFF",children:r.jsxs(t,{fontSize:"18px",lineHeight:"30px",fontWeight:"400",children:["Request an Account:"," ",r.jsx(p,{href:"mailto:<EMAIL>",color:"#FFFFFF",fontWeight:"bold",sx:{textDecoration:"none",":hover":{textDecoration:"underline"}},children:"<EMAIL>"})]})})]})}export{w as default};

import { Button, Grid, InputAdornment, OutlinedInput, Tab, Tabs } from "@mui/material";
import { Add, Search } from "@mui/icons-material";
import { useEffect, useMemo, useState } from "react";
import { useUser } from "../../../hooks/UserHook";
import { isAllowedUser, permissions } from "../../../utils";
import Vessels from "./Vessels";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";
import RegionGroups from "./RegionGroup/RegionGroups";
import useVesselInfo from "../../../hooks/VesselInfoHook";

export default function VesselManagement() {
    const { user } = useUser();
    const [tab, setTab] = useState("");
    const { isMobile } = useApp();

    const [showAddVesselModal, setShowAddVesselModal] = useState(false);
    const [showCreateModal, setShowCreateModal] = useState(false);

    const [searchQuery, setSearchQuery] = useState("");
    const { vesselInfo, fetchVesselsInfo } = useVesselInfo();
    const [vessels, setVessels] = useState([]);

    const isVesselEditDisabled = useMemo(() => {
        if (!user || !user._id) return true;
        return !isAllowedUser(user);
    }, [user]);

    useEffect(() => {
        if (!(user && user.hasPermissions([permissions.manageRegionsGroups]))) return;
        fetchVessels();
    }, [vesselInfo, user]);

    const fetchVessels = async () => {
        if (vesselInfo && Array.isArray(vesselInfo)) {
            setVessels(vesselInfo);
        } else {
            fetchVesselsInfo();
        }
    };
    const tabs = useMemo(
        () => [
            {
                value: "vessels",
                label: "Vessels",
                component: (
                    <Vessels
                        showAddVessel={showAddVesselModal}
                        setShowAddVessel={setShowAddVesselModal}
                        searchQuery={searchQuery}
                        isVesselEditDisabled={isVesselEditDisabled}
                    />
                ),
                display: user?.hasPermissions([permissions.manageVessels]),
            },
            {
                value: "regionGroups",
                label: "Regions",
                component: (
                    <RegionGroups
                        searchQuery={searchQuery}
                        vessels={vessels}
                        managedVessels={vesselInfo}
                        showCreateModal={showCreateModal}
                        setShowCreateModal={setShowCreateModal}
                    />
                ),
                display: user?.hasPermissions([permissions.manageRegionsGroups]),
            },
        ],
        [user, showAddVesselModal, searchQuery, isVesselEditDisabled, showCreateModal, setShowCreateModal, vessels, vesselInfo],
    );

    const handleSearchChange = (event) => {
        setSearchQuery(event.target.value);
    };

    useEffect(() => {
        if (!tab) {
            setTab(tabs.find((t) => t.display)?.value || "");
        }
    }, [tabs]);

    return (
        user &&
        tabs.some((t) => t.display) &&
        tab && (
            <Grid
                container
                direction="column"
                sx={{
                    color: "#FFFFFF",
                    height: "100%",
                    overflow: "auto",
                    backgroundColor: theme.palette.custom.darkBlue,
                }}
            >
                <Grid container padding={2} display={"flex"} rowGap={2} justifyContent={"space-between"} alignItems={"center"} flexWrap={"wrap"}>
                    <Grid size={{ xs: 12, lg: 2.5 }}>
                        <Tabs
                            value={tab}
                            onChange={(_, v) => setTab(v)}
                            sx={{
                                width: "100%",
                                padding: "4px",
                                border: `2px solid ${theme.palette.custom.borderColor}`,
                                borderRadius: "8px",
                                backgroundColor: "transparent",
                                "& .MuiTabs-flexContainer": {
                                    height: "100%",
                                },
                                "& .MuiButtonBase-root": {
                                    width: 100 / tabs.filter((t) => t.display).length + "%",
                                    borderRadius: "8px",
                                },
                                "& .MuiButtonBase-root.Mui-selected": {
                                    backgroundColor: theme.palette.custom.mainBlue,
                                },
                            }}
                        >
                            {tabs
                                .filter((t) => t.display)
                                .map((t) => (
                                    <Tab
                                        key={t.value}
                                        label={t.label}
                                        value={t.value}
                                        sx={{
                                            maxWidth: "none",
                                        }}
                                    />
                                ))}
                        </Tabs>
                    </Grid>
                    <Grid container columnGap={2} justifyContent={"space-between"} size={{ xs: 12, lg: 9.4 }}>
                        {tab === "vessels" && (
                            <Grid
                                size={{ xs: "grow", lg: "100%" }}
                                display={"flex"}
                                alignItems={"center"}
                                justifyContent={"space-between"}
                                minHeight={50}
                                gap={1}
                            >
                                <Grid size={{ xs: "grow", lg: 5.8 }} height={"100%"}>
                                    <OutlinedInput
                                        type="text"
                                        value={searchQuery}
                                        onChange={handleSearchChange}
                                        startAdornment={
                                            <InputAdornment position="start">
                                                <Search sx={{ color: "#FFFFFF" }} />
                                            </InputAdornment>
                                        }
                                        placeholder="Search by vessel name or unit ID"
                                        sx={{
                                            color: "#FFFFFF",
                                            width: "100%",
                                            "& .MuiOutlinedInput-notchedOutline": {
                                                border: "2px solid",
                                                borderColor: theme.palette.custom.borderColor + " !important",
                                                borderRadius: "8px",
                                            },
                                        }}
                                    />
                                </Grid>
                                <Grid alignItems={"center"} display={"flex"} justifyContent={"flex-end"} gap={2} size="auto" height={"100%"}>
                                    <Button
                                        variant="contained"
                                        sx={{
                                            "&.MuiButtonBase-root": {
                                                color: "#FFFFFF",
                                                height: { xs: "100%", lg: "auto" },
                                                padding: { xs: 0, lg: "10px 20px" },
                                                backgroundColor: theme.palette.custom.mainBlue,
                                                fontWeight: "bold",
                                            },
                                            "&.MuiButtonBase-root.Mui-disabled": {
                                                backgroundColor: "#9A9CA2",
                                            },
                                            "& .MuiButton-icon": {
                                                marginRight: { xs: 0, lg: "10px" },
                                            },
                                        }}
                                        startIcon={<Add />}
                                        onClick={() => setShowAddVesselModal(true)}
                                        disabled={isVesselEditDisabled}
                                    >
                                        {!isMobile && "Add Vessel"}
                                    </Button>
                                </Grid>
                            </Grid>
                        )}
                        {tab === "regionGroups" && (
                            <Grid
                                size={{
                                    xs: "grow",
                                    lg: 12,
                                }}
                                display={"flex"}
                                alignItems={"center"}
                                justifyContent={"space-between"}
                                minHeight={50}
                                gap={1}
                            >
                                <Grid
                                    size={{
                                        xs: "grow",
                                        lg: 5.8,
                                    }}
                                    height={"100%"}
                                >
                                    <OutlinedInput
                                        type="text"
                                        value={searchQuery}
                                        onChange={handleSearchChange}
                                        startAdornment={
                                            <InputAdornment position="start">
                                                <Search sx={{ color: "#FFFFFF" }} />
                                            </InputAdornment>
                                        }
                                        placeholder="Search by name, unit or created by"
                                        sx={{
                                            color: "#FFFFFF",
                                            width: "100%",
                                            "& .MuiOutlinedInput-notchedOutline": {
                                                border: "2px solid",
                                                borderColor: (theme) => theme.palette.custom.borderColor + " !important",
                                                borderRadius: "8px",
                                            },
                                        }}
                                    />
                                </Grid>
                                <Grid alignItems={"center"} display={"flex"} justifyContent={"flex-end"} gap={2} size="auto" height={"100%"}>
                                    <Button
                                        variant="contained"
                                        sx={{
                                            "&.MuiButtonBase-root": {
                                                color: "#FFFFFF",
                                                height: { xs: "100%", lg: "auto" },
                                                padding: { xs: 0, lg: "10px 20px" },
                                                backgroundColor: (theme) => theme.palette.custom.mainBlue,
                                                fontWeight: "bold",
                                                "& .MuiButton-icon": {
                                                    marginRight: { xs: 0, lg: "10px" },
                                                },
                                            },
                                        }}
                                        startIcon={<Add />}
                                        onClick={() => setShowCreateModal(true)}
                                    >
                                        {!isMobile && "Create New Region"}
                                    </Button>
                                </Grid>
                            </Grid>
                        )}
                    </Grid>
                </Grid>
                {tabs
                    .filter((t) => t.display)
                    .map((t) => (
                        <Grid
                            key={t.value}
                            size="grow"
                            sx={{
                                display: tab !== t.value ? "none" : "block",
                                px: 2,
                                pb: 2,
                                width: "100%",
                            }}
                        >
                            {t.component}
                        </Grid>
                    ))}
            </Grid>
        )
    );
}

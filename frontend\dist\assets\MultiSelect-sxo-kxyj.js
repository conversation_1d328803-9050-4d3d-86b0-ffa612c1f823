import{ar as e,j as t,bK as o,b5 as r,c1 as n,c2 as l,W as a,c3 as i}from"./vendor-B98I-pgv.js";function s({InputLabelStyle:s,TextFieldProps:c,backgroundColor:p,borderRadius:d,...u}){const m=e();return t.jsx(o,{size:"small",sx:{".MuiInputBase-input":{height:16},...u.sx},slotProps:{popper:{sx:{"& .MuiAutocomplete-groupLabel":{color:m.palette.primary.contrastText,backgroundColor:m.palette.primary.dark,display:"flex",alignItems:"center",gap:1},"& .MuiAutocomplete-option":{padding:0,paddingLeft:2,fontSize:13}}}},renderGroup:e=>{const o=e.children.filter((e=>e?.props?.children[0]?.props?.checked||!1)).length,a=o===e.children.length?"checked":o>0?"indeterminate":"unchecked";return t.jsxs("li",{children:[t.jsxs("div",{className:"MuiAutocomplete-groupLabel",children:[t.jsx(r,{onChange:(t,o)=>((e,t)=>{const o=u.options.filter((t=>u.groupBy(t)===e)),r=t?[...u.value,...o]:u.value.filter((e=>!o.find((t=>u.isOptionEqualToValue(t,e)))));u.onChange(null,r)})(e.group,o),icon:t.jsx(l,{fontSize:"small"}),checkedIcon:t.jsx(n,{fontSize:"small"}),checked:"checked"===a,indeterminate:"indeterminate"===a,indeterminateIcon:t.jsx(i,{fontSize:"small"}),size:"small",sx:{color:m.palette.primary.contrastText,"&.Mui-checked":{color:m.palette.primary.contrastText}}}),e.group]}),t.jsx("ul",{children:e.children})]},e.key)},renderInput:e=>t.jsx(a,{size:"small",variant:"outlined",...e,InputLabelProps:{sx:{...e.InputProps.style,color:"primary.contrastText",fontSize:13,"&.Mui-disabled":{color:"primary.contrastText",opacity:.38},...s}},InputProps:{...e.InputProps,style:{...e.InputProps.style,borderRadius:d||4,backgroundColor:p||m.palette.primary.main,opacity:u.disabled?.38:1}},label:u.label,...c}),renderOption:(e,o,{selected:a})=>{const{key:i,...s}=e,c=u.getOptionLabel?u.getOptionLabel(o):"label not found",p=!!u.getOptionDisabled&&u.getOptionDisabled(o,a);return t.jsxs("li",{...s,"aria-disabled":p,children:[t.jsx(r,{icon:t.jsx(l,{fontSize:"small"}),checkedIcon:t.jsx(n,{fontSize:"small"}),checked:a}),c]},i)},...u})}export{s as M};

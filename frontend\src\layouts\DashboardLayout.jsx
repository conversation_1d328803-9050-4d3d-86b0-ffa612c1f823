import { Drawer, Grid } from "@mui/material";
import { Outlet, useLocation, useNavigate } from "react-router-dom";
import { useUser } from "../hooks/UserHook";
import { useEffect, useRef, useState } from "react";
import { useApp } from "../hooks/AppHook";
import { registerSessionExpiryCallback } from "../axios";
import TourGuide from "../components/TourGuide";
import { jwtDecode } from "jwt-decode";
import { Navigate } from "react-router-dom";
import Appbar from "./Appbar";
import Sidebar from "./Sidebar";
import { logEvent } from "../utils";
import { getSocket } from "../socket";
import artifactFlagController from "../controllers/ArtifactFlag.controller";

const regex = /^\/dashboard\/events\/[a-fA-F0-9]+$/;

export default function DashboardLayout() {
    const { screenSize, isMobile } = useApp();
    const { fetchUser, logout, user } = useUser();
    const navigate = useNavigate();
    const { pathname } = useLocation();
    const appbarRef = useRef(null);
    const [drawerOpen, setDrawerOpen] = useState(false);
    const startTimeRef = useRef(Date.now());

    const location = useLocation();
    useEffect(() => {
        if (window.gtag && user) {
            console.log("GA: Setting user ID and properties", user.name, user._id);

            window.gtag("set", { user_id: user._id });
            window.gtag("set", "user_properties", { user_name: user.name });
        }
    }, [user]);
    useEffect(() => {
        console.log("GA: Logging screen view event", location.pathname, location.search);
        // On unmount, send time spent
        logEvent("page_view", {
            page_title: "Quartermaster page" + location.search,
            page_location: location.search,
        });
        return () => {
            if (user) {
                const duration = Math.round((Date.now() - startTimeRef.current) / 1000); // seconds
                logEvent("time_on_page", {
                    page_path: window.location.pathname || location.pathname,
                    user_id: user._id,
                    user_name: user.name,
                    duration_seconds: duration,
                });
            }
        };
    }, [location]);
    useEffect(() => {
        const socket = getSocket();
        if (regex.test(pathname)) {
            sessionStorage.setItem("eventPath", pathname);
        }
        const handleFlagChanged = async () => {
            await artifactFlagController.getUserFlaggedArtifactIds();
        };

        fetchUser()
            .then(() => handleFlagChanged())
            .catch(console.error);
        // Register the callback to handle 401 errors
        registerSessionExpiryCallback(() => {
            logout(() => navigate("/login"));
        });

        socket.on("artifacts_flagged/changed", handleFlagChanged);
        return () => {
            socket.off("artifacts_flagged/changed", handleFlagChanged);
        };
    }, []);

    useEffect(() => {
        if (drawerOpen) {
            if (!screenSize.xs && !screenSize.sm && !screenSize.md) {
                setDrawerOpen(false);
            }
        }
    }, [screenSize]);

    const checkTokenExpiry = (token) => {
        if (!token) return true;
        try {
            const decodedToken = jwtDecode(token);
            const currentTime = Date.now() / 1000;
            return decodedToken.exp < currentTime;
        } catch (error) {
            console.error("Invalid Token", error);
            return true;
        }
    };

    const token = localStorage.getItem("jwt_token");
    const isExpired = checkTokenExpiry(token);

    if (isExpired) {
        return <Navigate to="/login" />;
    }

    return (
        <Grid container height={"100vh"} flexDirection={"column"} bgcolor={"black"}>
            <TourGuide isMobile={isMobile} />
            <Grid ref={appbarRef} size="auto">
                <Appbar setDrawerOpen={setDrawerOpen} />
            </Grid>
            <Grid container flexDirection={"row"} overflow={"auto"} flexWrap={"nowrap"} size="grow">
                <Grid display={{ xs: "none", lg: "flex" }}>
                    <Sidebar />
                </Grid>
                <Grid minHeight={{ xs: "auto", lg: "auto" }} height={{ xs: "auto", lg: "100%" }} size="grow">
                    <Outlet />
                </Grid>
            </Grid>
            <Grid display={{ xs: "flex", lg: "none" }}>
                <Drawer open={drawerOpen} onClose={() => setDrawerOpen(false)}>
                    <Sidebar drawerOpen={drawerOpen} setDrawerOpen={setDrawerOpen} />
                </Drawer>
            </Grid>
        </Grid>
    );
}

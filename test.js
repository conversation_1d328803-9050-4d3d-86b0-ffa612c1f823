function formatMB(bytes) {
    return (bytes / 1024 / 1024).toFixed(2) + " MB";
}

function createObjectData(count) {
    const arr = [];
    for (let i = 0; i < count; i++) {
        arr.push({
            timestamp: 1755103937000,
            lat: 8.2909382,
            lng: -83.1727897,
            stationary: false
        });
    }
    return arr;
}

function createArrayData(count) {
    const arr = [];
    for (let i = 0; i < count; i++) {
        arr.push([
            8.2909382,
            -83.1727897,
            false
        ]);
    }
    return arr;
}

function runTest(label, generator) {
    global.gc(); // force garbage collection
    const before = process.memoryUsage().heapUsed;
    const data = generator(1_000_000);
    global.gc();
    const after = process.memoryUsage().heapUsed;
    console.log(`${label}: ${formatMB(after - before)}`);
}

console.log("Memory usage for 1M records:");
runTest("Array of Objects", createObjectData);
runTest("Array of Arrays", createArrayData);

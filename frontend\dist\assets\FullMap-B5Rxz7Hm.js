import{r as e,C as t,_ as s,W as n,aa as o,D as a,j as r,a2 as i,a3 as l,a4 as c,a5 as d,a6 as u,T as p,aj as m,ak as h,al as f,B as g,G as x,Y as y,aC as b,ao as w,b3 as j,b4 as v,aA as C,az as S,R as k,ay as F,b5 as _,F as A,H as O,a0 as D,aw as T,ax as M,J as P,b6 as I,b7 as E,b8 as L,aE as R,x as z,b9 as V,ba as W,bb as N,bc as $,bd as B,ac as H,be as U,bf as G,am as Z,bg as Y,aL as X}from"./vendor-B98I-pgv.js";import{_ as K,m as q,n as J,v as Q,o as ee,s as te,p as se,q as ne,r as oe,u as ae,a as re,x as ie,C as le,F as ce,G as de,H as ue,N as pe,O as me,Q as he,R as fe,U as ge,W as xe,X as ye,Y as be,Z as we}from"./Aritfact.controller-B2VYOB-U.js";import{u as je}from"./AppHook-CvjturwY.js";import{M as ve,S as Ce,G as Se}from"./maps--fsV2DPB.js";import{e as ke,U as Fe,p as _e,y as Ae,m as Oe,d as De,a as Te,s as Me,c as Pe,S as Ie,b as Ee,o as Le,z as Re,A as ze,B as Ve,D as We,u as Ne,t as $e}from"./index-Cmi2ob6r.js";import{P as Be}from"./PreviewMedia-CZ7OLmuB.js";import{a as He}from"./ArtifactFlag.controller-BiYkhhNT.js";import{s as Ue}from"./S3.controller-BioajDex.js";import{u as Ge}from"./VesselInfoHook-D_QDbUmn.js";import{u as Ze}from"./GroupRegionHook-DlxDjEC4.js";import"./charts-gTQAinvd.js";import"./utils-D3r61PVZ.js";import"./ModalContainer-CTYPbNwV.js";const Ye=["slots","slotProps","InputProps","inputProps"],Xe=e.forwardRef((function(e,i){const l=t({props:e,name:"MuiDateField"}),{slots:c,slotProps:d,InputProps:u,inputProps:p}=l,m=s(l,Ye),h=l,f=c?.textField??(e.enableAccessibleFieldDOMStructure?se:n),g=o({elementType:f,externalSlotProps:d?.textField,externalForwardedProps:m,additionalProps:{ref:i},ownerState:h});g.inputProps=a({},p,g.inputProps),g.InputProps=a({},u,g.InputProps);const x=(e=>{const t=K(e),{forwardedProps:s,internalProps:n}=q(t,"date");return J({forwardedProps:s,internalProps:n,valueManager:te,fieldValueManager:ee,validator:Q,valueType:"date"})})(g),y=ne(x),b=oe(a({},y,{slots:c,slotProps:d}));return r.jsx(f,a({},b))}));function Ke(e){return i("MuiDatePickerToolbar",e)}l("MuiDatePickerToolbar",["root","title"]);const qe=["value","isLandscape","onChange","toolbarFormat","toolbarPlaceholder","views","className","onViewChange","view"],Je=c(le,{name:"MuiDatePickerToolbar",slot:"Root",overridesResolver:(e,t)=>t.root})({}),Qe=c(p,{name:"MuiDatePickerToolbar",slot:"Title",overridesResolver:(e,t)=>t.title})({variants:[{props:{isLandscape:!0},style:{margin:"auto 16px auto auto"}}]}),et=e.forwardRef((function(n,o){const i=t({props:n,name:"MuiDatePickerToolbar"}),{value:l,isLandscape:c,toolbarFormat:p,toolbarPlaceholder:m="––",views:h,className:f}=i,g=s(i,qe),x=ae(),y=re(),b=(e=>{const{classes:t}=e;return u({root:["root"],title:["title"]},Ke,t)})(i),w=e.useMemo((()=>{if(!l)return m;const e=ie(x,{format:p,views:h},!0);return x.formatByString(l,e)}),[l,p,m,x,h]),j=i;return r.jsx(Je,a({ref:o,toolbarTitle:y.datePickerToolbarTitle,isLandscape:c,className:d(b.root,f)},g,{children:r.jsx(Qe,{variant:"h4",align:c?"left":"center",ownerState:j,className:b.title,children:w})}))}));function tt(s,n){const o=ae(),r=ce(),i=t({props:s,name:n}),l=e.useMemo((()=>null==i.localeText?.toolbarTitle?i.localeText:a({},i.localeText,{datePickerToolbarTitle:i.localeText.toolbarTitle})),[i.localeText]);return a({},i,{localeText:l},de({views:i.views,openTo:i.openTo,defaultViews:["year","day"],defaultOpenTo:"day"}),{disableFuture:i.disableFuture??!1,disablePast:i.disablePast??!1,minDate:ue(o,i.minDate,r.minDate),maxDate:ue(o,i.maxDate,r.maxDate),slots:a({toolbar:et},i.slots)})}const st=e.forwardRef((function(e,t){const s=re(),n=ae(),o=tt(e,"MuiDesktopDatePicker"),r=a({day:pe,month:pe,year:pe},o.viewRenderers),i=a({},o,{viewRenderers:r,format:ie(n,o,!1),yearsPerRow:o.yearsPerRow??4,slots:a({openPickerIcon:he,field:Xe},o.slots),slotProps:a({},o.slotProps,{field:e=>a({},m(o.slotProps?.field,e),me(o),{ref:t}),toolbar:a({hidden:!0},o.slotProps?.toolbar)})}),{renderPicker:l}=fe({props:i,valueManager:te,valueType:"date",getOpenDialogAriaText:ge({utils:n,formatKey:"fullDate",contextTranslation:s.openDatePickerDialogue,propsTranslation:i.localeText?.openDatePickerDialogue}),validator:Q});return l()}));st.propTypes={autoFocus:h.bool,className:h.string,closeOnSelect:h.bool,dayOfWeekFormatter:h.func,defaultValue:h.object,disabled:h.bool,disableFuture:h.bool,disableHighlightToday:h.bool,disableOpenPicker:h.bool,disablePast:h.bool,displayWeekNumber:h.bool,enableAccessibleFieldDOMStructure:h.any,fixedWeekNumber:h.number,format:h.string,formatDensity:h.oneOf(["dense","spacious"]),inputRef:f,label:h.node,loading:h.bool,localeText:h.object,maxDate:h.object,minDate:h.object,monthsPerRow:h.oneOf([3,4]),name:h.string,onAccept:h.func,onChange:h.func,onClose:h.func,onError:h.func,onMonthChange:h.func,onOpen:h.func,onSelectedSectionsChange:h.func,onViewChange:h.func,onYearChange:h.func,open:h.bool,openTo:h.oneOf(["day","month","year"]),orientation:h.oneOf(["landscape","portrait"]),readOnly:h.bool,reduceAnimations:h.bool,referenceDate:h.object,renderLoading:h.func,selectedSections:h.oneOfType([h.oneOf(["all","day","empty","hours","meridiem","minutes","month","seconds","weekDay","year"]),h.number]),shouldDisableDate:h.func,shouldDisableMonth:h.func,shouldDisableYear:h.func,showDaysOutsideCurrentMonth:h.bool,slotProps:h.object,slots:h.object,sx:h.oneOfType([h.arrayOf(h.oneOfType([h.func,h.object,h.bool])),h.func,h.object]),timezone:h.string,value:h.object,view:h.oneOf(["day","month","year"]),viewRenderers:h.shape({day:h.func,month:h.func,year:h.func}),views:h.arrayOf(h.oneOf(["day","month","year"]).isRequired),yearsOrder:h.oneOf(["asc","desc"]),yearsPerRow:h.oneOf([3,4])};const nt=e.forwardRef((function(e,t){const s=re(),n=ae(),o=tt(e,"MuiMobileDatePicker"),r=a({day:pe,month:pe,year:pe},o.viewRenderers),i=a({},o,{viewRenderers:r,format:ie(n,o,!1),slots:a({field:Xe},o.slots),slotProps:a({},o.slotProps,{field:e=>a({},m(o.slotProps?.field,e),me(o),{ref:t}),toolbar:a({hidden:!1},o.slotProps?.toolbar)})}),{renderPicker:l}=xe({props:i,valueManager:te,valueType:"date",getOpenDialogAriaText:ge({utils:n,formatKey:"fullDate",contextTranslation:s.openDatePickerDialogue,propsTranslation:i.localeText?.openDatePickerDialogue}),validator:Q});return l()}));nt.propTypes={autoFocus:h.bool,className:h.string,closeOnSelect:h.bool,dayOfWeekFormatter:h.func,defaultValue:h.object,disabled:h.bool,disableFuture:h.bool,disableHighlightToday:h.bool,disableOpenPicker:h.bool,disablePast:h.bool,displayWeekNumber:h.bool,enableAccessibleFieldDOMStructure:h.any,fixedWeekNumber:h.number,format:h.string,formatDensity:h.oneOf(["dense","spacious"]),inputRef:f,label:h.node,loading:h.bool,localeText:h.object,maxDate:h.object,minDate:h.object,monthsPerRow:h.oneOf([3,4]),name:h.string,onAccept:h.func,onChange:h.func,onClose:h.func,onError:h.func,onMonthChange:h.func,onOpen:h.func,onSelectedSectionsChange:h.func,onViewChange:h.func,onYearChange:h.func,open:h.bool,openTo:h.oneOf(["day","month","year"]),orientation:h.oneOf(["landscape","portrait"]),readOnly:h.bool,reduceAnimations:h.bool,referenceDate:h.object,renderLoading:h.func,selectedSections:h.oneOfType([h.oneOf(["all","day","empty","hours","meridiem","minutes","month","seconds","weekDay","year"]),h.number]),shouldDisableDate:h.func,shouldDisableMonth:h.func,shouldDisableYear:h.func,showDaysOutsideCurrentMonth:h.bool,slotProps:h.object,slots:h.object,sx:h.oneOfType([h.arrayOf(h.oneOfType([h.func,h.object,h.bool])),h.func,h.object]),timezone:h.string,value:h.object,view:h.oneOf(["day","month","year"]),viewRenderers:h.shape({day:h.func,month:h.func,year:h.func}),views:h.arrayOf(h.oneOf(["day","month","year"]).isRequired),yearsOrder:h.oneOf(["asc","desc"]),yearsPerRow:h.oneOf([3,4])};const ot=["desktopModeMediaQuery"],at=e.forwardRef((function(e,n){const o=t({props:e,name:"MuiDatePicker"}),{desktopModeMediaQuery:i=ye}=o,l=s(o,ot);return g(i,{defaultMatches:!0})?r.jsx(st,a({ref:n},l)):r.jsx(nt,a({ref:n},l))}));const rt=new class{async fetchAll(){return(await ke.get("/homePorts")).data}},it=({artifact:t,artifactInfowWindow:s,vesselInfo:n,user:o})=>{const[a,i]=e.useState(null),[l,c]=e.useState(null),[d,u]=e.useState(!1),[m,h]=e.useState(null),[f,g]=e.useState(!0),j=o?.hasPermissions([_e.manageArtifacts]),v=e.useMemo((()=>Ae(m||t,n)),[m,t,n]),C=async()=>{await He.getUserFlaggedArtifactIds(),u(He.isArtifactFlaggedByUser(t._id))};e.useEffect((()=>{const e=Oe();return C(),(async()=>{try{g(!0);const e=await be.getArtifactDetail(t._id);h(e)}catch(e){if("AbortError"===e.name||"CanceledError"===e.name||"ERR_CANCELED"===e.code)return;h(t)}finally{g(!1)}})(),e.on("artifacts_flagged/changed",C),()=>e.off("artifacts_flagged/changed",C)}),[t._id]),e.useEffect((()=>{if(m){const e=m.thumbnail_url,t=m.video_url,s=m.image_url;m.video_path?(c(e||s||null),i(t||null)):(c(e||s||null),i(s||null))}}),[m]);return r.jsxs(x,{container:!0,direction:"column",sx:{padding:2,backgroundColor:"#343B44",color:"white",borderRadius:2,maxWidth:330,gap:2},children:[r.jsx("style",{children:"\n                    .gm-style-iw-chr, .gm-style-iw-tc {\n                        display: none !important;\n                    }\n                    .gm-style .gm-style-iw-c {\n                        background-color: #343B44 !important;\n                        outline: none;\n                        padding: 0;\n                    }\n                    .gm-style .gm-style-iw-d {\n                        overflow: auto !important;\n                    }\n                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {\n                        background-color: #fff !important;\n                    }\n                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {\n                        background: #343B44 !important;\n                    }\n                "}),r.jsxs(x,{container:!0,justifyContent:"space-between",alignItems:"center",children:[r.jsx(p,{variant:"h6",children:t.name||"Artifact"}),r.jsx(y,{onClick:()=>{s.close()},sx:{color:"white",border:"1px solid white","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(b,{sx:{fontSize:"14px"}})})]}),r.jsx(x,{sx:{position:"relative",backgroundColor:"#343B44",display:"flex",alignItems:"center",justifyContent:"center",height:200,borderRadius:1},children:f||!m?r.jsx(w,{variant:"rectangular",width:"100%",height:"100%",sx:{borderRadius:1,minHeight:200,minWidth:290}}):r.jsx(Be,{thumbnailLink:l,originalLink:a,cardId:m._id,isImage:!m.video_path,style:{borderRadius:8},showFullscreenIconForMap:!m.video_path,showVideoThumbnail:m.video_path,showArchiveButton:j,isArchived:m.is_archived,vesselId:m?.onboard_vessel_id,skeletonStyle:{minHeight:200,minWidth:290},flaggedArtifact:d})}),r.jsx(x,{children:f||!m?r.jsxs(r.Fragment,{children:[r.jsx(w,{variant:"text",width:"80%",height:24}),r.jsx(w,{variant:"text",width:"70%",height:24}),r.jsx(w,{variant:"text",width:"60%",height:24}),r.jsx(w,{variant:"text",width:"75%",height:24}),r.jsx(w,{variant:"text",width:"90%",height:24}),r.jsx(w,{variant:"text",width:"85%",height:24})]}):r.jsxs(r.Fragment,{children:[r.jsxs(p,{children:[r.jsx("strong",{children:"Super Category:"})," ",m.super_category||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Category:"})," ",m.category||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Color:"})," ",m.color||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Size:"})," ",m.size||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Location:"})," ",De([m.location?.coordinates?.[0],m.location?.coordinates?.[1]],!!o?.use_MGRS),r.jsx("br",{})]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Timestamp"}),":"," ",m.timestamp?Te(m.timestamp).tz(v).format(Pe.dateTimeFormat()):"Not available"," ",v&&Me(v)]}),r.jsx(p,{children:m.others||"Not available"})]})})]})},lt=e=>r.jsx(Fe,{children:r.jsx(it,{...e})}),ct=({currentArtifacts:t,currentClusterInfoWindow:s,vesselInfo:n,user:o})=>{const[a,i]=e.useState(0),[l,c]=e.useState([]),d=e.useMemo((()=>{const e=t.filter((e=>!l.includes(e._id)));return a>=e.length&&e.length>0&&i(e.length-1),e}),[t,l]),u=o?.hasPermissions([_e.manageArtifacts]),m=d[a],[h,f]=e.useState(null),[g,C]=e.useState(null),[S,k]=e.useState(!0),[F,_]=e.useState(!1),[A,O]=e.useState(!1),[D,T]=e.useState(null),M=e.useRef(new Map),P=e.useRef(new Set),I=e.useRef(null),E=e.useRef(0);e.useEffect((()=>{m&&d.length>0&&(k(!0),(async(e,t=!0)=>{I.current&&I.current.abort(),I.current=new AbortController;const s=++E.current;if(t&&M.current.has(e))return M.current.get(e);try{const t=await be.getArtifactDetail(e,I.current.signal);return s===E.current?(M.current.set(e,t),t):null}catch(n){return"AbortError"===n.name||"CanceledError"===n.name||"ERR_CANCELED"===n.code?null:d.find((t=>t._id===e))}})(m._id).then((e=>{const t=e.thumbnail_url,s=e.video_url,n=e.image_url;e&&m._id===d[a]?._id&&(T(e),e.video_path?(C(t||n||null),f(s||null)):(C(t||n||null),f(n||null)),k(!1),_(!1),(async()=>{const e=(a+1)%d.length,t=d[e];if(t&&!P.current.has(t._id)&&!M.current.has(t._id)){P.current.add(t._id);const e=new AbortController;try{const s=await be.getArtifactDetail(t._id,e.signal);e.signal.aborted||M.current.set(t._id,s)}catch(s){s.name}finally{P.current.delete(t._id)}}})())})))}),[m,a,d]);const L=e.useMemo((()=>Ae(D||m,n)),[D,m,n]),R=e=>{I.current&&I.current.abort(),k(!0),i((t=>"prev"===e?t>0?t-1:d.length-1:(t+1)%d.length))},z=async()=>{await He.getUserFlaggedArtifactIds(),m&&O(He.isArtifactFlaggedByUser(m._id))};e.useEffect((()=>{z()}),[]);return e.useEffect((()=>{const e=Oe(),t=e=>{const t=e?.artifact;t&&(c((e=>[...e,t._id])),M.current.delete(t._id),T(null),f(null),C(null),k(!0))};return e.on("artifact/changed",t),e.on("artifacts_flagged/changed",z),()=>{e.off("artifact/changed",t),e.off("artifacts_flagged/changed",z)}}),[]),e.useEffect((()=>()=>{I.current&&I.current.abort()}),[]),r.jsxs(x,{container:!0,direction:"column",style:{color:"white",padding:"20px",background:"#343B44",maxWidth:"330px",height:F?"auto":"570px"},children:[r.jsx("style",{children:"\n                    .gm-style-iw-chr, .gm-style-iw-tc {\n                        display: none !important;\n                    }\n                    .gm-style .gm-style-iw-c {\n                        background-color: #343B44 !important;\n                        outline: none;\n                        padding: 0;\n                    }\n                    .gm-style .gm-style-iw-d {\n                        overflow: auto !important;\n                    }\n                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {\n                        background-color: #fff !important;\n                    }\n                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {\n                        background: #343B44 !important;\n                    }\n                "}),r.jsxs(x,{sx:{height:F?"auto":"230px"},children:[r.jsxs(x,{container:!0,justifyContent:"space-between",alignItems:"center",style:{marginBottom:"10px"},children:[r.jsxs(p,{variant:"h6",children:["Artifact ",d.length>1?`${a+1} / ${d.length}`:""]}),r.jsx(y,{onClick:()=>{I.current&&I.current.abort(),s.close()},sx:{color:"white",border:"1px solid white","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(b,{sx:{fontSize:"16px"}})})]}),r.jsx(x,{sx:{position:"relative",backgroundColor:"#343B44",display:"flex",alignItems:"center",justifyContent:"center",height:200,borderRadius:1},children:S?r.jsx(w,{variant:"rectangular",width:"100%",height:"100%",sx:{borderRadius:1,minHeight:200,minWidth:290}}):r.jsx(Be,{thumbnailLink:g,originalLink:h,cardId:D?._id||m._id,isImage:!D?.video_path,style:{borderRadius:8},showFullscreenIcon:!0,showFullscreenIconForMap:!D?.video_path,showVideoThumbnail:D?.video_path,showArchiveButton:u,isArchived:D?.is_archived,vesselId:D?.onboard_vessel_id,skeletonStyle:{minHeight:200,minWidth:290},flaggedArtifact:A})})]}),r.jsx(x,{sx:{height:F?"auto":"265px",display:"flex",flexDirection:"column"},children:S||!D?r.jsxs(r.Fragment,{children:[r.jsx(w,{variant:"text",width:"80%",height:24,sx:{marginTop:"20px"}}),r.jsx(w,{variant:"text",width:"70%",height:24}),r.jsx(w,{variant:"text",width:"60%",height:24}),r.jsx(w,{variant:"text",width:"75%",height:24}),r.jsx(w,{variant:"text",width:"90%",height:24}),r.jsx(w,{variant:"text",width:"85%",height:24}),r.jsx(w,{variant:"text",width:"95%",height:24})]}):r.jsxs(r.Fragment,{children:[r.jsxs(p,{sx:{marginTop:"20px"},children:[r.jsx("strong",{children:"Super Category"}),": ",D.super_category||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Category"}),": ",D.category||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Size"}),": ",D.size||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Color"}),": ",D.color||"Not available"]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Location:"})," ",De([D.location?.coordinates?.[1],D.location?.coordinates?.[0]],!!o?.use_MGRS),r.jsx("br",{})]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Timestamp"}),":"," ",D.timestamp?Te(D.timestamp).tz(L).format(Pe.dateTimeFormat()):"Not available"," ",L&&Me(L)]}),D.others?r.jsxs(p,{sx:{textTransform:"lowercase"},children:[D.others&&(F||D.others.length<=90?D.others:D.others.slice(0,90)+"..."),D.others.length>90&&r.jsx("span",{onClick:()=>{_((e=>!e))},style:{color:"#007bff",cursor:"pointer",marginLeft:"5px"},children:F?"Show Less":"Read More"})]}):r.jsx(p,{children:"Not available"})]})}),r.jsx(x,{sx:{height:F?"auto":"35px"},children:d.length>1&&r.jsxs(x,{container:!0,justifyContent:"space-between",sx:{marginBottom:"1px"},children:[r.jsx(y,{onClick:()=>R("prev"),sx:{color:"white",fontSize:"24px",fontWeight:"bold","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(j,{sx:{fontSize:"24px"}})}),r.jsx(y,{onClick:()=>R("next"),sx:{color:"white",fontWeight:"bold","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(v,{sx:{fontSize:"24px"}})})]})})]})},dt=e=>r.jsx(Fe,{children:r.jsx(Ie,{children:r.jsx(ct,{...e})})});function ut(e){if(isNaN(e))return"00:00";const t=Math.floor(e/60),s=Math.floor(e%60);return`${String(t).padStart(2,"0")}:${String(s).padStart(2,"0")}`}function pt({src:t}){const s=e.useRef(),n=e.useRef(),[o,a]=e.useState(0),[i,l]=e.useState(!1),[c,d]=e.useState(0),[u,m]=e.useState(0);return r.jsxs(x,{style:{background:"#282C39",borderRadius:"10px",padding:"8px 12px",display:"flex",alignItems:"center",gap:"10px",color:"white",width:"100%",height:"40px"},children:[r.jsx(y,{onClick:()=>{s.current&&(s.current.paused?(s.current.play(),l(!0)):(s.current.pause(),l(!1)))},sx:{padding:0,background:"none",border:"none",color:"white",cursor:"pointer",fontWeight:"bold"},children:i?r.jsx(C,{sx:{fontSize:20}}):r.jsx(S,{sx:{fontSize:20}})}),r.jsx(x,{ref:n,onClick:e=>{if(!s.current||!n.current)return;const t=n.current.getBoundingClientRect(),o=(e.clientX-t.left)/t.width*u;s.current.currentTime=o,d(o),a(o/u*100),s.current.play(),l(!0)},style:{flex:1,height:"7px",background:"white",borderRadius:"3px",position:"relative",cursor:"pointer"},children:r.jsx(x,{style:{height:"7px",width:`${o}%`,background:"#3A8DFF",borderRadius:"3px"}})}),r.jsx(x,{style:{minWidth:"70px",textAlign:"right",fontSize:"12px"},children:r.jsxs(p,{sx:{fontWeight:"bold"},children:[ut(c)," / ",ut(u)]})}),r.jsx("audio",{ref:s,src:t,onTimeUpdate:()=>{if(!s.current)return;const e=s.current.currentTime;d(e),a(e/u*100||0)},onLoadedMetadata:()=>{s.current&&m(s.current.duration)},onEnded:()=>{l(!1)},style:{display:"none"}})]})}const mt=({audioPoint:t,audioInfoWindow:s,user:n})=>{const[o,a]=e.useState(null);e.useEffect((()=>{t&&(async({audioPoint:e})=>{try{const t=await Ue.getSignedUrl({bucket_name:e.bucket_name,key:e.audio_path,region:e.aws_region});a(t)}catch(t){}})({audioPoint:t})}),[t]);const i=t&&t.host_location&&Array.isArray(t.host_location.coordinates)&&t.host_location.coordinates.length>0;return r.jsxs(x,{container:!0,direction:"column",sx:{padding:2,backgroundColor:"#343B44",color:"white",borderRadius:2,maxWidth:330,gap:2},children:[r.jsx("style",{children:"\n                    .gm-style-iw-chr, .gm-style-iw-tc {\n                        display: none !important;\n                    }\n                    .gm-style .gm-style-iw-c {\n                        background-color: #343B44 !important;\n                        outline: none;\n                        padding: 0;\n                    }\n                    .gm-style .gm-style-iw-d {\n                        overflow: auto !important;\n                    }\n                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {\n                        background-color: #fff !important;\n                    }\n                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {\n                        background: #343B44 !important;\n                    }\n                    \n                    audio {\n                     background-color: #343B44 !important;\n                    }\n                "}),r.jsxs(x,{container:!0,justifyContent:"space-between",alignItems:"center",children:[r.jsx(p,{variant:"h6",children:"Artifact Audio"}),r.jsx(y,{onClick:()=>{s.close()},sx:{color:"white",border:"1px solid white","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(b,{sx:{fontSize:"14px"}})})]}),r.jsx(x,{sx:{position:"relative",backgroundColor:"#343B44",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:1,marginBottom:"10px",marginTop:"10px"},children:o?r.jsx(pt,{src:o}):r.jsx(w,{sx:{width:"100%",height:"50px"}})}),r.jsxs(x,{children:[r.jsxs(p,{children:[r.jsx("strong",{children:"Frequency:"})," ",t.frequency||"Not available"]}),i&&r.jsxs(p,{children:[r.jsx("strong",{children:"Location:"})," ",De(t.host_location.coordinates,!(!n||!n.use_MGRS))]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Timestamp"}),":"," ",t.timestamp?Te(t.timestamp).format(Pe.dateTimeFormat()):"Not available"," "]})]})]})},ht=e=>r.jsx(Fe,{children:r.jsx(mt,{...e})}),ft=({markers:t,currentAudioClusterInfoWindow:s,user:n})=>{const[o,a]=e.useState(0),i=t.map((e=>e.audioArtifactData)),l=i[o],[c,d]=e.useState(null),[u,m]=e.useState(!0),h=e.useRef(null);e.useEffect((()=>{l&&f({audio:l})}),[l]);const f=async({audio:e})=>{try{const t=await Ue.getSignedUrl({bucket_name:e.bucket_name,key:e.audio_path,region:e.aws_region});d(t),t&&m(!1)}catch(t){}},g=e=>{h.current&&(h.current.pause(),h.current.currentTime=0),m(!0),a((t=>"prev"===e?t>0?t-1:i.length-1:(t+1)%i.length))},C=l&&l.host_location&&Array.isArray(l.host_location.coordinates)&&l.host_location.coordinates.length>0;return r.jsxs(x,{container:!0,direction:"column",style:{color:"white",padding:"20px",background:"#343B44",maxWidth:"330px",height:"auto"},children:[r.jsx("style",{children:"\n                    .gm-style-iw-chr, .gm-style-iw-tc {\n                        display: none !important;\n                    }\n                    .gm-style .gm-style-iw-c {\n                        background-color: #343B44 !important;\n                        outline: none;\n                        padding: 0;\n                    }\n                    .gm-style .gm-style-iw-d {\n                        overflow: auto !important;\n                    }\n                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {\n                        background-color: #fff !important;\n                    }\n                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {\n                        background: #343B44 !important;\n                    }\n                "}),r.jsxs(x,{sx:{height:"auto"},children:[r.jsxs(x,{container:!0,justifyContent:"space-between",alignItems:"center",style:{marginBottom:"10px"},children:[r.jsxs(p,{variant:"h6",children:["Artifact Audio ",i.length>1?`${o+1} / ${i.length}`:""]}),r.jsx(y,{onClick:()=>{s.close()},sx:{color:"white",border:"1px solid white","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(b,{sx:{fontSize:"16px"}})})]}),r.jsx(x,{sx:{position:"relative",backgroundColor:"#343B44",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:1},children:u?r.jsx(w,{sx:{width:"100%",height:"50px"}}):r.jsx(pt,{src:c})})]}),r.jsxs(x,{sx:{height:"auto",display:"flex",flexDirection:"column"},children:[r.jsxs(p,{children:[r.jsx("strong",{children:"Frequency:"})," ",l.frequency||"Not available"]}),C&&r.jsxs(p,{children:[r.jsx("strong",{children:"Location:"})," ",De(l.host_location.coordinates,!(!n||!n.use_MGRS))]}),r.jsxs(p,{children:[r.jsx("strong",{children:"Timestamp"}),":"," ",l.timestamp?Te(l.timestamp).format(Pe.dateTimeFormat()):"Not available"," "]})]}),r.jsx(x,{sx:{height:"auto"},children:i.length>1&&r.jsxs(x,{container:!0,justifyContent:"space-between",sx:{marginBottom:"1px"},children:[r.jsx(y,{onClick:()=>g("prev"),sx:{color:"white",fontSize:"24px",fontWeight:"bold","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(j,{sx:{fontSize:"24px"}})}),r.jsx(y,{onClick:()=>g("next"),sx:{color:"white",fontWeight:"bold","&:hover":{backgroundColor:"white",color:"#4F5968"}},children:r.jsx(v,{sx:{fontSize:"24px"}})})]})})]})},gt=e=>r.jsx(Fe,{children:r.jsx(ft,{...e})}),xt=[{url:`data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="${Pe.icons.image}" /></svg>`,height:50,width:50,textColor:"#FFFFFF",textSize:14},{url:`data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="${Pe.icons.image}" /></svg>`,height:60,width:60,textColor:"#FFFFFF",textSize:16},{url:`data:image/svg+xml;charset=UTF-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="${Pe.icons.image}" /></svg>`,height:70,width:70,textColor:"#FFFFFF",textSize:18}],yt=(e,t,s,n)=>{const o=new s.maps.OverlayView;o.draw=function(){},o.setMap(n);let a=null;if(e instanceof s.maps.Marker)a=o.getProjection().fromLatLngToDivPixel(e.getPosition());else{if(!(e instanceof s.maps.Data.Feature))return;{const t=e.getGeometry().getArray(),s=t[Math.floor(t.length/2)];a=o.getProjection().fromLatLngToDivPixel(s)}}var r,i="";return i+=a.y>0?"b":"t","tr"===(i+=a.x<0?"l":"r")?r=new s.maps.Size("coordinate"===t?-150:"artifact"===t?-290:"audio"===t?-390:"ais"===t?-150:0,"coordinate"===t?100:"artifact"===t?490:"audio"===t?250:"ais"===t?200:0):"tl"===i?r=new s.maps.Size("coordinate"===t?150:"artifact"===t?290:"audio"===t?320:"ais"===t?150:0,"coordinate"===t?100:"artifact"===t?490:"audio"===t?250:"ais"===t?200:0):"br"===i?r=new s.maps.Size("coordinate"===t?-130:"artifact"===t?-200:"audio"===t?-250:"ais"===t?-130:0,"coordinate"===t?0:"artifact"===t?100:"audio"===t?50:0):"bl"===i&&(r=new s.maps.Size("coordinate"===t?130:"artifact"===t?200:"audio"===t||"ais"===t?130:0,"coordinate"===t?0:"artifact"===t?100:"audio"===t?50:0)),r},bt=(e,t,s,n,o,a,r,i,l,c,d,u,p,m,h,f,g,x,y,b)=>{Date.now();const w=[];if(Object.keys(t).forEach((e=>{n[e]&&s.current[e]&&s.current[e]===t[e].length&&w.push(e)})),Object.keys(n).filter((e=>n[e])).filter((e=>!w.includes(e))).forEach((e=>{n[e].forEach((e=>{e&&e.setMap(null)})),n[e]=null})),Object.keys(a).filter((e=>!w.includes(e))).forEach((e=>{const t=a[e];t.forEach((e=>t.remove(e))),t.setMap(null),delete a[e]})),!u||0===Object.keys(t).length)return;const j=Te(c).valueOf(),v="now"===d?Te().valueOf():Te(d).valueOf();Object.keys(t).filter((e=>!w.includes(e))).forEach((s=>{const n=t[s],a=l[s],c=a?.filter((e=>{const t=new Date(e[1]).getTime();return t>=j&&t<=v})),d=(Date.now(),((e,t)=>{if(!e||0===e.length)return[];const s=[];let n=[],o=null;return e.forEach(((a,r)=>{const i={lat:a[2],lng:a[3],timestamp:a[1],isStationary:a[4]};return 0===r?(n.push(i),void(o=i)):e.length===r+1?(s.push({path:[n[n.length-1],i],polylineType:Pe.polylineTypes.SOLID}),void(o=i)):!t&&o.isStationary&&i.isStationary?(o.stationary_connected=!0,void(o=i)):!t&&o.stationary_connected?(s.push({path:[o,i],polylineType:Pe.polylineTypes.SOLID}),n=[i],void(o=i)):(new Date(i.timestamp)-new Date(o.timestamp)>72e5?(n.length>0&&s.push({path:[...n],polylineType:Pe.polylineTypes.SOLID}),s.push({path:[o,i],polylineType:Pe.polylineTypes.DASHED}),n=[i]):n.push(i),void(o=i))})),n.length>0&&s.push({path:[...n],polylineType:Pe.polylineTypes.SOLID}),s})(c,x));if(n.length>0){const t=d.map((({path:e,polylineType:t})=>{const n=new f.maps.Polyline({path:e.map((e=>({lat:e.lat,lng:e.lng}))),geodesic:!0,strokeColor:Pe.polylineColors[s]||"#FF0000",strokeOpacity:t==Pe.polylineTypes.SOLID?1:0,strokeWeight:2,zIndex:-999,icons:t==Pe.polylineTypes.DASHED?[{icon:{path:"M 0,-2 0,2",strokeOpacity:1,scale:2,strokeColor:Pe.polylineColors[s]||"#FF0000"},offset:"0",repeat:"20px"}]:t===Pe.polylineTypes.DOTTED?[{icon:{path:f.maps.SymbolPath.CIRCLE,fillOpacity:1,scale:2,strokeColor:Pe.polylineColors[s]||"#FF0000",fillColor:Pe.polylineColors[s]||"#FF0000"},offset:"0",repeat:"10px"}]:null});return n.setMap(u),n}));o((e=>({...e,[s]:t})));const a=((e,t)=>{let s=null;return e.filter(((e,n)=>{if(0===n)return s=e,!0;const o=new Date(s[1]).getTime();return(new Date(e[1]).getTime()-o)/6e4>=t&&(s=e,!0)}))})(n,y),l=((e,t,s)=>{if(!t)return[];const n=[];return e.forEach(((o,a)=>{if(0===n.length||a===e.length-1)return n.push(o);const r=n[n.length-1];t.maps.geometry.spherical.computeDistanceBetween(new t.maps.LatLng(r[2],r[3]),new t.maps.LatLng(o[2],o[3]))>s&&n.push(o)})),n})(a,f,m).filter(((e,t,s)=>!!h||(0===t||t===s.length-1))),c={type:"FeatureCollection",features:l.map(((e,t,s)=>({type:"Feature",geometry:{type:"LineString",coordinates:[[e[3],e[2]],[e[3],e[2]]]},properties:{rotation:t<s.length-1?e[4]?f.maps.geometry.spherical.computeHeading(new f.maps.LatLng(e[2],e[3]),new f.maps.LatLng(s[t+1][2],s[t+1][3])):e[5]:0,coordinate:e,isFirstCoordinate:0===t}})))},x=new f.maps.Data;x.addGeoJson(c),x.setStyle((e=>({zIndex:999,strokeWeight:15,fillColor:"transparent",strokeColor:"transparent",icons:[{icon:{path:e.getProperty("isFirstCoordinate")?f.maps.SymbolPath.CIRCLE:f.maps.SymbolPath.FORWARD_CLOSED_ARROW,scale:e.getProperty("isFirstCoordinate")?4:2,strokeColor:"#000000",strokeWeight:e.getProperty("isFirstCoordinate")?1:.5,rotation:e.getProperty("isFirstCoordinate")?0:e.getProperty("rotation"),offset:"0%",anchor:e.getProperty("isFirstCoordinate")?new f.maps.Point(0,0):new f.maps.Point(0,3),fillColor:Pe.polylineColors[s]||"#0000FF",fillOpacity:1}}]}))),x.setMap(u),x.addListener("mouseover",(async t=>{const n=t.feature,o=n.getProperty("coordinate");let a=g,r="";const l=b&&b.find((e=>e.vessel_id===s));l&&l.timezone&&(a=l.timezone,r=Me(a));const c=`\n                            <div style="color: #fff; align-items: center; padding: 15px;">\n                                <strong>${i.find((e=>e.id===s))?.name}</strong><br/>\n                                <strong>Location:</strong> ${De([o[3],o[2]],!!e?.use_MGRS)}<br/>\n                                <strong>Time:</strong> ${Te(o[1]).tz(a).format(Ee.dateTimeFormat(e))} ${r} <br/>\n                                <style>\n                                    .gm-style-iw-chr {\n                                        display: none !important; /* Hide the close button */;\n                                    }\n                                    .gm-style-iw-tc {\n                                        display: none !important; /* Hide the close button */;\n                                    }\n                                    .gm-style .gm-style-iw-c  {\n                                        background-color: #343B44 !important;\n                                        outline: none;\n                                        padding: 0;\n                                    }\n                                    .gm-style .gm-style-iw-d {\n                                        overflow:auto !important;\n                                    }\n                                    p {\n                                        margin: 0;\n                                        color:white\n                                    }\n                                    strong {\n                                        color:white\n                                    }\n                                </style>\n                            </div>\n                        `;p.setContent(c),p.setPosition(t.latLng),p.open(u);const d=await yt(n,"coordinate",f,u);p.setOptions({pixelOffset:d})})),x.addListener("mouseout",(()=>{p.close()})),r((e=>({...e,[s]:x})))}}))},wt=({user:e,aisLayer:t,setAisDataLayers:s,aisData:n,map:o,infoWindow:a,google:r,timezone:i,vesselInfo:l,showAisData:c})=>{Date.now();if(!c)return t&&(t.setMap(null),t=null),void s(null);if(t&&t.setMap(null),!o)return;const d={type:"FeatureCollection",features:n.map(((e,t)=>({type:"Feature",geometry:{type:"LineString",coordinates:[[e.location.coordinates[0],e.location.coordinates[1]],[e.location.coordinates[0],e.location.coordinates[1]]]},properties:{aisPoint:e,index:t,rotation:e.metadata.message.nav_heading_true}})))};(t=new r.maps.Data).addGeoJson(d);t.setStyle((e=>{const t=function({rotationInRadians:e}){return e*(180/Math.PI)}({rotationInRadians:e.getProperty("rotation")}),s=0===t,n=e.getProperty("aisPoint"),o=ze(n.metadata.message.design_ais_ship_type_name)||"#8B5CF6";return{zIndex:999,strokeWeight:15,fillColor:"transparent",strokeColor:"transparent",icons:[{icon:{path:s?r.maps.SymbolPath.CIRCLE:"M12 2L16 5V18L12 16L8 18V5Z",scale:s?3:.9,strokeColor:"#000000",strokeWeight:1,fillColor:o,fillOpacity:1,anchor:s?new r.maps.Point(0,0):new r.maps.Point(12,12),rotation:s?0:t}}]}})),t.setMap(o),t.addListener("mouseover",(async t=>{const s=t.feature,n=s.getProperty("aisPoint");let c=i,d="";const u=l&&l.find((e=>e.vessel_id===n.onboard_vessel_id));u&&u.timezone&&(c=u.timezone,d=Me(c));const p=`\n                    <div style="color: #fff; align-items: center; padding: 15px;">\n                       ${n.mmsi?`<strong>MMSI:</strong> ${n.mmsi}<br/>`:""}\n                        ${n.name?`<strong>Name:</strong> ${n.name}<br/>`:""}\n                        ${n.metadata.message.sensor_ais_class?`<strong>AIS Ship Message Class:</strong> ${n.metadata.message.sensor_ais_class}<br/>`:""}\n                        ${n.metadata.message.nav_state?`<strong>AIS Ship  State :</strong> ${n.metadata.message.nav_state}<br/>`:""}\n                        ${n.metadata.message.design_ais_ship_type_name?`<strong>AIS Ship Type:</strong> ${n.metadata.message.design_ais_ship_type_name}<br/>`:""}\n                        ${n.metadata.message.design_length_type?`<strong>Ship Length Type:</strong> ${n.metadata.message.design_length_type}<br/>`:""}\n                        <strong>Location:</strong> ${De(n.location.coordinates,!!e?.use_MGRS)}<br/>\n                        ${n.metadata.message.design_beam?`<strong>Ship Beam:</strong> ${n.metadata.message.design_beam}<br/>`:""}\n                        ${n.metadata.message.design_length?`<strong>Ship Length:</strong> ${n.metadata.message.design_length}<br/>`:""}\n                        ${n.metadata.message.nav_speed_over_ground?`<strong>Speed over ground:</strong> ${n.metadata.message.nav_speed_over_ground}<br/>`:""}\n                        ${n.metadata.message.comm_callsign_vhf?`<strong>VHF Call Sign:</strong> ${n.metadata.message.comm_callsign_vhf}<br/>`:""}\n                        <strong>Timestamp:</strong> ${Te(n.timestamp).tz(c).format(Ee.dateTimeFormat(e))} ${d}<br/>\n\n                        <style>\n                            .gm-style-iw-chr {\n                                display: none !important;\n                            }\n                            .gm-style-iw-tc {\n                                display: none !important;\n                            }\n                            .gm-style .gm-style-iw-c  {\n                                background-color: #343B44 !important;\n                                outline: none;\n                                padding: 0;\n                            }\n                            .gm-style .gm-style-iw-d {\n                                overflow:auto !important;\n                            }\n                            p {\n                                margin: 0;\n                                color:white\n                            }\n                            strong {\n                                color:white\n                            }\n                        </style>\n                    </div>\n                `;a.setContent(p),a.setPosition(t.latLng),a.open(o);const m=await yt(s,"ais",r,o);a.setOptions({pixelOffset:m})})),t.addListener("mouseout",(()=>{a.close()})),s(t)},jt=({user:e,filteredAudioData:t,prevFilteredAudioData:s,audioDataMarkers:n,setAudioDataMarkers:o,audioClustererRef:a,map:r,google:i,showAudioData:l,audioInfoWindow:c,currentAudioClusterInfoWindow:d,artifactInfowWindow:u,currentClusterInfoWindow:p,infoWindow:m})=>{Date.now();const h=[];if(!l)return Object.keys(n).forEach((e=>{n[e].forEach((e=>{e.setMap(null),i.maps.event.clearInstanceListeners(e)})),n[e]=[]})),o({}),void(a.current&&(a.current.clearMarkers(),a.current=null));if(Object.keys(t).forEach((e=>{n[e]&&s.current[e]&&s.current[e]===t[e].length&&h.push(e)})),Object.keys(n).filter((e=>!h.includes(e))).forEach((e=>{n[e].forEach((e=>{e.setMap(null),i.maps.event.clearInstanceListeners(e)})),n[e]=[]})),!r||0===Object.keys(t).length)return;const f=Object.keys(n).map((e=>n[e])).flat();Object.keys(t).filter((e=>!h.includes(e))).forEach((s=>{const n=t[s];if(0===n.length)return;const a=Pe.polylineColors[s]||"#8B5CF6",l=`data:image/svg+xml;charset=UTF-8,${encodeURIComponent((d=a,`\n            <svg width="11" height="14" viewBox="0 0 11 14" xmlns="http://www.w3.org/2000/svg">\n            <path d="M1.83301 0.150391H6.11621C6.56579 0.150472 6.99475 0.31964 7.30957 0.620117L10.3604 3.53418V3.53516C10.675 3.83549 10.8496 4.24195 10.8496 4.66504V12.25C10.8496 13.1258 10.1018 13.8494 9.16699 13.8496H1.83301C0.898152 13.8494 0.150391 13.1258 0.150391 12.25V1.75C0.150391 0.87421 0.898152 0.150559 1.83301 0.150391ZM7.58203 6.53809C7.25693 6.32348 6.81134 6.39623 6.58203 6.71191C6.35071 7.03085 6.43315 7.46589 6.7627 7.68457V7.68555C7.40898 8.11661 7.8252 8.82593 7.8252 9.625C7.8252 10.3743 7.45717 11.0473 6.88086 11.4814L6.76367 11.5645C6.43354 11.7827 6.34971 12.221 6.58203 12.5381C6.81052 12.8497 7.25919 12.9273 7.58301 12.7109C8.59776 12.0347 9.27051 10.9042 9.27051 9.625C9.27051 8.34582 8.60018 7.21539 7.58203 6.53906V6.53809ZM4.11621 7.28711C3.93403 7.28717 3.75943 7.36544 3.64355 7.50488L2.90918 8.38086H2.29199C1.96372 8.38086 1.68359 8.63878 1.68359 8.96875V10.2812C1.68359 10.6112 1.96372 10.8691 2.29199 10.8691H2.90918L3.64258 11.7432V11.7441C3.75845 11.8842 3.93358 11.9628 4.11621 11.9629H4.125C4.45327 11.9629 4.7334 11.705 4.7334 11.375V7.875C4.7334 7.54503 4.45327 7.28711 4.125 7.28711H4.11621ZM6.59082 8.30762C6.41732 8.17555 6.21371 8.18422 6.05957 8.28027C5.91019 8.37339 5.80873 8.54654 5.80859 8.74414V10.5059C5.80873 10.7036 5.91021 10.876 6.05957 10.9688C6.21345 11.0642 6.41652 11.0724 6.58984 10.9424H6.59082C6.99473 10.6345 7.25391 10.1593 7.25391 9.625C7.25391 9.09074 6.99473 8.61546 6.59082 8.30762ZM5.80859 4.15625C5.80859 4.60916 6.18847 4.96272 6.64551 4.96289H9.69824L9.42773 4.7041L6.06152 1.49121L5.80859 1.24902V4.15625Z" fill="${d}" stroke="black" stroke-width="0.3"/>\n            </svg>\n            `))}`;var d;const u=n.map((t=>{const n=new i.maps.Marker({position:{lat:t.host_location.coordinates[1],lng:t.host_location.coordinates[0]},icon:{url:l,scaledSize:new i.maps.Size(20,20),scale:6,strokeColor:"#FFFFFF",strokeWeight:2,fillColor:Pe.polylineColors[s]||"#8B5CF6",fillOpacity:.8},zIndex:1e3,map:null});return n.audioArtifactData=t,n.addListener("click",(async()=>{const s=document.createElement("div");Re.createRoot(s).render(k.createElement(ht,{audioPoint:t,audioInfoWindow:c,user:e})),c.setContent(s);const o=await yt(n,"audio",i,r);c.setOptions({pixelOffset:o}),c.open(r,n)})),f.push(n),n}));o((e=>({...e,[s]:u})))})),a.current&&a.current.clearMarkers(),a.current=new ve({markers:f,map:r,zoomOnClick:!1,styles:xt,algorithm:new Ce({maxZoom:22,radius:30}),onClusterClick:()=>{}});a.current.addListener("click",(async t=>{const s=t.markers,n=document.createElement("div"),o=(e=>{const t=new i.maps.OverlayView;t.draw=function(){},t.setMap(r);const s=e._position,n=t.getProjection().fromLatLngToDivPixel(s);if(!n)return new i.maps.Size(0,0);const o=`${n.y>0?"b":"t"}${n.x<0?"l":"r"}`;return"tr"==o?new i.maps.Size(-200,250):"tl"==o?new i.maps.Size(200,440):"br"==o?new i.maps.Size(-220,80):"bl"==o?new i.maps.Size(200,40):void 0})(t);u&&u.close(),p&&p.close(),m&&m.close();Re.createRoot(n).render(k.createElement(gt,{markers:s,currentAudioClusterInfoWindow:d,user:e})),d?(d.setContent(n),d.setOptions({pixelOffset:o})):d=new i.maps.InfoWindow({content:n,pixelOffset:o}),d.setPosition(t._position),d.open(r)}))},vt=(e,t,s,n,o,a,r,i,l,c,d,u,p,m)=>{Date.now();if(!e)return Object.keys(t.current).forEach((e=>{t.current[e].forEach((e=>{e.setMap(null),s.maps.event.clearInstanceListeners(e)})),t.current[e]=[]})),void(n.current&&(n.current.clearMarkers(),n.current.setMap(null),n.current=null));const h=[];if(Object.keys(a).forEach((e=>{t.current[e]&&t.current[e].length&&r.current[e]&&r.current[e]===a[e].length&&h.push(e)})),Object.keys(t.current).filter((e=>!h.includes(e))).forEach((e=>{t.current[e].forEach((e=>{e.setMap(null),s.maps.event.clearInstanceListeners(e)})),t.current[e]=[]})),!o||0===Object.keys(a).length)return;const f=Object.keys(t.current).map((e=>t.current[e])).flat();Object.keys(a).filter((e=>!h.includes(e))).forEach((e=>{const n=a[e].map((t=>{if(!t.location)return null;const n=Pe.polylineColors[e]||"#3873E4",a=t.video_exists?`data:image/svg+xml;charset=UTF-8,${encodeURIComponent((r=n,`\n                <svg width="40" height="35" viewBox="0 0 40 35" xmlns="http://www.w3.org/2000/svg">\n                    <path d="M17.1777 9.7661L17.1775 9.766C16.9126 9.60044 16.6082 9.5088 16.2959 9.5006C15.9837 9.4924 15.6749 9.56794 15.4017 9.71937C15.1284 9.87081 14.9007 10.0926 14.7422 10.3618C14.5836 10.6309 14.5 10.9376 14.5 11.25V24.1249C14.5 24.1249 14.5 24.125 14.5 24.125C14.5 24.4413 14.5856 24.7516 14.7479 25.0231C14.9102 25.2946 15.143 25.517 15.4216 25.6667C15.7002 25.8164 16.0142 25.8878 16.3302 25.8734C16.6461 25.8589 16.9523 25.759 17.216 25.5844L17.2161 25.5844L27.2161 18.9619L27.2167 18.9615C27.4603 18.7996 27.6594 18.5793 27.7959 18.3207C27.9324 18.0621 28.0019 17.7734 27.9981 17.481C27.9942 17.1886 27.9172 16.9018 27.774 16.6468C27.6308 16.3919 27.4259 16.1769 27.1782 16.0214L27.1777 16.0211L17.1777 9.7661ZM2.18414 2.18414C3.26247 1.1058 4.72501 0.5 6.25 0.5H33.75C35.275 0.5 36.7375 1.1058 37.8159 2.18414C38.8942 3.26247 39.5 4.725 39.5 6.25V28.75C39.5 30.275 38.8942 31.7375 37.8159 32.8159C36.7375 33.8942 35.275 34.5 33.75 34.5H6.25C4.725 34.5 3.26247 33.8942 2.18414 32.8159C1.1058 31.7375 0.5 30.275 0.5 28.75V6.25C0.5 4.72501 1.1058 3.26247 2.18414 2.18414Z" fill="${r}" stroke="#282C39"/>\n                </svg>\n            `))}`:`data:image/svg+xml;charset=UTF-8,${encodeURIComponent((e=>`\n                <svg width="40" height="36" viewBox="0 0 40 36" xmlns="http://www.w3.org/2000/svg">\n                    <path d="M21.7871 13.543L15.374 22.9488L13.7263 20.8906L13.725 20.8891C13.2717 20.3273 12.5901 20 11.875 20C11.1636 20 10.4713 20.3246 10.0226 20.892C10.0223 20.8924 10.022 20.8929 10.0216 20.8933L5.02469 27.1395C5.02455 27.1396 5.02441 27.1398 5.02427 27.14C4.4496 27.8538 4.34146 28.8337 4.73708 29.6546C5.13248 30.475 5.96319 31 6.875 31H33.125C34.0038 31 34.8173 30.516 35.225 29.7303C35.6283 28.9533 35.5816 28.0138 35.0845 27.288C35.0845 27.2879 35.0844 27.2878 35.0844 27.2878L25.71 13.5386C25.2656 12.8869 24.5331 12.5 23.75 12.5C22.9711 12.5 22.2279 12.8836 21.7887 13.5406C21.7881 13.5414 21.7876 13.5422 21.7871 13.543ZM0.5 5.5C0.5 3.01833 2.51833 1 5 1H35C37.4817 1 39.5 3.01833 39.5 5.5V30.5C39.5 32.9817 37.4817 35 35 35H5C2.51833 35 0.5 32.9817 0.5 30.5V5.5ZM8.75 13.5C9.87717 13.5 10.9582 13.0522 11.7552 12.2552C12.5522 11.4582 13 10.3772 13 9.25C13 8.12283 12.5522 7.04183 11.7552 6.2448C10.9582 5.44777 9.87717 5 8.75 5C7.62283 5 6.54183 5.44777 5.7448 6.2448C4.94777 7.04183 4.5 8.12283 4.5 9.25C4.5 10.3772 4.94777 11.4582 5.7448 12.2552C6.54183 13.0522 7.62283 13.5 8.75 13.5Z" fill="${e}" stroke="#282C39"/>\n                </svg>\n            `)(n))}`;var r;const c=t.location.coordinates[1],d=t.location.coordinates[0],h=new s.maps.Marker({position:{lat:c,lng:d},icon:{url:a,scaledSize:new s.maps.Size(20,20)},map:null});return h.artifactId=t._id,h.addListener("click",(async()=>{Le("ArtifactViewed",{artifactId:t._id}),i&&i.close(),l&&l.close();const e=document.createElement("div");Re.createRoot(e).render(k.createElement(lt,{artifact:t,artifactInfowWindow:i,timezone:p,vesselInfo:m,user:u})),i.setContent(e);const n=await yt(h,"artifact",s,o);i.setOptions({pixelOffset:n}),i.open(o,h)})),f.push(h),h})).filter(Boolean);t.current={...t.current,[e]:n}})),n.current&&n.current.clearMarkers(),n.current=new ve({markers:f,map:o,zoomOnClick:!1,styles:xt,algorithm:new Ce({maxZoom:22,radius:30}),onClusterClick:()=>{Le("ClusterOpened",{artifactIds:f.map((e=>e.artifactId)).filter(Boolean),count:f.length})}});n.current.addListener("click",(async e=>{Le("ClusterOpened",{artifactIds:e.markers.map((e=>e.artifactId)).filter(Boolean),count:e.markers.length});const t=e.markers.map((e=>e.artifactId)).filter(Boolean),n=Object.values(a).flat().filter((e=>t.includes(e._id))),r=document.createElement("div"),h=(e=>{const t=new s.maps.OverlayView;t.draw=function(){},t.setMap(o);const n=e._position,a=t.getProjection().fromLatLngToDivPixel(n);if(!a)return new s.maps.Size(0,0);const r=`${a.y>0?"b":"t"}${a.x<0?"l":"r"}`;return"tr"==r?new s.maps.Size(-300,450):"tl"==r?new s.maps.Size(300,540):"br"==r?new s.maps.Size(-220,80):"bl"==r?new s.maps.Size(200,40):void 0})(e);i&&i.close(),l&&l.close(),c&&c.close(),d&&d.close();Re.createRoot(r).render(k.createElement(dt,{currentArtifacts:n,currentClusterInfoWindow:l,timezone:p,vesselInfo:m,user:u})),l?(l.setContent(r),l.setOptions({pixelOffset:h})):l=new s.maps.InfoWindow({content:r,pixelOffset:h}),l.setPosition(e._position),l.open(o)}))},Ct=({vessels:t=[],loadingVessels:s,setLoadingVessels:n,errorsomeVessels:o,setAvailableVessels:a,setEmptyVessels:i,emptyVessels:l,focusedVessel:c,datapointsDistance:d=Pe.datapointsDistance,journeyStart:u=Pe.journeyStart,journeyEnd:p=Pe.journeyEnd,initialZoom:m=Pe.zoom,interval:h=Pe.interval,precision:f=Pe.precision,setArtifactsCategories:g,selectedArtifactsCategory:x,timeSlider:y,showDatapoints:b,showArtifacts:j,showAisData:v,showAudioData:C,homePortsArtifactsMode:S,selectedNumberOfArtifacts:k,artifactsType:_,showFilteredCoordinates:A,setSelectedArtifactsCategory:O})=>{const D={lat:0,lng:0},{google:T,timezone:M}=je(),{vesselInfo:P}=Ge(),I=We(),{pathname:E}=F(),[L,R]=e.useState(null),[z,V]=e.useState(!0),[W,N]=e.useState({}),[$,B]=e.useState({}),[H,U]=e.useState({}),[G,Z]=e.useState({}),[Y,X]=e.useState([]),[K,q]=e.useState(null),[J,Q]=e.useState({}),[ee,te]=e.useState({}),[se,ne]=e.useState(!0),[oe,ae]=e.useState({}),re=e.useRef(null),ie=e.useRef(null),le=e.useRef([]),[ce,de]=e.useState(!0),[ue,pe]=e.useState({}),me=e.useRef({}),[he,fe]=e.useState({}),[ge,xe]=e.useState(D),ye=e.useMemo((()=>T&&new T.maps.InfoWindow({disableAutoPan:!0})),[T]),be=e.useMemo((()=>T&&new T.maps.InfoWindow({disableAutoPan:!0})),[T]),we=e.useMemo((()=>T&&new T.maps.InfoWindow({disableAutoPan:!0})),[T]);let ve=e.useMemo((()=>T&&new T.maps.InfoWindow({disableAutoPan:!0})),[T]),Ce=e.useMemo((()=>T&&new T.maps.InfoWindow({disableAutoPan:!0})),[T]);const Fe=e.useRef({}),_e=e.useRef({}),Ae=e.useRef({});e.useRef({});const{user:De}=Ne();e.useEffect((()=>{})),e.useEffect((()=>{const e=$[c];if(!e&&ge)return;if(!e&&!ge)return xe(D);const t=e?.[e.length-1];t&&xe({lat:t[2],lng:t[3]})}),[c]);const Me=e.useCallback((e=>{L&&(_e.current=Object.keys(e).reduce(((t,s)=>({...t,[s]:e[s].length})),{}))}),[L]),Ie=e.useCallback((e=>{L&&(Ae.current=Object.keys(e).reduce(((t,s)=>({...t,[s]:e[s].length})),{}))}),[L]);e.useEffect((()=>{Me(he);(async()=>{const e={...ue};Object.keys(e).forEach((t=>{e[t]=ue[t].filter((e=>Te(e.timestamp).valueOf()>=u.valueOf()&&Te(e.timestamp).valueOf()<=("now"===p?Te().valueOf():p.valueOf()))).filter((e=>!(!x||0===x.length)&&x.includes(e.super_category))).filter((e=>"video"===_?e.video_exists:"image"!==_||!e.video_exists)),e[t].length>=k&&"all"!==k&&(e[t]=e[t].slice(0,k-1))}));const t=await(async(e,t)=>{if(!e||0===Object.keys(e).length)return e;if("ALL"===t)return e;try{const s=await rt.fetchAll();if(!s||0===s.length)return e;const n={},o=Pe.HOME_PORT_RADIUS;for(const a in e){const r=e[a];"ONLY_NON_HOME_PORTS"===t?n[a]=r.filter((e=>!s.some((t=>Ve({lat:e.location.coordinates[1],lng:e.location.coordinates[0]},{lat:t.lat,lng:t.lng})<=o)))):"ONLY_HOME_PORTS"===t&&(n[a]=r.filter((e=>s.some((t=>Ve({lat:e.location.coordinates[1],lng:e.location.coordinates[0]},{lat:t.lat,lng:t.lng})<=o)))))}return n}catch(s){return e}})(e,S);fe(t)})()}),[ue,x,k,_,u,p,S]);const Ee=e.useCallback((e=>{L&&(Fe.current=Object.keys(e).reduce(((t,s)=>({...t,[s]:e[s].length})),{}))}),[L]);e.useEffect((()=>{Date.now();Ee($);const e={},t=Te(u).valueOf(),s="now"===p?Te().valueOf():Te(p).valueOf();Object.keys(W).forEach((n=>{const o=[],a=W[n];let r=null;for(const e of a){const n=new Date(e[1]).getTime();n<t||n>s||(e[2]=parseFloat(e[2].toFixed(f)),e[3]=parseFloat(e[3].toFixed(f)),r&&(r[5]=T.maps.geometry.spherical.computeHeading(new T.maps.LatLng(r[2],r[3]),new T.maps.LatLng(e[2],e[3])),o.push(r)),r=e)}r&&(r[5]=0,o.push(r)),e[n]=o})),B(e)}),[W,u,p,f]),e.useEffect((()=>{Ie(ee);Date.now();const e={},t=Te(u).valueOf(),s="now"===p?Te().valueOf():Te(p).valueOf();Object.keys(J).forEach((n=>{const o=[],a=J[n];for(const e of a){const n=new Date(e.timestamp).getTime();n<t||n>s||o.push(e)}e[n]=o})),te(e)}),[J,u,p,Ie]);const Le=e.useRef(t);e.useEffect((()=>{const e=t.filter((e=>Le.current.find((t=>t.id===e.id))&&Object.keys(W).includes(e.id))).map((e=>e.id)),s=t.filter((e=>Le.current.find((t=>t.id===e.id))&&Object.keys(ue).includes(e.id))).map((e=>e.id)),n=t.filter((e=>Le.current.find((t=>t.id===e.id))&&Object.keys(J).includes(e.id))).map((e=>e.id));if(0===t.length)return N({}),pe({}),X([]),void Q({});Object.keys(W).some((e=>!t.find((t=>t.id===e))))&&N((e=>{const s={};return Object.keys(W).filter((e=>t.find((t=>t.id===e)))).forEach((t=>{s[t]=e[t]})),s})),Object.keys(ue).some((e=>!t.find((t=>t.id===e))))&&pe((e=>{const s={};return Object.keys(ue).filter((e=>t.find((t=>t.id===e)))).forEach((t=>{s[t]=e[t]})),s})),Object.keys(J).some((e=>!t.find((t=>t.id===e))))&&Q((e=>{const s={};return Object.keys(J).filter((e=>t.find((t=>t.id===e)))).forEach((t=>{s[t]=e[t]})),s})),Le.current=t,ze({skipVessels:e}),$e({skipVessels:s}),He({skipVessels:n})}),[t]),e.useEffect((()=>{ze(),$e(),He()}),[y]);const Re=e.useRef(null);e.useEffect((()=>("now"===p?(Re.current&&clearInterval(Re.current),Re.current=setInterval((()=>{ze(),$e(),He()}),3e5)):clearInterval(Re.current),()=>clearInterval(Re.current))),[y,p,W]),e.useEffect((()=>{let e=null;return Be(),e=setInterval((()=>{Be()}),3e5),()=>clearInterval(e)}),[]),e.useEffect((()=>{(async()=>{try{const e=await ke.get("/artifacts/filters").then((e=>e.data)).catch((e=>{}));if(0!==Object.keys(e).length){const t=e.superCategories||[];g(t),O(t)}}catch(e){}})();const e=Oe(),t=e=>{const t=e?.artifact;t&&(t.lat=t.location.coordinates[1],t.lng=t.location.coordinates[0],pe((e=>{if(!t.onboard_vessel_id)return e;const s={...e},n=t.onboard_vessel_id,o=s[n]?[...s[n]]:[];if(t.is_archived)s[n]=o.filter((e=>e._id!==t._id));else{const e=o.findIndex((e=>e._id===t._id));-1!==e?o[e]=t:o.push(t),s[n]=o}return s})))};return e.on("artifact/changed",t),()=>e.off("artifact/changed",t)}),[]),e.useEffect((()=>{Fe.current={}}),[d,b,A]),e.useEffect((()=>{L&&bt(De,$,Fe,G,Z,H,U,t,W,u,p,L,ye,d,b,T,M,A,h,P)}),[L,W,$,d,b,T,A,h]),e.useEffect((()=>{L&&vt(j,me,T,re,L,he,_e,be,Ce,ve,we,De,M,P)}),[L,he,j]),e.useEffect((()=>{L&&wt({user:De,aisLayer:K,setAisDataLayers:q,aisData:Y,map:L,infoWindow:ye,google:T,timezone:M,vesselInfo:P,showAisData:v})}),[L,v,T,P,Y]),e.useEffect((()=>{L&&jt({user:De,filteredAudioData:ee,prevFilteredAudioData:Ae,audioDataMarkers:oe,setAudioDataMarkers:ae,map:L,google:T,showAudioData:C,audioClustererRef:ie,audioInfoWindow:we,currentAudioClusterInfoWindow:ve,artifactInfowWindow:be,currentClusterInfoWindow:Ce,infoWindow:ye})}),[L,ee,C,T,P]),e.useEffect((()=>{a(t.filter((e=>$[e.id]&&$[e.id].length>0)).map((e=>e.id))),i(t.filter((e=>$[e.id]&&0===$[e.id].length)).map((e=>e.id))),z||0===t.length&&E.includes("/map")&&I("No vessel selected",{variant:"warning"})}),[t,$]),e.useEffect((()=>{const e=[...o,...l].some((e=>e.id!==c));z||0==s.length&&e&&Object.values($).every((e=>0===e.length))&&E.includes("/map")&&I("No coordinates found for the selected vessels",{variant:"warning"})}),[s,o,l]),e.useEffect((()=>{Object.keys(H).forEach((e=>{const t=H[e];if(!t)return;let s=null;if(t.forEach((e=>{s=e})),!s)return;const n=c===e?"#ff0000":Pe.polylineColors[e]||"#0000FF";t.overrideStyle(s,{icons:[{icon:{path:Pe.icons.location,strokeColor:"#000000",strokeWeight:.5,scale:1.2,fillColor:n,fillOpacity:1,anchor:new T.maps.Point(10,20)}}]})}))}),[c,H]);const ze=({skipVessels:e=[]}={})=>{const t=Le.current.filter((t=>!e.includes(t.id))).map((e=>e.id));if(0===t.length)return;n((e=>(le.current=e.concat(t),le.current)));const s={vesselIds:t.join(","),startTimestampISO:Te(y[0]).toISOString(),endTimestampISO:"now"===p?Te().toISOString():Te(y[1]).toISOString()};ke.get("/vesselLocations/bulk",{params:s},{meta:{showSnackbar:!1}}).then((e=>{const t=e.data;Object.entries(t).forEach((([e,t])=>{t.length>0?(a((t=>t.some((t=>t===e))?t:[...t,e])),i((t=>t.filter((t=>t!==e))))):(i((t=>t.some((t=>t===e))?t:[...t,e])),a((t=>t.filter((t=>t!==e)))))})),N((e=>({...e,...Object.keys(t).reduce(((e,s)=>(Le.current.find((e=>e.id===s))&&(e[s]=t[s]),e)),{})})))})).catch((e=>{})).finally((()=>{n((e=>(le.current=e.filter((e=>!t.includes(e))),le.current))),V(!1)}))},$e=({skipVessels:e=[]}={})=>{const t=Le.current.filter((t=>!e.includes(t.id))).map((e=>e.id));if(0===t.length)return;const s={vesselIds:t.join(","),startTimestamp:Te(y[0]).toISOString(),endTimestamp:"now"===p?Te().toISOString():Te(y[1]).toISOString()};ke.get("/artifacts/bulk",{params:s},{meta:{showSnackbar:!1}}).then((e=>{pe((t=>({...t,...Object.keys(e.data.artifacts).reduce(((t,s)=>(Le.current.find((e=>e.id===s))&&(t[s]=e.data.artifacts[s]),t)),{})})))})).catch((e=>{}))},Be=()=>{try{const e=new Date(Date.now()-864e5).toISOString();ke.get(`/vesselAis/latest?startTimestampISO=${e}`,{meta:{showSnackbar:!1}}).then((e=>{X(e.data)})).catch((e=>{}))}catch(e){}},He=({skipVessels:e=[]}={})=>{const t=Le.current.filter((t=>!e.includes(t.id))).map((e=>e.id));if(0===t.length)return;const s={vesselIds:t.join(","),startTimestamp:y[0],endTimestamp:"now"===p?Te().valueOf():y[1]};ke.get("/audios/bulk",{params:s},{meta:{showSnackbar:!1}}).then((e=>{Q((t=>({...t,...Object.keys(e.data).reduce(((t,s)=>(Le.current.find((e=>e.id===s))&&(t[s]=e.data[s]),t)),{})})))})).catch((e=>{}))};return e.useEffect((()=>{!E.includes("/map")&&Ce&&Ce.close(),!E.includes("/map")&&be&&be.close(),!E.includes("/map")&&we&&we.close(),!E.includes("/map")&&ve&&ve.close()}),[E]),r.jsxs("div",{style:{color:"#FFFFFF",width:"100%",height:"100%"},children:[z?r.jsx(w,{animation:"wave",variant:"rectangular",height:"100%"}):r.jsx(r.Fragment,{}),r.jsx("div",{style:{display:!T||z?"none":"flex",width:"100%",height:"100%"},children:T&&r.jsx(Se,{mapContainerStyle:{width:"100%",height:"100%"},center:ge,zoom:m,onLoad:e=>{R(e)},onUnmount:()=>{R(null),Fe.current={}},options:{mapId:"c94897bab52d6290",zoomControl:!0,streetViewControl:!1,mapTypeControl:!1,fullscreenControl:!1}})})]})},St=({vessels:t=[],selectedVessels:s,availableVessels:n,emptyVessels:o,errorsomeVessels:a,loadingVessels:i,handleVesselSelect:l,setFocusedVessel:c,focusedVessel:d})=>{const{devMode:u}=je(),{regions:m}=Ze(),[h,f]=e.useState([]),[g,b]=e.useState(null);e.useEffect((()=>{if(!t.length||!m.length)return;const e=m.filter((e=>t.some((t=>t.region_group===e._id))));f(e)}),[t,m]);const w=e.useMemo((()=>t.filter((e=>e.region_group)).map((e=>({...e,region_group_object:h.find((t=>t._id===e.region_group))}))).sort(((e,t)=>{const s=e.region_group_object?.name?.toLowerCase()||"",n=t.region_group_object?.name?.toLowerCase()||"";return s<n?-1:s>n?1:(e.name?.toLowerCase()||e.id).localeCompare(t.name?.toLowerCase()||t.id)}))),[t,h]),j=e.useMemo((()=>[...new Set(w.map((e=>e.region_group_object?.name||"Ungrouped")))]),[w]),v=s.length===w.length&&w.length>0,C=s.length>0&&s.length<w.length;return r.jsxs(x,{container:!0,flexDirection:"column",sx:{p:0,m:0,color:"#fff",bgcolor:"transparent"},className:"map-step-2",minWidth:{xs:"auto",md:"auto",lg:"420px"},children:[0!==j.length&&r.jsx(x,{item:!0,sx:{borderRadius:1},children:r.jsxs(x,{container:!0,alignItems:"center",sx:{height:45,pl:2},children:[r.jsx(_,{checked:v,indeterminate:C,onChange:e=>{e.target.checked?w.forEach((e=>{s.some((t=>t.id===e.id))||l(e)})):s.forEach((e=>l(e)))},sx:{color:"#fff",p:0,mr:1}}),r.jsx(p,{sx:{fontSize:"20px",fontWeight:500},children:"Select All"})]})}),r.jsxs(x,{item:!0,sx:{borderRadius:1},children:[0===j.length&&r.jsx(p,{sx:{color:"#fff",textAlign:"center",p:2},children:"No vessels found"}),j.map((e=>{const t=w.filter((t=>(t.region_group_object&&t.region_group_object.name||"Ungrouped")===e)),m=t.filter((e=>s.some((t=>t.id===e.id)))).length,h=t.length,f=m===h&&h>0,j=m>0&&m<h,v=g===e,C=t.length>0&&t[0].region_group_object?t[0].region_group_object.timezone:"UTC",S=t.some((e=>i.includes(e.id)));return r.jsxs(A,{disableGutters:!0,expanded:v,onChange:()=>(e=>{b(g===e?null:e)})(e),sx:{boxShadow:"none"},children:[r.jsxs(O,{expandIcon:S?r.jsx(D,{size:20,sx:{color:"#bfc9e0"}}):v?r.jsx(T,{sx:{color:"#bfc9e0"}}):r.jsx(M,{sx:{color:"#bfc9e0"}}),sx:{height:44,pl:2,backgroundColor:"#464F59"},children:[r.jsx(_,{checked:f,indeterminate:j,onChange:e=>{e.target.checked?t.forEach((e=>{s.some((t=>t.id===e.id))||l(e)})):t.forEach((e=>{s.some((t=>t.id===e.id))&&l(e)}))},sx:{color:"#fff",p:0,mr:1},onClick:e=>e.stopPropagation()}),r.jsxs(p,{fontWeight:600,children:[e," (UTC ",C,")"]})]}),r.jsx(P,{sx:{p:0,m:0,maxHeight:"400px",overflowY:"auto"},children:t.map((e=>{const t=s.some((t=>t.id===e.id));let m=null;return i.includes(e.id)?m=r.jsx(D,{size:18,sx:{color:"white"}}):t&&(n.some((t=>t===e.id))?m=r.jsx(y,{onClick:t=>{t.stopPropagation(),c(e.id)},sx:{width:10,height:10},children:r.jsx(I,{sx:{color:e.id===d?"red":"white"}})}):o.some((t=>t===e.id))?m="":a.some((t=>t===e.id))&&(m=r.jsx(E,{sx:{fontSize:18,color:"yellow"}}))),r.jsxs(x,{container:!0,alignItems:"center",justifyContent:"space-between",sx:{p:"8px 16px",pl:3,"&:hover":{bgcolor:"#464F59"},cursor:"pointer"},onClick:()=>l(e),children:[r.jsxs(x,{item:!0,display:"flex",alignItems:"center",children:[r.jsx(_,{checked:t,sx:{color:"white",p:0,mr:1,"&.Mui-checked":{color:Pe.polylineColors[e.id]||void 0}}}),r.jsx(p,{sx:{color:"white",fontWeight:500,width:{xs:"180px",sm:"220px",md:"260px",lg:"300px"},maxWidth:{xs:"180px",sm:"220px",md:"260px",lg:"300px"},whiteSpace:"normal",overflow:"hidden",textOverflow:"ellipsis",wordBreak:"break-word",display:"block"},children:u?`${e.name} (${e.unit_id||""})`:e.name})]}),r.jsx(x,{item:!0,display:"flex",alignItems:"center",children:m})]},e.id)}))})]},e)}))]})]})},kt=({value:t,onChange:s,minDate:n,maxDate:o,slots:a,sx:i,iconPosition:l="end"})=>{const c=e.useRef(null),[d,u]=e.useState(!1),{user:p}=Ne(),m={width:"100%",backgroundColor:"transparent",borderRadius:"40px","& .MuiButtonBase-root":{padding:0,margin:0},"& .MuiSvgIcon-root":{color:$e.palette.custom.mediumGrey,fontSize:"18px"},fontSize:"12px",height:"40px","& .MuiInputBase-root":{display:"flex",flexDirection:"end"===l?"row":"row-reverse",padding:0,gap:"end"===l?0:"10px",border:"none",fontSize:"12px",color:$e.palette.custom.mediumGrey,justifyContent:"space-between","& input":{padding:0,maxWidth:"80px",cursor:"pointer"}}},h=()=>u(!0);e.useEffect((()=>(c.current&&c.current.addEventListener("click",h),()=>{c.current&&c.current.removeEventListener("click",h)})),[]);const f={...m,...i};return r.jsx(at,{value:t,onChange:s,minDate:n,maxDate:o,open:d,onOpen:h,onClose:()=>u(!1),slots:a,sx:f,format:Ee.dateTimeFormat(p,{exclude_hours:!0}),inputRef:c})},Ft=({journeyStart:t,journeyEnd:s,timeSlider:n,timeSliderKey:o,artifactsCategories:a,selectedArtifactsCategory:i,showDatapoints:l,showArtifacts:c,showAisData:d,showAudioData:u,disableFiltersReset:m,setJourneyStart:h,setJourneyEnd:f,setTimeSlider:g,setTimeSliderKey:b,setSelectedArtifactsCategory:j,setShowDatapoints:v,setShowArtifacts:C,setShowAisData:S,setShowAudioData:k,resetFilters:F,loadingVessels:A,selectedNumberOfArtifacts:O,setSelectedNumberOfArtifacts:D,artifactsType:T,setArtifactsType:M})=>{const{timezone:P}=je(),{user:I}=Ne(),E=e.useMemo((()=>{const[e,t]=n,s=[];for(let n=e;n<=t;n+=864e5)s.push({value:n});return s}),[n]);return r.jsxs(x,{container:!0,flexDirection:"column",gap:1,color:"#FFFFFF",paddingX:2,paddingY:2,position:"relative",className:"map-step-4",children:[r.jsx(y,{disabled:m,disableRipple:!0,sx:{width:15,height:15,marginLeft:1,paddingX:2,position:"absolute",top:{xs:"-50px",md:"-30px"},right:{xs:"0",md:"35px"}},onClick:e=>{e.stopPropagation(),F()},children:r.jsx(L,{fontSize:"small"})}),r.jsx(p,{fontSize:"16px",fontWeight:400,textAlign:"center",children:"Date Range"}),A.length>0?r.jsxs(r.Fragment,{children:[r.jsx(x,{paddingX:4,children:r.jsx(w,{variant:"rounded",width:"100%",height:30})}),r.jsxs(x,{container:!0,justifyContent:"space-between",children:[r.jsx(x,{children:r.jsx(w,{variant:"rounded",width:100,height:30})}),r.jsx(x,{children:r.jsx(w,{variant:"rounded",width:100,height:30})})]})]}):r.jsx(r.Fragment,{children:r.jsxs(x,{container:!0,children:[r.jsx(x,{paddingX:8,size:12,children:r.jsx(R,{valueLabelFormat:e=>Te(e).tz(P).format(Ee.dateTimeFormat(I,{exclude_seconds:!0})),valueLabelDisplay:"auto",defaultValue:n,min:n[0],max:n[1],step:6e4,marks:E,onChangeCommitted:(e,t)=>{h(Te(t[0])),f(Te(t[1])),Le("MapFilterApplied",{filter:"dateRange",start:t[0],end:t[1]})},sx:{color:z($e.palette.background.default,.32),"& .MuiSlider-thumb":{height:22,width:22,border:`5px solid ${$e.palette.custom.unfocused}`},"& .MuiSlider-mark":{height:"7px",width:"3px"}}},o)}),r.jsxs(x,{container:!0,justifyContent:"space-between",size:12,children:[r.jsx(x,{children:r.jsx(kt,{value:t,onChange:e=>{h(e),g([e.valueOf(),"now"===s?Te().valueOf():s.valueOf()]),b((e=>e+1)),Le("MapFilterApplied",{filter:"startDate",value:e.valueOf()})},maxDateTime:Te(),slots:{openPickerIcon:V}})}),r.jsx(x,{children:r.jsx(kt,{value:"now"===s?Te():s,onChange:e=>{f(e),g([t.valueOf(),e.valueOf()]),b((e=>e+1)),Le("MapFilterApplied",{filter:"endDate",value:e.valueOf()})},minDateTime:t,maxDateTime:Te(),slots:{openPickerIcon:V},iconPosition:"start"})})]})]})}),r.jsxs(x,{container:!0,justifyContent:"space-between",gap:6,children:[r.jsx(x,{children:r.jsx(W,{sx:{gap:1,margin:0},componentsProps:{typography:{fontSize:{xs:12,sm:16}}},control:r.jsx(_,{checked:l,onChange:e=>{v(e.target.checked),Le("MapFilterApplied",{filter:"showDatapoints",value:!l})},size:"small",disableRipple:!0,sx:{width:10,height:10}}),label:"Show Data Points"})}),r.jsx(x,{children:r.jsx(W,{sx:{gap:1,margin:0},componentsProps:{typography:{fontSize:{xs:12,sm:16}}},control:r.jsx(_,{checked:c,onChange:e=>{C(e.target.checked),Le("MapFilterApplied",{filter:"showArtifacts",value:!c})},size:"small",disableRipple:!0,sx:{width:10,height:10}}),label:"Show Artifacts"})})]}),r.jsxs(x,{container:!0,justifyContent:"space-between",gap:6,children:[r.jsx(x,{children:r.jsx(W,{sx:{gap:1,margin:0},componentsProps:{typography:{fontSize:{xs:12,sm:16}}},control:r.jsx(_,{checked:d,onChange:e=>{S(e.target.checked),Le("MapFilterApplied",{filter:"showAisData",value:!d})},size:"small",disableRipple:!0,sx:{width:10,height:10}}),label:"Show AIS Data"})}),r.jsx(x,{children:r.jsx(W,{sx:{gap:1,margin:0},componentsProps:{typography:{fontSize:{xs:12,sm:16}}},control:r.jsx(_,{checked:u,onChange:e=>{k(e.target.checked),Le("MapFilterApplied",{filter:"showAudioData",value:!u})},size:"small",disableRipple:!0,sx:{width:10,height:10}}),label:"Show Audio Data"})})]}),a?0===a.length?r.jsx(p,{variant:"caption",children:"No artifacts found."}):r.jsxs(x,{item:!0,container:!0,flexDirection:"column",gap:2,marginTop:2,children:[r.jsx(x,{children:r.jsxs(N,{sx:{width:"100%"},size:"small",children:[r.jsx($,{id:"artifacts-multiselect-label",sx:{color:(c?"#FFFFFF":"#9A9CA2")+" !important"},shrink:!0,children:"Artifacts"}),r.jsxs(B,{labelId:"artifacts-multiselect-label",id:"artifacts-multiselect",multiple:!0,value:i,onChange:e=>{const t=e.target.value;t.includes("All")?i.length===a.length?(j([]),Le("MapFilterApplied",{filter:"artifactsCategory",value:[]})):(j(a),Le("MapFilterApplied",{filter:"artifactsCategory",value:a})):(j(t),Le("MapFilterApplied",{filter:"artifactsCategory",value:t}))},input:r.jsx(G,{label:"Artifacts"}),native:!1,displayEmpty:!0,disabled:!c,renderValue:e=>e.length===a.length?"All":0===e.length?"None Selected":`${e.length} Selected`,sx:{color:"#FFFFFF",textTransform:"capitalize","&.Mui-disabled fieldset":{borderColor:"#9A9CA2 !important"},"&.Mui-disabled .MuiSelect-select, &.Mui-disabled svg":{cursor:"not-allowed !important",color:"#9A9CA2 !important","-webkit-text-fill-color":"#9A9CA2 !important"}},children:[r.jsxs(H,{value:"All",children:[r.jsx(_,{checked:i.length===a.length,sx:{padding:"0 10px"}}),r.jsx(U,{primary:"All"})]}),a.map(((e,t)=>r.jsxs(H,{value:e,children:[r.jsx(_,{checked:i.includes(e),sx:{padding:"0 10px"}}),r.jsx(U,{primary:e,sx:{textTransform:"capitalize"}})]},t)))]})]})}),r.jsx(x,{children:r.jsxs(N,{sx:{width:"100%"},size:"small",children:[r.jsx($,{id:"artifacts-select-label",sx:{color:(c?"#FFFFFF":"#9A9CA2")+" !important"},shrink:!0,children:"Visible"}),r.jsx(B,{labelId:"artifacts-select-label",id:"artifacts-select",value:O||100,onChange:e=>{D(e.target.value),Le("MapFilterApplied",{filter:"numberOfArtifacts",value:e.target.value})},input:r.jsx(G,{label:"Visible"}),native:!1,displayEmpty:!0,disabled:!c,renderValue:e=>e?`${e} Artifacts`:"None Selected",sx:{color:"#FFFFFF",textTransform:"capitalize","&.Mui-disabled fieldset":{borderColor:"#9A9CA2 !important"},"&.Mui-disabled .MuiSelect-select, &.Mui-disabled svg":{cursor:"not-allowed !important",color:"#9A9CA2 !important","-webkit-text-fill-color":"#9A9CA2 !important"}},children:["all",100,250,500,1e3,2e3].map(((e,t)=>r.jsx(H,{value:e,children:r.jsx(U,{primary:e,sx:{textTransform:"capitalize"}})},t)))})]})}),r.jsx(x,{children:r.jsxs(N,{sx:{width:"100%"},size:"small",children:[r.jsx($,{id:"artifacts-type-label",sx:{color:(c?"#FFFFFF":"#9A9CA2")+" !important"},shrink:!0,children:"Type"}),r.jsx(B,{labelId:"artifacts-type-label",id:"artifacts-type",value:T,onChange:e=>{M(e.target.value),Le("MapFilterApplied",{filter:"artifactsType",value:e.target.value})},input:r.jsx(G,{label:"Type"}),native:!1,displayEmpty:!0,disabled:!c,renderValue:e=>e?`${e} Artifacts`:"None Selected",sx:{color:"#FFFFFF",textTransform:"capitalize","&.Mui-disabled fieldset":{borderColor:"#9A9CA2 !important"},"&.Mui-disabled .MuiSelect-select, &.Mui-disabled svg":{cursor:"not-allowed !important",color:"#9A9CA2 !important","-webkit-text-fill-color":"#9A9CA2 !important"}},children:["image","video","both"].map(((e,t)=>r.jsx(H,{value:e,children:r.jsx(U,{primary:e,sx:{textTransform:"capitalize"}})},t)))})]})})]}):r.jsx(w,{variant:"rectangular",height:40})]})},_t=({interval:e,precision:t,datapointsDistance:s,precisionSliderKey:n,disableAdvancedSettingsReset:o,resetAdvancedSettings:a,setInterval:i,setPrecision:l,setDatapointsDistance:c,temporaryInterval:d,setTemporaryInterval:u})=>r.jsxs(x,{container:!0,flexDirection:"column",color:"#FFFFFF",gap:2,paddingX:2,paddingY:2,position:"relative",className:"map-step-6",sx:{maxWidth:{xs:"none",md:380},width:"100%"},children:[r.jsx(y,{disabled:o,disableRipple:!0,sx:{width:15,height:15,marginLeft:1,paddingX:2,position:"absolute",top:{xs:"-50px",md:"-30px"},right:{xs:"0",md:"35px"}},onClick:e=>{e.stopPropagation(),a()},children:r.jsx(L,{fontSize:"small",sx:{opacity:o&&.5}})}),r.jsxs(x,{container:!0,flexDirection:"column",paddingRight:2,children:[r.jsxs(x,{justifyContent:"space-between",display:"flex",alignItems:"center",gap:1,children:[r.jsx(p,{textAlign:"center",justifyContent:"center",color:$e.palette.custom.mediumGrey,fontSize:"12px",children:"Interval"}),r.jsx(Z,{enterDelay:300,title:"Change the minimum interval between each captured coordinate. e.g., if interval is 5m, then all the points on the polyline will be at least 5 minutes apart.",children:r.jsx(Y,{sx:{color:$e.palette.custom.mediumGrey,backgroundColor:z($e.palette.custom.offline,.08),fontSize:"18px"}})})]}),r.jsx(x,{paddingLeft:2,children:r.jsx(R,{valueLabelFormat:e=>`${e}m`,valueLabelDisplay:"auto",value:d,onChange:(e,t)=>u(t),onChangeCommitted:(e,t)=>i(t),min:1,max:60,sx:{padding:0,color:$e.palette.custom.mediumGrey}})})]}),r.jsxs(x,{container:!0,flexDirection:"column",paddingRight:2,children:[r.jsxs(x,{justifyContent:"space-between",display:"flex",alignItems:"center",gap:1,children:[r.jsx(p,{textAlign:"center",justifyContent:"center",color:$e.palette.custom.mediumGrey,fontSize:"12px",children:"Precision"}),r.jsx(Z,{enterDelay:300,title:"Change the precision of the coordinates degrees in decimal places. e.g., if precision is 3d, then coordinates (10.754019, 115.7928663) will be rounded up to (10.754, 115.793). This will reduce the amount of points on the polyline especially if a vessel is standing still.",children:r.jsx(Y,{sx:{color:$e.palette.custom.mediumGrey,backgroundColor:z($e.palette.custom.offline,.08),fontSize:"18px"}})})]}),r.jsx(x,{paddingLeft:2,children:r.jsx(R,{valueLabelFormat:e=>`${e}d`,valueLabelDisplay:"auto",sx:{padding:0,color:$e.palette.custom.mediumGrey},defaultValue:t,onChangeCommitted:(e,t)=>l(t),min:3,max:15},n)})]}),r.jsxs(x,{container:!0,flexDirection:"column",paddingRight:2,children:[r.jsxs(x,{justifyContent:"space-between",display:"flex",alignItems:"center",gap:1,children:[r.jsx(p,{textAlign:"center",justifyContent:"center",color:$e.palette.custom.mediumGrey,fontSize:"12px",children:"Distance b/w Data Points"}),r.jsx(Z,{enterDelay:300,title:"Change the minimum distance between data points. Decreasing this value may cause performance drop.",children:r.jsx(Y,{sx:{color:$e.palette.custom.mediumGrey,fontSize:"18px"}})})]}),r.jsx(x,{paddingLeft:2,children:r.jsx(R,{valueLabelFormat:e=>`${e} meters`,valueLabelDisplay:"auto",sx:{padding:0,color:$e.palette.custom.mediumGrey},value:s,onChange:(e,t)=>c(t),min:50,max:1e4})})]})]}),At=[{title:"Vessels",imgSrc:"/icons/vessels-icon.svg",className:"map-step-1"},{title:"Filters",imgSrc:"/icons/filters-icon.svg",className:"map-step-3"},{title:"Advanced Settings",imgSrc:"/icons/advance-settings-icon.svg",className:"map-step-5"}],Ot=()=>{const{isMobile:t,selectedVessel:s,setSelectedVessel:n,devMode:o}=je(),{vesselInfo:a,fetchVesselsInfo:i}=Ge(),{user:l}=Ne(),[c,d]=e.useState(Pe.journeyStart),[u,m]=e.useState("now"),[h,f]=e.useState(Pe.interval),[g,y]=e.useState(Pe.precision),[b,j]=e.useState(0),[v,C]=e.useState(Pe.datapointsDistance),[S,k]=e.useState(),[_,A]=e.useState([]),[O,D]=e.useState([]),[T,M]=e.useState([]),[P,I]=e.useState([]),[E,L]=e.useState([]),[R,V]=e.useState([]),[W,N]=e.useState(),[$,B]=e.useState([]),[H,U]=e.useState(!0),[G,Z]=e.useState(!0),[Y,K]=e.useState([c.valueOf(),Te().valueOf()]),[q,J]=e.useState(0),[Q,ee]=e.useState(!0),[te,se]=e.useState(!0),[ne,oe]=e.useState(!0),[ae,re]=e.useState(!0),[ie,le]=e.useState(l?.home_port_filter_mode||"ONLY_NON_HOME_PORTS"),[ce,de]=e.useState(null),[ue,pe]=e.useState(h),[me,he]=e.useState(""),[fe,ge]=e.useState("all"),{pathname:xe}=F(),[ye,be]=e.useState("both"),[ve,Ce]=e.useState(!1),[Se,ke]=e.useState(Pe.journeyStart),[Fe,_e]=e.useState(Te(Pe.journeyStart).add(1,"hour")),[Ae,Oe]=e.useState(!0);e.useEffect((()=>{const e=Te(c),t=24*("now"===u?Te():Te(u)).diff(e,"day")*60,s=Math.min(60,Math.floor(t/280));pe(s),f(s)}),[c,u]),e.useEffect((()=>{le(l?.home_port_filter_mode||"ONLY_NON_HOME_PORTS")}),[l?.home_port_filter_mode]),e.useEffect((()=>{xe.includes("/map")||ge("all")}),[xe]),e.useEffect((()=>{if(s){const e=P.some((e=>e===s)),t=E.concat(R).some((e=>e===s));e?(k(s),n(null)):t&&n(null)}}),[P,E,R]),e.useEffect((()=>{0===O.length?(se(!1),M([]),I([]),L([]),V([])):se(!0)}),[O]),e.useEffect((()=>{if(s){const e=Array.isArray(_)&&_.find((e=>e.id===s));if(e&&D([e]),O.some((e=>e.id===s))){P.some((e=>e===s))&&k(s),n(null)}}}),[s]),e.useEffect((()=>{A(),k()}),[]),e.useEffect((()=>{a&&a.length>0&&De()}),[a]),e.useEffect((()=>{if(!_||!Array.isArray(_)||0===_.length)return D([]);D([_[0]])}),[_]),e.useEffect((()=>{0!==P.length&&(void 0===S||S&&P.some((e=>e===S))||k(P[0]))}),[P]),e.useEffect((()=>{c===Pe.journeyStart&&"now"===u?U(!0):U(!1)}),[c,u]),e.useEffect((()=>{h===Pe.interval&&g===Pe.precision&&v===Pe.datapointsDistance?Z(!0):Z(!1)}),[h,g,v]);const De=async()=>{he("");try{if(a&&Array.isArray(a)){const e=a.filter((e=>!!o||e.is_active));0===e.length?he("No vessel coordinates found."):(he(""),A(e.sort(((e,t)=>e.is_live&&!t.is_live?-1:1)).map((e=>({id:e.vessel_id,unit_id:e.unit_id,name:e.name||"Unregistered",region_group:e.region_group_id,vessel_id:e.vessel_id})))))}else i()}catch(e){he("Failed to fetch vessels coordinates. Please try again later. "+e?.response?.data?.message)}},Me={"Advanced Settings":r.jsx(_t,{interval:h,precision:g,datapointsDistance:v,precisionSliderKey:b,disableAdvancedSettingsReset:G,resetAdvancedSettings:()=>{f(Pe.interval),pe(Pe.interval),y(Pe.precision),C(Pe.datapointsDistance),j((e=>e+1))},setInterval:f,temporaryInterval:ue,setTemporaryInterval:pe,setPrecision:y,setDatapointsDistance:C,showFilteredCoordinates:ve,setShowFilteredCoordinates:Ce,fromDate:Se,setFromDate:ke,toDate:Fe,setToDate:_e,showCoordinatesPoints:Ae,setShowCoordinatesPoints:Oe}),Filters:r.jsx(Ft,{journeyStart:c,journeyEnd:u,timeSlider:Y,timeSliderKey:q,selectedArtifactsCategory:$,showDatapoints:Q,showArtifacts:te,showAisData:ne,showAudioData:ae,homePortsArtifactsMode:ie,disableFiltersReset:H,artifactsCategories:W,setJourneyStart:d,setJourneyEnd:m,setTimeSlider:K,setTimeSliderKey:J,setSelectedArtifactsCategory:B,setShowDatapoints:ee,setShowArtifacts:se,setShowAisData:oe,setShowAudioData:re,setHomePortsArtifactsMode:le,resetFilters:()=>{d(Pe.journeyStart),m("now"),K([Pe.journeyStart.valueOf(),Te().valueOf()]),J((e=>e+1)),le(l?.home_port_filter_mode||"ONLY_NON_HOME_PORTS")},loadingVessels:T,selectedNumberOfArtifacts:fe,setSelectedNumberOfArtifacts:ge,artifactsType:ye,setArtifactsType:be}),Vessels:r.jsx(St,{vessels:_,selectedVessels:O,availableVessels:P,emptyVessels:E,errorsomeVessels:R,loadingVessels:T,handleVesselSelect:e=>{const t=O.some((t=>t.id===e.id));Le("VesselSelected",{vesselId:e.id,selected:!t}),D((t=>t.some((t=>t.id===e.id))?t.filter((t=>t.id!==e.id)):[...t,e]))},setFocusedVessel:k,focusedVessel:S})};return r.jsxs(x,{container:!0,height:"100%",flexWrap:"nowrap",overflow:"auto",flexDirection:{xs:"column",lg:"row"},color:"#FFFFFF",position:"relative",children:[r.jsxs(x,{minHeight:{xs:400,lg:"auto"},position:"relative",size:"grow",children:[_?r.jsx(Ct,{setAvailableVessels:I,setEmptyVessels:L,setErrorsomeVessels:V,focusedVessel:S,datapointsDistance:v,vessels:O,journeyStart:c,journeyEnd:u,initialZoom:3,interval:h,precision:g,setArtifactsCategories:N,selectedArtifactsCategory:$,timeSlider:Y,showDatapoints:Q,showArtifacts:te,showAisData:ne,showAudioData:ae,setLoadingVessels:M,loadingVessels:T,emptyVessels:E,errorsomeVessels:R,selectedNumberOfArtifacts:fe,setSelectedNumberOfArtifacts:ge,artifactsType:ye,showFilteredCoordinates:ve,fromDate:Se,toDate:Fe,showCoordinatesPoints:Ae,setSelectedArtifactsCategory:B,homePortsArtifactsMode:ie}):r.jsx(w,{animation:"wave",variant:"rectange",width:"100%",height:"100%"}),me&&r.jsx(x,{sx:{position:"absolute",bottom:16,left:16,zIndex:10,display:"flex"},size:"grow",children:r.jsx(X,{severity:"error",children:me})})]}),r.jsx(we,{menuItems:At,activeComponentTitle:ce,onMenuUpdate:e=>{de(e)},withPadding:!1,children:t?At.map(((e,t)=>r.jsxs(x,{container:!0,display:{xs:"flex",lg:"none"},flexDirection:"column",gap:2,paddingTop:4,paddingX:1.2,sx:{backgroundColor:$e.palette.custom.darkBlue,paddingBottom:1.2,position:"relative",maxWidth:"calc(100vw - 10px)"},children:[r.jsx(x,{size:12,children:r.jsx(p,{fontSize:"16px",fontWeight:"600",color:"#FFFFFF",children:e.title})}),r.jsx(x,{sx:{backgroundColor:z($e.palette.primary.light,.5),borderRadius:"10px",padding:"Vessels"===e.title?0:"15px 20px"},children:Me[e.title]})]},t))):Me[ce]||null})]})};export{Ot as default};

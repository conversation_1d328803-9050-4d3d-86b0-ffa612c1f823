import { PauseCircleOutline, PlayArrowOutlined } from "@mui/icons-material";
import { IconButton, CircularProgress } from "@mui/material";
import { useRef, useState, useEffect } from "react";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import FullscreenExitIcon from "@mui/icons-material/FullscreenExit";
import { alpha } from "@mui/material/styles";
import theme from "../theme";
import CustomScrubBar from "./CustomScrubBar";
import videojs from "video.js";
import "video.js/dist/video-js.css";

const seaTheme = () => {
    return {
        playerWrapper: {
            position: "relative",
            backgroundColor: "#000000",
            overflow: "hidden",
            width: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
        },
        controlsWrapper: {
            overflow: "hidden",
            background: "#FFFFFF4D",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            paddingX: 1,
        },
        playButton: {
            color: "white",
            borderRadius: 0,
            "&:hover": {
                color: theme.palette.custom.mainBlue,
                backgroundColor: "#FFFFFF",
            },
        },
        sliderStyles: {
            color: theme.palette.custom.mainBlue,
            padding: "4px 0",
            height: 24,
            flexGrow: 1,
            "& .MuiSlider-thumb": {
                width: 8,
                height: 28,
                borderRadius: 0,
                backgroundColor: "#FFFFFF",
                transform: "translate(-50%, -50%)",
                transition: "all 0.5s ease !important",
                "&:hover, &.Mui-focusVisible": {
                    boxShadow: "none",
                },
            },
            "& .MuiSlider-track": {
                height: 24,
                borderRadius: 0,
                transition: "all 0.5s ease !important",
                color: theme.palette.custom.mainBlue,
            },
            "& .MuiSlider-rail": {
                height: 24,
                borderRadius: 0,
                opacity: 0.28,
                backgroundColor: "#FFFFFF4D",
            },
            "& .MuiSlider-mark": { height: 24, width: 4 },
        },
        fullscreenButton: {
            color: "white",
            borderRadius: 0,
            "&:hover": {
                color: theme.palette.custom.mainBlue,
                backgroundColor: "#FFFFFF",
            },
        },
        loadingIndicator: {
            position: "absolute",
            inset: 0,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: theme.palette.custom.mainBlue,
        },
        centerPlayButton: {
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            backgroundColor: alpha(theme.palette.custom.mainBlue, 0.7),
            color: "white",
            width: 80,
            height: 80,
            zIndex: 2,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            "&:hover": {
                backgroundColor: theme.palette.custom.mainBlue,
            },
            boxShadow: "0px 0px 15px rgba(0, 0, 0, 0.3)",
        },
    };
};

function DetailVideoPlayer({
    src,
    style,
    onLoadedData,
    onCurrentTimeChange,
    currentTime = 0,
    fullscreenOpen = false,
    isInFullScreen = false,
    showFullscreenIcon = false,
    setFullscreenOpen,
}) {
    const videoRef = useRef(null);
    const playerRef = useRef(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const [progress, setProgress] = useState(0); // 0–100
    const [duration, setDuration] = useState(0);
    const [isLoading, setIsLoading] = useState(true);

    // Initialize Video.js player
    useEffect(() => {
        if (!videoRef.current || !src) return;

        // Initialize Video.js player
        const player = videojs(videoRef.current, {
            controls: false, // We'll use custom controls
            preload: "auto",
            fluid: false,
            responsive: false,
            sources: [{
                src: src,
                type: "video/mp4"
            }]
        });

        playerRef.current = player;

        // Set up event listeners
        player.on("loadstart", () => setIsLoading(true));
        player.on("loadeddata", (e) => {
            setIsLoading(false);
            if (onLoadedData) onLoadedData(e);
        });
        player.on("waiting", () => setIsLoading(true));
        player.on("playing", () => setIsLoading(false));

        player.on("timeupdate", () => {
            const currentTime = player.currentTime();
            const duration = player.duration();
            if (duration && isFinite(duration)) {
                setProgress((currentTime / duration) * 100);
                setDuration(duration);
            }
            onCurrentTimeChange(currentTime);
            if (currentTime === duration) {
                setIsPlaying(false);
            }
        });

        player.on("play", () => setIsPlaying(true));
        player.on("pause", () => setIsPlaying(false));

        // Set initial time if provided
        if (currentTime) {
            player.currentTime(currentTime);
        }

        // Auto-play if in fullscreen
        if (isInFullScreen) {
            player.play();
            setIsPlaying(true);
        }

        return () => {
            if (playerRef.current) {
                playerRef.current.dispose();
                playerRef.current = null;
            }
        };
    }, [src]);

    // Apply custom styling to hide Video.js controls and maintain seatheme
    useEffect(() => {
        if (playerRef.current) {
            const playerEl = playerRef.current.el();
            if (playerEl) {
                // Hide Video.js control bar
                const controlBar = playerEl.querySelector('.vjs-control-bar');
                if (controlBar) {
                    controlBar.style.display = 'none';
                }

                // Hide Video.js big play button
                const bigPlayButton = playerEl.querySelector('.vjs-big-play-button');
                if (bigPlayButton) {
                    bigPlayButton.style.display = 'none';
                }

                // Apply seatheme background
                playerEl.style.backgroundColor = '#000000';
            }
        }
    }, [playerRef.current]);

    const togglePlay = () => {
        const player = playerRef.current;
        if (!player) return;
        if (player.paused()) {
            player.play();
            setIsPlaying(true);
        } else {
            player.pause();
            setIsPlaying(false);
        }
    };

    useEffect(() => {
        if (!isInFullScreen) {
            const player = playerRef.current;
            if (!player) return;
            if (!player.paused() && fullscreenOpen) {
                player.pause();
                setIsPlaying(false);
            } else if (currentTime > 0 && player.paused() && !fullscreenOpen) {
                //auto play the video when the video gets back from fullscreen
                //prevent the video from playing at start
                player.play();
                player.currentTime(currentTime);
                setIsPlaying(true);
            }
        }
    }, [fullscreenOpen]);

    const handleProgressChange = (e) => {
        const player = playerRef.current;
        if (!player) return;
        const newProgress = +e.target.value;
        const duration = player.duration();
        if (duration && isFinite(duration)) {
            player.currentTime((newProgress / 100) * duration);
            setProgress(newProgress);
        }
    };

    // const handleVolumeChange = (e) => {
    //     const newVol = +e.target.value;
    //     videoRef.current.volume = newVol;
    //     setVolume(newVol);
    // };

    const formatTime = (seconds) => {
        if (seconds === undefined || seconds === null || seconds < 0 || !isFinite(seconds)) {
            return "00:00";
        }
        const totalSeconds = Math.floor(seconds);
        const minutes = Math.floor(totalSeconds / 60);
        const secs = totalSeconds % 60;
        return `${String(minutes).padStart(2, "0")}:${String(secs).padStart(2, "0")}`;
    };



    const seaThemeStyles = seaTheme();

    return (
        <div style={{ ...seaThemeStyles.playerWrapper, width: "100%", ...style }}>
            {/* Video.js player element */}
            <video
                ref={videoRef}
                className="video-js vjs-default-skin"
                preload="auto"
                style={{ width: "100%", height: "100%", objectFit: "contain" }}
                onClick={togglePlay}
            />

            {isLoading && (
                <div style={seaThemeStyles.loadingIndicator}>
                    <CircularProgress sx={{ color: theme.palette.custom.mainBlue }} size={50} />
                </div>
            )}

            {!isPlaying && !isLoading && (
                <IconButton sx={seaThemeStyles.centerPlayButton} onClick={togglePlay} aria-label="play video">
                    <PlayArrowOutlined sx={{ fontSize: 48 }} />
                </IconButton>
            )}

            {/* custom controls overlay */}
            <div
                style={{
                    ...seaThemeStyles.controlsWrapper,
                    position: "absolute",
                    bottom: 0,
                    left: 0,
                    right: 0,
                    zIndex: 1,
                }}
                onClick={(e) => e.stopPropagation()}
            >
                {/* Play/Pause */}
                <IconButton sx={seaThemeStyles.playButton} onClick={togglePlay}>
                    {isPlaying ? <PauseCircleOutline /> : <PlayArrowOutlined />}
                </IconButton>

                {/* Progress Bar */}
                <CustomScrubBar
                    value={progress}
                    onChange={(_, v) => handleProgressChange({ target: { value: v } })}
                    onChangeCommitted={(_, v) => handleProgressChange({ target: { value: v } })}
                    height={24}
                    totalSeconds={duration && isFinite(duration) ? duration : 0}
                    getHoverTitle={(sec) => (duration > 0 ? formatTime(sec) : "00:00")}
                    tooltipContainer={document.body}
                />

                {showFullscreenIcon &&
                    (fullscreenOpen ? (
                        <IconButton sx={seaThemeStyles.fullscreenButton} onClick={() => setFullscreenOpen(!fullscreenOpen)}>
                            <FullscreenExitIcon sx={{ height: 18 }} />
                        </IconButton>
                    ) : (
                        <IconButton sx={seaThemeStyles.fullscreenButton} onClick={() => setFullscreenOpen(!fullscreenOpen)}>
                            <FullscreenIcon sx={{ height: 18 }} />
                        </IconButton>
                    ))}
            </div>
        </div>
    );
}

export default DetailVideoPlayer;

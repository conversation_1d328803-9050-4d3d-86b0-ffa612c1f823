import { PauseCircleOutline, PlayArrowOutlined } from "@mui/icons-material";
import { IconButton, CircularProgress } from "@mui/material";
import { useRef, useState, useEffect } from "react";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import FullscreenExitIcon from "@mui/icons-material/FullscreenExit";
import { alpha } from "@mui/material/styles";
import theme from "../theme";
import CustomScrubBar from "./CustomScrubBar";

const seaTheme = () => {
    return {
        playerWrapper: {
            position: "relative",
            backgroundColor: "#000000",
            overflow: "hidden",
            width: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
        },
        controlsWrapper: {
            overflow: "hidden",
            background: "#FFFFFF4D",
            display: "flex",
            alignItems: "center",
            justifyContent: "space-between",
            paddingX: 1,
        },
        playButton: {
            color: "white",
            borderRadius: 0,
            "&:hover": {
                color: theme.palette.custom.mainBlue,
                backgroundColor: "#FFFFFF",
            },
        },
        sliderStyles: {
            color: theme.palette.custom.mainBlue,
            padding: "4px 0",
            height: 24,
            flexGrow: 1,
            "& .MuiSlider-thumb": {
                width: 8,
                height: 28,
                borderRadius: 0,
                backgroundColor: "#FFFFFF",
                transform: "translate(-50%, -50%)",
                transition: "all 0.5s ease !important",
                "&:hover, &.Mui-focusVisible": {
                    boxShadow: "none",
                },
            },
            "& .MuiSlider-track": {
                height: 24,
                borderRadius: 0,
                transition: "all 0.5s ease !important",
                color: theme.palette.custom.mainBlue,
            },
            "& .MuiSlider-rail": {
                height: 24,
                borderRadius: 0,
                opacity: 0.28,
                backgroundColor: "#FFFFFF4D",
            },
            "& .MuiSlider-mark": { height: 24, width: 4 },
        },
        fullscreenButton: {
            color: "white",
            borderRadius: 0,
            "&:hover": {
                color: theme.palette.custom.mainBlue,
                backgroundColor: "#FFFFFF",
            },
        },
        loadingIndicator: {
            position: "absolute",
            inset: 0,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            color: theme.palette.custom.mainBlue,
        },
        centerPlayButton: {
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
            backgroundColor: alpha(theme.palette.custom.mainBlue, 0.7),
            color: "white",
            width: 80,
            height: 80,
            zIndex: 2,
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            "&:hover": {
                backgroundColor: theme.palette.custom.mainBlue,
            },
            boxShadow: "0px 0px 15px rgba(0, 0, 0, 0.3)",
        },
    };
};

function DetailVideoPlayer({
    src,
    style,
    onLoadedData,
    onCurrentTimeChange,
    currentTime = 0,
    fullscreenOpen = false,
    isInFullScreen = false,
    showFullscreenIcon = false,
    setFullscreenOpen,
}) {
    const videoRef = useRef(null);
    const [isPlaying, setIsPlaying] = useState(false);
    const [progress, setProgress] = useState(0); // 0–100
    const [duration, setDuration] = useState(0);
    const [isLoading, setIsLoading] = useState(true);
    // const [volume, setVolume] = useState(1); // 0–1

    // Update progress as the video plays
    useEffect(() => {
        const video = videoRef.current;
        if (!video) return;
        if (currentTime) {
            video.currentTime = currentTime;
        }
        if (isInFullScreen) {
            video.play();
            setIsPlaying(true);
        }
        const onTimeUpdate = () => {
            if (video.duration && isFinite(video.duration)) {
                setProgress((video.currentTime / video.duration) * 100);
                setDuration(video.duration);
            }
            onCurrentTimeChange(video.currentTime);
            if (video.currentTime === video.duration) {
                setIsPlaying(false);
            }
        };
        video.addEventListener("timeupdate", onTimeUpdate);
        return () => video.removeEventListener("timeupdate", onTimeUpdate);
    }, []);

    const togglePlay = () => {
        const video = videoRef.current;
        if (!video) return;
        if (video.paused) {
            video.play();
            setIsPlaying(true);
        } else {
            video.pause();
            setIsPlaying(false);
        }
    };

    useEffect(() => {
        if (!isInFullScreen) {
            const video = videoRef.current;
            if (!video) return;
            if (!video.paused && fullscreenOpen) {
                video.pause();
                setIsPlaying(false);
            } else if (currentTime > 0 && video.paused && !fullscreenOpen) {
                //auto play the video when the video gets back from fullscreen
                //prevent the video from playing at start
                video.play();
                video.currentTime = currentTime;
                setIsPlaying(true);
            }
        }
    }, [fullscreenOpen]);

    const handleProgressChange = (e) => {
        const video = videoRef.current;
        const newProgress = +e.target.value;
        video.currentTime = (newProgress / 100) * video.duration;
        setProgress(newProgress);
    };

    // const handleVolumeChange = (e) => {
    //     const newVol = +e.target.value;
    //     videoRef.current.volume = newVol;
    //     setVolume(newVol);
    // };

    const formatTime = (seconds) => {
        if (seconds === undefined || seconds === null || seconds < 0 || !isFinite(seconds)) {
            return "00:00";
        }
        const totalSeconds = Math.floor(seconds);
        const minutes = Math.floor(totalSeconds / 60);
        const secs = totalSeconds % 60;
        return `${String(minutes).padStart(2, "0")}:${String(secs).padStart(2, "0")}`;
    };

    useEffect(() => {
        const video = videoRef.current;
        if (!video) return;
        const handleLoadStart = () => setIsLoading(true);
        const handleLoadedData = (e) => {
            setIsLoading(false);
            if (onLoadedData) onLoadedData(e);
        };
        const handleWaiting = () => setIsLoading(true);
        const handlePlaying = () => setIsLoading(false);
        video.addEventListener("loadstart", handleLoadStart);
        video.addEventListener("loadeddata", handleLoadedData);
        video.addEventListener("waiting", handleWaiting);
        video.addEventListener("playing", handlePlaying);
        return () => {
            video.removeEventListener("loadstart", handleLoadStart);
            video.removeEventListener("loadeddata", handleLoadedData);
            video.removeEventListener("waiting", handleWaiting);
            video.removeEventListener("playing", handlePlaying);
        };
    }, [src]);

    const seaThemeStyles = seaTheme();

    return (
        <div style={{ ...seaThemeStyles.playerWrapper, width: "100%", ...style }}>
            {/* hide native controls */}
            <video
                ref={videoRef}
                src={src}
                preload="auto"
                style={{ width: "100%", height: "100%", objectFit: "contain" }}
                onLoadedData={onLoadedData}
                onClick={togglePlay}
            />

            {isLoading && (
                <div style={seaThemeStyles.loadingIndicator}>
                    <CircularProgress sx={{ color: theme.palette.custom.mainBlue }} size={50} />
                </div>
            )}

            {!isPlaying && !isLoading && (
                <IconButton sx={seaThemeStyles.centerPlayButton} onClick={togglePlay} aria-label="play video">
                    <PlayArrowOutlined sx={{ fontSize: 48 }} />
                </IconButton>
            )}

            {/* custom controls overlay */}
            <div
                style={{
                    ...seaThemeStyles.controlsWrapper,
                    position: "absolute",
                    bottom: 0,
                    left: 0,
                    right: 0,
                    zIndex: 1,
                }}
                onClick={(e) => e.stopPropagation()}
            >
                {/* Play/Pause */}
                <IconButton sx={seaThemeStyles.playButton} onClick={togglePlay}>
                    {isPlaying ? <PauseCircleOutline /> : <PlayArrowOutlined />}
                </IconButton>

                {/* Progress Bar */}
                <CustomScrubBar
                    value={progress}
                    onChange={(_, v) => handleProgressChange({ target: { value: v } })}
                    onChangeCommitted={(_, v) => handleProgressChange({ target: { value: v } })}
                    height={24}
                    totalSeconds={duration && isFinite(duration) ? duration : 0}
                    getHoverTitle={(sec) => (duration > 0 ? formatTime(sec) : "00:00")}
                    tooltipContainer={document.body}
                />

                {showFullscreenIcon &&
                    (fullscreenOpen ? (
                        <IconButton sx={seaThemeStyles.fullscreenButton} onClick={() => setFullscreenOpen(!fullscreenOpen)}>
                            <FullscreenExitIcon sx={{ height: 18 }} />
                        </IconButton>
                    ) : (
                        <IconButton sx={seaThemeStyles.fullscreenButton} onClick={() => setFullscreenOpen(!fullscreenOpen)}>
                            <FullscreenIcon sx={{ height: 18 }} />
                        </IconButton>
                    ))}
            </div>
        </div>
    );
}

export default DetailVideoPlayer;

import{r as e,j as t,a as n}from"./vendor-B98I-pgv.js";import{e as s}from"./charts-gTQAinvd.js";function o(e){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function i(e){var t=function(e,t){if("object"!=o(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var s=n.call(e,t);if("object"!=o(s))return s;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==o(t)?t:t+""}function r(e,t,n){return(t=i(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var l,u;var p=a(u?l:(u=1,l=function(e,t,n,s,o,i,r,a){if(!e){var l;if(void 0===t)l=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var u=[n,s,o,i,r,a],p=0;(l=new Error(t.replace(/%s/g,(function(){return u[p++]})))).name="Invariant Violation"}throw l.framesToPop=1,l}})),h=e.createContext(null);function c(e,t,n,s){var o,i,r={};return o=e,i=(e,o)=>{var i=n[o];i!==t[o]&&(r[o]=i,e(s,i))},Object.keys(o).forEach((e=>i(o[e],e))),r}function d(e,t,n){var s,o,i,r=(s=n,o=function(n,s,o){return"function"==typeof e[o]&&n.push(google.maps.event.addListener(t,s,e[o])),n},i=[],Object.keys(s).reduce((function(e,t){return o(e,s[t],t)}),i));return r}function g(e){google.maps.event.removeListener(e)}function m(){(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).forEach(g)}function v(e){var{updaterMap:t,eventMap:n,prevProps:s,nextProps:o,instance:i}=e,r=d(o,i,n);return c(t,s,o,i),r}var f={onDblClick:"dblclick",onDragEnd:"dragend",onDragStart:"dragstart",onMapTypeIdChanged:"maptypeid_changed",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseDown:"mousedown",onMouseUp:"mouseup",onRightClick:"rightclick",onTilesLoaded:"tilesloaded",onBoundsChanged:"bounds_changed",onCenterChanged:"center_changed",onClick:"click",onDrag:"drag",onHeadingChanged:"heading_changed",onIdle:"idle",onProjectionChanged:"projection_changed",onResize:"resize",onTiltChanged:"tilt_changed",onZoomChanged:"zoom_changed"},y={extraMapTypes(e,t){t.forEach((function(t,n){e.mapTypes.set(String(n),t)}))},center(e,t){e.setCenter(t)},clickableIcons(e,t){e.setClickableIcons(t)},heading(e,t){e.setHeading(t)},mapTypeId(e,t){e.setMapTypeId(t)},options(e,t){e.setOptions(t)},streetView(e,t){e.setStreetView(t)},tilt(e,t){e.setTilt(t)},zoom(e,t){e.setZoom(t)}};e.memo((function(n){var{children:s,options:o,id:i,mapContainerStyle:r,mapContainerClassName:a,center:l,onClick:u,onDblClick:p,onDrag:c,onDragEnd:d,onDragStart:g,onMouseMove:m,onMouseOut:v,onMouseOver:f,onMouseDown:y,onMouseUp:b,onRightClick:L,onCenterChanged:E,onLoad:C,onUnmount:M}=n,[w,x]=e.useState(null),k=e.useRef(null),[P,O]=e.useState(null),[S,I]=e.useState(null),[D,j]=e.useState(null),[_,T]=e.useState(null),[A,B]=e.useState(null),[U,R]=e.useState(null),[z,Z]=e.useState(null),[V,N]=e.useState(null),[W,H]=e.useState(null),[F,G]=e.useState(null),[K,Y]=e.useState(null),[q,$]=e.useState(null);return e.useEffect((()=>{o&&null!==w&&w.setOptions(o)}),[w,o]),e.useEffect((()=>{null!==w&&void 0!==l&&w.setCenter(l)}),[w,l]),e.useEffect((()=>{w&&p&&(null!==S&&google.maps.event.removeListener(S),I(google.maps.event.addListener(w,"dblclick",p)))}),[p]),e.useEffect((()=>{w&&d&&(null!==D&&google.maps.event.removeListener(D),j(google.maps.event.addListener(w,"dragend",d)))}),[d]),e.useEffect((()=>{w&&g&&(null!==_&&google.maps.event.removeListener(_),T(google.maps.event.addListener(w,"dragstart",g)))}),[g]),e.useEffect((()=>{w&&y&&(null!==A&&google.maps.event.removeListener(A),B(google.maps.event.addListener(w,"mousedown",y)))}),[y]),e.useEffect((()=>{w&&m&&(null!==U&&google.maps.event.removeListener(U),R(google.maps.event.addListener(w,"mousemove",m)))}),[m]),e.useEffect((()=>{w&&v&&(null!==z&&google.maps.event.removeListener(z),Z(google.maps.event.addListener(w,"mouseout",v)))}),[v]),e.useEffect((()=>{w&&f&&(null!==V&&google.maps.event.removeListener(V),N(google.maps.event.addListener(w,"mouseover",f)))}),[f]),e.useEffect((()=>{w&&b&&(null!==W&&google.maps.event.removeListener(W),H(google.maps.event.addListener(w,"mouseup",b)))}),[b]),e.useEffect((()=>{w&&L&&(null!==F&&google.maps.event.removeListener(F),G(google.maps.event.addListener(w,"rightclick",L)))}),[L]),e.useEffect((()=>{w&&u&&(null!==K&&google.maps.event.removeListener(K),Y(google.maps.event.addListener(w,"click",u)))}),[u]),e.useEffect((()=>{w&&c&&(null!==q&&google.maps.event.removeListener(q),$(google.maps.event.addListener(w,"drag",c)))}),[c]),e.useEffect((()=>{w&&E&&(null!==P&&google.maps.event.removeListener(P),O(google.maps.event.addListener(w,"center_changed",E)))}),[u]),e.useEffect((()=>{var e=null===k.current?null:new google.maps.Map(k.current,o);return x(e),null!==e&&C&&C(e),()=>{null!==e&&M&&M(e)}}),[]),t.jsx("div",{id:i,ref:k,style:r,className:a,children:t.jsx(h.Provider,{value:w,children:null!==w?s:null})})}));class b extends e.PureComponent{constructor(){super(...arguments),r(this,"state",{map:null}),r(this,"registeredEvents",[]),r(this,"mapRef",null),r(this,"getInstance",(()=>null===this.mapRef?null:new google.maps.Map(this.mapRef,this.props.options))),r(this,"panTo",(e=>{var t=this.getInstance();t&&t.panTo(e)})),r(this,"setMapCallback",(()=>{null!==this.state.map&&this.props.onLoad&&this.props.onLoad(this.state.map)})),r(this,"getRef",(e=>{this.mapRef=e}))}componentDidMount(){var e=this.getInstance();this.registeredEvents=v({updaterMap:y,eventMap:f,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{map:e}}),this.setMapCallback)}componentDidUpdate(e){null!==this.state.map&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:y,eventMap:f,prevProps:e,nextProps:this.props,instance:this.state.map}))}componentWillUnmount(){null!==this.state.map&&(this.props.onUnmount&&this.props.onUnmount(this.state.map),m(this.registeredEvents))}render(){return t.jsx("div",{id:this.props.id,ref:this.getRef,style:this.props.mapContainerStyle,className:this.props.mapContainerClassName,children:t.jsx(h.Provider,{value:this.state.map,children:null!==this.state.map?this.props.children:null})})}}function L(e,t,n,s,o,i,r){try{var a=e[i](r),l=a.value}catch(u){return void n(u)}a.done?t(l):Promise.resolve(l).then(s,o)}function E(e){return function(){var t=this,n=arguments;return new Promise((function(s,o){var i=e.apply(t,n);function r(e){L(i,s,o,r,a,"next",e)}function a(e){L(i,s,o,r,a,"throw",e)}r(void 0)}))}}function C(e){var{googleMapsApiKey:t,googleMapsClientId:n,version:s="weekly",language:o,region:i,libraries:r,channel:a,mapIds:l,authReferrerPolicy:u}=e,h=[];return p(t&&n||!(t&&n),"You need to specify either googleMapsApiKey or googleMapsClientId for @react-google-maps/api load script to work. You cannot use both at the same time."),t?h.push("key=".concat(t)):n&&h.push("client=".concat(n)),s&&h.push("v=".concat(s)),o&&h.push("language=".concat(o)),i&&h.push("region=".concat(i)),r&&r.length&&h.push("libraries=".concat(r.sort().join(","))),a&&h.push("channel=".concat(a)),l&&l.length&&h.push("map_ids=".concat(l.join(","))),u&&h.push("auth_referrer_policy=".concat(u)),h.push("loading=async"),h.push("callback=initMap"),"https://maps.googleapis.com/maps/api/js?".concat(h.join("&"))}var M="undefined"!=typeof document;function w(e){var{url:t,id:n,nonce:s}=e;return M?new Promise((function(e,o){var i=document.getElementById(n),r=window;if(i){var a=i.getAttribute("data-state");if(i.src===t&&"error"!==a){if("ready"===a)return e(n);var l=r.initMap,u=i.onerror;return r.initMap=function(){l&&l(),e(n)},void(i.onerror=function(e){u&&u(e),o(e)})}i.remove()}var p=document.createElement("script");p.type="text/javascript",p.src=t,p.id=n,p.async=!0,p.nonce=s||"",p.onerror=function(e){p.setAttribute("data-state","error"),o(e)},r.initMap=function(){p.setAttribute("data-state","ready"),e(n)},document.head.appendChild(p)})).catch((e=>{throw e})):Promise.reject(new Error("document is undefined"))}function x(e){var t=e.href;return!(!t||0!==t.indexOf("https://fonts.googleapis.com/css?family=Roboto")&&0!==t.indexOf("https://fonts.googleapis.com/css?family=Google+Sans+Text"))||("style"===e.tagName.toLowerCase()&&e.styleSheet&&e.styleSheet.cssText&&0===e.styleSheet.cssText.replace("\r\n","").indexOf(".gm-style")?(e.styleSheet.cssText="",!0):"style"===e.tagName.toLowerCase()&&e.innerHTML&&0===e.innerHTML.replace("\r\n","").indexOf(".gm-style")?(e.innerHTML="",!0):"style"===e.tagName.toLowerCase()&&!e.styleSheet&&!e.innerHTML)}function k(){var e=document.getElementsByTagName("head")[0];if(e){var t=e.insertBefore.bind(e);e.insertBefore=function(n,s){return x(n)||Reflect.apply(t,e,[n,s]),n};var n=e.appendChild.bind(e);e.appendChild=function(t){return x(t)||Reflect.apply(n,e,[t]),t}}}var P=!1;function O(){return t.jsx("div",{children:"Loading..."})}var S,I={id:"script-loader",version:"weekly"};class D extends e.PureComponent{constructor(){super(...arguments),r(this,"check",null),r(this,"state",{loaded:!1}),r(this,"cleanupCallback",(()=>{delete window.google.maps,this.injectScript()})),r(this,"isCleaningUp",E((function*(){return new Promise((function(e){if(P){if(M)var t=window.setInterval((function(){P||(window.clearInterval(t),e())}),1)}else e()}))}))),r(this,"cleanup",(()=>{P=!0;var e=document.getElementById(this.props.id);e&&e.parentNode&&e.parentNode.removeChild(e),Array.prototype.slice.call(document.getElementsByTagName("script")).filter((function(e){return"string"==typeof e.src&&e.src.includes("maps.googleapis")})).forEach((function(e){e.parentNode&&e.parentNode.removeChild(e)})),Array.prototype.slice.call(document.getElementsByTagName("link")).filter((function(e){return"https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Google+Sans"===e.href})).forEach((function(e){e.parentNode&&e.parentNode.removeChild(e)})),Array.prototype.slice.call(document.getElementsByTagName("style")).filter((function(e){return void 0!==e.innerText&&e.innerText.length>0&&e.innerText.includes(".gm-")})).forEach((function(e){e.parentNode&&e.parentNode.removeChild(e)}))})),r(this,"injectScript",(()=>{this.props.preventGoogleFontsLoading&&k(),p(!!this.props.id,'LoadScript requires "id" prop to be a string: %s',this.props.id),w({id:this.props.id,nonce:this.props.nonce,url:C(this.props)}).then((()=>{this.props.onLoad&&this.props.onLoad(),this.setState((function(){return{loaded:!0}}))})).catch((e=>{this.props.onError&&this.props.onError(e)}))})),r(this,"getRef",(e=>{this.check=e}))}componentDidMount(){if(M){if(window.google&&window.google.maps&&!P)return;this.isCleaningUp().then(this.injectScript).catch((function(e){}))}}componentDidUpdate(e){this.props.libraries,e.libraries,M&&e.language!==this.props.language&&(this.cleanup(),this.setState((function(){return{loaded:!1}}),this.cleanupCallback))}componentWillUnmount(){if(M){this.cleanup();window.setTimeout((()=>{this.check||(delete window.google,P=!1)}),1),this.props.onUnmount&&this.props.onUnmount()}}render(){return t.jsxs(t.Fragment,{children:[t.jsx("div",{ref:this.getRef}),this.state.loaded?this.props.children:this.props.loadingElement||t.jsx(O,{})]})}}function j(e,t){if(null==e)return{};var n,s,o=function(e,t){if(null==e)return{};var n={};for(var s in e)if({}.hasOwnProperty.call(e,s)){if(t.includes(s))continue;n[s]=e[s]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(s=0;s<i.length;s++)n=i[s],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}r(D,"defaultProps",I);var _=["loadingElement","onLoad","onError","onUnmount","children"],T=t.jsx(O,{});function A(e,t,n,s){return new(n||(n=Promise))((function(t,o){function i(e){try{a(s.next(e))}catch(t){o(t)}}function r(e){try{a(s.throw(e))}catch(t){o(t)}}function a(e){var s;e.done?t(e.value):(s=e.value,s instanceof n?s:new n((function(e){e(s)}))).then(i,r)}a((s=s.apply(e,[])).next())}))}function B(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}e.memo((function(t){var{loadingElement:n,onLoad:s,onError:o,onUnmount:i,children:r}=t,a=j(t,_),{isLoaded:l,loadError:u}=function(t){var{id:n=I.id,version:s=I.version,nonce:o,googleMapsApiKey:i,googleMapsClientId:r,language:a,region:l,libraries:u,preventGoogleFontsLoading:h,channel:c,mapIds:d,authReferrerPolicy:g}=t,m=e.useRef(!1),[v,f]=e.useState(!1),[y,b]=e.useState(void 0);e.useEffect((function(){return m.current=!0,()=>{m.current=!1}}),[]),e.useEffect((function(){M&&h&&k()}),[h]),e.useEffect((function(){v&&p(!!window.google,"useLoadScript was marked as loaded, but window.google is not present. Something went wrong.")}),[v]);var L=C({version:s,googleMapsApiKey:i,googleMapsClientId:r,language:a,region:l,libraries:u,channel:c,mapIds:d,authReferrerPolicy:g});e.useEffect((function(){function e(){m.current&&(f(!0),S=L)}M&&(window.google&&window.google.maps&&S===L?e():w({id:n,url:L,nonce:o}).then(e).catch((function(e){m.current&&b(e)})))}),[n,L,o]);var E=e.useRef(void 0);return e.useEffect((function(){E.current&&E.current,E.current=u}),[u]),{isLoaded:v,loadError:y,url:L}}(a);return e.useEffect((function(){l&&"function"==typeof s&&s()}),[l,s]),e.useEffect((function(){u&&"function"==typeof o&&o(u)}),[u,o]),e.useEffect((function(){return()=>{i&&i()}}),[i]),l?r:n||T})),"function"==typeof SuppressedError&&SuppressedError;var U,R,z=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var s,o,i;if(Array.isArray(t)){if((s=t.length)!=n.length)return!1;for(o=s;0!=o--;)if(!e(t[o],n[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((s=(i=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(o=s;0!=o--;)if(!Object.prototype.hasOwnProperty.call(n,i[o]))return!1;for(o=s;0!=o--;){var r=i[o];if(!e(t[r],n[r]))return!1}return!0}return t!=t&&n!=n},Z=B(z),V="__googleMapsScriptId";(R=U||(U={}))[R.INITIALIZED=0]="INITIALIZED",R[R.LOADING=1]="LOADING",R[R.SUCCESS=2]="SUCCESS",R[R.FAILURE=3]="FAILURE";class N{constructor(e){var{apiKey:t,authReferrerPolicy:n,channel:s,client:o,id:i=V,language:r,libraries:a=[],mapIds:l,nonce:u,region:p,retries:h=3,url:c="https://maps.googleapis.com/maps/api/js",version:d}=e;if(this.callbacks=[],this.done=!1,this.loading=!1,this.errors=[],this.apiKey=t,this.authReferrerPolicy=n,this.channel=s,this.client=o,this.id=i||V,this.language=r,this.libraries=a,this.mapIds=l,this.nonce=u,this.region=p,this.retries=h,this.url=c,this.version=d,N.instance){if(!Z(this.options,N.instance.options))throw new Error("Loader must not be called again with different options. ".concat(JSON.stringify(this.options)," !== ").concat(JSON.stringify(N.instance.options)));return N.instance}N.instance=this}get options(){return{version:this.version,apiKey:this.apiKey,channel:this.channel,client:this.client,id:this.id,libraries:this.libraries,language:this.language,region:this.region,mapIds:this.mapIds,nonce:this.nonce,url:this.url,authReferrerPolicy:this.authReferrerPolicy}}get status(){return this.errors.length?U.FAILURE:this.done?U.SUCCESS:this.loading?U.LOADING:U.INITIALIZED}get failed(){return this.done&&!this.loading&&this.errors.length>=this.retries+1}createUrl(){var e=this.url;return e+="?callback=__googleMapsCallback&loading=async",this.apiKey&&(e+="&key=".concat(this.apiKey)),this.channel&&(e+="&channel=".concat(this.channel)),this.client&&(e+="&client=".concat(this.client)),this.libraries.length>0&&(e+="&libraries=".concat(this.libraries.join(","))),this.language&&(e+="&language=".concat(this.language)),this.region&&(e+="&region=".concat(this.region)),this.version&&(e+="&v=".concat(this.version)),this.mapIds&&(e+="&map_ids=".concat(this.mapIds.join(","))),this.authReferrerPolicy&&(e+="&auth_referrer_policy=".concat(this.authReferrerPolicy)),e}deleteScript(){var e=document.getElementById(this.id);e&&e.remove()}load(){return this.loadPromise()}loadPromise(){return new Promise(((e,t)=>{this.loadCallback((n=>{n?t(n.error):e(window.google)}))}))}importLibrary(e){return this.execute(),google.maps.importLibrary(e)}loadCallback(e){this.callbacks.push(e),this.execute()}setScript(){var e,t;if(document.getElementById(this.id))this.callback();else{var n={key:this.apiKey,channel:this.channel,client:this.client,libraries:this.libraries.length&&this.libraries,v:this.version,mapIds:this.mapIds,language:this.language,region:this.region,authReferrerPolicy:this.authReferrerPolicy};Object.keys(n).forEach((e=>!n[e]&&delete n[e])),(null===(t=null===(e=null===window||void 0===window?void 0:window.google)||void 0===e?void 0:e.maps)||void 0===t?void 0:t.importLibrary)||(e=>{var t,n,s,o="google",i="importLibrary",r="__ib__",a=document,l=window,u=(l=l[o]||(l[o]={})).maps||(l.maps={}),p=new Set,h=new URLSearchParams,c=()=>t||(t=new Promise(((i,l)=>A(this,0,void 0,(function*(){var c;for(s in yield n=a.createElement("script"),n.id=this.id,h.set("libraries",[...p]+""),e)h.set(s.replace(/[A-Z]/g,(e=>"_"+e[0].toLowerCase())),e[s]);h.set("callback",o+".maps."+r),n.src=this.url+"?"+h,u[r]=i,n.onerror=()=>t=l(Error("The Google Maps JavaScript API could not load.")),n.nonce=this.nonce||(null===(c=a.querySelector("script[nonce]"))||void 0===c?void 0:c.nonce)||"",a.head.append(n)})))));u[i]||(u[i]=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),s=1;s<t;s++)n[s-1]=arguments[s];return p.add(e)&&c().then((()=>u[i](e,...n)))})})(n);var s=this.libraries.map((e=>this.importLibrary(e)));s.length||s.push(this.importLibrary("core")),Promise.all(s).then((()=>this.callback()),(e=>{var t=new ErrorEvent("error",{error:e});this.loadErrorCallback(t)}))}}reset(){this.deleteScript(),this.done=!1,this.loading=!1,this.errors=[],this.onerrorEvent=null}resetIfRetryingFailed(){this.failed&&this.reset()}loadErrorCallback(e){if(this.errors.push(e),this.errors.length<=this.retries){var t=this.errors.length*Math.pow(2,this.errors.length);setTimeout((()=>{this.deleteScript(),this.setScript()}),t)}else this.onerrorEvent=e,this.callback()}callback(){this.done=!0,this.loading=!1,this.callbacks.forEach((e=>{e(this.onerrorEvent)})),this.callbacks=[]}execute(){if(this.resetIfRetryingFailed(),!this.loading)if(this.done)this.callback();else{if(window.google&&window.google.maps&&window.google.maps.version)return void this.callback();this.loading=!0,this.setScript()}}}var W=["maps"];function H(t){var{id:n=I.id,version:s=I.version,nonce:o,googleMapsApiKey:i,language:r,region:a,libraries:l=W,preventGoogleFontsLoading:u,mapIds:p,authReferrerPolicy:h}=t,c=e.useRef(!1),[d,g]=e.useState(!1),[m,v]=e.useState(void 0);e.useEffect((function(){return c.current=!0,()=>{c.current=!1}}),[]);var f=e.useMemo((()=>new N({id:n,apiKey:i,version:s,libraries:l,language:r||"en",region:a||"US",mapIds:p||[],nonce:o||"",authReferrerPolicy:h||"origin"})),[n,i,s,l,r,a,p,o,h]);e.useEffect((function(){d||f.load().then((()=>{c.current&&g(!0)})).catch((e=>{v(e)}))}),[]),e.useEffect((()=>{M&&u&&k()}),[u]);var y=e.useRef();return e.useEffect((()=>{y.current&&y.current,y.current=l}),[l]),{isLoaded:d,loadError:m}}function F(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function G(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?F(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):F(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var K={},Y={options(e,t){e.setOptions(t)}};e.memo((function(t){var{options:n,onLoad:s,onUnmount:o}=t,i=e.useContext(h),[r,a]=e.useState(null);return e.useEffect((()=>{null!==r&&r.setMap(i)}),[i]),e.useEffect((()=>{n&&null!==r&&r.setOptions(n)}),[r,n]),e.useEffect((()=>{var e=new google.maps.TrafficLayer(G(G({},n),{},{map:i}));return a(e),s&&s(e),()=>{null!==r&&(o&&o(r),r.setMap(null))}}),[]),null}));class q extends e.PureComponent{constructor(){super(...arguments),r(this,"state",{trafficLayer:null}),r(this,"setTrafficLayerCallback",(()=>{null!==this.state.trafficLayer&&this.props.onLoad&&this.props.onLoad(this.state.trafficLayer)})),r(this,"registeredEvents",[])}componentDidMount(){var e=new google.maps.TrafficLayer(G(G({},this.props.options),{},{map:this.context}));this.registeredEvents=v({updaterMap:Y,eventMap:K,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{trafficLayer:e}}),this.setTrafficLayerCallback)}componentDidUpdate(e){null!==this.state.trafficLayer&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:Y,eventMap:K,prevProps:e,nextProps:this.props,instance:this.state.trafficLayer}))}componentWillUnmount(){null!==this.state.trafficLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.trafficLayer),m(this.registeredEvents),this.state.trafficLayer.setMap(null))}render(){return null}}r(q,"contextType",h),e.memo((function(t){var{onLoad:n,onUnmount:s}=t,o=e.useContext(h),[i,r]=e.useState(null);return e.useEffect((()=>{null!==i&&i.setMap(o)}),[o]),e.useEffect((()=>{var e=new google.maps.BicyclingLayer;return r(e),e.setMap(o),n&&n(e),()=>{null!==e&&(s&&s(e),e.setMap(null))}}),[]),null}));class $ extends e.PureComponent{constructor(){super(...arguments),r(this,"state",{bicyclingLayer:null}),r(this,"setBicyclingLayerCallback",(()=>{null!==this.state.bicyclingLayer&&(this.state.bicyclingLayer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.bicyclingLayer))}))}componentDidMount(){var e=new google.maps.BicyclingLayer;this.setState((()=>({bicyclingLayer:e})),this.setBicyclingLayerCallback)}componentWillUnmount(){null!==this.state.bicyclingLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.bicyclingLayer),this.state.bicyclingLayer.setMap(null))}render(){return null}}r($,"contextType",h),e.memo((function(t){var{onLoad:n,onUnmount:s}=t,o=e.useContext(h),[i,r]=e.useState(null);return e.useEffect((()=>{null!==i&&i.setMap(o)}),[o]),e.useEffect((()=>{var e=new google.maps.TransitLayer;return r(e),e.setMap(o),n&&n(e),()=>{null!==i&&(s&&s(i),i.setMap(null))}}),[]),null}));class J extends e.PureComponent{constructor(){super(...arguments),r(this,"state",{transitLayer:null}),r(this,"setTransitLayerCallback",(()=>{null!==this.state.transitLayer&&(this.state.transitLayer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.transitLayer))}))}componentDidMount(){var e=new google.maps.TransitLayer;this.setState((function(){return{transitLayer:e}}),this.setTransitLayerCallback)}componentWillUnmount(){null!==this.state.transitLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.transitLayer),this.state.transitLayer.setMap(null))}render(){return null}}function X(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Q(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?X(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):X(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}r(J,"contextType",h);var ee={onCircleComplete:"circlecomplete",onMarkerComplete:"markercomplete",onOverlayComplete:"overlaycomplete",onPolygonComplete:"polygoncomplete",onPolylineComplete:"polylinecomplete",onRectangleComplete:"rectanglecomplete"},te={drawingMode(e,t){e.setDrawingMode(t)},options(e,t){e.setOptions(t)}};e.memo((function(t){var{options:n,drawingMode:s,onCircleComplete:o,onMarkerComplete:i,onOverlayComplete:r,onPolygonComplete:a,onPolylineComplete:l,onRectangleComplete:u,onLoad:c,onUnmount:d}=t,g=e.useContext(h),[m,v]=e.useState(null),[f,y]=e.useState(null),[b,L]=e.useState(null),[E,C]=e.useState(null),[M,w]=e.useState(null),[x,k]=e.useState(null),[P,O]=e.useState(null);return e.useEffect((()=>{null!==m&&m.setMap(g)}),[g]),e.useEffect((()=>{n&&null!==m&&m.setOptions(n)}),[m,n]),e.useEffect((()=>{null!==m&&m.setDrawingMode(null!=s?s:null)}),[m,s]),e.useEffect((()=>{m&&o&&(null!==f&&google.maps.event.removeListener(f),y(google.maps.event.addListener(m,"circlecomplete",o)))}),[m,o]),e.useEffect((()=>{m&&i&&(null!==b&&google.maps.event.removeListener(b),L(google.maps.event.addListener(m,"markercomplete",i)))}),[m,i]),e.useEffect((()=>{m&&r&&(null!==E&&google.maps.event.removeListener(E),C(google.maps.event.addListener(m,"overlaycomplete",r)))}),[m,r]),e.useEffect((()=>{m&&a&&(null!==M&&google.maps.event.removeListener(M),w(google.maps.event.addListener(m,"polygoncomplete",a)))}),[m,a]),e.useEffect((()=>{m&&l&&(null!==x&&google.maps.event.removeListener(x),k(google.maps.event.addListener(m,"polylinecomplete",l)))}),[m,l]),e.useEffect((()=>{m&&u&&(null!==P&&google.maps.event.removeListener(P),O(google.maps.event.addListener(m,"rectanglecomplete",u)))}),[m,u]),e.useEffect((()=>{p(!!google.maps.drawing,"Did you include prop libraries={['drawing']} in the URL? %s",google.maps.drawing);var e=new google.maps.drawing.DrawingManager(Q(Q({},n),{},{map:g}));return s&&e.setDrawingMode(s),o&&y(google.maps.event.addListener(e,"circlecomplete",o)),i&&L(google.maps.event.addListener(e,"markercomplete",i)),r&&C(google.maps.event.addListener(e,"overlaycomplete",r)),a&&w(google.maps.event.addListener(e,"polygoncomplete",a)),l&&k(google.maps.event.addListener(e,"polylinecomplete",l)),u&&O(google.maps.event.addListener(e,"rectanglecomplete",u)),v(e),c&&c(e),()=>{null!==m&&(f&&google.maps.event.removeListener(f),b&&google.maps.event.removeListener(b),E&&google.maps.event.removeListener(E),M&&google.maps.event.removeListener(M),x&&google.maps.event.removeListener(x),P&&google.maps.event.removeListener(P),d&&d(m),m.setMap(null))}}),[]),null}));class ne extends e.PureComponent{constructor(e){super(e),r(this,"registeredEvents",[]),r(this,"state",{drawingManager:null}),r(this,"setDrawingManagerCallback",(()=>{null!==this.state.drawingManager&&this.props.onLoad&&this.props.onLoad(this.state.drawingManager)})),p(!!google.maps.drawing,"Did you include prop libraries={['drawing']} in the URL? %s",google.maps.drawing)}componentDidMount(){var e=new google.maps.drawing.DrawingManager(Q(Q({},this.props.options),{},{map:this.context}));this.registeredEvents=v({updaterMap:te,eventMap:ee,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{drawingManager:e}}),this.setDrawingManagerCallback)}componentDidUpdate(e){null!==this.state.drawingManager&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:te,eventMap:ee,prevProps:e,nextProps:this.props,instance:this.state.drawingManager}))}componentWillUnmount(){null!==this.state.drawingManager&&(this.props.onUnmount&&this.props.onUnmount(this.state.drawingManager),m(this.registeredEvents),this.state.drawingManager.setMap(null))}render(){return null}}function se(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function oe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?se(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):se(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}r(ne,"contextType",h);var ie={onAnimationChanged:"animation_changed",onClick:"click",onClickableChanged:"clickable_changed",onCursorChanged:"cursor_changed",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDraggableChanged:"draggable_changed",onDragStart:"dragstart",onFlatChanged:"flat_changed",onIconChanged:"icon_changed",onMouseDown:"mousedown",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onPositionChanged:"position_changed",onRightClick:"rightclick",onShapeChanged:"shape_changed",onTitleChanged:"title_changed",onVisibleChanged:"visible_changed",onZindexChanged:"zindex_changed"},re={animation(e,t){e.setAnimation(t)},clickable(e,t){e.setClickable(t)},cursor(e,t){e.setCursor(t)},draggable(e,t){e.setDraggable(t)},icon(e,t){e.setIcon(t)},label(e,t){e.setLabel(t)},map(e,t){e.setMap(t)},opacity(e,t){e.setOpacity(t)},options(e,t){e.setOptions(t)},position(e,t){e.setPosition(t)},shape(e,t){e.setShape(t)},title(e,t){e.setTitle(t)},visible(e,t){e.setVisible(t)},zIndex(e,t){e.setZIndex(t)}},ae={};e.memo((function(n){var{position:s,options:o,clusterer:i,noClustererRedraw:r,children:a,draggable:l,visible:u,animation:p,clickable:c,cursor:d,icon:g,label:m,opacity:v,shape:f,title:y,zIndex:b,onClick:L,onDblClick:E,onDrag:C,onDragEnd:M,onDragStart:w,onMouseOut:x,onMouseOver:k,onMouseUp:P,onMouseDown:O,onRightClick:S,onClickableChanged:I,onCursorChanged:D,onAnimationChanged:j,onDraggableChanged:_,onFlatChanged:T,onIconChanged:A,onPositionChanged:B,onShapeChanged:U,onTitleChanged:R,onVisibleChanged:z,onZindexChanged:Z,onLoad:V,onUnmount:N}=n,W=e.useContext(h),[H,F]=e.useState(null),[G,K]=e.useState(null),[Y,q]=e.useState(null),[$,J]=e.useState(null),[X,Q]=e.useState(null),[ee,te]=e.useState(null),[ne,se]=e.useState(null),[ie,re]=e.useState(null),[le,ue]=e.useState(null),[pe,he]=e.useState(null),[ce,de]=e.useState(null),[ge,me]=e.useState(null),[ve,fe]=e.useState(null),[ye,be]=e.useState(null),[Le,Ee]=e.useState(null),[Ce,Me]=e.useState(null),[we,xe]=e.useState(null),[ke,Pe]=e.useState(null),[Oe,Se]=e.useState(null),[Ie,De]=e.useState(null),[je,_e]=e.useState(null),[Te,Ae]=e.useState(null);e.useEffect((()=>{null!==H&&H.setMap(W)}),[W]),e.useEffect((()=>{void 0!==o&&null!==H&&H.setOptions(o)}),[H,o]),e.useEffect((()=>{void 0!==l&&null!==H&&H.setDraggable(l)}),[H,l]),e.useEffect((()=>{s&&null!==H&&H.setPosition(s)}),[H,s]),e.useEffect((()=>{void 0!==u&&null!==H&&H.setVisible(u)}),[H,u]),e.useEffect((()=>{null==H||H.setAnimation(p)}),[H,p]),e.useEffect((()=>{H&&void 0!==c&&H.setClickable(c)}),[H,c]),e.useEffect((()=>{H&&void 0!==d&&H.setCursor(d)}),[H,d]),e.useEffect((()=>{H&&void 0!==g&&H.setIcon(g)}),[H,g]),e.useEffect((()=>{H&&void 0!==m&&H.setLabel(m)}),[H,m]),e.useEffect((()=>{H&&void 0!==v&&H.setOpacity(v)}),[H,v]),e.useEffect((()=>{H&&void 0!==f&&H.setShape(f)}),[H,f]),e.useEffect((()=>{H&&void 0!==y&&H.setTitle(y)}),[H,y]),e.useEffect((()=>{H&&void 0!==b&&H.setZIndex(b)}),[H,b]),e.useEffect((()=>{H&&E&&(null!==G&&google.maps.event.removeListener(G),K(google.maps.event.addListener(H,"dblclick",E)))}),[E]),e.useEffect((()=>{H&&M&&(null!==Y&&google.maps.event.removeListener(Y),q(google.maps.event.addListener(H,"dragend",M)))}),[M]),e.useEffect((()=>{H&&w&&(null!==$&&google.maps.event.removeListener($),J(google.maps.event.addListener(H,"dragstart",w)))}),[w]),e.useEffect((()=>{H&&O&&(null!==X&&google.maps.event.removeListener(X),Q(google.maps.event.addListener(H,"mousedown",O)))}),[O]),e.useEffect((()=>{H&&x&&(null!==ee&&google.maps.event.removeListener(ee),te(google.maps.event.addListener(H,"mouseout",x)))}),[x]),e.useEffect((()=>{H&&k&&(null!==ne&&google.maps.event.removeListener(ne),se(google.maps.event.addListener(H,"mouseover",k)))}),[k]),e.useEffect((()=>{H&&P&&(null!==ie&&google.maps.event.removeListener(ie),re(google.maps.event.addListener(H,"mouseup",P)))}),[P]),e.useEffect((()=>{H&&S&&(null!==le&&google.maps.event.removeListener(le),ue(google.maps.event.addListener(H,"rightclick",S)))}),[S]),e.useEffect((()=>{H&&L&&(null!==pe&&google.maps.event.removeListener(pe),he(google.maps.event.addListener(H,"click",L)))}),[L]),e.useEffect((()=>{H&&C&&(null!==ce&&google.maps.event.removeListener(ce),de(google.maps.event.addListener(H,"drag",C)))}),[C]),e.useEffect((()=>{H&&I&&(null!==ge&&google.maps.event.removeListener(ge),me(google.maps.event.addListener(H,"clickable_changed",I)))}),[I]),e.useEffect((()=>{H&&D&&(null!==ve&&google.maps.event.removeListener(ve),fe(google.maps.event.addListener(H,"cursor_changed",D)))}),[D]),e.useEffect((()=>{H&&j&&(null!==ye&&google.maps.event.removeListener(ye),be(google.maps.event.addListener(H,"animation_changed",j)))}),[j]),e.useEffect((()=>{H&&_&&(null!==Le&&google.maps.event.removeListener(Le),Ee(google.maps.event.addListener(H,"draggable_changed",_)))}),[_]),e.useEffect((()=>{H&&T&&(null!==Ce&&google.maps.event.removeListener(Ce),Me(google.maps.event.addListener(H,"flat_changed",T)))}),[T]),e.useEffect((()=>{H&&A&&(null!==we&&google.maps.event.removeListener(we),xe(google.maps.event.addListener(H,"icon_changed",A)))}),[A]),e.useEffect((()=>{H&&B&&(null!==ke&&google.maps.event.removeListener(ke),Pe(google.maps.event.addListener(H,"position_changed",B)))}),[B]),e.useEffect((()=>{H&&U&&(null!==Oe&&google.maps.event.removeListener(Oe),Se(google.maps.event.addListener(H,"shape_changed",U)))}),[U]),e.useEffect((()=>{H&&R&&(null!==Ie&&google.maps.event.removeListener(Ie),De(google.maps.event.addListener(H,"title_changed",R)))}),[R]),e.useEffect((()=>{H&&z&&(null!==je&&google.maps.event.removeListener(je),_e(google.maps.event.addListener(H,"visible_changed",z)))}),[z]),e.useEffect((()=>{H&&Z&&(null!==Te&&google.maps.event.removeListener(Te),Ae(google.maps.event.addListener(H,"zindex_changed",Z)))}),[Z]),e.useEffect((()=>{var e=oe(oe(oe({},o||ae),i?ae:{map:W}),{},{position:s}),t=new google.maps.Marker(e);return i?i.addMarker(t,!!r):t.setMap(W),s&&t.setPosition(s),void 0!==u&&t.setVisible(u),void 0!==l&&t.setDraggable(l),void 0!==c&&t.setClickable(c),"string"==typeof d&&t.setCursor(d),g&&t.setIcon(g),void 0!==m&&t.setLabel(m),void 0!==v&&t.setOpacity(v),f&&t.setShape(f),"string"==typeof y&&t.setTitle(y),"number"==typeof b&&t.setZIndex(b),E&&K(google.maps.event.addListener(t,"dblclick",E)),M&&q(google.maps.event.addListener(t,"dragend",M)),w&&J(google.maps.event.addListener(t,"dragstart",w)),O&&Q(google.maps.event.addListener(t,"mousedown",O)),x&&te(google.maps.event.addListener(t,"mouseout",x)),k&&se(google.maps.event.addListener(t,"mouseover",k)),P&&re(google.maps.event.addListener(t,"mouseup",P)),S&&ue(google.maps.event.addListener(t,"rightclick",S)),L&&he(google.maps.event.addListener(t,"click",L)),C&&de(google.maps.event.addListener(t,"drag",C)),I&&me(google.maps.event.addListener(t,"clickable_changed",I)),D&&fe(google.maps.event.addListener(t,"cursor_changed",D)),j&&be(google.maps.event.addListener(t,"animation_changed",j)),_&&Ee(google.maps.event.addListener(t,"draggable_changed",_)),T&&Me(google.maps.event.addListener(t,"flat_changed",T)),A&&xe(google.maps.event.addListener(t,"icon_changed",A)),B&&Pe(google.maps.event.addListener(t,"position_changed",B)),U&&Se(google.maps.event.addListener(t,"shape_changed",U)),R&&De(google.maps.event.addListener(t,"title_changed",R)),z&&_e(google.maps.event.addListener(t,"visible_changed",z)),Z&&Ae(google.maps.event.addListener(t,"zindex_changed",Z)),F(t),V&&V(t),()=>{null!==G&&google.maps.event.removeListener(G),null!==Y&&google.maps.event.removeListener(Y),null!==$&&google.maps.event.removeListener($),null!==X&&google.maps.event.removeListener(X),null!==ee&&google.maps.event.removeListener(ee),null!==ne&&google.maps.event.removeListener(ne),null!==ie&&google.maps.event.removeListener(ie),null!==le&&google.maps.event.removeListener(le),null!==pe&&google.maps.event.removeListener(pe),null!==ge&&google.maps.event.removeListener(ge),null!==ve&&google.maps.event.removeListener(ve),null!==ye&&google.maps.event.removeListener(ye),null!==Le&&google.maps.event.removeListener(Le),null!==Ce&&google.maps.event.removeListener(Ce),null!==we&&google.maps.event.removeListener(we),null!==ke&&google.maps.event.removeListener(ke),null!==Ie&&google.maps.event.removeListener(Ie),null!==je&&google.maps.event.removeListener(je),null!==Te&&google.maps.event.removeListener(Te),N&&N(t),i?i.removeMarker(t,!!r):t&&t.setMap(null)}}),[]);var Be=e.useMemo((()=>a?e.Children.map(a,(t=>{if(!e.isValidElement(t))return t;var n=t;return e.cloneElement(n,{anchor:H})})):null),[a,H]);return t.jsx(t.Fragment,{children:Be})||null}));class le extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[])}componentDidMount(){var e=this;return E((function*(){var t=oe(oe(oe({},e.props.options||ae),e.props.clusterer?ae:{map:e.context}),{},{position:e.props.position});e.marker=new google.maps.Marker(t),e.props.clusterer?e.props.clusterer.addMarker(e.marker,!!e.props.noClustererRedraw):e.marker.setMap(e.context),e.registeredEvents=v({updaterMap:re,eventMap:ie,prevProps:{},nextProps:e.props,instance:e.marker}),e.props.onLoad&&e.props.onLoad(e.marker)}))()}componentDidUpdate(e){this.marker&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:re,eventMap:ie,prevProps:e,nextProps:this.props,instance:this.marker}))}componentWillUnmount(){this.marker&&(this.props.onUnmount&&this.props.onUnmount(this.marker),m(this.registeredEvents),this.props.clusterer?this.props.clusterer.removeMarker(this.marker,!!this.props.noClustererRedraw):this.marker&&this.marker.setMap(null))}render(){return(this.props.children?e.Children.map(this.props.children,(t=>{if(!e.isValidElement(t))return t;var n=t;return e.cloneElement(n,{anchor:this.marker})})):null)||null}}r(le,"contextType",h);var ue=function(){function e(t,n){t.getClusterer().extend(e,google.maps.OverlayView),this.cluster=t,this.clusterClassName=this.cluster.getClusterer().getClusterClass(),this.className=this.clusterClassName,this.styles=n,this.center=void 0,this.div=null,this.sums=null,this.visible=!1,this.boundsChangedListener=null,this.url="",this.height=0,this.width=0,this.anchorText=[0,0],this.anchorIcon=[0,0],this.textColor="black",this.textSize=11,this.textDecoration="none",this.fontWeight="bold",this.fontStyle="normal",this.fontFamily="Arial,sans-serif",this.backgroundPosition="0 0",this.cMouseDownInCluster=null,this.cDraggingMapByCluster=null,this.timeOut=null,this.setMap(t.getMap()),this.onBoundsChanged=this.onBoundsChanged.bind(this),this.onMouseDown=this.onMouseDown.bind(this),this.onClick=this.onClick.bind(this),this.onMouseOver=this.onMouseOver.bind(this),this.onMouseOut=this.onMouseOut.bind(this),this.onAdd=this.onAdd.bind(this),this.onRemove=this.onRemove.bind(this),this.draw=this.draw.bind(this),this.hide=this.hide.bind(this),this.show=this.show.bind(this),this.useStyle=this.useStyle.bind(this),this.setCenter=this.setCenter.bind(this),this.getPosFromLatLng=this.getPosFromLatLng.bind(this)}return e.prototype.onBoundsChanged=function(){this.cDraggingMapByCluster=this.cMouseDownInCluster},e.prototype.onMouseDown=function(){this.cMouseDownInCluster=!0,this.cDraggingMapByCluster=!1},e.prototype.onClick=function(e){if(this.cMouseDownInCluster=!1,!this.cDraggingMapByCluster){var t=this.cluster.getClusterer();if(google.maps.event.trigger(t,"click",this.cluster),google.maps.event.trigger(t,"clusterclick",this.cluster),t.getZoomOnClick()){var n=t.getMaxZoom(),s=this.cluster.getBounds(),o=t.getMap();null!==o&&"fitBounds"in o&&o.fitBounds(s),this.timeOut=window.setTimeout((function(){var e=t.getMap();if(null!==e){"fitBounds"in e&&e.fitBounds(s);var o=e.getZoom()||0;null!==n&&o>n&&e.setZoom(n+1)}}),100)}e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()}},e.prototype.onMouseOver=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseover",this.cluster)},e.prototype.onMouseOut=function(){google.maps.event.trigger(this.cluster.getClusterer(),"mouseout",this.cluster)},e.prototype.onAdd=function(){var e;this.div=document.createElement("div"),this.div.className=this.className,this.visible&&this.show(),null===(e=this.getPanes())||void 0===e||e.overlayMouseTarget.appendChild(this.div);var t=this.getMap();null!==t&&(this.boundsChangedListener=google.maps.event.addListener(t,"bounds_changed",this.onBoundsChanged),this.div.addEventListener("mousedown",this.onMouseDown),this.div.addEventListener("click",this.onClick),this.div.addEventListener("mouseover",this.onMouseOver),this.div.addEventListener("mouseout",this.onMouseOut))},e.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.hide(),null!==this.boundsChangedListener&&google.maps.event.removeListener(this.boundsChangedListener),this.div.removeEventListener("mousedown",this.onMouseDown),this.div.removeEventListener("click",this.onClick),this.div.removeEventListener("mouseover",this.onMouseOver),this.div.removeEventListener("mouseout",this.onMouseOut),this.div.parentNode.removeChild(this.div),null!==this.timeOut&&(window.clearTimeout(this.timeOut),this.timeOut=null),this.div=null)},e.prototype.draw=function(){if(this.visible&&null!==this.div&&this.center){var e=this.getPosFromLatLng(this.center);this.div.style.top=null!==e?"".concat(e.y,"px"):"0",this.div.style.left=null!==e?"".concat(e.x,"px"):"0"}},e.prototype.hide=function(){this.div&&(this.div.style.display="none"),this.visible=!1},e.prototype.show=function(){var e,t,n,s,o,i;if(this.div&&this.center){var r=null===this.sums||void 0===this.sums.title||""===this.sums.title?this.cluster.getClusterer().getTitle():this.sums.title,a=this.backgroundPosition.split(" "),l=parseInt((null===(e=a[0])||void 0===e?void 0:e.replace(/^\s+|\s+$/g,""))||"0",10),u=parseInt((null===(t=a[1])||void 0===t?void 0:t.replace(/^\s+|\s+$/g,""))||"0",10),p=this.getPosFromLatLng(this.center);this.div.className=this.className,this.div.setAttribute("style","cursor: pointer; position: absolute; top: ".concat(null!==p?"".concat(p.y,"px"):"0","; left: ").concat(null!==p?"".concat(p.x,"px"):"0","; width: ").concat(this.width,"px; height: ").concat(this.height,"px; "));var h=document.createElement("img");h.alt=r,h.src=this.url,h.width=this.width,h.height=this.height,h.setAttribute("style","position: absolute; top: ".concat(u,"px; left: ").concat(l,"px")),this.cluster.getClusterer().enableRetinaIcons||(h.style.clip="rect(-".concat(u,"px, -").concat(l+this.width,"px, -").concat(u+this.height,", -").concat(l,")"));var c=document.createElement("div");c.setAttribute("style","position: absolute; top: ".concat(this.anchorText[0],"px; left: ").concat(this.anchorText[1],"px; color: ").concat(this.textColor,"; font-size: ").concat(this.textSize,"px; font-family: ").concat(this.fontFamily,"; font-weight: ").concat(this.fontWeight,"; fontStyle: ").concat(this.fontStyle,"; text-decoration: ").concat(this.textDecoration,"; text-align: center; width: ").concat(this.width,"px; line-height: ").concat(this.height,"px")),(null===(n=this.sums)||void 0===n?void 0:n.text)&&(c.innerText="".concat(null===(s=this.sums)||void 0===s?void 0:s.text)),(null===(o=this.sums)||void 0===o?void 0:o.html)&&(c.innerHTML="".concat(null===(i=this.sums)||void 0===i?void 0:i.html)),this.div.innerHTML="",this.div.appendChild(h),this.div.appendChild(c),this.div.title=r,this.div.style.display=""}this.visible=!0},e.prototype.useStyle=function(e){this.sums=e;var t=this.cluster.getClusterer().getStyles(),n=t[Math.min(t.length-1,Math.max(0,e.index-1))];n&&(this.url=n.url,this.height=n.height,this.width=n.width,n.className&&(this.className="".concat(this.clusterClassName," ").concat(n.className)),this.anchorText=n.anchorText||[0,0],this.anchorIcon=n.anchorIcon||[this.height/2,this.width/2],this.textColor=n.textColor||"black",this.textSize=n.textSize||11,this.textDecoration=n.textDecoration||"none",this.fontWeight=n.fontWeight||"bold",this.fontStyle=n.fontStyle||"normal",this.fontFamily=n.fontFamily||"Arial,sans-serif",this.backgroundPosition=n.backgroundPosition||"0 0")},e.prototype.setCenter=function(e){this.center=e},e.prototype.getPosFromLatLng=function(e){var t=this.getProjection().fromLatLngToDivPixel(e);return null!==t&&(t.x-=this.anchorIcon[1],t.y-=this.anchorIcon[0]),t},e}(),pe=function(){function e(e){this.markerClusterer=e,this.map=this.markerClusterer.getMap(),this.gridSize=this.markerClusterer.getGridSize(),this.minClusterSize=this.markerClusterer.getMinimumClusterSize(),this.averageCenter=this.markerClusterer.getAverageCenter(),this.markers=[],this.center=void 0,this.bounds=null,this.clusterIcon=new ue(this,this.markerClusterer.getStyles()),this.getSize=this.getSize.bind(this),this.getMarkers=this.getMarkers.bind(this),this.getCenter=this.getCenter.bind(this),this.getMap=this.getMap.bind(this),this.getClusterer=this.getClusterer.bind(this),this.getBounds=this.getBounds.bind(this),this.remove=this.remove.bind(this),this.addMarker=this.addMarker.bind(this),this.isMarkerInClusterBounds=this.isMarkerInClusterBounds.bind(this),this.calculateBounds=this.calculateBounds.bind(this),this.updateIcon=this.updateIcon.bind(this),this.isMarkerAlreadyAdded=this.isMarkerAlreadyAdded.bind(this)}return e.prototype.getSize=function(){return this.markers.length},e.prototype.getMarkers=function(){return this.markers},e.prototype.getCenter=function(){return this.center},e.prototype.getMap=function(){return this.map},e.prototype.getClusterer=function(){return this.markerClusterer},e.prototype.getBounds=function(){for(var e=new google.maps.LatLngBounds(this.center,this.center),t=0,n=this.getMarkers();t<n.length;t++){var s=n[t].getPosition();s&&e.extend(s)}return e},e.prototype.remove=function(){this.clusterIcon.setMap(null),this.markers=[],delete this.markers},e.prototype.addMarker=function(e){var t,n;if(this.isMarkerAlreadyAdded(e))return!1;if(this.center){if(this.averageCenter&&(n=e.getPosition())){var s=this.markers.length+1;this.center=new google.maps.LatLng((this.center.lat()*(s-1)+n.lat())/s,(this.center.lng()*(s-1)+n.lng())/s),this.calculateBounds()}}else(n=e.getPosition())&&(this.center=n,this.calculateBounds());e.isAdded=!0,this.markers.push(e);var o=this.markers.length,i=this.markerClusterer.getMaxZoom(),r=null===(t=this.map)||void 0===t?void 0:t.getZoom();if(null!==i&&void 0!==r&&r>i)e.getMap()!==this.map&&e.setMap(this.map);else if(o<this.minClusterSize)e.getMap()!==this.map&&e.setMap(this.map);else if(o===this.minClusterSize)for(var a=0,l=this.markers;a<l.length;a++){l[a].setMap(null)}else e.setMap(null);return!0},e.prototype.isMarkerInClusterBounds=function(e){if(null!==this.bounds){var t=e.getPosition();if(t)return this.bounds.contains(t)}return!1},e.prototype.calculateBounds=function(){this.bounds=this.markerClusterer.getExtendedBounds(new google.maps.LatLngBounds(this.center,this.center))},e.prototype.updateIcon=function(){var e,t=this.markers.length,n=this.markerClusterer.getMaxZoom(),s=null===(e=this.map)||void 0===e?void 0:e.getZoom();null!==n&&void 0!==s&&s>n||t<this.minClusterSize?this.clusterIcon.hide():(this.center&&this.clusterIcon.setCenter(this.center),this.clusterIcon.useStyle(this.markerClusterer.getCalculator()(this.markers,this.markerClusterer.getStyles().length)),this.clusterIcon.show())},e.prototype.isMarkerAlreadyAdded=function(e){if(this.markers.includes)return this.markers.includes(e);for(var t=0;t<this.markers.length;t++)if(e===this.markers[t])return!0;return!1},e}();function he(e,t){var n=e.length,s=n.toString().length,o=Math.min(s,t);return{text:n.toString(),index:o,title:""}}var ce=[53,56,66,78,90],de=function(){function e(t,n,s){void 0===n&&(n=[]),void 0===s&&(s={}),this.getMinimumClusterSize=this.getMinimumClusterSize.bind(this),this.setMinimumClusterSize=this.setMinimumClusterSize.bind(this),this.getEnableRetinaIcons=this.getEnableRetinaIcons.bind(this),this.setEnableRetinaIcons=this.setEnableRetinaIcons.bind(this),this.addToClosestCluster=this.addToClosestCluster.bind(this),this.getImageExtension=this.getImageExtension.bind(this),this.setImageExtension=this.setImageExtension.bind(this),this.getExtendedBounds=this.getExtendedBounds.bind(this),this.getAverageCenter=this.getAverageCenter.bind(this),this.setAverageCenter=this.setAverageCenter.bind(this),this.getTotalClusters=this.getTotalClusters.bind(this),this.fitMapToMarkers=this.fitMapToMarkers.bind(this),this.getIgnoreHidden=this.getIgnoreHidden.bind(this),this.setIgnoreHidden=this.setIgnoreHidden.bind(this),this.getClusterClass=this.getClusterClass.bind(this),this.setClusterClass=this.setClusterClass.bind(this),this.getTotalMarkers=this.getTotalMarkers.bind(this),this.getZoomOnClick=this.getZoomOnClick.bind(this),this.setZoomOnClick=this.setZoomOnClick.bind(this),this.getBatchSizeIE=this.getBatchSizeIE.bind(this),this.setBatchSizeIE=this.setBatchSizeIE.bind(this),this.createClusters=this.createClusters.bind(this),this.onZoomChanged=this.onZoomChanged.bind(this),this.getImageSizes=this.getImageSizes.bind(this),this.setImageSizes=this.setImageSizes.bind(this),this.getCalculator=this.getCalculator.bind(this),this.setCalculator=this.setCalculator.bind(this),this.removeMarkers=this.removeMarkers.bind(this),this.resetViewport=this.resetViewport.bind(this),this.getImagePath=this.getImagePath.bind(this),this.setImagePath=this.setImagePath.bind(this),this.pushMarkerTo=this.pushMarkerTo.bind(this),this.removeMarker=this.removeMarker.bind(this),this.clearMarkers=this.clearMarkers.bind(this),this.setupStyles=this.setupStyles.bind(this),this.getGridSize=this.getGridSize.bind(this),this.setGridSize=this.setGridSize.bind(this),this.getClusters=this.getClusters.bind(this),this.getMaxZoom=this.getMaxZoom.bind(this),this.setMaxZoom=this.setMaxZoom.bind(this),this.getMarkers=this.getMarkers.bind(this),this.addMarkers=this.addMarkers.bind(this),this.getStyles=this.getStyles.bind(this),this.setStyles=this.setStyles.bind(this),this.addMarker=this.addMarker.bind(this),this.onRemove=this.onRemove.bind(this),this.getTitle=this.getTitle.bind(this),this.setTitle=this.setTitle.bind(this),this.repaint=this.repaint.bind(this),this.onIdle=this.onIdle.bind(this),this.redraw=this.redraw.bind(this),this.onAdd=this.onAdd.bind(this),this.draw=this.draw.bind(this),this.extend=this.extend.bind(this),this.extend(e,google.maps.OverlayView),this.markers=[],this.clusters=[],this.listeners=[],this.activeMap=null,this.ready=!1,this.gridSize=s.gridSize||60,this.minClusterSize=s.minimumClusterSize||2,this.maxZoom=s.maxZoom||null,this.styles=s.styles||[],this.title=s.title||"",this.zoomOnClick=!0,void 0!==s.zoomOnClick&&(this.zoomOnClick=s.zoomOnClick),this.averageCenter=!1,void 0!==s.averageCenter&&(this.averageCenter=s.averageCenter),this.ignoreHidden=!1,void 0!==s.ignoreHidden&&(this.ignoreHidden=s.ignoreHidden),this.enableRetinaIcons=!1,void 0!==s.enableRetinaIcons&&(this.enableRetinaIcons=s.enableRetinaIcons),this.imagePath=s.imagePath||"https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m",this.imageExtension=s.imageExtension||"png",this.imageSizes=s.imageSizes||ce,this.calculator=s.calculator||he,this.batchSize=s.batchSize||2e3,this.batchSizeIE=s.batchSizeIE||500,this.clusterClass=s.clusterClass||"cluster",-1!==navigator.userAgent.toLowerCase().indexOf("msie")&&(this.batchSize=this.batchSizeIE),this.timerRefStatic=null,this.setupStyles(),this.addMarkers(n,!0),this.setMap(t)}return e.prototype.onZoomChanged=function(){var e,t;this.resetViewport(!1),(null===(e=this.getMap())||void 0===e?void 0:e.getZoom())!==(this.get("minZoom")||0)&&(null===(t=this.getMap())||void 0===t?void 0:t.getZoom())!==this.get("maxZoom")||google.maps.event.trigger(this,"idle")},e.prototype.onIdle=function(){this.redraw()},e.prototype.onAdd=function(){var e=this.getMap();this.activeMap=e,this.ready=!0,this.repaint(),null!==e&&(this.listeners=[google.maps.event.addListener(e,"zoom_changed",this.onZoomChanged),google.maps.event.addListener(e,"idle",this.onIdle)])},e.prototype.onRemove=function(){for(var e=0,t=this.markers;e<t.length;e++){var n=t[e];n.getMap()!==this.activeMap&&n.setMap(this.activeMap)}for(var s=0,o=this.clusters;s<o.length;s++){o[s].remove()}this.clusters=[];for(var i=0,r=this.listeners;i<r.length;i++){var a=r[i];google.maps.event.removeListener(a)}this.listeners=[],this.activeMap=null,this.ready=!1},e.prototype.draw=function(){},e.prototype.getMap=function(){return null},e.prototype.getPanes=function(){return null},e.prototype.getProjection=function(){return{fromContainerPixelToLatLng:function(){return null},fromDivPixelToLatLng:function(){return null},fromLatLngToContainerPixel:function(){return null},fromLatLngToDivPixel:function(){return null},getVisibleRegion:function(){return null},getWorldWidth:function(){return 0}}},e.prototype.setMap=function(){},e.prototype.addListener=function(){return{remove:function(){}}},e.prototype.bindTo=function(){},e.prototype.get=function(){},e.prototype.notify=function(){},e.prototype.set=function(){},e.prototype.setValues=function(){},e.prototype.unbind=function(){},e.prototype.unbindAll=function(){},e.prototype.setupStyles=function(){if(!(this.styles.length>0))for(var e=0;e<this.imageSizes.length;e++)this.styles.push({url:"".concat(this.imagePath+(e+1),".").concat(this.imageExtension),height:this.imageSizes[e]||0,width:this.imageSizes[e]||0})},e.prototype.fitMapToMarkers=function(){for(var e=this.getMarkers(),t=new google.maps.LatLngBounds,n=0,s=e;n<s.length;n++){var o=s[n].getPosition();o&&t.extend(o)}var i=this.getMap();null!==i&&"fitBounds"in i&&i.fitBounds(t)},e.prototype.getGridSize=function(){return this.gridSize},e.prototype.setGridSize=function(e){this.gridSize=e},e.prototype.getMinimumClusterSize=function(){return this.minClusterSize},e.prototype.setMinimumClusterSize=function(e){this.minClusterSize=e},e.prototype.getMaxZoom=function(){return this.maxZoom},e.prototype.setMaxZoom=function(e){this.maxZoom=e},e.prototype.getStyles=function(){return this.styles},e.prototype.setStyles=function(e){this.styles=e},e.prototype.getTitle=function(){return this.title},e.prototype.setTitle=function(e){this.title=e},e.prototype.getZoomOnClick=function(){return this.zoomOnClick},e.prototype.setZoomOnClick=function(e){this.zoomOnClick=e},e.prototype.getAverageCenter=function(){return this.averageCenter},e.prototype.setAverageCenter=function(e){this.averageCenter=e},e.prototype.getIgnoreHidden=function(){return this.ignoreHidden},e.prototype.setIgnoreHidden=function(e){this.ignoreHidden=e},e.prototype.getEnableRetinaIcons=function(){return this.enableRetinaIcons},e.prototype.setEnableRetinaIcons=function(e){this.enableRetinaIcons=e},e.prototype.getImageExtension=function(){return this.imageExtension},e.prototype.setImageExtension=function(e){this.imageExtension=e},e.prototype.getImagePath=function(){return this.imagePath},e.prototype.setImagePath=function(e){this.imagePath=e},e.prototype.getImageSizes=function(){return this.imageSizes},e.prototype.setImageSizes=function(e){this.imageSizes=e},e.prototype.getCalculator=function(){return this.calculator},e.prototype.setCalculator=function(e){this.calculator=e},e.prototype.getBatchSizeIE=function(){return this.batchSizeIE},e.prototype.setBatchSizeIE=function(e){this.batchSizeIE=e},e.prototype.getClusterClass=function(){return this.clusterClass},e.prototype.setClusterClass=function(e){this.clusterClass=e},e.prototype.getMarkers=function(){return this.markers},e.prototype.getTotalMarkers=function(){return this.markers.length},e.prototype.getClusters=function(){return this.clusters},e.prototype.getTotalClusters=function(){return this.clusters.length},e.prototype.addMarker=function(e,t){this.pushMarkerTo(e),t||this.redraw()},e.prototype.addMarkers=function(e,t){for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var s=e[n];s&&this.pushMarkerTo(s)}t||this.redraw()},e.prototype.pushMarkerTo=function(e){var t=this;e.getDraggable()&&google.maps.event.addListener(e,"dragend",(function(){t.ready&&(e.isAdded=!1,t.repaint())})),e.isAdded=!1,this.markers.push(e)},e.prototype.removeMarker_=function(e){var t=-1;if(this.markers.indexOf)t=this.markers.indexOf(e);else for(var n=0;n<this.markers.length;n++)if(e===this.markers[n]){t=n;break}return-1!==t&&(e.setMap(null),this.markers.splice(t,1),!0)},e.prototype.removeMarker=function(e,t){var n=this.removeMarker_(e);return!t&&n&&this.repaint(),n},e.prototype.removeMarkers=function(e,t){for(var n=!1,s=0,o=e;s<o.length;s++){var i=o[s];n=n||this.removeMarker_(i)}return!t&&n&&this.repaint(),n},e.prototype.clearMarkers=function(){this.resetViewport(!0),this.markers=[]},e.prototype.repaint=function(){var e=this.clusters.slice();this.clusters=[],this.resetViewport(!1),this.redraw(),setTimeout((function(){for(var t=0,n=e;t<n.length;t++){n[t].remove()}}),0)},e.prototype.getExtendedBounds=function(e){var t=this.getProjection(),n=t.fromLatLngToDivPixel(new google.maps.LatLng(e.getNorthEast().lat(),e.getNorthEast().lng()));null!==n&&(n.x+=this.gridSize,n.y-=this.gridSize);var s=t.fromLatLngToDivPixel(new google.maps.LatLng(e.getSouthWest().lat(),e.getSouthWest().lng()));if(null!==s&&(s.x-=this.gridSize,s.y+=this.gridSize),null!==n){var o=t.fromDivPixelToLatLng(n);null!==o&&e.extend(o)}if(null!==s){var i=t.fromDivPixelToLatLng(s);null!==i&&e.extend(i)}return e},e.prototype.redraw=function(){this.createClusters(0)},e.prototype.resetViewport=function(e){for(var t=0,n=this.clusters;t<n.length;t++){n[t].remove()}this.clusters=[];for(var s=0,o=this.markers;s<o.length;s++){var i=o[s];i.isAdded=!1,e&&i.setMap(null)}},e.prototype.distanceBetweenPoints=function(e,t){var n=(t.lat()-e.lat())*Math.PI/180,s=(t.lng()-e.lng())*Math.PI/180,o=Math.sin(n/2)*Math.sin(n/2)+Math.cos(e.lat()*Math.PI/180)*Math.cos(t.lat()*Math.PI/180)*Math.sin(s/2)*Math.sin(s/2);return 2*Math.atan2(Math.sqrt(o),Math.sqrt(1-o))*6371},e.prototype.isMarkerInBounds=function(e,t){var n=e.getPosition();return!!n&&t.contains(n)},e.prototype.addToClosestCluster=function(e){for(var t,n=4e4,s=null,o=0,i=this.clusters;o<i.length;o++){var r=(t=i[o]).getCenter(),a=e.getPosition();if(r&&a){var l=this.distanceBetweenPoints(r,a);l<n&&(n=l,s=t)}}s&&s.isMarkerInClusterBounds(e)?s.addMarker(e):((t=new pe(this)).addMarker(e),this.clusters.push(t))},e.prototype.createClusters=function(e){var t=this;if(this.ready){0===e&&(google.maps.event.trigger(this,"clusteringbegin",this),null!==this.timerRefStatic&&(window.clearTimeout(this.timerRefStatic),delete this.timerRefStatic));for(var n=this.getMap(),s=(null!==n&&"getBounds"in n?n.getBounds():null),o=((null==n?void 0:n.getZoom())||0)>3?new google.maps.LatLngBounds(null==s?void 0:s.getSouthWest(),null==s?void 0:s.getNorthEast()):new google.maps.LatLngBounds(new google.maps.LatLng(85.02070771743472,-178.48388434375),new google.maps.LatLng(-85.08136444384544,178.00048865625)),i=this.getExtendedBounds(o),r=Math.min(e+this.batchSize,this.markers.length),a=e;a<r;a++){var l=this.markers[a];l&&!l.isAdded&&this.isMarkerInBounds(l,i)&&(!this.ignoreHidden||this.ignoreHidden&&l.getVisible())&&this.addToClosestCluster(l)}if(r<this.markers.length)this.timerRefStatic=window.setTimeout((function(){t.createClusters(r)}),0);else{this.timerRefStatic=null,google.maps.event.trigger(this,"clusteringend",this);for(var u=0,p=this.clusters;u<p.length;u++){p[u].updateIcon()}}}},e.prototype.extend=function(e,t){return function(e){for(var t in e.prototype){var n=t;this.prototype[n]=e.prototype[n]}return this}.apply(e,[t])},e}();function ge(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}var me={onClick:"click",onClusteringBegin:"clusteringbegin",onClusteringEnd:"clusteringend",onMouseOut:"mouseout",onMouseOver:"mouseover"},ve={averageCenter(e,t){e.setAverageCenter(t)},batchSizeIE(e,t){e.setBatchSizeIE(t)},calculator(e,t){e.setCalculator(t)},clusterClass(e,t){e.setClusterClass(t)},enableRetinaIcons(e,t){e.setEnableRetinaIcons(t)},gridSize(e,t){e.setGridSize(t)},ignoreHidden(e,t){e.setIgnoreHidden(t)},imageExtension(e,t){e.setImageExtension(t)},imagePath(e,t){e.setImagePath(t)},imageSizes(e,t){e.setImageSizes(t)},maxZoom(e,t){e.setMaxZoom(t)},minimumClusterSize(e,t){e.setMinimumClusterSize(t)},styles(e,t){e.setStyles(t)},title(e,t){e.setTitle(t)},zoomOnClick(e,t){e.setZoomOnClick(t)}},fe={};e.memo((function(t){var{children:n,options:s,averageCenter:o,batchSizeIE:i,calculator:a,clusterClass:l,enableRetinaIcons:u,gridSize:p,ignoreHidden:c,imageExtension:d,imagePath:g,imageSizes:m,maxZoom:v,minimumClusterSize:f,styles:y,title:b,zoomOnClick:L,onClick:E,onClusteringBegin:C,onClusteringEnd:M,onMouseOver:w,onMouseOut:x,onLoad:k,onUnmount:P}=t,[O,S]=e.useState(null),I=e.useContext(h),[D,j]=e.useState(null),[_,T]=e.useState(null),[A,B]=e.useState(null),[U,R]=e.useState(null),[z,Z]=e.useState(null);return e.useEffect((()=>{O&&x&&(null!==U&&google.maps.event.removeListener(U),R(google.maps.event.addListener(O,me.onMouseOut,x)))}),[x]),e.useEffect((()=>{O&&w&&(null!==z&&google.maps.event.removeListener(z),Z(google.maps.event.addListener(O,me.onMouseOver,w)))}),[w]),e.useEffect((()=>{O&&E&&(null!==D&&google.maps.event.removeListener(D),j(google.maps.event.addListener(O,me.onClick,E)))}),[E]),e.useEffect((()=>{O&&C&&(null!==_&&google.maps.event.removeListener(_),T(google.maps.event.addListener(O,me.onClusteringBegin,C)))}),[C]),e.useEffect((()=>{O&&M&&(null!==A&&google.maps.event.removeListener(A),T(google.maps.event.addListener(O,me.onClusteringEnd,M)))}),[M]),e.useEffect((()=>{void 0!==o&&null!==O&&ve.averageCenter(O,o)}),[O,o]),e.useEffect((()=>{void 0!==i&&null!==O&&ve.batchSizeIE(O,i)}),[O,i]),e.useEffect((()=>{void 0!==a&&null!==O&&ve.calculator(O,a)}),[O,a]),e.useEffect((()=>{void 0!==l&&null!==O&&ve.clusterClass(O,l)}),[O,l]),e.useEffect((()=>{void 0!==u&&null!==O&&ve.enableRetinaIcons(O,u)}),[O,u]),e.useEffect((()=>{void 0!==p&&null!==O&&ve.gridSize(O,p)}),[O,p]),e.useEffect((()=>{void 0!==c&&null!==O&&ve.ignoreHidden(O,c)}),[O,c]),e.useEffect((()=>{void 0!==d&&null!==O&&ve.imageExtension(O,d)}),[O,d]),e.useEffect((()=>{void 0!==g&&null!==O&&ve.imagePath(O,g)}),[O,g]),e.useEffect((()=>{void 0!==m&&null!==O&&ve.imageSizes(O,m)}),[O,m]),e.useEffect((()=>{void 0!==v&&null!==O&&ve.maxZoom(O,v)}),[O,v]),e.useEffect((()=>{void 0!==f&&null!==O&&ve.minimumClusterSize(O,f)}),[O,f]),e.useEffect((()=>{void 0!==y&&null!==O&&ve.styles(O,y)}),[O,y]),e.useEffect((()=>{void 0!==b&&null!==O&&ve.title(O,b)}),[O,b]),e.useEffect((()=>{void 0!==L&&null!==O&&ve.zoomOnClick(O,L)}),[O,L]),e.useEffect((()=>{if(I){var e=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ge(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ge(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},s||fe),t=new de(I,[],e);return o&&ve.averageCenter(t,o),i&&ve.batchSizeIE(t,i),a&&ve.calculator(t,a),l&&ve.clusterClass(t,l),u&&ve.enableRetinaIcons(t,u),p&&ve.gridSize(t,p),c&&ve.ignoreHidden(t,c),d&&ve.imageExtension(t,d),g&&ve.imagePath(t,g),m&&ve.imageSizes(t,m),v&&ve.maxZoom(t,v),f&&ve.minimumClusterSize(t,f),y&&ve.styles(t,y),b&&ve.title(t,b),L&&ve.zoomOnClick(t,L),x&&R(google.maps.event.addListener(t,me.onMouseOut,x)),w&&Z(google.maps.event.addListener(t,me.onMouseOver,w)),E&&j(google.maps.event.addListener(t,me.onClick,E)),C&&T(google.maps.event.addListener(t,me.onClusteringBegin,C)),M&&B(google.maps.event.addListener(t,me.onClusteringEnd,M)),S(t),k&&k(t),()=>{null!==U&&google.maps.event.removeListener(U),null!==z&&google.maps.event.removeListener(z),null!==D&&google.maps.event.removeListener(D),null!==_&&google.maps.event.removeListener(_),null!==A&&google.maps.event.removeListener(A),P&&P(t)}}}),[]),null!==O&&n(O)||null}));class ye extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"state",{markerClusterer:null}),r(this,"setClustererCallback",(()=>{null!==this.state.markerClusterer&&this.props.onLoad&&this.props.onLoad(this.state.markerClusterer)}))}componentDidMount(){if(this.context){var e=new de(this.context,[],this.props.options);this.registeredEvents=v({updaterMap:ve,eventMap:me,prevProps:{},nextProps:this.props,instance:e}),this.setState((()=>({markerClusterer:e})),this.setClustererCallback)}}componentDidUpdate(e){this.state.markerClusterer&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:ve,eventMap:me,prevProps:e,nextProps:this.props,instance:this.state.markerClusterer}))}componentWillUnmount(){null!==this.state.markerClusterer&&(this.props.onUnmount&&this.props.onUnmount(this.state.markerClusterer),m(this.registeredEvents),this.state.markerClusterer.setMap(null))}render(){return null!==this.state.markerClusterer?this.props.children(this.state.markerClusterer):null}}function be(e){e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation()}r(ye,"contextType",h);var Le=function(){function e(t){void 0===t&&(t={}),this.getCloseClickHandler=this.getCloseClickHandler.bind(this),this.closeClickHandler=this.closeClickHandler.bind(this),this.createInfoBoxDiv=this.createInfoBoxDiv.bind(this),this.addClickHandler=this.addClickHandler.bind(this),this.getCloseBoxImg=this.getCloseBoxImg.bind(this),this.getBoxWidths=this.getBoxWidths.bind(this),this.setBoxStyle=this.setBoxStyle.bind(this),this.setPosition=this.setPosition.bind(this),this.getPosition=this.getPosition.bind(this),this.setOptions=this.setOptions.bind(this),this.setContent=this.setContent.bind(this),this.setVisible=this.setVisible.bind(this),this.getContent=this.getContent.bind(this),this.getVisible=this.getVisible.bind(this),this.setZIndex=this.setZIndex.bind(this),this.getZIndex=this.getZIndex.bind(this),this.onRemove=this.onRemove.bind(this),this.panBox=this.panBox.bind(this),this.extend=this.extend.bind(this),this.close=this.close.bind(this),this.draw=this.draw.bind(this),this.show=this.show.bind(this),this.hide=this.hide.bind(this),this.open=this.open.bind(this),this.extend(e,google.maps.OverlayView),this.content=t.content||"",this.disableAutoPan=t.disableAutoPan||!1,this.maxWidth=t.maxWidth||0,this.pixelOffset=t.pixelOffset||new google.maps.Size(0,0),this.position=t.position||new google.maps.LatLng(0,0),this.zIndex=t.zIndex||null,this.boxClass=t.boxClass||"infoBox",this.boxStyle=t.boxStyle||{},this.closeBoxMargin=t.closeBoxMargin||"2px",this.closeBoxURL=t.closeBoxURL||"http://www.google.com/intl/en_us/mapfiles/close.gif",""===t.closeBoxURL&&(this.closeBoxURL=""),this.infoBoxClearance=t.infoBoxClearance||new google.maps.Size(1,1),void 0===t.visible&&(void 0===t.isHidden?t.visible=!0:t.visible=!t.isHidden),this.isHidden=!t.visible,this.alignBottom=t.alignBottom||!1,this.pane=t.pane||"floatPane",this.enableEventPropagation=t.enableEventPropagation||!1,this.div=null,this.closeListener=null,this.moveListener=null,this.mapListener=null,this.contextListener=null,this.eventListeners=null,this.fixedWidthSet=null}return e.prototype.createInfoBoxDiv=function(){var e=this;if(!this.div){this.div=document.createElement("div"),this.setBoxStyle(),"string"==typeof this.content?this.div.innerHTML=this.getCloseBoxImg()+this.content:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(this.content));var t=this.getPanes();if(null!==t&&t[this.pane].appendChild(this.div),this.addClickHandler(),this.div.style.width)this.fixedWidthSet=!0;else if(0!==this.maxWidth&&this.div.offsetWidth>this.maxWidth)this.div.style.width=this.maxWidth+"px",this.fixedWidthSet=!0;else{var n=this.getBoxWidths();this.div.style.width=this.div.offsetWidth-n.left-n.right+"px",this.fixedWidthSet=!1}if(this.panBox(this.disableAutoPan),!this.enableEventPropagation){this.eventListeners=[];for(var s=0,o=["mousedown","mouseover","mouseout","mouseup","click","dblclick","touchstart","touchend","touchmove"];s<o.length;s++){var i=o[s];this.eventListeners.push(google.maps.event.addListener(this.div,i,be))}this.eventListeners.push(google.maps.event.addListener(this.div,"mouseover",(function(){e.div&&(e.div.style.cursor="default")})))}this.contextListener=google.maps.event.addListener(this.div,"contextmenu",(function(t){t.returnValue=!1,t.preventDefault&&t.preventDefault(),e.enableEventPropagation||be(t)})),google.maps.event.trigger(this,"domready")}},e.prototype.getCloseBoxImg=function(){var e="";return""!==this.closeBoxURL&&(e='<img alt=""',e+=' aria-hidden="true"',e+=" src='"+this.closeBoxURL+"'",e+=" align=right",e+=" style='",e+=" position: relative;",e+=" cursor: pointer;",e+=" margin: "+this.closeBoxMargin+";",e+="'>"),e},e.prototype.addClickHandler=function(){this.closeListener=this.div&&this.div.firstChild&&""!==this.closeBoxURL?google.maps.event.addListener(this.div.firstChild,"click",this.getCloseClickHandler()):null},e.prototype.closeClickHandler=function(e){e.cancelBubble=!0,e.stopPropagation&&e.stopPropagation(),google.maps.event.trigger(this,"closeclick"),this.close()},e.prototype.getCloseClickHandler=function(){return this.closeClickHandler},e.prototype.panBox=function(e){if(this.div&&!e){var t=this.getMap();if(t instanceof google.maps.Map){var n=0,s=0,o=t.getBounds();o&&!o.contains(this.position)&&t.setCenter(this.position);var i=t.getDiv(),r=i.offsetWidth,a=i.offsetHeight,l=this.pixelOffset.width,u=this.pixelOffset.height,p=this.div.offsetWidth,h=this.div.offsetHeight,c=this.infoBoxClearance.width,d=this.infoBoxClearance.height,g=this.getProjection().fromLatLngToContainerPixel(this.position);null!==g&&(g.x<-l+c?n=g.x+l-c:g.x+p+l+c>r&&(n=g.x+p+l+c-r),this.alignBottom?g.y<-u+d+h?s=g.y+u-d-h:g.y+u+d>a&&(s=g.y+u+d-a):g.y<-u+d?s=g.y+u-d:g.y+h+u+d>a&&(s=g.y+h+u+d-a)),0===n&&0===s||t.panBy(n,s)}}},e.prototype.setBoxStyle=function(){if(this.div){this.div.className=this.boxClass,this.div.style.cssText="";var e=this.boxStyle;for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(this.div.style[t]=e[t]);if(this.div.style.webkitTransform="translateZ(0)",void 0!==this.div.style.opacity&&""!==this.div.style.opacity){var n=parseFloat(this.div.style.opacity||"");this.div.style.msFilter='"progid:DXImageTransform.Microsoft.Alpha(Opacity='+100*n+')"',this.div.style.filter="alpha(opacity="+100*n+")"}this.div.style.position="absolute",this.div.style.visibility="hidden",null!==this.zIndex&&(this.div.style.zIndex=this.zIndex+""),this.div.style.overflow||(this.div.style.overflow="auto")}},e.prototype.getBoxWidths=function(){var e={top:0,bottom:0,left:0,right:0};if(!this.div)return e;if(document.defaultView){var t=this.div.ownerDocument,n=t&&t.defaultView?t.defaultView.getComputedStyle(this.div,""):null;n&&(e.top=parseInt(n.borderTopWidth||"",10)||0,e.bottom=parseInt(n.borderBottomWidth||"",10)||0,e.left=parseInt(n.borderLeftWidth||"",10)||0,e.right=parseInt(n.borderRightWidth||"",10)||0)}else if(document.documentElement.currentStyle){var s=this.div.currentStyle;s&&(e.top=parseInt(s.borderTopWidth||"",10)||0,e.bottom=parseInt(s.borderBottomWidth||"",10)||0,e.left=parseInt(s.borderLeftWidth||"",10)||0,e.right=parseInt(s.borderRightWidth||"",10)||0)}return e},e.prototype.onRemove=function(){this.div&&this.div.parentNode&&(this.div.parentNode.removeChild(this.div),this.div=null)},e.prototype.draw=function(){if(this.createInfoBoxDiv(),this.div){var e=this.getProjection().fromLatLngToDivPixel(this.position);null!==e&&(this.div.style.left=e.x+this.pixelOffset.width+"px",this.alignBottom?this.div.style.bottom=-(e.y+this.pixelOffset.height)+"px":this.div.style.top=e.y+this.pixelOffset.height+"px"),this.isHidden?this.div.style.visibility="hidden":this.div.style.visibility="visible"}},e.prototype.setOptions=function(e){void 0===e&&(e={}),void 0!==e.boxClass&&(this.boxClass=e.boxClass,this.setBoxStyle()),void 0!==e.boxStyle&&(this.boxStyle=e.boxStyle,this.setBoxStyle()),void 0!==e.content&&this.setContent(e.content),void 0!==e.disableAutoPan&&(this.disableAutoPan=e.disableAutoPan),void 0!==e.maxWidth&&(this.maxWidth=e.maxWidth),void 0!==e.pixelOffset&&(this.pixelOffset=e.pixelOffset),void 0!==e.alignBottom&&(this.alignBottom=e.alignBottom),void 0!==e.position&&this.setPosition(e.position),void 0!==e.zIndex&&this.setZIndex(e.zIndex),void 0!==e.closeBoxMargin&&(this.closeBoxMargin=e.closeBoxMargin),void 0!==e.closeBoxURL&&(this.closeBoxURL=e.closeBoxURL),void 0!==e.infoBoxClearance&&(this.infoBoxClearance=e.infoBoxClearance),void 0!==e.isHidden&&(this.isHidden=e.isHidden),void 0!==e.visible&&(this.isHidden=!e.visible),void 0!==e.enableEventPropagation&&(this.enableEventPropagation=e.enableEventPropagation),this.div&&this.draw()},e.prototype.setContent=function(e){this.content=e,this.div&&(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.fixedWidthSet||(this.div.style.width=""),"string"==typeof e?this.div.innerHTML=this.getCloseBoxImg()+e:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(e)),this.fixedWidthSet||(this.div.style.width=this.div.offsetWidth+"px","string"==typeof e?this.div.innerHTML=this.getCloseBoxImg()+e:(this.div.innerHTML=this.getCloseBoxImg(),this.div.appendChild(e))),this.addClickHandler()),google.maps.event.trigger(this,"content_changed")},e.prototype.setPosition=function(e){this.position=e,this.div&&this.draw(),google.maps.event.trigger(this,"position_changed")},e.prototype.setVisible=function(e){this.isHidden=!e,this.div&&(this.div.style.visibility=this.isHidden?"hidden":"visible")},e.prototype.setZIndex=function(e){this.zIndex=e,this.div&&(this.div.style.zIndex=e+""),google.maps.event.trigger(this,"zindex_changed")},e.prototype.getContent=function(){return this.content},e.prototype.getPosition=function(){return this.position},e.prototype.getZIndex=function(){return this.zIndex},e.prototype.getVisible=function(){var e=this.getMap();return null!=e&&!this.isHidden},e.prototype.show=function(){this.isHidden=!1,this.div&&(this.div.style.visibility="visible")},e.prototype.hide=function(){this.isHidden=!0,this.div&&(this.div.style.visibility="hidden")},e.prototype.open=function(e,t){var n=this;t&&(this.position=t.getPosition(),this.moveListener=google.maps.event.addListener(t,"position_changed",(function(){var e=t.getPosition();n.setPosition(e)})),this.mapListener=google.maps.event.addListener(t,"map_changed",(function(){n.setMap(t.map)}))),this.setMap(e),this.div&&this.panBox()},e.prototype.close=function(){if(this.closeListener&&(google.maps.event.removeListener(this.closeListener),this.closeListener=null),this.eventListeners){for(var e=0,t=this.eventListeners;e<t.length;e++){var n=t[e];google.maps.event.removeListener(n)}this.eventListeners=null}this.moveListener&&(google.maps.event.removeListener(this.moveListener),this.moveListener=null),this.mapListener&&(google.maps.event.removeListener(this.mapListener),this.mapListener=null),this.contextListener&&(google.maps.event.removeListener(this.contextListener),this.contextListener=null),this.setMap(null)},e.prototype.extend=function(e,t){return function(e){for(var t in e.prototype)Object.prototype.hasOwnProperty.call(this,t)||(this.prototype[t]=e.prototype[t]);return this}.apply(e,[t])},e}(),Ee=["position"],Ce=["position"];function Me(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function we(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Me(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Me(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var xe,ke,Pe={onCloseClick:"closeclick",onContentChanged:"content_changed",onDomReady:"domready",onPositionChanged:"position_changed",onZindexChanged:"zindex_changed"},Oe={options(e,t){e.setOptions(t)},position(e,t){t instanceof google.maps.LatLng?e.setPosition(t):e.setPosition(new google.maps.LatLng(t.lat,t.lng))},visible(e,t){e.setVisible(t)},zIndex(e,t){e.setZIndex(t)}},Se={};e.memo((function(t){var{children:s,anchor:o,options:i,position:r,zIndex:a,onCloseClick:l,onDomReady:u,onContentChanged:c,onPositionChanged:d,onZindexChanged:g,onLoad:m,onUnmount:v}=t,f=e.useContext(h),[y,b]=e.useState(null),[L,E]=e.useState(null),[C,M]=e.useState(null),[w,x]=e.useState(null),[k,P]=e.useState(null),[O,S]=e.useState(null),I=e.useRef(null);return e.useEffect((()=>{f&&null!==y&&(y.close(),o?y.open(f,o):y.getPosition()&&y.open(f))}),[f,y,o]),e.useEffect((()=>{i&&null!==y&&y.setOptions(i)}),[y,i]),e.useEffect((()=>{if(r&&null!==y){var e=r instanceof google.maps.LatLng?r:new google.maps.LatLng(r.lat,r.lng);y.setPosition(e)}}),[r]),e.useEffect((()=>{"number"==typeof a&&null!==y&&y.setZIndex(a)}),[a]),e.useEffect((()=>{y&&l&&(null!==L&&google.maps.event.removeListener(L),E(google.maps.event.addListener(y,"closeclick",l)))}),[l]),e.useEffect((()=>{y&&u&&(null!==C&&google.maps.event.removeListener(C),M(google.maps.event.addListener(y,"domready",u)))}),[u]),e.useEffect((()=>{y&&c&&(null!==w&&google.maps.event.removeListener(w),x(google.maps.event.addListener(y,"content_changed",c)))}),[c]),e.useEffect((()=>{y&&d&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(y,"position_changed",d)))}),[d]),e.useEffect((()=>{y&&g&&(null!==O&&google.maps.event.removeListener(O),S(google.maps.event.addListener(y,"zindex_changed",g)))}),[g]),e.useEffect((()=>{if(f){var e,t=i||Se,{position:n}=t,s=j(t,Ee);!n||n instanceof google.maps.LatLng||(e=new google.maps.LatLng(n.lat,n.lng));var r=new Le(we(we({},s),e?{position:e}:{}));I.current=document.createElement("div"),b(r),l&&E(google.maps.event.addListener(r,"closeclick",l)),u&&M(google.maps.event.addListener(r,"domready",u)),c&&x(google.maps.event.addListener(r,"content_changed",c)),d&&P(google.maps.event.addListener(r,"position_changed",d)),g&&S(google.maps.event.addListener(r,"zindex_changed",g)),r.setContent(I.current),o?r.open(f,o):r.getPosition()?r.open(f):p(!1,"You must provide either an anchor or a position prop for <InfoBox>."),m&&m(r)}return()=>{null!==y&&(L&&google.maps.event.removeListener(L),w&&google.maps.event.removeListener(w),C&&google.maps.event.removeListener(C),k&&google.maps.event.removeListener(k),O&&google.maps.event.removeListener(O),v&&v(y),y.close())}}),[]),I.current?n.createPortal(e.Children.only(s),I.current):null}));class Ie extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"containerElement",null),r(this,"state",{infoBox:null}),r(this,"open",((e,t)=>{t?null!==this.context&&e.open(this.context,t):e.getPosition()?null!==this.context&&e.open(this.context):p(!1,"You must provide either an anchor or a position prop for <InfoBox>.")})),r(this,"setInfoBoxCallback",(()=>{null!==this.state.infoBox&&null!==this.containerElement&&(this.state.infoBox.setContent(this.containerElement),this.open(this.state.infoBox,this.props.anchor),this.props.onLoad&&this.props.onLoad(this.state.infoBox))}))}componentDidMount(){var e,t=this.props.options||{},{position:n}=t,s=j(t,Ce);!n||n instanceof google.maps.LatLng||(e=new google.maps.LatLng(n.lat,n.lng));var o=new Le(we(we({},s),e?{position:e}:{}));this.containerElement=document.createElement("div"),this.registeredEvents=v({updaterMap:Oe,eventMap:Pe,prevProps:{},nextProps:this.props,instance:o}),this.setState({infoBox:o},this.setInfoBoxCallback)}componentDidUpdate(e){var{infoBox:t}=this.state;null!==t&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:Oe,eventMap:Pe,prevProps:e,nextProps:this.props,instance:t}))}componentWillUnmount(){var{onUnmount:e}=this.props,{infoBox:t}=this.state;null!==t&&(e&&e(t),m(this.registeredEvents),t.close())}render(){return this.containerElement?n.createPortal(e.Children.only(this.props.children),this.containerElement):null}}r(Ie,"contextType",h);var De=a(ke?xe:(ke=1,xe=function e(t,n){if(t===n)return!0;if(t&&n&&"object"==typeof t&&"object"==typeof n){if(t.constructor!==n.constructor)return!1;var s,o,i;if(Array.isArray(t)){if((s=t.length)!=n.length)return!1;for(o=s;0!=o--;)if(!e(t[o],n[o]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if((s=(i=Object.keys(t)).length)!==Object.keys(n).length)return!1;for(o=s;0!=o--;)if(!Object.prototype.hasOwnProperty.call(n,i[o]))return!1;for(o=s;0!=o--;){var r=i[o];if(!e(t[r],n[r]))return!1}return!0}return t!=t&&n!=n})),je=[Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];let _e=class e{static from(t){if(!(t instanceof ArrayBuffer))throw new Error("Data must be an instance of ArrayBuffer.");var[n,s]=new Uint8Array(t,0,2);if(219!==n)throw new Error("Data does not appear to be in a KDBush format.");var o=s>>4;if(1!==o)throw new Error("Got v".concat(o," data when expected v").concat(1,"."));var i=je[15&s];if(!i)throw new Error("Unrecognized array type.");var[r]=new Uint16Array(t,2,1),[a]=new Uint32Array(t,4,1);return new e(a,r,i,t)}constructor(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:64,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Float64Array,s=arguments.length>3?arguments[3]:void 0;if(isNaN(e)||e<0)throw new Error("Unpexpected numItems value: ".concat(e,"."));this.numItems=+e,this.nodeSize=Math.min(Math.max(+t,2),65535),this.ArrayType=n,this.IndexArrayType=e<65536?Uint16Array:Uint32Array;var o=je.indexOf(this.ArrayType),i=2*e*this.ArrayType.BYTES_PER_ELEMENT,r=e*this.IndexArrayType.BYTES_PER_ELEMENT,a=(8-r%8)%8;if(o<0)throw new Error("Unexpected typed array class: ".concat(n,"."));s&&s instanceof ArrayBuffer?(this.data=s,this.ids=new this.IndexArrayType(this.data,8,e),this.coords=new this.ArrayType(this.data,8+r+a,2*e),this._pos=2*e,this._finished=!0):(this.data=new ArrayBuffer(8+i+r+a),this.ids=new this.IndexArrayType(this.data,8,e),this.coords=new this.ArrayType(this.data,8+r+a,2*e),this._pos=0,this._finished=!1,new Uint8Array(this.data,0,2).set([219,16+o]),new Uint16Array(this.data,2,1)[0]=t,new Uint32Array(this.data,4,1)[0]=e)}add(e,t){var n=this._pos>>1;return this.ids[n]=n,this.coords[this._pos++]=e,this.coords[this._pos++]=t,n}finish(){var e=this._pos>>1;if(e!==this.numItems)throw new Error("Added ".concat(e," items when expected ").concat(this.numItems,"."));return Te(this.ids,this.coords,this.nodeSize,0,this.numItems-1,0),this._finished=!0,this}range(e,t,n,s){if(!this._finished)throw new Error("Data not yet indexed - call index.finish().");for(var{ids:o,coords:i,nodeSize:r}=this,a=[0,o.length-1,0],l=[];a.length;){var u=a.pop()||0,p=a.pop()||0,h=a.pop()||0;if(p-h<=r)for(var c=h;c<=p;c++){var d=i[2*c],g=i[2*c+1];d>=e&&d<=n&&g>=t&&g<=s&&l.push(o[c])}else{var m=h+p>>1,v=i[2*m],f=i[2*m+1];v>=e&&v<=n&&f>=t&&f<=s&&l.push(o[m]),(0===u?e<=v:t<=f)&&(a.push(h),a.push(m-1),a.push(1-u)),(0===u?n>=v:s>=f)&&(a.push(m+1),a.push(p),a.push(1-u))}}return l}within(e,t,n){if(!this._finished)throw new Error("Data not yet indexed - call index.finish().");for(var{ids:s,coords:o,nodeSize:i}=this,r=[0,s.length-1,0],a=[],l=n*n;r.length;){var u=r.pop()||0,p=r.pop()||0,h=r.pop()||0;if(p-h<=i)for(var c=h;c<=p;c++)Re(o[2*c],o[2*c+1],e,t)<=l&&a.push(s[c]);else{var d=h+p>>1,g=o[2*d],m=o[2*d+1];Re(g,m,e,t)<=l&&a.push(s[d]),(0===u?e-n<=g:t-n<=m)&&(r.push(h),r.push(d-1),r.push(1-u)),(0===u?e+n>=g:t+n>=m)&&(r.push(d+1),r.push(p),r.push(1-u))}}return a}};function Te(e,t,n,s,o,i){if(!(o-s<=n)){var r=s+o>>1;Ae(e,t,r,s,o,i),Te(e,t,n,s,r-1,1-i),Te(e,t,n,r+1,o,1-i)}}function Ae(e,t,n,s,o,i){for(;o>s;){if(o-s>600){var r=o-s+1,a=n-s+1,l=Math.log(r),u=.5*Math.exp(2*l/3),p=.5*Math.sqrt(l*u*(r-u)/r)*(a-r/2<0?-1:1);Ae(e,t,n,Math.max(s,Math.floor(n-a*u/r+p)),Math.min(o,Math.floor(n+(r-a)*u/r+p)),i)}var h=t[2*n+i],c=s,d=o;for(Be(e,t,s,n),t[2*o+i]>h&&Be(e,t,s,o);c<d;){for(Be(e,t,c,d),c++,d--;t[2*c+i]<h;)c++;for(;t[2*d+i]>h;)d--}t[2*s+i]===h?Be(e,t,s,d):Be(e,t,++d,o),d<=n&&(s=d+1),n<=d&&(o=d-1)}}function Be(e,t,n,s){Ue(e,n,s),Ue(t,2*n,2*s),Ue(t,2*n+1,2*s+1)}function Ue(e,t,n){var s=e[t];e[t]=e[n],e[n]=s}function Re(e,t,n,s){var o=e-n,i=t-s;return o*o+i*i}var ze={minZoom:0,maxZoom:16,minPoints:2,radius:40,extent:512,nodeSize:64,log:!1,generateId:!1,reduce:null,map:e=>e},Ze=Math.fround||(e=>t=>(e[0]=+t,e[0]))(new Float32Array(1));let Ve=class{constructor(e){this.options=Object.assign(Object.create(ze),e),this.trees=new Array(this.options.maxZoom+1),this.stride=this.options.reduce?7:6,this.clusterProps=[]}load(e){var{log:t,minZoom:n,maxZoom:s}=this.options;"prepare ".concat(e.length," points");this.points=e;for(var o=[],i=0;i<e.length;i++){var r=e[i];if(r.geometry){var[a,l]=r.geometry.coordinates,u=Ze(He(a)),p=Ze(Fe(l));o.push(u,p,1/0,i,-1,1),this.options.reduce&&o.push(0)}}for(var h=this.trees[s+1]=this._createTree(o),c=s;c>=n;c--){Date.now();h=this.trees[c]=this._createTree(this._cluster(h,c))}return this}getClusters(e,t){var n=((e[0]+180)%360+360)%360-180,s=Math.max(-90,Math.min(90,e[1])),o=180===e[2]?180:((e[2]+180)%360+360)%360-180,i=Math.max(-90,Math.min(90,e[3]));if(e[2]-e[0]>=360)n=-180,o=180;else if(n>o){var r=this.getClusters([n,s,180,i],t),a=this.getClusters([-180,s,o,i],t);return r.concat(a)}var l=this.trees[this._limitZoom(t)],u=l.range(He(n),Fe(i),He(o),Fe(s)),p=l.data,h=[];for(var c of u){var d=this.stride*c;h.push(p[d+5]>1?Ne(p,d,this.clusterProps):this.points[p[d+3]])}return h}getChildren(e){var t=this._getOriginId(e),n=this._getOriginZoom(e),s="No cluster with the specified id.",o=this.trees[n];if(!o)throw new Error(s);var i=o.data;if(t*this.stride>=i.length)throw new Error(s);var r=this.options.radius/(this.options.extent*Math.pow(2,n-1)),a=i[t*this.stride],l=i[t*this.stride+1],u=o.within(a,l,r),p=[];for(var h of u){var c=h*this.stride;i[c+4]===e&&p.push(i[c+5]>1?Ne(i,c,this.clusterProps):this.points[i[c+3]])}if(0===p.length)throw new Error(s);return p}getLeaves(e,t,n){t=t||10,n=n||0;var s=[];return this._appendLeaves(s,e,t,n,0),s}getTile(e,t,n){var s=this.trees[this._limitZoom(e)],o=Math.pow(2,e),{extent:i,radius:r}=this.options,a=r/i,l=(n-a)/o,u=(n+1+a)/o,p={features:[]};return this._addTileFeatures(s.range((t-a)/o,l,(t+1+a)/o,u),s.data,t,n,o,p),0===t&&this._addTileFeatures(s.range(1-a/o,l,1,u),s.data,o,n,o,p),t===o-1&&this._addTileFeatures(s.range(0,l,a/o,u),s.data,-1,n,o,p),p.features.length?p:null}getClusterExpansionZoom(e){for(var t=this._getOriginZoom(e)-1;t<=this.options.maxZoom;){var n=this.getChildren(e);if(t++,1!==n.length)break;e=n[0].properties.cluster_id}return t}_appendLeaves(e,t,n,s,o){var i=this.getChildren(t);for(var r of i){var a=r.properties;if(a&&a.cluster?o+a.point_count<=s?o+=a.point_count:o=this._appendLeaves(e,a.cluster_id,n,s,o):o<s?o++:e.push(r),e.length===n)break}return o}_createTree(e){for(var t=new _e(e.length/this.stride|0,this.options.nodeSize,Float32Array),n=0;n<e.length;n+=this.stride)t.add(e[n],e[n+1]);return t.finish(),t.data=e,t}_addTileFeatures(e,t,n,s,o,i){for(var r of e){var a=r*this.stride,l=t[a+5]>1,u=void 0,p=void 0,h=void 0;if(l)u=We(t,a,this.clusterProps),p=t[a],h=t[a+1];else{var c=this.points[t[a+3]];u=c.properties;var[d,g]=c.geometry.coordinates;p=He(d),h=Fe(g)}var m={type:1,geometry:[[Math.round(this.options.extent*(p*o-n)),Math.round(this.options.extent*(h*o-s))]],tags:u},v=void 0;void 0!==(v=l||this.options.generateId?t[a+3]:this.points[t[a+3]].id)&&(m.id=v),i.features.push(m)}}_limitZoom(e){return Math.max(this.options.minZoom,Math.min(Math.floor(+e),this.options.maxZoom+1))}_cluster(e,t){for(var{radius:n,extent:s,reduce:o,minPoints:i}=this.options,r=n/(s*Math.pow(2,t)),a=e.data,l=[],u=this.stride,p=0;p<a.length;p+=u)if(!(a[p+2]<=t)){a[p+2]=t;var h=a[p],c=a[p+1],d=e.within(a[p],a[p+1],r),g=a[p+5],m=g;for(var v of d){var f=v*u;a[f+2]>t&&(m+=a[f+5])}if(m>g&&m>=i){var y=h*g,b=c*g,L=void 0,E=-1,C=(p/u<<5)+(t+1)+this.points.length;for(var M of d){var w=M*u;if(!(a[w+2]<=t)){a[w+2]=t;var x=a[w+5];y+=a[w]*x,b+=a[w+1]*x,a[w+4]=C,o&&(L||(L=this._map(a,p,!0),E=this.clusterProps.length,this.clusterProps.push(L)),o(L,this._map(a,w)))}}a[p+4]=C,l.push(y/m,b/m,1/0,C,-1,m),o&&l.push(E)}else{for(var k=0;k<u;k++)l.push(a[p+k]);if(m>1)for(var P of d){var O=P*u;if(!(a[O+2]<=t)){a[O+2]=t;for(var S=0;S<u;S++)l.push(a[O+S])}}}}return l}_getOriginId(e){return e-this.points.length>>5}_getOriginZoom(e){return(e-this.points.length)%32}_map(e,t,n){if(e[t+5]>1){var s=this.clusterProps[e[t+6]];return n?Object.assign({},s):s}var o=this.points[e[t+3]].properties,i=this.options.map(o);return n&&i===o?Object.assign({},i):i}};function Ne(e,t,n){return{type:"Feature",id:e[t+3],properties:We(e,t,n),geometry:{type:"Point",coordinates:[(i=e[t],360*(i-.5)),(s=e[t+1],o=(180-360*s)*Math.PI/180,360*Math.atan(Math.exp(o))/Math.PI-90)]}};var s,o,i;
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */}function We(e,t,n){var s=e[t+5],o=s>=1e4?"".concat(Math.round(s/1e3),"k"):s>=1e3?"".concat(Math.round(s/100)/10,"k"):s,i=e[t+6],r=-1===i?{}:Object.assign({},n[i]);return Object.assign(r,{cluster:!0,cluster_id:e[t+3],point_count:s,point_count_abbreviated:o})}function He(e){return e/360+.5}function Fe(e){var t=Math.sin(e*Math.PI/180),n=.5-.25*Math.log((1+t)/(1-t))/Math.PI;return n<0?0:n>1?1:n}let Ge=class{static isAdvancedMarkerAvailable(e){return google.maps.marker&&!0===e.getMapCapabilities().isAdvancedMarkersAvailable}static isAdvancedMarker(e){return google.maps.marker&&e instanceof google.maps.marker.AdvancedMarkerElement}static setMap(e,t){this.isAdvancedMarker(e)?e.map=t:e.setMap(t)}static getPosition(e){if(this.isAdvancedMarker(e)){if(e.position){if(e.position instanceof google.maps.LatLng)return e.position;if(e.position.lat&&e.position.lng)return new google.maps.LatLng(e.position.lat,e.position.lng)}return new google.maps.LatLng(null)}return e.getPosition()}static getVisible(e){return!!this.isAdvancedMarker(e)||e.getVisible()}},Ke=class{constructor(e){var{markers:t,position:n}=e;this.markers=t,n&&(n instanceof google.maps.LatLng?this._position=n:this._position=new google.maps.LatLng(n))}get bounds(){if(0!==this.markers.length||this._position){var e=new google.maps.LatLngBounds(this._position,this._position);for(var t of this.markers)e.extend(Ge.getPosition(t));return e}}get position(){return this._position||this.bounds.getCenter()}get count(){return this.markers.filter((e=>Ge.getVisible(e))).length}push(e){this.markers.push(e)}delete(){this.marker&&(Ge.setMap(this.marker,null),this.marker=void 0),this.markers.length=0}},Ye=class{constructor(e){var{maxZoom:t=16}=e;this.maxZoom=t}noop(e){var{markers:t}=e;return qe(t)}};var qe=e=>e.map((e=>new Ke({position:Ge.getPosition(e),markers:[e]})));let $e=class extends Ye{constructor(e){var{maxZoom:t,radius:n=60}=e,s=function(e,t){var n={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(n[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(s=Object.getOwnPropertySymbols(e);o<s.length;o++)t.indexOf(s[o])<0&&Object.prototype.propertyIsEnumerable.call(e,s[o])&&(n[s[o]]=e[s[o]])}return n}(e,["maxZoom","radius"]);super({maxZoom:t}),this.state={zoom:-1},this.superCluster=new Ve(Object.assign({maxZoom:this.maxZoom,radius:n},s))}calculate(e){var t=!1,n={zoom:e.map.getZoom()};if(!De(e.markers,this.markers)){t=!0,this.markers=[...e.markers];var s=this.markers.map((e=>{var t=Ge.getPosition(e);return{type:"Feature",geometry:{type:"Point",coordinates:[t.lng(),t.lat()]},properties:{marker:e}}}));this.superCluster.load(s)}return t||(this.state.zoom<=this.maxZoom||n.zoom<=this.maxZoom)&&(t=!De(this.state,n)),this.state=n,t&&(this.clusters=this.cluster(e)),{clusters:this.clusters,changed:t}}cluster(e){var{map:t}=e;return this.superCluster.getClusters([-180,-90,180,90],Math.round(t.getZoom())).map((e=>this.transformCluster(e)))}transformCluster(e){var{geometry:{coordinates:[t,n]},properties:s}=e;if(s.cluster)return new Ke({markers:this.superCluster.getLeaves(s.cluster_id,1/0).map((e=>e.properties.marker)),position:{lat:n,lng:t}});var o=s.marker;return new Ke({markers:[o],position:Ge.getPosition(o)})}},Je=class{constructor(e,t){this.markers={sum:e.length};var n=t.map((e=>e.count)),s=n.reduce(((e,t)=>e+t),0);this.clusters={count:t.length,markers:{mean:s/t.length,sum:s,min:Math.min(...n),max:Math.max(...n)}}}},Xe=class{render(e,t,n){var{count:s,position:o}=e,i=s>Math.max(10,t.clusters.markers.mean)?"#ff0000":"#0000ff",r='<svg fill="'.concat(i,'" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 240" width="50" height="50">\n<circle cx="120" cy="120" opacity=".6" r="70" />\n<circle cx="120" cy="120" opacity=".3" r="90" />\n<circle cx="120" cy="120" opacity=".2" r="110" />\n<text x="50%" y="50%" style="fill:#fff" text-anchor="middle" font-size="50" dominant-baseline="middle" font-family="roboto,arial,sans-serif">').concat(s,"</text>\n</svg>"),a="Cluster of ".concat(s," markers"),l=Number(google.maps.Marker.MAX_ZINDEX)+s;if(Ge.isAdvancedMarkerAvailable(n)){var u=(new DOMParser).parseFromString(r,"image/svg+xml").documentElement;u.setAttribute("transform","translate(0 25)");var p={map:n,position:o,zIndex:l,title:a,content:u};return new google.maps.marker.AdvancedMarkerElement(p)}var h={position:o,zIndex:l,title:a,icon:{url:"data:image/svg+xml;base64,".concat(btoa(r)),anchor:new google.maps.Point(25,25)}};return new google.maps.Marker(h)}};let Qe=class e{constructor(){!function(e,t){for(var n in t.prototype)e.prototype[n]=t.prototype[n]}(e,google.maps.OverlayView)}};var et,tt;(tt=et||(et={})).CLUSTERING_BEGIN="clusteringbegin",tt.CLUSTERING_END="clusteringend",tt.CLUSTER_CLICK="click";var nt=(e,t,n)=>{n.fitBounds(t.bounds)};let st=class extends Qe{constructor(e){var{map:t,markers:n=[],algorithmOptions:s={},algorithm:o=new $e(s),renderer:i=new Xe,onClusterClick:r=nt}=e;super(),this.markers=[...n],this.clusters=[],this.algorithm=o,this.renderer=i,this.onClusterClick=r,t&&this.setMap(t)}addMarker(e,t){this.markers.includes(e)||(this.markers.push(e),t||this.render())}addMarkers(e,t){e.forEach((e=>{this.addMarker(e,!0)})),t||this.render()}removeMarker(e,t){var n=this.markers.indexOf(e);return-1!==n&&(Ge.setMap(e,null),this.markers.splice(n,1),t||this.render(),!0)}removeMarkers(e,t){var n=!1;return e.forEach((e=>{n=this.removeMarker(e,!0)||n})),n&&!t&&this.render(),n}clearMarkers(e){this.markers.length=0,e||this.render()}render(){var e=this.getMap();if(e instanceof google.maps.Map&&e.getProjection()){google.maps.event.trigger(this,et.CLUSTERING_BEGIN,this);var{clusters:t,changed:n}=this.algorithm.calculate({markers:this.markers,map:e,mapCanvasProjection:this.getProjection()});if(n||null==n){var s=new Set;for(var o of t)1==o.markers.length&&s.add(o.markers[0]);var i=[];for(var r of this.clusters)null!=r.marker&&(1==r.markers.length?s.has(r.marker)||Ge.setMap(r.marker,null):i.push(r.marker));this.clusters=t,this.renderClusters(),requestAnimationFrame((()=>i.forEach((e=>Ge.setMap(e,null)))))}google.maps.event.trigger(this,et.CLUSTERING_END,this)}}onAdd(){this.idleListener=this.getMap().addListener("idle",this.render.bind(this)),this.render()}onRemove(){google.maps.event.removeListener(this.idleListener),this.reset()}reset(){this.markers.forEach((e=>Ge.setMap(e,null))),this.clusters.forEach((e=>e.delete())),this.clusters=[]}renderClusters(){var e=new Je(this.markers,this.clusters),t=this.getMap();this.clusters.forEach((n=>{1===n.markers.length?n.marker=n.markers[0]:(n.marker=this.renderer.render(n,e,t),n.markers.forEach((e=>Ge.setMap(e,null))),this.onClusterClick&&n.marker.addListener("click",(e=>{google.maps.event.trigger(this,et.CLUSTER_CLICK,n),this.onClusterClick(e,n,t)}))),Ge.setMap(n.marker,t)}))}};function ot(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function it(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ot(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ot(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function rt(t){var n=function(){p(!!e.useContext,"useGoogleMap is React hook and requires React version 16.8+");var t=e.useContext(h);return p(!!t,"useGoogleMap needs a GoogleMap available up in the tree"),t}(),[s,o]=e.useState(null);return e.useEffect((()=>{if(n&&null===s){var e=new st(it(it({},t),{},{map:n}));o(e)}}),[n]),s}e.memo((function(e){var{children:t,options:n}=e,s=rt(n);return null!==s?t(s):null}));var at={onCloseClick:"closeclick",onContentChanged:"content_changed",onDomReady:"domready",onPositionChanged:"position_changed",onZindexChanged:"zindex_changed"},lt={options(e,t){e.setOptions(t)},position(e,t){e.setPosition(t)},zIndex(e,t){e.setZIndex(t)}};e.memo((function(t){var{children:s,anchor:o,options:i,position:r,zIndex:a,onCloseClick:l,onDomReady:u,onContentChanged:c,onPositionChanged:d,onZindexChanged:g,onLoad:m,onUnmount:v}=t,f=e.useContext(h),[y,b]=e.useState(null),[L,E]=e.useState(null),[C,M]=e.useState(null),[w,x]=e.useState(null),[k,P]=e.useState(null),[O,S]=e.useState(null),I=e.useRef(null);return e.useEffect((()=>{null!==y&&(y.close(),o?y.open(f,o):y.getPosition()&&y.open(f))}),[f,y,o]),e.useEffect((()=>{i&&null!==y&&y.setOptions(i)}),[y,i]),e.useEffect((()=>{r&&null!==y&&y.setPosition(r)}),[r]),e.useEffect((()=>{"number"==typeof a&&null!==y&&y.setZIndex(a)}),[a]),e.useEffect((()=>{y&&l&&(null!==L&&google.maps.event.removeListener(L),E(google.maps.event.addListener(y,"closeclick",l)))}),[l]),e.useEffect((()=>{y&&u&&(null!==C&&google.maps.event.removeListener(C),M(google.maps.event.addListener(y,"domready",u)))}),[u]),e.useEffect((()=>{y&&c&&(null!==w&&google.maps.event.removeListener(w),x(google.maps.event.addListener(y,"content_changed",c)))}),[c]),e.useEffect((()=>{y&&d&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(y,"position_changed",d)))}),[d]),e.useEffect((()=>{y&&g&&(null!==O&&google.maps.event.removeListener(O),S(google.maps.event.addListener(y,"zindex_changed",g)))}),[g]),e.useEffect((()=>{var e=new google.maps.InfoWindow(i);return b(e),I.current=document.createElement("div"),l&&E(google.maps.event.addListener(e,"closeclick",l)),u&&M(google.maps.event.addListener(e,"domready",u)),c&&x(google.maps.event.addListener(e,"content_changed",c)),d&&P(google.maps.event.addListener(e,"position_changed",d)),g&&S(google.maps.event.addListener(e,"zindex_changed",g)),e.setContent(I.current),r&&e.setPosition(r),a&&e.setZIndex(a),o?e.open(f,o):e.getPosition()?e.open(f):p(!1,"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>."),m&&m(e),()=>{L&&google.maps.event.removeListener(L),w&&google.maps.event.removeListener(w),C&&google.maps.event.removeListener(C),k&&google.maps.event.removeListener(k),O&&google.maps.event.removeListener(O),v&&v(e),e.close()}}),[]),I.current?n.createPortal(e.Children.only(s),I.current):null}));class ut extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"containerElement",null),r(this,"state",{infoWindow:null}),r(this,"open",((e,t)=>{t?e.open(this.context,t):e.getPosition()?e.open(this.context):p(!1,"You must provide either an anchor (typically render it inside a <Marker>) or a position props for <InfoWindow>.")})),r(this,"setInfoWindowCallback",(()=>{null!==this.state.infoWindow&&null!==this.containerElement&&(this.state.infoWindow.setContent(this.containerElement),this.open(this.state.infoWindow,this.props.anchor),this.props.onLoad&&this.props.onLoad(this.state.infoWindow))}))}componentDidMount(){var e=new google.maps.InfoWindow(this.props.options);this.containerElement=document.createElement("div"),this.registeredEvents=v({updaterMap:lt,eventMap:at,prevProps:{},nextProps:this.props,instance:e}),this.setState((()=>({infoWindow:e})),this.setInfoWindowCallback)}componentDidUpdate(e){null!==this.state.infoWindow&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:lt,eventMap:at,prevProps:e,nextProps:this.props,instance:this.state.infoWindow}))}componentWillUnmount(){null!==this.state.infoWindow&&(m(this.registeredEvents),this.props.onUnmount&&this.props.onUnmount(this.state.infoWindow),this.state.infoWindow.close())}render(){return this.containerElement?n.createPortal(e.Children.only(this.props.children),this.containerElement):null}}function pt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function ht(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?pt(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}r(ut,"contextType",h);var ct={onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},dt={draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},path(e,t){e.setPath(t)},visible(e,t){e.setVisible(t)}},gt={};e.memo((function(t){var{options:n,draggable:s,editable:o,visible:i,path:r,onDblClick:a,onDragEnd:l,onDragStart:u,onMouseDown:p,onMouseMove:c,onMouseOut:d,onMouseOver:g,onMouseUp:m,onRightClick:v,onClick:f,onDrag:y,onLoad:b,onUnmount:L}=t,E=e.useContext(h),[C,M]=e.useState(null),[w,x]=e.useState(null),[k,P]=e.useState(null),[O,S]=e.useState(null),[I,D]=e.useState(null),[j,_]=e.useState(null),[T,A]=e.useState(null),[B,U]=e.useState(null),[R,z]=e.useState(null),[Z,V]=e.useState(null),[N,W]=e.useState(null),[H,F]=e.useState(null);return e.useEffect((()=>{null!==C&&C.setMap(E)}),[E]),e.useEffect((()=>{void 0!==n&&null!==C&&C.setOptions(n)}),[C,n]),e.useEffect((()=>{void 0!==s&&null!==C&&C.setDraggable(s)}),[C,s]),e.useEffect((()=>{void 0!==o&&null!==C&&C.setEditable(o)}),[C,o]),e.useEffect((()=>{void 0!==i&&null!==C&&C.setVisible(i)}),[C,i]),e.useEffect((()=>{void 0!==r&&null!==C&&C.setPath(r)}),[C,r]),e.useEffect((()=>{C&&a&&(null!==w&&google.maps.event.removeListener(w),x(google.maps.event.addListener(C,"dblclick",a)))}),[a]),e.useEffect((()=>{C&&l&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(C,"dragend",l)))}),[l]),e.useEffect((()=>{C&&u&&(null!==O&&google.maps.event.removeListener(O),S(google.maps.event.addListener(C,"dragstart",u)))}),[u]),e.useEffect((()=>{C&&p&&(null!==I&&google.maps.event.removeListener(I),D(google.maps.event.addListener(C,"mousedown",p)))}),[p]),e.useEffect((()=>{C&&c&&(null!==j&&google.maps.event.removeListener(j),_(google.maps.event.addListener(C,"mousemove",c)))}),[c]),e.useEffect((()=>{C&&d&&(null!==T&&google.maps.event.removeListener(T),A(google.maps.event.addListener(C,"mouseout",d)))}),[d]),e.useEffect((()=>{C&&g&&(null!==B&&google.maps.event.removeListener(B),U(google.maps.event.addListener(C,"mouseover",g)))}),[g]),e.useEffect((()=>{C&&m&&(null!==R&&google.maps.event.removeListener(R),z(google.maps.event.addListener(C,"mouseup",m)))}),[m]),e.useEffect((()=>{C&&v&&(null!==Z&&google.maps.event.removeListener(Z),V(google.maps.event.addListener(C,"rightclick",v)))}),[v]),e.useEffect((()=>{C&&f&&(null!==N&&google.maps.event.removeListener(N),W(google.maps.event.addListener(C,"click",f)))}),[f]),e.useEffect((()=>{C&&y&&(null!==H&&google.maps.event.removeListener(H),F(google.maps.event.addListener(C,"drag",y)))}),[y]),e.useEffect((()=>{var e=new google.maps.Polyline(ht(ht({},n||gt),{},{map:E}));return r&&e.setPath(r),void 0!==i&&e.setVisible(i),void 0!==o&&e.setEditable(o),void 0!==s&&e.setDraggable(s),a&&x(google.maps.event.addListener(e,"dblclick",a)),l&&P(google.maps.event.addListener(e,"dragend",l)),u&&S(google.maps.event.addListener(e,"dragstart",u)),p&&D(google.maps.event.addListener(e,"mousedown",p)),c&&_(google.maps.event.addListener(e,"mousemove",c)),d&&A(google.maps.event.addListener(e,"mouseout",d)),g&&U(google.maps.event.addListener(e,"mouseover",g)),m&&z(google.maps.event.addListener(e,"mouseup",m)),v&&V(google.maps.event.addListener(e,"rightclick",v)),f&&W(google.maps.event.addListener(e,"click",f)),y&&F(google.maps.event.addListener(e,"drag",y)),M(e),b&&b(e),()=>{null!==w&&google.maps.event.removeListener(w),null!==k&&google.maps.event.removeListener(k),null!==O&&google.maps.event.removeListener(O),null!==I&&google.maps.event.removeListener(I),null!==j&&google.maps.event.removeListener(j),null!==T&&google.maps.event.removeListener(T),null!==B&&google.maps.event.removeListener(B),null!==R&&google.maps.event.removeListener(R),null!==Z&&google.maps.event.removeListener(Z),null!==N&&google.maps.event.removeListener(N),L&&L(e),e.setMap(null)}}),[]),null}));class mt extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"state",{polyline:null}),r(this,"setPolylineCallback",(()=>{null!==this.state.polyline&&this.props.onLoad&&this.props.onLoad(this.state.polyline)}))}componentDidMount(){var e=new google.maps.Polyline(ht(ht({},this.props.options),{},{map:this.context}));this.registeredEvents=v({updaterMap:dt,eventMap:ct,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{polyline:e}}),this.setPolylineCallback)}componentDidUpdate(e){null!==this.state.polyline&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:dt,eventMap:ct,prevProps:e,nextProps:this.props,instance:this.state.polyline}))}componentWillUnmount(){null!==this.state.polyline&&(this.props.onUnmount&&this.props.onUnmount(this.state.polyline),m(this.registeredEvents),this.state.polyline.setMap(null))}render(){return null}}function vt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function ft(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?vt(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):vt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}r(mt,"contextType",h);var yt={onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},bt={draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},path(e,t){e.setPath(t)},paths(e,t){e.setPaths(t)},visible(e,t){e.setVisible(t)}};e.memo((function(t){var{options:n,draggable:s,editable:o,visible:i,path:r,paths:a,onDblClick:l,onDragEnd:u,onDragStart:p,onMouseDown:c,onMouseMove:d,onMouseOut:g,onMouseOver:m,onMouseUp:v,onRightClick:f,onClick:y,onDrag:b,onLoad:L,onUnmount:E,onEdit:C}=t,M=e.useContext(h),[w,x]=e.useState(null),[k,P]=e.useState(null),[O,S]=e.useState(null),[I,D]=e.useState(null),[j,_]=e.useState(null),[T,A]=e.useState(null),[B,U]=e.useState(null),[R,z]=e.useState(null),[Z,V]=e.useState(null),[N,W]=e.useState(null),[H,F]=e.useState(null),[G,K]=e.useState(null);return e.useEffect((()=>{null!==w&&w.setMap(M)}),[M]),e.useEffect((()=>{void 0!==n&&null!==w&&w.setOptions(n)}),[w,n]),e.useEffect((()=>{void 0!==s&&null!==w&&w.setDraggable(s)}),[w,s]),e.useEffect((()=>{void 0!==o&&null!==w&&w.setEditable(o)}),[w,o]),e.useEffect((()=>{void 0!==i&&null!==w&&w.setVisible(i)}),[w,i]),e.useEffect((()=>{void 0!==r&&null!==w&&w.setPath(r)}),[w,r]),e.useEffect((()=>{void 0!==a&&null!==w&&w.setPaths(a)}),[w,a]),e.useEffect((()=>{w&&"function"==typeof l&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(w,"dblclick",l)))}),[l]),e.useEffect((()=>{w&&(google.maps.event.addListener(w.getPath(),"insert_at",(()=>{null==C||C(w)})),google.maps.event.addListener(w.getPath(),"set_at",(()=>{null==C||C(w)})),google.maps.event.addListener(w.getPath(),"remove_at",(()=>{null==C||C(w)})))}),[w,C]),e.useEffect((()=>{w&&"function"==typeof u&&(null!==O&&google.maps.event.removeListener(O),S(google.maps.event.addListener(w,"dragend",u)))}),[u]),e.useEffect((()=>{w&&"function"==typeof p&&(null!==I&&google.maps.event.removeListener(I),D(google.maps.event.addListener(w,"dragstart",p)))}),[p]),e.useEffect((()=>{w&&"function"==typeof c&&(null!==j&&google.maps.event.removeListener(j),_(google.maps.event.addListener(w,"mousedown",c)))}),[c]),e.useEffect((()=>{w&&"function"==typeof d&&(null!==T&&google.maps.event.removeListener(T),A(google.maps.event.addListener(w,"mousemove",d)))}),[d]),e.useEffect((()=>{w&&"function"==typeof g&&(null!==B&&google.maps.event.removeListener(B),U(google.maps.event.addListener(w,"mouseout",g)))}),[g]),e.useEffect((()=>{w&&"function"==typeof m&&(null!==R&&google.maps.event.removeListener(R),z(google.maps.event.addListener(w,"mouseover",m)))}),[m]),e.useEffect((()=>{w&&"function"==typeof v&&(null!==Z&&google.maps.event.removeListener(Z),V(google.maps.event.addListener(w,"mouseup",v)))}),[v]),e.useEffect((()=>{w&&"function"==typeof f&&(null!==N&&google.maps.event.removeListener(N),W(google.maps.event.addListener(w,"rightclick",f)))}),[f]),e.useEffect((()=>{w&&"function"==typeof y&&(null!==H&&google.maps.event.removeListener(H),F(google.maps.event.addListener(w,"click",y)))}),[y]),e.useEffect((()=>{w&&"function"==typeof b&&(null!==G&&google.maps.event.removeListener(G),K(google.maps.event.addListener(w,"drag",b)))}),[b]),e.useEffect((()=>{var e=new google.maps.Polygon(ft(ft({},n),{},{map:M}));return r&&e.setPath(r),a&&e.setPaths(a),void 0!==i&&e.setVisible(i),void 0!==o&&e.setEditable(o),void 0!==s&&e.setDraggable(s),l&&P(google.maps.event.addListener(e,"dblclick",l)),u&&S(google.maps.event.addListener(e,"dragend",u)),p&&D(google.maps.event.addListener(e,"dragstart",p)),c&&_(google.maps.event.addListener(e,"mousedown",c)),d&&A(google.maps.event.addListener(e,"mousemove",d)),g&&U(google.maps.event.addListener(e,"mouseout",g)),m&&z(google.maps.event.addListener(e,"mouseover",m)),v&&V(google.maps.event.addListener(e,"mouseup",v)),f&&W(google.maps.event.addListener(e,"rightclick",f)),y&&F(google.maps.event.addListener(e,"click",y)),b&&K(google.maps.event.addListener(e,"drag",b)),x(e),L&&L(e),()=>{null!==k&&google.maps.event.removeListener(k),null!==O&&google.maps.event.removeListener(O),null!==I&&google.maps.event.removeListener(I),null!==j&&google.maps.event.removeListener(j),null!==T&&google.maps.event.removeListener(T),null!==B&&google.maps.event.removeListener(B),null!==R&&google.maps.event.removeListener(R),null!==Z&&google.maps.event.removeListener(Z),null!==N&&google.maps.event.removeListener(N),null!==H&&google.maps.event.removeListener(H),E&&E(e),e.setMap(null)}}),[]),null}));class Lt extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[])}componentDidMount(){var e=this.props.options||{};this.polygon=new google.maps.Polygon(e),this.polygon.setMap(this.context),this.registeredEvents=v({updaterMap:bt,eventMap:yt,prevProps:{},nextProps:this.props,instance:this.polygon}),this.props.onLoad&&this.props.onLoad(this.polygon)}componentDidUpdate(e){this.polygon&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:bt,eventMap:yt,prevProps:e,nextProps:this.props,instance:this.polygon}))}componentWillUnmount(){this.polygon&&(this.props.onUnmount&&this.props.onUnmount(this.polygon),m(this.registeredEvents),this.polygon&&this.polygon.setMap(null))}render(){return null}}function Et(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Ct(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Et(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Et(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}r(Lt,"contextType",h);var Mt={onBoundsChanged:"bounds_changed",onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},wt={bounds(e,t){e.setBounds(t)},draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},visible(e,t){e.setVisible(t)}};e.memo((function(t){var{options:n,bounds:s,draggable:o,editable:i,visible:r,onDblClick:a,onDragEnd:l,onDragStart:u,onMouseDown:p,onMouseMove:c,onMouseOut:d,onMouseOver:g,onMouseUp:m,onRightClick:v,onClick:f,onDrag:y,onBoundsChanged:b,onLoad:L,onUnmount:E}=t,C=e.useContext(h),[M,w]=e.useState(null),[x,k]=e.useState(null),[P,O]=e.useState(null),[S,I]=e.useState(null),[D,j]=e.useState(null),[_,T]=e.useState(null),[A,B]=e.useState(null),[U,R]=e.useState(null),[z,Z]=e.useState(null),[V,N]=e.useState(null),[W,H]=e.useState(null),[F,G]=e.useState(null),[K,Y]=e.useState(null);return e.useEffect((()=>{null!==M&&M.setMap(C)}),[C]),e.useEffect((()=>{void 0!==n&&null!==M&&M.setOptions(n)}),[M,n]),e.useEffect((()=>{void 0!==o&&null!==M&&M.setDraggable(o)}),[M,o]),e.useEffect((()=>{void 0!==i&&null!==M&&M.setEditable(i)}),[M,i]),e.useEffect((()=>{void 0!==r&&null!==M&&M.setVisible(r)}),[M,r]),e.useEffect((()=>{void 0!==s&&null!==M&&M.setBounds(s)}),[M,s]),e.useEffect((()=>{M&&a&&(null!==x&&google.maps.event.removeListener(x),k(google.maps.event.addListener(M,"dblclick",a)))}),[a]),e.useEffect((()=>{M&&l&&(null!==P&&google.maps.event.removeListener(P),O(google.maps.event.addListener(M,"dragend",l)))}),[l]),e.useEffect((()=>{M&&u&&(null!==S&&google.maps.event.removeListener(S),I(google.maps.event.addListener(M,"dragstart",u)))}),[u]),e.useEffect((()=>{M&&p&&(null!==D&&google.maps.event.removeListener(D),j(google.maps.event.addListener(M,"mousedown",p)))}),[p]),e.useEffect((()=>{M&&c&&(null!==_&&google.maps.event.removeListener(_),T(google.maps.event.addListener(M,"mousemove",c)))}),[c]),e.useEffect((()=>{M&&d&&(null!==A&&google.maps.event.removeListener(A),B(google.maps.event.addListener(M,"mouseout",d)))}),[d]),e.useEffect((()=>{M&&g&&(null!==U&&google.maps.event.removeListener(U),R(google.maps.event.addListener(M,"mouseover",g)))}),[g]),e.useEffect((()=>{M&&m&&(null!==z&&google.maps.event.removeListener(z),Z(google.maps.event.addListener(M,"mouseup",m)))}),[m]),e.useEffect((()=>{M&&v&&(null!==V&&google.maps.event.removeListener(V),N(google.maps.event.addListener(M,"rightclick",v)))}),[v]),e.useEffect((()=>{M&&f&&(null!==W&&google.maps.event.removeListener(W),H(google.maps.event.addListener(M,"click",f)))}),[f]),e.useEffect((()=>{M&&y&&(null!==F&&google.maps.event.removeListener(F),G(google.maps.event.addListener(M,"drag",y)))}),[y]),e.useEffect((()=>{M&&b&&(null!==K&&google.maps.event.removeListener(K),Y(google.maps.event.addListener(M,"bounds_changed",b)))}),[b]),e.useEffect((()=>{var e=new google.maps.Rectangle(Ct(Ct({},n),{},{map:C}));return void 0!==r&&e.setVisible(r),void 0!==i&&e.setEditable(i),void 0!==o&&e.setDraggable(o),void 0!==s&&e.setBounds(s),a&&k(google.maps.event.addListener(e,"dblclick",a)),l&&O(google.maps.event.addListener(e,"dragend",l)),u&&I(google.maps.event.addListener(e,"dragstart",u)),p&&j(google.maps.event.addListener(e,"mousedown",p)),c&&T(google.maps.event.addListener(e,"mousemove",c)),d&&B(google.maps.event.addListener(e,"mouseout",d)),g&&R(google.maps.event.addListener(e,"mouseover",g)),m&&Z(google.maps.event.addListener(e,"mouseup",m)),v&&N(google.maps.event.addListener(e,"rightclick",v)),f&&H(google.maps.event.addListener(e,"click",f)),y&&G(google.maps.event.addListener(e,"drag",y)),b&&Y(google.maps.event.addListener(e,"bounds_changed",b)),w(e),L&&L(e),()=>{null!==x&&google.maps.event.removeListener(x),null!==P&&google.maps.event.removeListener(P),null!==S&&google.maps.event.removeListener(S),null!==D&&google.maps.event.removeListener(D),null!==_&&google.maps.event.removeListener(_),null!==A&&google.maps.event.removeListener(A),null!==U&&google.maps.event.removeListener(U),null!==z&&google.maps.event.removeListener(z),null!==V&&google.maps.event.removeListener(V),null!==W&&google.maps.event.removeListener(W),null!==F&&google.maps.event.removeListener(F),null!==K&&google.maps.event.removeListener(K),E&&E(e),e.setMap(null)}}),[]),null}));class xt extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"state",{rectangle:null}),r(this,"setRectangleCallback",(()=>{null!==this.state.rectangle&&this.props.onLoad&&this.props.onLoad(this.state.rectangle)}))}componentDidMount(){var e=new google.maps.Rectangle(Ct(Ct({},this.props.options),{},{map:this.context}));this.registeredEvents=v({updaterMap:wt,eventMap:Mt,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{rectangle:e}}),this.setRectangleCallback)}componentDidUpdate(e){null!==this.state.rectangle&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:wt,eventMap:Mt,prevProps:e,nextProps:this.props,instance:this.state.rectangle}))}componentWillUnmount(){null!==this.state.rectangle&&(this.props.onUnmount&&this.props.onUnmount(this.state.rectangle),m(this.registeredEvents),this.state.rectangle.setMap(null))}render(){return null}}function kt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Pt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?kt(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):kt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}r(xt,"contextType",h);var Ot={onCenterChanged:"center_changed",onRadiusChanged:"radius_changed",onClick:"click",onDblClick:"dblclick",onDrag:"drag",onDragEnd:"dragend",onDragStart:"dragstart",onMouseDown:"mousedown",onMouseMove:"mousemove",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick"},St={center(e,t){e.setCenter(t)},draggable(e,t){e.setDraggable(t)},editable(e,t){e.setEditable(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},radius(e,t){e.setRadius(t)},visible(e,t){e.setVisible(t)}},It={};e.memo((function(t){var{options:n,center:s,radius:o,draggable:i,editable:r,visible:a,onDblClick:l,onDragEnd:u,onDragStart:p,onMouseDown:c,onMouseMove:d,onMouseOut:g,onMouseOver:m,onMouseUp:v,onRightClick:f,onClick:y,onDrag:b,onCenterChanged:L,onRadiusChanged:E,onLoad:C,onUnmount:M}=t,w=e.useContext(h),[x,k]=e.useState(null),[P,O]=e.useState(null),[S,I]=e.useState(null),[D,j]=e.useState(null),[_,T]=e.useState(null),[A,B]=e.useState(null),[U,R]=e.useState(null),[z,Z]=e.useState(null),[V,N]=e.useState(null),[W,H]=e.useState(null),[F,G]=e.useState(null),[K,Y]=e.useState(null),[q,$]=e.useState(null),[J,X]=e.useState(null);return e.useEffect((()=>{null!==x&&x.setMap(w)}),[w]),e.useEffect((()=>{void 0!==n&&null!==x&&x.setOptions(n)}),[x,n]),e.useEffect((()=>{void 0!==i&&null!==x&&x.setDraggable(i)}),[x,i]),e.useEffect((()=>{void 0!==r&&null!==x&&x.setEditable(r)}),[x,r]),e.useEffect((()=>{void 0!==a&&null!==x&&x.setVisible(a)}),[x,a]),e.useEffect((()=>{"number"==typeof o&&null!==x&&x.setRadius(o)}),[x,o]),e.useEffect((()=>{void 0!==s&&null!==x&&x.setCenter(s)}),[x,s]),e.useEffect((()=>{x&&l&&(null!==P&&google.maps.event.removeListener(P),O(google.maps.event.addListener(x,"dblclick",l)))}),[l]),e.useEffect((()=>{x&&u&&(null!==S&&google.maps.event.removeListener(S),I(google.maps.event.addListener(x,"dragend",u)))}),[u]),e.useEffect((()=>{x&&p&&(null!==D&&google.maps.event.removeListener(D),j(google.maps.event.addListener(x,"dragstart",p)))}),[p]),e.useEffect((()=>{x&&c&&(null!==_&&google.maps.event.removeListener(_),T(google.maps.event.addListener(x,"mousedown",c)))}),[c]),e.useEffect((()=>{x&&d&&(null!==A&&google.maps.event.removeListener(A),B(google.maps.event.addListener(x,"mousemove",d)))}),[d]),e.useEffect((()=>{x&&g&&(null!==U&&google.maps.event.removeListener(U),R(google.maps.event.addListener(x,"mouseout",g)))}),[g]),e.useEffect((()=>{x&&m&&(null!==z&&google.maps.event.removeListener(z),Z(google.maps.event.addListener(x,"mouseover",m)))}),[m]),e.useEffect((()=>{x&&v&&(null!==V&&google.maps.event.removeListener(V),N(google.maps.event.addListener(x,"mouseup",v)))}),[v]),e.useEffect((()=>{x&&f&&(null!==W&&google.maps.event.removeListener(W),H(google.maps.event.addListener(x,"rightclick",f)))}),[f]),e.useEffect((()=>{x&&y&&(null!==F&&google.maps.event.removeListener(F),G(google.maps.event.addListener(x,"click",y)))}),[y]),e.useEffect((()=>{x&&b&&(null!==K&&google.maps.event.removeListener(K),Y(google.maps.event.addListener(x,"drag",b)))}),[b]),e.useEffect((()=>{x&&L&&(null!==q&&google.maps.event.removeListener(q),$(google.maps.event.addListener(x,"center_changed",L)))}),[y]),e.useEffect((()=>{x&&E&&(null!==J&&google.maps.event.removeListener(J),X(google.maps.event.addListener(x,"radius_changed",E)))}),[E]),e.useEffect((()=>{var e=new google.maps.Circle(Pt(Pt({},n||It),{},{map:w}));return"number"==typeof o&&e.setRadius(o),void 0!==s&&e.setCenter(s),"number"==typeof o&&e.setRadius(o),void 0!==a&&e.setVisible(a),void 0!==r&&e.setEditable(r),void 0!==i&&e.setDraggable(i),l&&O(google.maps.event.addListener(e,"dblclick",l)),u&&I(google.maps.event.addListener(e,"dragend",u)),p&&j(google.maps.event.addListener(e,"dragstart",p)),c&&T(google.maps.event.addListener(e,"mousedown",c)),d&&B(google.maps.event.addListener(e,"mousemove",d)),g&&R(google.maps.event.addListener(e,"mouseout",g)),m&&Z(google.maps.event.addListener(e,"mouseover",m)),v&&N(google.maps.event.addListener(e,"mouseup",v)),f&&H(google.maps.event.addListener(e,"rightclick",f)),y&&G(google.maps.event.addListener(e,"click",y)),b&&Y(google.maps.event.addListener(e,"drag",b)),L&&$(google.maps.event.addListener(e,"center_changed",L)),E&&X(google.maps.event.addListener(e,"radius_changed",E)),k(e),C&&C(e),()=>{null!==P&&google.maps.event.removeListener(P),null!==S&&google.maps.event.removeListener(S),null!==D&&google.maps.event.removeListener(D),null!==_&&google.maps.event.removeListener(_),null!==A&&google.maps.event.removeListener(A),null!==U&&google.maps.event.removeListener(U),null!==z&&google.maps.event.removeListener(z),null!==V&&google.maps.event.removeListener(V),null!==W&&google.maps.event.removeListener(W),null!==F&&google.maps.event.removeListener(F),null!==q&&google.maps.event.removeListener(q),null!==J&&google.maps.event.removeListener(J),M&&M(e),e.setMap(null)}}),[]),null}));class Dt extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"state",{circle:null}),r(this,"setCircleCallback",(()=>{null!==this.state.circle&&this.props.onLoad&&this.props.onLoad(this.state.circle)}))}componentDidMount(){var e=new google.maps.Circle(Pt(Pt({},this.props.options),{},{map:this.context}));this.registeredEvents=v({updaterMap:St,eventMap:Ot,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{circle:e}}),this.setCircleCallback)}componentDidUpdate(e){null!==this.state.circle&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:St,eventMap:Ot,prevProps:e,nextProps:this.props,instance:this.state.circle}))}componentWillUnmount(){var e;null!==this.state.circle&&(this.props.onUnmount&&this.props.onUnmount(this.state.circle),m(this.registeredEvents),null===(e=this.state.circle)||void 0===e||e.setMap(null))}render(){return null}}function jt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function _t(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?jt(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):jt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}r(Dt,"contextType",h);var Tt={onClick:"click",onDblClick:"dblclick",onMouseDown:"mousedown",onMouseOut:"mouseout",onMouseOver:"mouseover",onMouseUp:"mouseup",onRightClick:"rightclick",onAddFeature:"addfeature",onRemoveFeature:"removefeature",onRemoveProperty:"removeproperty",onSetGeometry:"setgeometry",onSetProperty:"setproperty"},At={add(e,t){e.add(t)},addgeojson(e,t,n){e.addGeoJson(t,n)},contains(e,t){e.contains(t)},foreach(e,t){e.forEach(t)},loadgeojson(e,t,n,s){e.loadGeoJson(t,n,s)},overridestyle(e,t,n){e.overrideStyle(t,n)},remove(e,t){e.remove(t)},revertstyle(e,t){e.revertStyle(t)},controlposition(e,t){e.setControlPosition(t)},controls(e,t){e.setControls(t)},drawingmode(e,t){e.setDrawingMode(t)},map(e,t){e.setMap(t)},style(e,t){e.setStyle(t)},togeojson(e,t){e.toGeoJson(t)}};e.memo((function(t){var{options:n,onClick:s,onDblClick:o,onMouseDown:i,onMouseMove:r,onMouseOut:a,onMouseOver:l,onMouseUp:u,onRightClick:p,onAddFeature:c,onRemoveFeature:d,onRemoveProperty:g,onSetGeometry:m,onSetProperty:v,onLoad:f,onUnmount:y}=t,b=e.useContext(h),[L,E]=e.useState(null),[C,M]=e.useState(null),[w,x]=e.useState(null),[k,P]=e.useState(null),[O,S]=e.useState(null),[I,D]=e.useState(null),[j,_]=e.useState(null),[T,A]=e.useState(null),[B,U]=e.useState(null),[R,z]=e.useState(null),[Z,V]=e.useState(null),[N,W]=e.useState(null),[H,F]=e.useState(null),[G,K]=e.useState(null);return e.useEffect((()=>{null!==L&&L.setMap(b)}),[b]),e.useEffect((()=>{L&&o&&(null!==C&&google.maps.event.removeListener(C),M(google.maps.event.addListener(L,"dblclick",o)))}),[o]),e.useEffect((()=>{L&&i&&(null!==w&&google.maps.event.removeListener(w),x(google.maps.event.addListener(L,"mousedown",i)))}),[i]),e.useEffect((()=>{L&&r&&(null!==k&&google.maps.event.removeListener(k),P(google.maps.event.addListener(L,"mousemove",r)))}),[r]),e.useEffect((()=>{L&&a&&(null!==O&&google.maps.event.removeListener(O),S(google.maps.event.addListener(L,"mouseout",a)))}),[a]),e.useEffect((()=>{L&&l&&(null!==I&&google.maps.event.removeListener(I),D(google.maps.event.addListener(L,"mouseover",l)))}),[l]),e.useEffect((()=>{L&&u&&(null!==j&&google.maps.event.removeListener(j),_(google.maps.event.addListener(L,"mouseup",u)))}),[u]),e.useEffect((()=>{L&&p&&(null!==T&&google.maps.event.removeListener(T),A(google.maps.event.addListener(L,"rightclick",p)))}),[p]),e.useEffect((()=>{L&&s&&(null!==B&&google.maps.event.removeListener(B),U(google.maps.event.addListener(L,"click",s)))}),[s]),e.useEffect((()=>{L&&c&&(null!==R&&google.maps.event.removeListener(R),z(google.maps.event.addListener(L,"addfeature",c)))}),[c]),e.useEffect((()=>{L&&d&&(null!==Z&&google.maps.event.removeListener(Z),V(google.maps.event.addListener(L,"removefeature",d)))}),[d]),e.useEffect((()=>{L&&g&&(null!==N&&google.maps.event.removeListener(N),W(google.maps.event.addListener(L,"removeproperty",g)))}),[g]),e.useEffect((()=>{L&&m&&(null!==H&&google.maps.event.removeListener(H),F(google.maps.event.addListener(L,"setgeometry",m)))}),[m]),e.useEffect((()=>{L&&v&&(null!==G&&google.maps.event.removeListener(G),K(google.maps.event.addListener(L,"setproperty",v)))}),[v]),e.useEffect((()=>{if(null!==b){var e=new google.maps.Data(_t(_t({},n),{},{map:b}));o&&M(google.maps.event.addListener(e,"dblclick",o)),i&&x(google.maps.event.addListener(e,"mousedown",i)),r&&P(google.maps.event.addListener(e,"mousemove",r)),a&&S(google.maps.event.addListener(e,"mouseout",a)),l&&D(google.maps.event.addListener(e,"mouseover",l)),u&&_(google.maps.event.addListener(e,"mouseup",u)),p&&A(google.maps.event.addListener(e,"rightclick",p)),s&&U(google.maps.event.addListener(e,"click",s)),c&&z(google.maps.event.addListener(e,"addfeature",c)),d&&V(google.maps.event.addListener(e,"removefeature",d)),g&&W(google.maps.event.addListener(e,"removeproperty",g)),m&&F(google.maps.event.addListener(e,"setgeometry",m)),v&&K(google.maps.event.addListener(e,"setproperty",v)),E(e),f&&f(e)}return()=>{L&&(null!==C&&google.maps.event.removeListener(C),null!==w&&google.maps.event.removeListener(w),null!==k&&google.maps.event.removeListener(k),null!==O&&google.maps.event.removeListener(O),null!==I&&google.maps.event.removeListener(I),null!==j&&google.maps.event.removeListener(j),null!==T&&google.maps.event.removeListener(T),null!==B&&google.maps.event.removeListener(B),null!==R&&google.maps.event.removeListener(R),null!==Z&&google.maps.event.removeListener(Z),null!==N&&google.maps.event.removeListener(N),null!==H&&google.maps.event.removeListener(H),null!==G&&google.maps.event.removeListener(G),y&&y(L),L.setMap(null))}}),[]),null}));class Bt extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"state",{data:null}),r(this,"setDataCallback",(()=>{null!==this.state.data&&this.props.onLoad&&this.props.onLoad(this.state.data)}))}componentDidMount(){if(null!==this.context){var e=new google.maps.Data(_t(_t({},this.props.options),{},{map:this.context}));this.registeredEvents=v({updaterMap:At,eventMap:Tt,prevProps:{},nextProps:this.props,instance:e}),this.setState((()=>({data:e})),this.setDataCallback)}}componentDidUpdate(e){null!==this.state.data&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:At,eventMap:Tt,prevProps:e,nextProps:this.props,instance:this.state.data}))}componentWillUnmount(){null!==this.state.data&&(this.props.onUnmount&&this.props.onUnmount(this.state.data),m(this.registeredEvents),this.state.data&&this.state.data.setMap(null))}render(){return null}}function Ut(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Rt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ut(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ut(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}r(Bt,"contextType",h);var zt={onClick:"click",onDefaultViewportChanged:"defaultviewport_changed",onStatusChanged:"status_changed"},Zt={options(e,t){e.setOptions(t)},url(e,t){e.setUrl(t)},zIndex(e,t){e.setZIndex(t)}};class Vt extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"state",{kmlLayer:null}),r(this,"setKmlLayerCallback",(()=>{null!==this.state.kmlLayer&&this.props.onLoad&&this.props.onLoad(this.state.kmlLayer)}))}componentDidMount(){var e=new google.maps.KmlLayer(Rt(Rt({},this.props.options),{},{map:this.context}));this.registeredEvents=v({updaterMap:Zt,eventMap:zt,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{kmlLayer:e}}),this.setKmlLayerCallback)}componentDidUpdate(e){null!==this.state.kmlLayer&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:Zt,eventMap:zt,prevProps:e,nextProps:this.props,instance:this.state.kmlLayer}))}componentWillUnmount(){null!==this.state.kmlLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.kmlLayer),m(this.registeredEvents),this.state.kmlLayer.setMap(null))}render(){return null}}function Nt(e,t){return"function"==typeof t?t(e.offsetWidth,e.offsetHeight):{x:0,y:0}}function Wt(e,t){return new t(e.lat,e.lng)}function Ht(e,t){return new t(new google.maps.LatLng(e.ne.lat,e.ne.lng),new google.maps.LatLng(e.sw.lat,e.sw.lng))}function Ft(e,t,n,s){return void 0!==n?function(e,t,n){var s=e&&e.fromLatLngToDivPixel(n.getNorthEast()),o=e&&e.fromLatLngToDivPixel(n.getSouthWest());return s&&o?{left:"".concat(o.x+t.x,"px"),top:"".concat(s.y+t.y,"px"),width:"".concat(s.x-o.x-t.x,"px"),height:"".concat(o.y-s.y-t.y,"px")}:{left:"-9999px",top:"-9999px"}}(e,t,(o=n,i=google.maps.LatLngBounds,r=Ht,o instanceof i?o:r(o,i))):function(e,t,n){var s=e&&e.fromLatLngToDivPixel(n);if(s){var{x:o,y:i}=s;return{left:"".concat(o+t.x,"px"),top:"".concat(i+t.y,"px")}}return{left:"-9999px",top:"-9999px"}}(e,t,function(e,t,n){return e instanceof t?e:n(e,t)}(s,google.maps.LatLng,Wt));var o,i,r}function Gt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Kt(e,t,n,s,o){class i extends google.maps.OverlayView{constructor(e,t,n,s){super(),this.container=e,this.pane=t,this.position=n,this.bounds=s}onAdd(){var e,t=null===(e=this.getPanes())||void 0===e?void 0:e[this.pane];null==t||t.appendChild(this.container)}draw(){var e=this.getProjection(),t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Gt(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Gt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},this.container?Nt(this.container,o):{x:0,y:0}),n=Ft(e,t,this.bounds,this.position);for(var[s,i]of Object.entries(n))this.container.style[s]=i}onRemove(){null!==this.container.parentNode&&this.container.parentNode.removeChild(this.container)}}return new i(e,t,n,s)}function Yt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function qt(e){return e?(e instanceof google.maps.LatLng?e:new google.maps.LatLng(e.lat,e.lng))+"":""}function $t(e){return e?(e instanceof google.maps.LatLngBounds?e:new google.maps.LatLngBounds(new google.maps.LatLng(e.south,e.east),new google.maps.LatLng(e.north,e.west)))+"":""}r(Vt,"contextType",h),e.memo((function(t){var{position:s,bounds:o,mapPaneName:i,zIndex:r,onLoad:a,onUnmount:l,getPixelPositionOffset:u,children:p}=t,c=e.useContext(h),d=e.useMemo((()=>{var e=document.createElement("div");return e.style.position="absolute",e}),[]),g=e.useMemo((()=>Kt(d,i,s,o,u)),[d,i,s,o]);return e.useEffect((()=>(null==a||a(g),null==g||g.setMap(c),()=>{null==l||l(g),null==g||g.setMap(null)})),[c,g]),e.useEffect((()=>{d.style.zIndex="".concat(r)}),[r,d]),n.createPortal(p,d)}));class Jt extends e.PureComponent{constructor(t){super(t),r(this,"state",{paneEl:null,containerStyle:{position:"absolute"}}),r(this,"updatePane",(()=>{var e=this.props.mapPaneName,t=this.overlayView.getPanes();p(!!e,"OverlayView requires props.mapPaneName but got %s",e),t?this.setState({paneEl:t[e]}):this.setState({paneEl:null})})),r(this,"onAdd",(()=>{var e,t;this.updatePane(),null===(e=(t=this.props).onLoad)||void 0===e||e.call(t,this.overlayView)})),r(this,"onPositionElement",(()=>{var e,t,n,s,o,i,a=this.overlayView.getProjection(),l=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Yt(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Yt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({x:0,y:0},this.containerRef.current?Nt(this.containerRef.current,this.props.getPixelPositionOffset):{}),u=Ft(a,l,this.props.bounds,this.props.position);(o=u,i={left:this.state.containerStyle.left,top:this.state.containerStyle.top,width:this.state.containerStyle.width,height:this.state.containerStyle.height},o.left!==i.left||o.top!==i.top||o.width!==i.height||o.height!==i.height)&&this.setState({containerStyle:{top:null!==(e=u.top)&&void 0!==e?e:0,left:null!==(t=u.left)&&void 0!==t?t:0,width:null!==(n=u.width)&&void 0!==n?n:0,height:null!==(s=u.height)&&void 0!==s?s:0,position:"absolute"}})})),r(this,"draw",(()=>{this.onPositionElement()})),r(this,"onRemove",(()=>{var e,t;this.setState((()=>({paneEl:null}))),null===(e=(t=this.props).onUnmount)||void 0===e||e.call(t,this.overlayView)})),this.containerRef=e.createRef();var n=new google.maps.OverlayView;n.onAdd=this.onAdd,n.draw=this.draw,n.onRemove=this.onRemove,this.overlayView=n}componentDidMount(){this.overlayView.setMap(this.context)}componentDidUpdate(e){var t=qt(e.position),n=qt(this.props.position),s=$t(e.bounds),o=$t(this.props.bounds);t===n&&s===o||this.overlayView.draw(),e.mapPaneName!==this.props.mapPaneName&&this.updatePane()}componentWillUnmount(){this.overlayView.setMap(null)}render(){var s=this.state.paneEl;return s?n.createPortal(t.jsx("div",{ref:this.containerRef,style:this.state.containerStyle,children:e.Children.only(this.props.children)}),s):null}}function Xt(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function Qt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Xt(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Xt(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}r(Jt,"FLOAT_PANE","floatPane"),r(Jt,"MAP_PANE","mapPane"),r(Jt,"MARKER_LAYER","markerLayer"),r(Jt,"OVERLAY_LAYER","overlayLayer"),r(Jt,"OVERLAY_MOUSE_TARGET","overlayMouseTarget"),r(Jt,"contextType",h);var en={onDblClick:"dblclick",onClick:"click"},tn={opacity(e,t){e.setOpacity(t)}};e.memo((function(t){var{url:n,bounds:s,options:o,visible:i}=t,r=e.useContext(h),a=new google.maps.LatLngBounds(new google.maps.LatLng(s.south,s.west),new google.maps.LatLng(s.north,s.east)),l=e.useMemo((()=>new google.maps.GroundOverlay(n,a,o)),[]);return e.useEffect((()=>{null!==l&&l.setMap(r)}),[r]),e.useEffect((()=>{void 0!==n&&null!==l&&(l.set("url",n),l.setMap(r))}),[l,n]),e.useEffect((()=>{void 0!==i&&null!==l&&l.setOpacity(i?1:0)}),[l,i]),e.useEffect((()=>{var e=new google.maps.LatLngBounds(new google.maps.LatLng(s.south,s.west),new google.maps.LatLng(s.north,s.east));void 0!==s&&null!==l&&(l.set("bounds",e),l.setMap(r))}),[l,s]),null}));class nn extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"state",{groundOverlay:null}),r(this,"setGroundOverlayCallback",(()=>{null!==this.state.groundOverlay&&this.props.onLoad&&this.props.onLoad(this.state.groundOverlay)}))}componentDidMount(){p(!!this.props.url||!!this.props.bounds,"For GroundOverlay, url and bounds are passed in to constructor and are immutable after instantiated. This is the behavior of Google Maps JavaScript API v3 ( See https://developers.google.com/maps/documentation/javascript/reference#GroundOverlay) Hence, use the corresponding two props provided by `react-google-maps-api`, url and bounds. In some cases, you'll need the GroundOverlay component to reflect the changes of url and bounds. You can leverage the React's key property to remount the component. Typically, just `key={url}` would serve your need. See https://github.com/tomchentw/react-google-maps/issues/655");var e=new google.maps.GroundOverlay(this.props.url,this.props.bounds,Qt(Qt({},this.props.options),{},{map:this.context}));this.registeredEvents=v({updaterMap:tn,eventMap:en,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{groundOverlay:e}}),this.setGroundOverlayCallback)}componentDidUpdate(e){null!==this.state.groundOverlay&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:tn,eventMap:en,prevProps:e,nextProps:this.props,instance:this.state.groundOverlay}))}componentWillUnmount(){this.state.groundOverlay&&(this.props.onUnmount&&this.props.onUnmount(this.state.groundOverlay),this.state.groundOverlay.setMap(null))}render(){return null}}function sn(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,s)}return n}function on(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?sn(Object(n),!0).forEach((function(t){r(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):sn(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}r(nn,"defaultProps",{onLoad:function(){}}),r(nn,"contextType",h);var rn={},an={data(e,t){e.setData(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)}};e.memo((function(t){var{data:n,onLoad:s,onUnmount:o,options:i}=t,r=e.useContext(h),[a,l]=e.useState(null);return e.useEffect((()=>{google.maps.visualization||p(!!google.maps.visualization,'Did you include prop libraries={["visualization"]} in useJsApiScript? %s',google.maps.visualization)}),[]),e.useEffect((()=>{p(!!n,"data property is required in HeatmapLayer %s",n)}),[n]),e.useEffect((()=>{null!==a&&a.setMap(r)}),[r]),e.useEffect((()=>{i&&null!==a&&a.setOptions(i)}),[a,i]),e.useEffect((()=>{var e=new google.maps.visualization.HeatmapLayer(on(on({},i),{},{data:n,map:r}));return l(e),s&&s(e),()=>{null!==a&&(o&&o(a),a.setMap(null))}}),[]),null}));class ln extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"state",{heatmapLayer:null}),r(this,"setHeatmapLayerCallback",(()=>{null!==this.state.heatmapLayer&&this.props.onLoad&&this.props.onLoad(this.state.heatmapLayer)}))}componentDidMount(){p(!!google.maps.visualization,'Did you include prop libraries={["visualization"]} to <LoadScript />? %s',google.maps.visualization),p(!!this.props.data,"data property is required in HeatmapLayer %s",this.props.data);var e=new google.maps.visualization.HeatmapLayer(on(on({},this.props.options),{},{data:this.props.data,map:this.context}));this.registeredEvents=v({updaterMap:an,eventMap:rn,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{heatmapLayer:e}}),this.setHeatmapLayerCallback)}componentDidUpdate(e){m(this.registeredEvents),this.registeredEvents=v({updaterMap:an,eventMap:rn,prevProps:e,nextProps:this.props,instance:this.state.heatmapLayer})}componentWillUnmount(){null!==this.state.heatmapLayer&&(this.props.onUnmount&&this.props.onUnmount(this.state.heatmapLayer),m(this.registeredEvents),this.state.heatmapLayer.setMap(null))}render(){return null}}r(ln,"contextType",h);var un={onCloseClick:"closeclick",onPanoChanged:"pano_changed",onPositionChanged:"position_changed",onPovChanged:"pov_changed",onResize:"resize",onStatusChanged:"status_changed",onVisibleChanged:"visible_changed",onZoomChanged:"zoom_changed"},pn={register(e,t,n){e.registerPanoProvider(t,n)},links(e,t){e.setLinks(t)},motionTracking(e,t){e.setMotionTracking(t)},options(e,t){e.setOptions(t)},pano(e,t){e.setPano(t)},position(e,t){e.setPosition(t)},pov(e,t){e.setPov(t)},visible(e,t){e.setVisible(t)},zoom(e,t){e.setZoom(t)}};class hn extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"state",{streetViewPanorama:null}),r(this,"setStreetViewPanoramaCallback",(()=>{null!==this.state.streetViewPanorama&&this.props.onLoad&&this.props.onLoad(this.state.streetViewPanorama)}))}componentDidMount(){var e,t,n=null!==(e=null===(t=this.context)||void 0===t?void 0:t.getStreetView())&&void 0!==e?e:null;this.registeredEvents=v({updaterMap:pn,eventMap:un,prevProps:{},nextProps:this.props,instance:n}),this.setState((()=>({streetViewPanorama:n})),this.setStreetViewPanoramaCallback)}componentDidUpdate(e){null!==this.state.streetViewPanorama&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:pn,eventMap:un,prevProps:e,nextProps:this.props,instance:this.state.streetViewPanorama}))}componentWillUnmount(){null!==this.state.streetViewPanorama&&(this.props.onUnmount&&this.props.onUnmount(this.state.streetViewPanorama),m(this.registeredEvents),this.state.streetViewPanorama.setVisible(!1))}render(){return null}}r(hn,"contextType",h);class cn extends e.PureComponent{constructor(){super(...arguments),r(this,"state",{streetViewService:null}),r(this,"setStreetViewServiceCallback",(()=>{null!==this.state.streetViewService&&this.props.onLoad&&this.props.onLoad(this.state.streetViewService)}))}componentDidMount(){var e=new google.maps.StreetViewService;this.setState((function(){return{streetViewService:e}}),this.setStreetViewServiceCallback)}componentWillUnmount(){null!==this.state.streetViewService&&this.props.onUnmount&&this.props.onUnmount(this.state.streetViewService)}render(){return null}}r(cn,"contextType",h);var dn={onDirectionsChanged:"directions_changed"},gn={directions(e,t){e.setDirections(t)},map(e,t){e.setMap(t)},options(e,t){e.setOptions(t)},panel(e,t){e.setPanel(t)},routeIndex(e,t){e.setRouteIndex(t)}};class mn extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"state",{directionsRenderer:null}),r(this,"setDirectionsRendererCallback",(()=>{null!==this.state.directionsRenderer&&(this.state.directionsRenderer.setMap(this.context),this.props.onLoad&&this.props.onLoad(this.state.directionsRenderer))}))}componentDidMount(){var e=new google.maps.DirectionsRenderer(this.props.options);this.registeredEvents=v({updaterMap:gn,eventMap:dn,prevProps:{},nextProps:this.props,instance:e}),this.setState((function(){return{directionsRenderer:e}}),this.setDirectionsRendererCallback)}componentDidUpdate(e){null!==this.state.directionsRenderer&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:gn,eventMap:dn,prevProps:e,nextProps:this.props,instance:this.state.directionsRenderer}))}componentWillUnmount(){null!==this.state.directionsRenderer&&(this.props.onUnmount&&this.props.onUnmount(this.state.directionsRenderer),m(this.registeredEvents),this.state.directionsRenderer&&this.state.directionsRenderer.setMap(null))}render(){return null}}r(mn,"contextType",h);var vn={onPlacesChanged:"places_changed"},fn={bounds(e,t){e.setBounds(t)}};class yn extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"containerElement",e.createRef()),r(this,"state",{searchBox:null}),r(this,"setSearchBoxCallback",(()=>{null!==this.state.searchBox&&this.props.onLoad&&this.props.onLoad(this.state.searchBox)}))}componentDidMount(){if(p(!!google.maps.places,'You need to provide libraries={["places"]} prop to <LoadScript /> component %s',google.maps.places),null!==this.containerElement&&null!==this.containerElement.current){var e=this.containerElement.current.querySelector("input");if(null!==e){var t=new google.maps.places.SearchBox(e,this.props.options);this.registeredEvents=v({updaterMap:fn,eventMap:vn,prevProps:{},nextProps:this.props,instance:t}),this.setState((function(){return{searchBox:t}}),this.setSearchBoxCallback)}}}componentDidUpdate(e){null!==this.state.searchBox&&(m(this.registeredEvents),this.registeredEvents=v({updaterMap:fn,eventMap:vn,prevProps:e,nextProps:this.props,instance:this.state.searchBox}))}componentWillUnmount(){null!==this.state.searchBox&&(this.props.onUnmount&&this.props.onUnmount(this.state.searchBox),m(this.registeredEvents))}render(){return t.jsx("div",{ref:this.containerElement,children:e.Children.only(this.props.children)})}}r(yn,"contextType",h);var bn={onPlaceChanged:"place_changed"},Ln={bounds(e,t){e.setBounds(t)},restrictions(e,t){e.setComponentRestrictions(t)},fields(e,t){e.setFields(t)},options(e,t){e.setOptions(t)},types(e,t){e.setTypes(t)}};class En extends e.PureComponent{constructor(){super(...arguments),r(this,"registeredEvents",[]),r(this,"containerElement",e.createRef()),r(this,"state",{autocomplete:null}),r(this,"setAutocompleteCallback",(()=>{null!==this.state.autocomplete&&this.props.onLoad&&this.props.onLoad(this.state.autocomplete)}))}componentDidMount(){var e;p(!!google.maps.places,'You need to provide libraries={["places"]} prop to <LoadScript /> component %s',google.maps.places);var t=null===(e=this.containerElement.current)||void 0===e?void 0:e.querySelector("input");if(t){var n=new google.maps.places.Autocomplete(t,this.props.options);this.registeredEvents=v({updaterMap:Ln,eventMap:bn,prevProps:{},nextProps:this.props,instance:n}),this.setState((()=>({autocomplete:n})),this.setAutocompleteCallback)}}componentDidUpdate(e){m(this.registeredEvents),this.registeredEvents=v({updaterMap:Ln,eventMap:bn,prevProps:e,nextProps:this.props,instance:this.state.autocomplete})}componentWillUnmount(){null!==this.state.autocomplete&&m(this.registeredEvents)}render(){return t.jsx("div",{ref:this.containerElement,className:this.props.className,children:e.Children.only(this.props.children)})}}r(En,"defaultProps",{className:""}),r(En,"contextType",h);const Cn=[Int8Array,Uint8Array,Uint8ClampedArray,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];class Mn{static from(e){if(!(e instanceof ArrayBuffer))throw new Error("Data must be an instance of ArrayBuffer.");const[t,n]=new Uint8Array(e,0,2);if(219!==t)throw new Error("Data does not appear to be in a KDBush format.");const s=n>>4;if(1!==s)throw new Error(`Got v${s} data when expected v1.`);const o=Cn[15&n];if(!o)throw new Error("Unrecognized array type.");const[i]=new Uint16Array(e,2,1),[r]=new Uint32Array(e,4,1);return new Mn(r,i,o,e)}constructor(e,t=64,n=Float64Array,s){if(isNaN(e)||e<0)throw new Error(`Unpexpected numItems value: ${e}.`);this.numItems=+e,this.nodeSize=Math.min(Math.max(+t,2),65535),this.ArrayType=n,this.IndexArrayType=e<65536?Uint16Array:Uint32Array;const o=Cn.indexOf(this.ArrayType),i=2*e*this.ArrayType.BYTES_PER_ELEMENT,r=e*this.IndexArrayType.BYTES_PER_ELEMENT,a=(8-r%8)%8;if(o<0)throw new Error(`Unexpected typed array class: ${n}.`);s&&s instanceof ArrayBuffer?(this.data=s,this.ids=new this.IndexArrayType(this.data,8,e),this.coords=new this.ArrayType(this.data,8+r+a,2*e),this._pos=2*e,this._finished=!0):(this.data=new ArrayBuffer(8+i+r+a),this.ids=new this.IndexArrayType(this.data,8,e),this.coords=new this.ArrayType(this.data,8+r+a,2*e),this._pos=0,this._finished=!1,new Uint8Array(this.data,0,2).set([219,16+o]),new Uint16Array(this.data,2,1)[0]=t,new Uint32Array(this.data,4,1)[0]=e)}add(e,t){const n=this._pos>>1;return this.ids[n]=n,this.coords[this._pos++]=e,this.coords[this._pos++]=t,n}finish(){const e=this._pos>>1;if(e!==this.numItems)throw new Error(`Added ${e} items when expected ${this.numItems}.`);return wn(this.ids,this.coords,this.nodeSize,0,this.numItems-1,0),this._finished=!0,this}range(e,t,n,s){if(!this._finished)throw new Error("Data not yet indexed - call index.finish().");const{ids:o,coords:i,nodeSize:r}=this,a=[0,o.length-1,0],l=[];for(;a.length;){const u=a.pop()||0,p=a.pop()||0,h=a.pop()||0;if(p-h<=r){for(let r=h;r<=p;r++){const a=i[2*r],u=i[2*r+1];a>=e&&a<=n&&u>=t&&u<=s&&l.push(o[r])}continue}const c=h+p>>1,d=i[2*c],g=i[2*c+1];d>=e&&d<=n&&g>=t&&g<=s&&l.push(o[c]),(0===u?e<=d:t<=g)&&(a.push(h),a.push(c-1),a.push(1-u)),(0===u?n>=d:s>=g)&&(a.push(c+1),a.push(p),a.push(1-u))}return l}within(e,t,n){if(!this._finished)throw new Error("Data not yet indexed - call index.finish().");const{ids:s,coords:o,nodeSize:i}=this,r=[0,s.length-1,0],a=[],l=n*n;for(;r.length;){const u=r.pop()||0,p=r.pop()||0,h=r.pop()||0;if(p-h<=i){for(let n=h;n<=p;n++)On(o[2*n],o[2*n+1],e,t)<=l&&a.push(s[n]);continue}const c=h+p>>1,d=o[2*c],g=o[2*c+1];On(d,g,e,t)<=l&&a.push(s[c]),(0===u?e-n<=d:t-n<=g)&&(r.push(h),r.push(c-1),r.push(1-u)),(0===u?e+n>=d:t+n>=g)&&(r.push(c+1),r.push(p),r.push(1-u))}return a}}function wn(e,t,n,s,o,i){if(o-s<=n)return;const r=s+o>>1;xn(e,t,r,s,o,i),wn(e,t,n,s,r-1,1-i),wn(e,t,n,r+1,o,1-i)}function xn(e,t,n,s,o,i){for(;o>s;){if(o-s>600){const r=o-s+1,a=n-s+1,l=Math.log(r),u=.5*Math.exp(2*l/3),p=.5*Math.sqrt(l*u*(r-u)/r)*(a-r/2<0?-1:1);xn(e,t,n,Math.max(s,Math.floor(n-a*u/r+p)),Math.min(o,Math.floor(n+(r-a)*u/r+p)),i)}const r=t[2*n+i];let a=s,l=o;for(kn(e,t,s,n),t[2*o+i]>r&&kn(e,t,s,o);a<l;){for(kn(e,t,a,l),a++,l--;t[2*a+i]<r;)a++;for(;t[2*l+i]>r;)l--}t[2*s+i]===r?kn(e,t,s,l):(l++,kn(e,t,l,o)),l<=n&&(s=l+1),n<=l&&(o=l-1)}}function kn(e,t,n,s){Pn(e,n,s),Pn(t,2*n,2*s),Pn(t,2*n+1,2*s+1)}function Pn(e,t,n){const s=e[t];e[t]=e[n],e[n]=s}function On(e,t,n,s){const o=e-n,i=t-s;return o*o+i*i}const Sn={minZoom:0,maxZoom:16,minPoints:2,radius:40,extent:512,nodeSize:64,log:!1,generateId:!1,reduce:null,map:e=>e},In=Math.fround||(Dn=new Float32Array(1),e=>(Dn[0]=+e,Dn[0]));var Dn;class jn{constructor(e){this.options=Object.assign(Object.create(Sn),e),this.trees=new Array(this.options.maxZoom+1),this.stride=this.options.reduce?7:6,this.clusterProps=[]}load(e){const{log:t,minZoom:n,maxZoom:s}=this.options;e.length;this.points=e;const o=[];for(let r=0;r<e.length;r++){const t=e[r];if(!t.geometry)continue;const[n,s]=t.geometry.coordinates,i=In(An(n)),a=In(Bn(s));o.push(i,a,1/0,r,-1,1),this.options.reduce&&o.push(0)}let i=this.trees[s+1]=this._createTree(o);for(let r=s;r>=n;r--){Date.now();i=this.trees[r]=this._createTree(this._cluster(i,r))}return this}getClusters(e,t){let n=((e[0]+180)%360+360)%360-180;const s=Math.max(-90,Math.min(90,e[1]));let o=180===e[2]?180:((e[2]+180)%360+360)%360-180;const i=Math.max(-90,Math.min(90,e[3]));if(e[2]-e[0]>=360)n=-180,o=180;else if(n>o){const e=this.getClusters([n,s,180,i],t),r=this.getClusters([-180,s,o,i],t);return e.concat(r)}const r=this.trees[this._limitZoom(t)],a=r.range(An(n),Bn(i),An(o),Bn(s)),l=r.data,u=[];for(const p of a){const e=this.stride*p;u.push(l[e+5]>1?_n(l,e,this.clusterProps):this.points[l[e+3]])}return u}getChildren(e){const t=this._getOriginId(e),n=this._getOriginZoom(e),s="No cluster with the specified id.",o=this.trees[n];if(!o)throw new Error(s);const i=o.data;if(t*this.stride>=i.length)throw new Error(s);const r=this.options.radius/(this.options.extent*Math.pow(2,n-1)),a=i[t*this.stride],l=i[t*this.stride+1],u=o.within(a,l,r),p=[];for(const h of u){const t=h*this.stride;i[t+4]===e&&p.push(i[t+5]>1?_n(i,t,this.clusterProps):this.points[i[t+3]])}if(0===p.length)throw new Error(s);return p}getLeaves(e,t,n){t=t||10,n=n||0;const s=[];return this._appendLeaves(s,e,t,n,0),s}getTile(e,t,n){const s=this.trees[this._limitZoom(e)],o=Math.pow(2,e),{extent:i,radius:r}=this.options,a=r/i,l=(n-a)/o,u=(n+1+a)/o,p={features:[]};return this._addTileFeatures(s.range((t-a)/o,l,(t+1+a)/o,u),s.data,t,n,o,p),0===t&&this._addTileFeatures(s.range(1-a/o,l,1,u),s.data,o,n,o,p),t===o-1&&this._addTileFeatures(s.range(0,l,a/o,u),s.data,-1,n,o,p),p.features.length?p:null}getClusterExpansionZoom(e){let t=this._getOriginZoom(e)-1;for(;t<=this.options.maxZoom;){const n=this.getChildren(e);if(t++,1!==n.length)break;e=n[0].properties.cluster_id}return t}_appendLeaves(e,t,n,s,o){const i=this.getChildren(t);for(const r of i){const t=r.properties;if(t&&t.cluster?o+t.point_count<=s?o+=t.point_count:o=this._appendLeaves(e,t.cluster_id,n,s,o):o<s?o++:e.push(r),e.length===n)break}return o}_createTree(e){const t=new Mn(e.length/this.stride|0,this.options.nodeSize,Float32Array);for(let n=0;n<e.length;n+=this.stride)t.add(e[n],e[n+1]);return t.finish(),t.data=e,t}_addTileFeatures(e,t,n,s,o,i){for(const r of e){const e=r*this.stride,a=t[e+5]>1;let l,u,p;if(a)l=Tn(t,e,this.clusterProps),u=t[e],p=t[e+1];else{const n=this.points[t[e+3]];l=n.properties;const[s,o]=n.geometry.coordinates;u=An(s),p=Bn(o)}const h={type:1,geometry:[[Math.round(this.options.extent*(u*o-n)),Math.round(this.options.extent*(p*o-s))]],tags:l};let c;c=a||this.options.generateId?t[e+3]:this.points[t[e+3]].id,void 0!==c&&(h.id=c),i.features.push(h)}}_limitZoom(e){return Math.max(this.options.minZoom,Math.min(Math.floor(+e),this.options.maxZoom+1))}_cluster(e,t){const{radius:n,extent:s,reduce:o,minPoints:i}=this.options,r=n/(s*Math.pow(2,t)),a=e.data,l=[],u=this.stride;for(let p=0;p<a.length;p+=u){if(a[p+2]<=t)continue;a[p+2]=t;const n=a[p],s=a[p+1],h=e.within(a[p],a[p+1],r),c=a[p+5];let d=c;for(const e of h){const n=e*u;a[n+2]>t&&(d+=a[n+5])}if(d>c&&d>=i){let e,i=n*c,r=s*c,g=-1;const m=(p/u<<5)+(t+1)+this.points.length;for(const n of h){const s=n*u;if(a[s+2]<=t)continue;a[s+2]=t;const l=a[s+5];i+=a[s]*l,r+=a[s+1]*l,a[s+4]=m,o&&(e||(e=this._map(a,p,!0),g=this.clusterProps.length,this.clusterProps.push(e)),o(e,this._map(a,s)))}a[p+4]=m,l.push(i/d,r/d,1/0,m,-1,d),o&&l.push(g)}else{for(let e=0;e<u;e++)l.push(a[p+e]);if(d>1)for(const e of h){const n=e*u;if(!(a[n+2]<=t)){a[n+2]=t;for(let e=0;e<u;e++)l.push(a[n+e])}}}}return l}_getOriginId(e){return e-this.points.length>>5}_getOriginZoom(e){return(e-this.points.length)%32}_map(e,t,n){if(e[t+5]>1){const s=this.clusterProps[e[t+6]];return n?Object.assign({},s):s}const s=this.points[e[t+3]].properties,o=this.options.map(s);return n&&o===s?Object.assign({},o):o}}function _n(e,t,n){return{type:"Feature",id:e[t+3],properties:Tn(e,t,n),geometry:{type:"Point",coordinates:[(s=e[t],360*(s-.5)),Un(e[t+1])]}};var s}function Tn(e,t,n){const s=e[t+5],o=s>=1e4?`${Math.round(s/1e3)}k`:s>=1e3?Math.round(s/100)/10+"k":s,i=e[t+6],r=-1===i?{}:Object.assign({},n[i]);return Object.assign(r,{cluster:!0,cluster_id:e[t+3],point_count:s,point_count_abbreviated:o})}function An(e){return e/360+.5}function Bn(e){const t=Math.sin(e*Math.PI/180),n=.5-.25*Math.log((1+t)/(1-t))/Math.PI;return n<0?0:n>1?1:n}function Un(e){const t=(180-360*e)*Math.PI/180;return 360*Math.atan(Math.exp(t))/Math.PI-90}
/*! *****************************************************************************
Copyright (c) Microsoft Corporation.

Permission to use, copy, modify, and/or distribute this software for any
purpose with or without fee is hereby granted.

THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
PERFORMANCE OF THIS SOFTWARE.
***************************************************************************** */class Rn{static isAdvancedMarkerAvailable(e){return google.maps.marker&&!0===e.getMapCapabilities().isAdvancedMarkersAvailable}static isAdvancedMarker(e){return google.maps.marker&&e instanceof google.maps.marker.AdvancedMarkerElement}static setMap(e,t){this.isAdvancedMarker(e)?e.map=t:e.setMap(t)}static getPosition(e){if(this.isAdvancedMarker(e)){if(e.position){if(e.position instanceof google.maps.LatLng)return e.position;if(e.position.lat&&e.position.lng)return new google.maps.LatLng(e.position.lat,e.position.lng)}return new google.maps.LatLng(null)}return e.getPosition()}static getVisible(e){return!!this.isAdvancedMarker(e)||e.getVisible()}}class zn{constructor({markers:e,position:t}){this.markers=e,t&&(t instanceof google.maps.LatLng?this._position=t:this._position=new google.maps.LatLng(t))}get bounds(){if(0===this.markers.length&&!this._position)return;const e=new google.maps.LatLngBounds(this._position,this._position);for(const t of this.markers)e.extend(Rn.getPosition(t));return e}get position(){return this._position||this.bounds.getCenter()}get count(){return this.markers.filter((e=>Rn.getVisible(e))).length}push(e){this.markers.push(e)}delete(){this.marker&&(Rn.setMap(this.marker,null),this.marker=void 0),this.markers.length=0}}class Zn{constructor({maxZoom:e=16}){this.maxZoom=e}noop({markers:e}){return Vn(e)}}const Vn=e=>e.map((e=>new zn({position:Rn.getPosition(e),markers:[e]})));class Nn extends Zn{constructor(e){var{maxZoom:t,radius:n=60}=e,s=function(e,t){var n={};for(var s in e)Object.prototype.hasOwnProperty.call(e,s)&&t.indexOf(s)<0&&(n[s]=e[s]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var o=0;for(s=Object.getOwnPropertySymbols(e);o<s.length;o++)t.indexOf(s[o])<0&&Object.prototype.propertyIsEnumerable.call(e,s[o])&&(n[s[o]]=e[s[o]])}return n}(e,["maxZoom","radius"]);super({maxZoom:t}),this.state={zoom:-1},this.superCluster=new jn(Object.assign({maxZoom:this.maxZoom,radius:n},s))}calculate(e){let t=!1;const n={zoom:e.map.getZoom()};if(!s(e.markers,this.markers)){t=!0,this.markers=[...e.markers];const n=this.markers.map((e=>{const t=Rn.getPosition(e);return{type:"Feature",geometry:{type:"Point",coordinates:[t.lng(),t.lat()]},properties:{marker:e}}}));this.superCluster.load(n)}return t||(this.state.zoom<=this.maxZoom||n.zoom<=this.maxZoom)&&(t=!s(this.state,n)),this.state=n,t&&(this.clusters=this.cluster(e)),{clusters:this.clusters,changed:t}}cluster({map:e}){return this.superCluster.getClusters([-180,-90,180,90],Math.round(e.getZoom())).map((e=>this.transformCluster(e)))}transformCluster({geometry:{coordinates:[e,t]},properties:n}){if(n.cluster)return new zn({markers:this.superCluster.getLeaves(n.cluster_id,1/0).map((e=>e.properties.marker)),position:{lat:t,lng:e}});const s=n.marker;return new zn({markers:[s],position:Rn.getPosition(s)})}}class Wn{constructor(e,t){this.markers={sum:e.length};const n=t.map((e=>e.count)),s=n.reduce(((e,t)=>e+t),0);this.clusters={count:t.length,markers:{mean:s/t.length,sum:s,min:Math.min(...n),max:Math.max(...n)}}}}class Hn{render({count:e,position:t},n,s){const o=`<svg fill="${e>Math.max(10,n.clusters.markers.mean)?"#ff0000":"#0000ff"}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 240" width="50" height="50">\n<circle cx="120" cy="120" opacity=".6" r="70" />\n<circle cx="120" cy="120" opacity=".3" r="90" />\n<circle cx="120" cy="120" opacity=".2" r="110" />\n<text x="50%" y="50%" style="fill:#fff" text-anchor="middle" font-size="50" dominant-baseline="middle" font-family="roboto,arial,sans-serif">${e}</text>\n</svg>`,i=`Cluster of ${e} markers`,r=Number(google.maps.Marker.MAX_ZINDEX)+e;if(Rn.isAdvancedMarkerAvailable(s)){const e=(new DOMParser).parseFromString(o,"image/svg+xml").documentElement;e.setAttribute("transform","translate(0 25)");const n={map:s,position:t,zIndex:r,title:i,content:e};return new google.maps.marker.AdvancedMarkerElement(n)}const a={position:t,zIndex:r,title:i,icon:{url:`data:image/svg+xml;base64,${btoa(o)}`,anchor:new google.maps.Point(25,25)}};return new google.maps.Marker(a)}}class Fn{constructor(){!function(e,t){for(let n in t.prototype)e.prototype[n]=t.prototype[n]}(Fn,google.maps.OverlayView)}}var Gn;!function(e){e.CLUSTERING_BEGIN="clusteringbegin",e.CLUSTERING_END="clusteringend",e.CLUSTER_CLICK="click"}(Gn||(Gn={}));const Kn=(e,t,n)=>{n.fitBounds(t.bounds)};class Yn extends Fn{constructor({map:e,markers:t=[],algorithmOptions:n={},algorithm:s=new Nn(n),renderer:o=new Hn,onClusterClick:i=Kn}){super(),this.markers=[...t],this.clusters=[],this.algorithm=s,this.renderer=o,this.onClusterClick=i,e&&this.setMap(e)}addMarker(e,t){this.markers.includes(e)||(this.markers.push(e),t||this.render())}addMarkers(e,t){e.forEach((e=>{this.addMarker(e,!0)})),t||this.render()}removeMarker(e,t){const n=this.markers.indexOf(e);return-1!==n&&(Rn.setMap(e,null),this.markers.splice(n,1),t||this.render(),!0)}removeMarkers(e,t){let n=!1;return e.forEach((e=>{n=this.removeMarker(e,!0)||n})),n&&!t&&this.render(),n}clearMarkers(e){this.markers.length=0,e||this.render()}render(){const e=this.getMap();if(e instanceof google.maps.Map&&e.getProjection()){google.maps.event.trigger(this,Gn.CLUSTERING_BEGIN,this);const{clusters:t,changed:n}=this.algorithm.calculate({markers:this.markers,map:e,mapCanvasProjection:this.getProjection()});if(n||null==n){const e=new Set;for(const s of t)1==s.markers.length&&e.add(s.markers[0]);const n=[];for(const t of this.clusters)null!=t.marker&&(1==t.markers.length?e.has(t.marker)||Rn.setMap(t.marker,null):n.push(t.marker));this.clusters=t,this.renderClusters(),requestAnimationFrame((()=>n.forEach((e=>Rn.setMap(e,null)))))}google.maps.event.trigger(this,Gn.CLUSTERING_END,this)}}onAdd(){this.idleListener=this.getMap().addListener("idle",this.render.bind(this)),this.render()}onRemove(){google.maps.event.removeListener(this.idleListener),this.reset()}reset(){this.markers.forEach((e=>Rn.setMap(e,null))),this.clusters.forEach((e=>e.delete())),this.clusters=[]}renderClusters(){const e=new Wn(this.markers,this.clusters),t=this.getMap();this.clusters.forEach((n=>{1===n.markers.length?n.marker=n.markers[0]:(n.marker=this.renderer.render(n,e,t),n.markers.forEach((e=>Rn.setMap(e,null))),this.onClusterClick&&n.marker.addListener("click",(e=>{google.maps.event.trigger(this,Gn.CLUSTER_CLICK,n),this.onClusterClick(e,n,t)}))),Rn.setMap(n.marker,t)}))}}export{b as G,Yn as M,Nn as S,H as u};

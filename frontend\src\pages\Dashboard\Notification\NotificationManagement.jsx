import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typo<PERSON> } from "@mui/material";
import { useEffect, useMemo, useState } from "react";
import { useApp } from "../../../hooks/AppHook";
import { useUser } from "../../../hooks/UserHook";
import theme from "../../../theme";
import Notification from "./Notification";
import Summary from "./Summary/Summary";
import useVesselInfo from "../../../hooks/VesselInfoHook";
import { Add } from "@mui/icons-material";
import { permissions } from "../../../utils";
import emailsDomainsController from "../../../controllers/EmailsDomains.controller";

export default function NotificationManagement() {
    const { user } = useUser();
    const [tabIndex, setTabIndex] = useState(0);
    const { vesselInfo, fetchVesselsInfo } = useVesselInfo();

    const [showCreateEditModal, setShowCreateEditModal] = useState(false);
    const [isEditModal, setIsEditModal] = useState(false);

    const [showSummaryModal, setShowSummaryModal] = useState(false);
    const [isEditSummaryModal, setIsEditSummaryModal] = useState(false);

    const { isMobile } = useApp();

    const [vessels, setVessels] = useState([]);
    const [emailDomains, setEmailDomains] = useState([]);

    const fetchVesselId = async () => {
        try {
            if (vesselInfo && Array.isArray(vesselInfo)) {
                setVessels(vesselInfo);
            } else {
                fetchVesselsInfo();
            }
        } catch (err) {
            console.error(`Error fetching vessels in Notification`, err);
        }
    };

    const fetchEmailDomains = async () => {
        const response = await emailsDomainsController.getAllAllowedEmailDomains();
        if (response.status === 200) {
            setEmailDomains(response.data);
        }
    };

    useEffect(() => {
        fetchVesselId();
    }, [vesselInfo]);
    useEffect(() => {
        fetchEmailDomains();
    }, []);

    const tabs = useMemo(() => {
        const res = [
            {
                value: 0,
                label: "Alerts",
                component: (
                    <Notification
                        vessels={vessels}
                        showCreateEditModal={showCreateEditModal}
                        setShowCreateEditModal={setShowCreateEditModal}
                        setIsEditModal={setIsEditModal}
                        isEditModal={isEditModal}
                        emailDomains={emailDomains}
                    />
                ),
                display: user?.hasPermissions([permissions.manageNotificationsAlerts]),
                disabled: false,
            },
            {
                value: 1,
                label: "Summary Reports",
                component: (
                    <Summary
                        vessels={vessels}
                        showSummaryModal={showSummaryModal}
                        setShowSummaryModal={setShowSummaryModal}
                        emailDomains={emailDomains}
                        isEditModal={isEditSummaryModal}
                        setIsEditModal={setIsEditSummaryModal}
                    />
                ),
                display: user?.hasPermissions([permissions.manageNotificationsAlerts]),
                disabled: user?.email ? false : true,
            },
        ];

        return res;
    }, [user, showCreateEditModal, isEditModal, isEditSummaryModal, showSummaryModal, vessels]);

    useEffect(() => {
        if (!tabIndex) {
            setTabIndex(tabs.find((t) => t.display)?.value || 0);
        }
    }, [tabs]);

    return (
        user &&
        tabs.some((t) => t.display) && (
            <Grid
                container
                color={"#FFFFFF"}
                flexDirection={"column"}
                gap={2}
                height={"100%"}
                overflow={"auto"}
                sx={{ backgroundColor: (theme) => theme.palette.custom.darkBlue }}
            >
                <Grid
                    container
                    padding={2}
                    paddingBottom={0}
                    display={"flex"}
                    columnGap={{ xs: 2, lg: 0 }}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                    flexWrap={"wrap"}
                >
                    <Grid
                        className="notification-step-1"
                        size={{
                            xs: "grow",
                            lg: tabs.filter((t) => t.display).length * 2.45,
                        }}
                    >
                        <Tabs
                            value={tabIndex}
                            onChange={(e, v) => setTabIndex(v)}
                            sx={{
                                width: "100%",
                                padding: "4px",
                                border: `2px solid ${theme.palette.custom.borderColor}`,
                                borderRadius: "8px",
                                backgroundColor: "transparent",
                                "& .MuiTabs-flexContainer": {
                                    height: "100%",
                                },
                                "& .MuiButtonBase-root": {
                                    width: 100 / tabs.filter((t) => t.display).length + "%",
                                    borderRadius: "8px",
                                },
                                "& .MuiButtonBase-root.Mui-selected": {
                                    backgroundColor: theme.palette.custom.mainBlue,
                                },
                            }}
                        >
                            {tabs
                                .filter((t) => t.display)
                                .map((t, index) => {
                                    // console.log("Tab", t, index);
                                    return (
                                        // <Tooltip key={index} title={t.disabled ? "No email linked to your account" : ""} arrow>
                                        <Tab
                                            key={t.value + index}
                                            label={
                                                <Tooltip key={index} title={t.disabled ? "No email linked to your account" : ""} arrow>
                                                    <Typography>{t.label} </Typography>
                                                </Tooltip>
                                            }
                                            value={t.value}
                                            disabled={t.disabled}
                                            sx={{
                                                maxWidth: "none",
                                                backgroundColor: t.value === tabIndex ? theme.palette.custom.mainBlue : "transparent",
                                                textTransform: "capitalize",
                                                "&.Mui-disabled": {
                                                    color: theme.palette.custom.mediumGrey,
                                                    opacity: 1,
                                                    cursor: "not-allowed",
                                                    pointerEvents: "fill",
                                                },
                                            }}
                                        />
                                        //  </Tooltip>
                                    );
                                })}
                        </Tabs>
                    </Grid>
                    <Grid
                        container
                        columnGap={2}
                        justifyContent={"flex-end"}
                        height={{ xs: "100%", lg: "auto" }}
                        size={{
                            xs: "auto",
                            lg: 7,
                        }}
                    >
                        {tabIndex === 0 && (
                            <Grid
                                alignItems={"center"}
                                display={"flex"}
                                justifyContent={"flex-end"}
                                gap={2}
                                size={{
                                    xs: 12,
                                    lg: 5.8,
                                }}
                            >
                                <Button
                                    className="notification-step-2"
                                    variant="outlined"
                                    startIcon={<Add />}
                                    sx={{
                                        "&.MuiButtonBase-root": {
                                            borderColor: theme.palette.custom.borderColor,
                                            backgroundColor: theme.palette.custom.mainBlue,
                                            textTransform: "capitalize",
                                            height: { xs: "50px", lg: "auto" },
                                            color: "#FFFFFF",
                                            padding: { xs: "0", lg: "10px 20px" },
                                            fontWeight: "bold",
                                        },
                                        "& .MuiButton-icon": {
                                            marginRight: { xs: 0, lg: "10px" },
                                        },
                                    }}
                                    onClick={() => {
                                        setIsEditModal(false);
                                        setShowCreateEditModal(true);
                                    }}
                                >
                                    {!isMobile && "Create new Alert"}
                                </Button>
                            </Grid>
                        )}
                        {tabIndex === 1 && (
                            <Grid
                                alignItems={"center"}
                                display={"flex"}
                                justifyContent={"flex-end"}
                                gap={2}
                                size={{
                                    xs: 12,
                                    lg: 5.8,
                                }}
                            >
                                <Button
                                    className="notification-step-3"
                                    variant="outlined"
                                    // startIcon={<img src={'/icons/filter_icon.svg'} width={20} height={20} />}
                                    startIcon={<Add />}
                                    sx={{
                                        "&.MuiButtonBase-root": {
                                            borderColor: theme.palette.custom.borderColor,
                                            backgroundColor: theme.palette.custom.mainBlue,
                                            textTransform: "capitalize",
                                            height: { xs: "50px", lg: "auto" },
                                            color: "#FFFFFF",
                                            padding: { xs: "0", lg: "10px 20px" },
                                            fontWeight: "bold",
                                        },
                                        "& .MuiButton-icon": {
                                            marginRight: { xs: 0, lg: "10px" },
                                        },
                                    }}
                                    onClick={() => {
                                        setIsEditSummaryModal(false);
                                        setShowSummaryModal(true);
                                    }}
                                >
                                    {!isMobile && "Create new summary"}
                                </Button>
                            </Grid>
                        )}
                    </Grid>
                </Grid>
                {tabs
                    .filter((t) => t.display)
                    .map((t) => (
                        <Grid key={t.value} display={tabIndex !== t.value && "none"} paddingX={2} paddingBottom={2} width={"100%"} size="grow">
                            {t.component}
                        </Grid>
                    ))}
            </Grid>
        )
    );
}

import { Grid, Modal, Typography, IconButton, Box, alpha, CircularProgress, Fade, Skeleton } from "@mui/material";
import React, { useEffect, useState, useCallback, useMemo } from "react";
import { ChevronLeft, ChevronRight } from "@mui/icons-material";
import ModalContainer from "../../../components/ModalContainer";
import dayjs from "dayjs";
import { displayCoordinates, permissions, userValues } from "../../../utils";
import { useApp } from "../../../hooks/AppHook";
import theme from "../../../theme";
import PreviewMedia from "../../../components/PreviewMedia";
import { useUser } from "../../../hooks/UserHook.jsx";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";

export default function GroupedDetailModal({ showDetailModal, setShowDetailModal, selectedCard, setSelectedCard, id, signedUrls }) {
    const { screenSize } = useApp();
    const { user } = useUser();
    const { vesselInfo } = useVesselInfo();

    const [src, setSrc] = useState(null);
    const [currentGroupIndex, setCurrentGroupIndex] = useState(selectedCard?.currentGroupIndex || 0);
    const [isImageLoading, setIsImageLoading] = useState(true);
    const [imageError, setImageError] = useState(false);
    const hasManageArtifacts = user?.hasPermissions([permissions.manageArtifacts]);

    // Handle grouped artifacts
    const isGrouped = selectedCard?.isGroup && selectedCard?.groupArtifacts?.length > 1;
    const totalCount = isGrouped ? selectedCard.groupArtifacts.length : 1;

    const currentArtifact = useMemo(() => {
        if (isGrouped) {
            return selectedCard.groupArtifacts[currentGroupIndex] || selectedCard;
        }
        return selectedCard;
    }, [selectedCard, currentGroupIndex, isGrouped]);

    const vessel = useMemo(
        () => vesselInfo.find((v) => v.vessel_id === currentArtifact?.onboard_vessel_id),
        [vesselInfo, currentArtifact?.onboard_vessel_id],
    );

    const key = currentArtifact?.location?.coordinates && displayCoordinates(currentArtifact.location.coordinates, !!user?.use_MGRS);

    // Navigation handlers
    const handlePrevious = useCallback(() => {
        if (currentGroupIndex > 0) {
            setIsImageLoading(true);
            setImageError(false);
            setCurrentGroupIndex(currentGroupIndex - 1);
        }
    }, [currentGroupIndex]);

    const handleNext = useCallback(() => {
        if (currentGroupIndex < totalCount - 1) {
            setIsImageLoading(true);
            setImageError(false);
            setCurrentGroupIndex(currentGroupIndex + 1);
        }
    }, [currentGroupIndex, totalCount]);

    const handleClose = () => {
        setSelectedCard(null);
        setShowDetailModal(false);
        setCurrentGroupIndex(0);
        setIsImageLoading(true);
        setImageError(false);
    };

    // Image loading effect
    useEffect(() => {
        if (!currentArtifact) return;

        setIsImageLoading(true);
        setImageError(false);

        const isVideo = Boolean(currentArtifact.video_path);
        const imageUrl = signedUrls.get(`${currentArtifact._id}:image`);
        const videoUrl = signedUrls.get(`${currentArtifact._id}:video`);

        if (isVideo) {
            setSrc(videoUrl);
            setIsImageLoading(false);
        } else {
            const img = new Image();
            img.onload = () => {
                setSrc(imageUrl);
                setIsImageLoading(false);
                setImageError(false);
            };
            img.onerror = () => {
                setIsImageLoading(false);
                setImageError(true);
            };
            img.src = imageUrl;
        }
    }, [currentArtifact]);

    // Reset group index when modal opens
    useEffect(() => {
        if (selectedCard?.currentGroupIndex !== undefined) {
            setCurrentGroupIndex(selectedCard.currentGroupIndex);
        }
    }, [selectedCard]);

    const details = [
        { label: "Location", value: key },
        { label: "Category", value: currentArtifact?.super_category || "Unspecified category" },
        { label: "Sub Category", value: currentArtifact?.category },
        { label: "Weapons", value: currentArtifact?.weapons },
        { label: "Size", value: currentArtifact?.size },
        { label: "Color", value: currentArtifact?.color },
        { label: "Imo Number", value: currentArtifact?.imo_number },
        { label: "Country Flag", value: currentArtifact?.country_flag },
        {
            label: "Text Detected",
            value:
                Array.isArray(currentArtifact?.text_extraction) && currentArtifact.text_extraction.length > 0
                    ? currentArtifact.text_extraction
                          .map((e) => e.text)
                          .slice(0, 5)
                          .join(", ")
                    : null,
        },
        { label: "Description", value: currentArtifact?.others },
    ];

    const fieldWithFullWidth = ["Text Detected", "Description"];

    return (
        <Modal open={Boolean(showDetailModal)} onClose={handleClose}>
            <ModalContainer title="Event Details" onClose={handleClose} showDivider>
                <Grid container gap={1} maxHeight="70vh" overflow="auto" minWidth={{ xs: 300, sm: 500 }} maxWidth={800}>
                    <Grid size={12} position="relative">
                        {/* Loading placeholder */}
                        {isImageLoading && (
                            <>
                                <Box
                                    sx={{
                                        display: "flex",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        position: "absolute",
                                        top: 0,
                                        left: 0,
                                        width: "100%",
                                        height: "300px",
                                        zIndex: 1,
                                        borderRadius: 2,
                                    }}
                                >
                                    <CircularProgress />
                                </Box>
                                <Skeleton variant="rectangular" width="100%" height="300px" sx={{ borderRadius: 2 }} />
                            </>
                        )}

                        {/* Error placeholder */}
                        {imageError && !isImageLoading && (
                            <Box
                                height="300px"
                                display="flex"
                                alignItems="center"
                                justifyContent="center"
                                backgroundColor="rgba(0,0,0,0.2)"
                                borderRadius={2}
                            >
                                <Typography color="text.secondary" variant="body1">
                                    Failed to load media
                                </Typography>
                            </Box>
                        )}

                        {/* Actual media */}
                        {!isImageLoading && !imageError && currentArtifact && (
                            <Fade in timeout={300} unmountOnExit>
                                <Box>
                                    <PreviewMedia
                                        thumbnailLink={src}
                                        originalLink={src}
                                        cardId={id || currentArtifact._id}
                                        isImage={!currentArtifact.video_path}
                                        style={{ borderRadius: 8, height: 300, objectFit: "contain", backgroundColor: "#000" }}
                                        skeletonStyle={{ height: 300, width: "100%", borderRadius: 2 }}
                                        showFullscreenIconForMap={!currentArtifact.video_path}
                                        showArchiveButton={hasManageArtifacts}
                                        isGrouped={isGrouped}
                                        groupArtifacts={selectedCard?.groupArtifacts}
                                    />
                                </Box>
                            </Fade>
                        )}
                    </Grid>

                    {/* Navigation panel for grouped artifacts */}
                    {isGrouped && (
                        <Grid size={12} container justifyContent="center">
                            <Box sx={{ display: "flex", alignItems: "center", padding: "4px 8px", gap: 1 }} onClick={(e) => e.stopPropagation()}>
                                <IconButton
                                    size="small"
                                    onClick={handlePrevious}
                                    disabled={currentGroupIndex === 0}
                                    sx={{
                                        color: "white",
                                        padding: "4px",
                                        background: alpha(theme.palette.custom.borderColor, 0.8) + " !important",
                                        "&:disabled": { color: "rgba(255,255,255,0.3)" },
                                    }}
                                >
                                    <ChevronLeft fontSize="small" />
                                </IconButton>

                                <Typography
                                    variant="caption"
                                    sx={{
                                        color: "white",
                                        fontWeight: 500,
                                        minWidth: "40px",
                                        textAlign: "center",
                                        padding: "5px 17px",
                                        borderRadius: "100px",
                                        background: alpha(theme.palette.primary.main, 0.8),
                                    }}
                                >
                                    {String(currentGroupIndex + 1).padStart(2, "0")}/{String(totalCount).padStart(2, "0")}
                                </Typography>

                                <IconButton
                                    size="small"
                                    onClick={handleNext}
                                    disabled={currentGroupIndex === totalCount - 1}
                                    sx={{
                                        color: "white",
                                        padding: "4px",
                                        background: alpha(theme.palette.custom.borderColor, 0.8) + " !important",
                                        "&:disabled": { color: "rgba(255,255,255,0.3)" },
                                    }}
                                >
                                    <ChevronRight fontSize="small" />
                                </IconButton>
                            </Box>
                        </Grid>
                    )}
                    <Grid
                        display="flex"
                        justifyContent={screenSize.xs ? "flex-start" : "space-between"}
                        alignItems={screenSize.xs ? "flex-start" : "center"}
                        paddingX={1}
                        flexDirection={screenSize.xs ? "column" : "row"}
                        size={12}
                    >
                        {screenSize.xs && (
                            <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500} color={theme.palette.custom.mainBlue}>
                                Name
                            </Typography>
                        )}
                        <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500}>
                            {vessel?.name || vessel?.vessel_id || "Unknown Vessel"}
                        </Typography>
                        {screenSize.xs && (
                            <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500} color={theme.palette.custom.mainBlue}>
                                Timestamp
                            </Typography>
                        )}
                        <Typography fontSize={{ xs: "16px", sm: "20px" }} fontWeight={500}>
                            {dayjs(currentArtifact?.timestamp).format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}
                        </Typography>
                    </Grid>

                    {details.map(({ label, value }, index) => (
                        <React.Fragment key={index}>
                            <Grid
                                display="flex"
                                alignItems={{
                                    xs: "flex-start",
                                    sm: index % 2 == 0 || fieldWithFullWidth.includes(label) ? "flex-start" : "flex-end",
                                }}
                                paddingX={1}
                                flexDirection="column"
                                size={{ xs: 12, sm: fieldWithFullWidth.includes(label) ? 12 : 5.9 }}
                            >
                                <Typography fontSize="16px" fontWeight={500} color={theme.palette.custom.mainBlue}>
                                    {label}
                                </Typography>
                                <Typography fontSize="16px" fontWeight={500}>
                                    {value ?? "--"}
                                </Typography>
                            </Grid>
                        </React.Fragment>
                    ))}
                </Grid>
            </ModalContainer>
        </Modal>
    );
}

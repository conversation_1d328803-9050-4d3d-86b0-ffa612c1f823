const express = require("express");
const router = express.Router();
const natural = require("natural");
const SpellingCorrector = require("spelling-corrector");
const ArtifactSuggestion = require("../models/ArtifactSuggestion");
const ArtifactSynonym = require("../models/ArtifactSynonym");
const { cleanSuggestion } = require("../utils/functions");

const wordnet = new natural.WordNet();
const spelling = new SpellingCorrector();
spelling.loadDictionary();

// NOTE: synonymCache is a simple in-memory object updated every 5 minutes on access.
// It replaces the value each time, so it does not grow unbounded; safe but increases memory proportional to synonyms size.

let lastSynonymCheckedAt = null;
const synonymCache = { synonyms: null };
const synonymCachePeriodMs = 300000; // 5 minutes
const SUGGESTION_LIMIT = 10;

const getSynonyms = (word) => {
    return new Promise((resolve) => {
        wordnet.lookup(word, (results) => {
            const synonyms = new Set();
            results.forEach((result) => {
                result.synonyms.forEach((syn) => {
                    if (syn.toLowerCase() !== word.toLowerCase()) {
                        synonyms.add(syn);
                    }
                });
            });
            resolve(Array.from(synonyms).slice(0, 5));
        });
    });
};

function rankSuggestions(suggestionsArr, suggestionDocs) {
    const docMap = new Map(suggestionDocs.map((s) => [s.search, s]));
    let ranked = suggestionsArr
        .map((s) => {
            const doc = docMap.get(s);
            return {
                suggestion: s,
                click: doc ? doc.click : 0,
            };
        })
        .sort((a, b) => {
            const aScore = a.click;
            const bScore = b.click;
            if (bScore !== aScore) return bScore - aScore;
            return a.suggestion.localeCompare(b.suggestion);
        })
        .map((r) => r.suggestion);
    return ranked;
}

function generateCombinations(wordAlternatives, limit = 20) {
    const out = [];
    function combine(arr, prefix = []) {
        if (out.length >= limit) return;
        if (arr.length === 0) {
            out.push(prefix.join(" "));
            return;
        }
        for (let i = 0; i < arr[0].length; i++) {
            combine(arr.slice(1), [...prefix, arr[0][i]]);
            if (out.length >= limit) break;
        }
    }
    combine(wordAlternatives);
    return out;
}

const getSynonymMap = async () => {
    if (!lastSynonymCheckedAt || !synonymCache.synonyms || Date.now() - lastSynonymCheckedAt.getTime() >= synonymCachePeriodMs) {
        const allSynonyms = await ArtifactSynonym.find({}, { word: 1, synonyms: 1 });
        const synonymMap = {};
        for (const syn of allSynonyms) {
            if (Array.isArray(syn.synonyms)) {
                for (const s of syn.synonyms) {
                    synonymMap[s.toLowerCase()] = syn.word;
                }
            }
            synonymMap[syn.word.toLowerCase()] = syn.word;
        }
        synonymCache.synonyms = synonymMap;
        lastSynonymCheckedAt = new Date();
    }
    return synonymCache.synonyms;
};

function normalizeSuggestionsArray(arr, synonymMap) {
    const normalized = arr.map((str) =>
        str
            .split(/\s+/)
            .map((word) => (synonymMap[word.toLowerCase()] || word).toLowerCase())
            .join(" ")
            .trim(),
    );
    return Array.from(new Set(normalized.filter(Boolean)));
}

router.post("/", async (req, res) => {
    try {
        // 1. Validate and clean the query
        const { query } = req.body;
        if (!query || typeof query !== "string") {
            return res.status(400).json({ message: "Query is required" });
        }
        const cleanQuery = cleanSuggestion(query);
        const synonymMap = await getSynonymMap();

        // 2. For each word, get alternatives: synonym, spell-correct, or WordNet synonyms
        const words = cleanQuery.split(/\s+/);
        const wordAlternatives = await Promise.all(
            words.map(async (word) => {
                const lower = word.toLowerCase();
                if (synonymMap[lower]) return [synonymMap[lower]];
                if (word && word.split(/\s+/).length === 1) {
                    const correction = spelling.correct(word);
                    if (correction && correction !== word) {
                        return [correction];
                    }
                }
                const syns = await getSynonyms(word);
                return [word, ...syns];
            }),
        );
        let generated = generateCombinations(wordAlternatives, 20).map(cleanSuggestion).filter(Boolean);

        // 3. Find DB suggestions (partial match, case-insensitive)
        const regex = new RegExp(words.map((w) => w.toLowerCase()).join("|"), "i");
        const dbSuggestions = await ArtifactSuggestion.find({ search: { $regex: regex } });
        const dbNorm = dbSuggestions.map((s) => s.search);

        // 4. Merge, normalize, and dedupe all suggestions
        let suggestions = normalizeSuggestionsArray([...generated, ...dbNorm], synonymMap);

        // 5. Rank suggestions
        suggestions = rankSuggestions(suggestions, dbSuggestions);

        // 6. Always show the normalized/corrected query at the top
        const normalizedQuery = wordAlternatives
            .map((a) => a[0])
            .join(" ")
            .toLowerCase();
        suggestions = [normalizedQuery, ...suggestions.filter((s) => s.toLowerCase() !== normalizedQuery)];

        // 7. Limit suggestions before updating impressions and returning
        suggestions = suggestions.map((s) => s.toLowerCase()).slice(0, SUGGESTION_LIMIT);
        const limitedDbNorm = suggestions.filter((s) => dbNorm.map((x) => x.toLowerCase()).includes(s));

        // 8. Update impressions for found suggestions only
        if (limitedDbNorm.length > 0) {
            ArtifactSuggestion.updateMany({ search: { $in: limitedDbNorm } }, { $inc: { impressions: 1 } }).catch(console.error);
        }

        return res.json({ suggestions });
    } catch {
        return res.status(500).json({ message: "Internal server error" });
    }
});

module.exports = router;

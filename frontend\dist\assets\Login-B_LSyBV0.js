import{V as e,r as t,j as s,G as n,T as o,W as r,X as a,Y as i,Z as l,$ as c,L as d,a0 as u,a1 as h}from"./vendor-B98I-pgv.js";import{u as x,v as p}from"./index-Cmi2ob6r.js";import"./utils-D3r61PVZ.js";import"./maps--fsV2DPB.js";import"./charts-gTQAinvd.js";const m=()=>{const m=e(),[j,g]=t.useState(""),[F,f]=t.useState(""),[v,S]=t.useState(!1),[b,y]=t.useState(""),[W,D]=t.useState(!1),w=t.useRef(),{login:q}=x();t.useEffect((()=>{b&&(clearTimeout(w.current),w.current=setTimeout((()=>y("")),3e3))}),[b]);return s.jsxs(n,{container:!0,flexDirection:"column",gap:4,children:[s.jsx(n,{container:!0,flexDirection:"column",color:"#FFFFFF",children:s.jsx(n,{children:s.jsx(o,{variant:"h3",fontWeight:"600",textAlign:"center",sx:{whiteSpace:"pre-line"},children:"SmartMast \n Dashboard Login"})})}),s.jsxs(n,{container:!0,flexDirection:"column",component:"form",onSubmit:async e=>{e.preventDefault(),D(!0);try{if(await q({username:j,password:F})){const e=sessionStorage.getItem("eventPath");m(e||"/dashboard/stream")}}catch(t){302===t.response?.status?m("/otp",{state:{username:j,password:F}}):y("Login failed: "+(t.response?.data?.message||t.message||JSON.stringify(t)))}finally{D(!1)}},gap:4,children:[s.jsx(n,{children:s.jsx(r,{className:"input-login",type:"text",inputProps:{autoComplete:"on"},autoComplete:"on",placeholder:"Email or Username",variant:"outlined",fullWidth:!0,value:j,onChange:e=>g(e.target.value),required:!0})}),s.jsx(n,{children:s.jsx(r,{className:"input-login",placeholder:"Password",variant:"outlined",fullWidth:!0,value:F,onChange:e=>f(e.target.value),required:!0,type:v?"text":"password",InputProps:{endAdornment:s.jsx(a,{position:"end",children:s.jsx(i,{onClick:()=>S((e=>!e)),sx:{color:"primary.contrastText"},"data-testid":"toggle-visibility-button",children:v?s.jsx(l,{}):s.jsx(c,{})})})}})}),s.jsx(n,{display:b?"block":"none",children:s.jsx(o,{color:"error",children:b})}),s.jsx(n,{children:s.jsx(d,{className:"btn-login",type:"submit",variant:"contained",color:"primary",fullWidth:!0,disabled:W,endIcon:W&&s.jsx(u,{}),children:"Sign in"})})]}),s.jsx(n,{children:s.jsx(o,{fontSize:"18px",fontWeight:600,children:s.jsx(h,{href:"/forgot-password",color:"#FFFFFF",fontWeight:"bold",sx:{textDecoration:"none",":hover":{textDecoration:"underline"}},children:"Forgot Password?"})})}),s.jsx(n,{color:"#FFFFFF",children:s.jsxs(o,{fontSize:"18px",lineHeight:"30px",fontWeight:"400",children:["Request an Account:"," ",s.jsx(h,{href:"mailto:<EMAIL>",color:"#FFFFFF",fontWeight:"bold",sx:{textDecoration:"none",":hover":{textDecoration:"underline"}},children:"<EMAIL>"})]})}),s.jsx(n,{color:"#FFFFFF",children:s.jsxs(o,{fontWeight:"light",children:["Version: ",p]})})]})};export{m as default};

import{r as e,j as o,aO as n,ac as t,x as i,ar as r,bE as s,G as a,W as l,L as d,T as c,bA as u,bb as h,bF as m,ba as g,bG as x,a0 as p,bH as f,bI as b,bd as j,bJ as _,bK as F,bw as C,b5 as w,bL as v,bM as y,R as S,ag as z,af as R,bf as k,X as M,bN as B,bO as A,ax as O}from"./vendor-B98I-pgv.js";import{u as E,t as W,E as P,e as D,p as I,a as N,D as T,m as U,b as $}from"./index-Cmi2ob6r.js";import{u as G}from"./AppHook-CvjturwY.js";import{C as L}from"./ConfirmModal-C7H-ur5S.js";import{F as H,a as V,b as q}from"./utils-D3r61PVZ.js";import{M as Q}from"./ModalContainer-CTYPbNwV.js";import{i as X}from"./validation-schemas-CuwDAm9M.js";import{M as J}from"./MultiSelect-sxo-kxyj.js";import{u as K}from"./VesselInfoHook-D_QDbUmn.js";import{u as Y}from"./GroupRegionHook-DlxDjEC4.js";import{D as Z,E as ee}from"./EditButton-DWRezK7R.js";import{D as oe}from"./DataGrid-BxMnRBo8.js";import{u as ne,C as te,D as ie,c as re,S as se,a as ae}from"./sortable.esm-NNrDZ8Px.js";import{u as le}from"./useDebounce-BI6pGekf.js";import"./maps--fsV2DPB.js";import"./charts-gTQAinvd.js";const de=({updateRoleAnchorEl:r,setUpdateRoleAnchorEl:s,roles:a,selectedUser:l,setUpdatingRole:d,onSuccess:c,organizations:u})=>{const{user:h}=E(),m=Boolean(r),[g,x]=e.useState(!1),[p,f]=e.useState({title:"",message:""}),b=({title:e,message:o})=>new Promise((n=>{f({title:e,message:o,onConfirm:()=>{n(!0),x(!1)},onCancel:()=>{n(!1),x(!1)}}),x(!0)})),j=()=>{s(null)};function _(e){return e.role_id===l.role_id||(h.role.hierarchy_number>=e.hierarchy_number||!(!u.find((e=>e._id==l.organization_id))?.is_miscellaneous||e.denied_permissions.includes(I.manageUsers)))}return o.jsxs(o.Fragment,{children:[o.jsx(n,{anchorEl:r,open:m,onClose:j,sx:{"& .MuiPaper-root":{minWidth:r?r.offsetWidth:"auto",backgroundColor:W.palette.primary.main}},anchorOrigin:{vertical:35,horizontal:0},children:a.slice().sort(((e,o)=>e.hierarchy_number-o.hierarchy_number)).map((e=>o.jsx(t,{disabled:_(e),onClick:()=>(async e=>{if(e.role_id===P.admin&&!(await b({title:"Confirm",message:o.jsxs(o.Fragment,{children:["Are you sure you want to assign ",o.jsx("b",{children:"Admin"})," role to ",o.jsx("b",{children:l.name}),"? This change is irreversible."]})})))return void j();d(l._id),D.patch(`/users/${l._id}/role`,{role_id:e.role_id},{meta:{showSnackbar:!0}}).then((()=>c&&c())).catch(console.error).finally((()=>d())),j()})(e),sx:{gap:1,"&:hover":{backgroundColor:i(W.palette.custom.darkBlue,.3)+" !important"}},children:e.role_name},e.role_id)))}),o.jsx(L,{title:p.title,message:p.message,initialState:g,onClose:p.onCancel,onConfirm:p.onConfirm,isDanger:!0})]})},ce=({showAddUser:n,setShowAddUser:c,onSuccess:u,roles:h,organizations:m,regionGroups:g})=>{const{user:x}=E(),{vesselInfo:p,fetchVesselsInfo:f}=K(),[b,j]=e.useState([]),_=r(),F=x?.hasPermissions([I.manageOrganizations]);e.useEffect((()=>{p&&Array.isArray(p)?j(p):f()}),[p,f]);const C=e.useMemo((()=>b.filter((e=>e.region_group_id)).map((e=>({...e,region_group_object:g.find((o=>o._id===e.region_group_id))}))).sort(((e,o)=>{const n=e.region_group_object?.name?.toLowerCase()||"",t=o.region_group_object?.name?.toLowerCase()||"";if(n<t)return-1;if(n>t)return 1;const i=e.name?.toLowerCase()||e.vessel_id,r=o.name?.toLowerCase()||o.vessel_id;return i.localeCompare(r)}))),[b,g]),w=(e,o)=>x.role.hierarchy_number>=e.hierarchy_number||!(!m.find((e=>e._id==o.organization_id))?.is_miscellaneous||e.denied_permissions.includes(I.manageUsers));return o.jsx(s,{open:n,onClose:()=>{},children:o.jsx(Q,{title:"Invite New User",onClose:()=>{c(!1)},children:o.jsx(H,{initialValues:{email:"",role_id:"",organization_id:F?"":x&&x.organization?x.organization._id:"",allowed_vessels:[]},validationSchema:X,onSubmit:(e,{setSubmitting:o,resetForm:n})=>{o(!0);const t={};Object.keys(e).forEach((o=>{const n=e[o];""!==n&&(t[o]=n)})),t.allowed_vessels=t.allowed_vessels.map((e=>{const o=b.find((o=>o.vessel_id===e));return o?o.vessel_id:null})).filter(Boolean),D.post("/users/invite",t,{meta:{showSnackbar:!0}}).then((()=>{u&&u(),n()})).catch(console.error).finally((()=>o(!1)))},children:({errors:e,touched:n,isSubmitting:r,values:s,setValues:c,setFieldTouched:u,setErrors:g})=>o.jsx(V,{children:o.jsxs(a,{container:!0,flexDirection:"column",gap:2,width:{xs:300,lg:500},sx:{"& .MuiFormHelperText-root":{color:i("#FFFFFF",.5)+" !important"},"& .MuiFormLabel-root":{color:"#FFFFFF !important"}},children:[o.jsx(a,{children:o.jsx(q,{as:l,required:!0,select:!0,name:"organization_id",value:s.organization_id,label:"Organization",variant:"filled",size:"small",error:n.organization_id&&Boolean(e.organization_id),helperText:n.organization_id&&e.organization_id,fullWidth:!0,disabled:!F,sx:{"& .Mui-disabled":{color:"white !important",cursor:"not-allowed !important",WebkitTextFillColor:"rgba(255, 255, 255, 0.7) !important"}},children:m.map((e=>o.jsx(t,{value:e._id,disabled:e.is_miscellaneous&&""!==s.role_id&&!h.find((e=>e.role_id===s.role_id))?.denied_permissions.includes(I.manageUsers),children:e.name},e._id)))})}),o.jsx(a,{children:o.jsx(q,{as:l,required:!0,select:!0,name:"role_id",value:s.role_id,label:"Role",variant:"filled",size:"small",disabled:!s.organization_id,error:n.role_id&&Boolean(e.role_id),helperText:s.organization_id?n.role_id&&e.role_id:"Please select organization first",fullWidth:!0,children:h.sort(((e,o)=>e.hierarchy_number-o.hierarchy_number)).map((e=>o.jsx(t,{value:e.role_id,disabled:w(e,s),children:e.role_name},e.role_id)))})}),o.jsx(a,{children:o.jsx(q,{as:l,required:!s.username,name:"email",value:s.email,type:"email",label:"Email",variant:"filled",error:n.email&&Boolean(e.email),helperText:n.email&&e.email,size:"small",fullWidth:!0,sx:{backgroundColor:"transparent","& .MuiFilledInput-root":{backgroundColor:"transparent",borderBottom:"none",border:e=>`1px solid ${e.palette.custom.borderColor}`,borderRadius:"8px"},"& .MuiInputBase-input":{padding:1.5},"& .MuiFilledInput-root::after,.MuiFilledInput-root::before":{border:"none !important"},"& .MuiInputBase-root":{backgroundColor:"transparent",borderBottom:"none",border:e=>`1px solid ${e.palette.custom.borderColor}`,borderRadius:"8px"},"& .MuiInputLabel-shrink":{display:"none"}}})}),o.jsx(a,{children:o.jsx(J,{loading:0===b.length,options:C,value:s.allowed_vessels,multiple:!0,groupBy:e=>e.region_group_object?.name,disableCloseOnSelect:!0,label:0===s.allowed_vessels.length?"Select Vessels *":`${s.allowed_vessels.length} selected *`,getOptionLabel:e=>e.name||e.vessel_id,isOptionEqualToValue:(e,o)=>o.includes(e.vessel_id),renderTags:()=>null,onChange:(e,o)=>{c((e=>({...e,allowed_vessels:o.map((e=>"string"==typeof e?e:e.vessel_id))})))},backgroundColor:_.palette.custom.darkBlue,sx:{".MuiInputBase-input":{height:24}},borderRadius:8,InputLabelStyle:{fontSize:16,opacity:.5},TextFieldProps:{error:n.allowed_vessels&&Boolean(e.allowed_vessels),helperText:n.allowed_vessels&&e.allowed_vessels,onBlur:()=>{n.allowed_vessels||u("allowed_vessels",!0,!0),0===s.allowed_vessels.length&&g({...e,allowed_vessels:"At least one vessel is required"})}}})}),o.jsx(a,{justifyContent:"center",display:"flex",children:o.jsx(d,{type:"submit",variant:"contained",disabled:!s.email||!s.role_id||!s.organization_id||0===s.allowed_vessels.length||r,sx:{backgroundColor:e=>e.palette.custom.mainBlue,color:"#FFFFFF",padding:"10px 24px"},children:"Submit"})})]})})})})})},ue=({deleteUser:e,setDeleteUser:n,setDeleting:t,onSuccess:i})=>{const r=()=>{n()};return o.jsx(s,{open:Boolean(e),onClose:r,children:o.jsx(Q,{title:"Delete User",onClose:r,children:o.jsxs(a,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:"auto"},children:[o.jsx(a,{children:o.jsxs(c,{children:['Are you sure you want to delete user "',e?.name,'"?']})}),o.jsxs(a,{container:!0,gap:1,justifyContent:"center",children:[o.jsx(a,{justifyContent:"center",display:"flex",children:o.jsx(d,{variant:"contained",onClick:r,className:"btn-cancel",children:"Cancel"})}),o.jsx(a,{justifyContent:"center",display:"flex",children:o.jsx(d,{variant:"contained",color:"error",onClick:()=>{r(),t(e._id),D.delete("/users/"+e._id,{meta:{showSnackbar:!0}}).then((()=>i&&i())).catch(console.error).finally((()=>t()))},sx:{textTransform:"none",padding:"10px 24px"},children:"Delete"})})]})]})})})},he=({showFilterModal:n,setShowFilterModal:t,roles:r,organizations:l,setFilters:p})=>{const{user:f}=E(),[b,j]=e.useState([]),[_,F]=e.useState([]),[C,w]=e.useState("both"),[v,y]=e.useState("all_time"),[S,z]=e.useState({isChanged:!1,changedRoles:[],changedOrganizations:[],changedTime:!1,changedEmail:!1,oldEmail:"",oldTime:""}),R=()=>{t(!1)};return o.jsx(s,{open:Boolean(n),onClose:R,children:o.jsxs(Q,{title:"Filter",onClose:R,showDivider:!0,children:[o.jsxs(a,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:500},maxHeight:"70vh",overflow:"auto",flexWrap:"nowrap",children:[o.jsxs(a,{children:[o.jsx(c,{variant:"h6",fontSize:"16px !important",marginBottom:2,fontWeight:500,children:"Roles"}),o.jsx(a,{container:!0,spacing:1,children:r.map((e=>o.jsx(a,{children:o.jsx(u,{label:e.role_name,disableRipple:!0,onClick:()=>{return o=e.role_id,j((e=>e.includes(o)?e.filter((e=>e!==o)):[...e,o])),void z({isChanged:!0,changedRoles:[...S.changedRoles,o]});var o},sx:{border:`1px solid ${b.includes(e.role_id)?i("#FFFFFF",.5):W.palette.custom.borderColor}`,borderRadius:"8px",color:b.includes(e.role_id)?"#FFFFFF":"#737791",backgroundColor:"transparent","&:hover":{backgroundColor:"transparent"}}})},e.role_id)))})]}),f.organization.is_internal&&o.jsxs(a,{children:[o.jsx(c,{variant:"h6",fontSize:"16px !important",marginBottom:2,fontWeight:500,children:"Organizations"}),o.jsx(a,{container:!0,spacing:1,children:l.map((e=>o.jsx(a,{children:o.jsx(u,{label:e.name,disableRipple:!0,onClick:()=>{return o=e._id,F((e=>e.includes(o)?e.filter((e=>e!==o)):[...e,o])),void z((e=>({...e,isChanged:!0,changedOrganizations:[...e.changedOrganizations||[],o]})));var o},sx:{border:`1px solid ${_.includes(e._id)?i("#FFFFFF",.5):W.palette.custom.borderColor}`,borderRadius:"8px",color:_.includes(e._id)?"#FFFFFF":"#737791",backgroundColor:"transparent","&:hover":{backgroundColor:"transparent"}}})},e._id)))})]}),o.jsxs(a,{children:[o.jsx(c,{variant:"h6",fontSize:"16px !important",marginBottom:2,fontWeight:500,children:"Filter by Email"}),o.jsx(a,{container:!0,border:`1px solid ${W.palette.custom.borderColor}`,borderRadius:"8px",padding:"3px",children:["email","no_email","both"].map((e=>o.jsx(a,{size:4,children:o.jsx(d,{onClick:()=>{z({...S,changedEmail:!0,oldEmail:C}),w(e)},sx:{width:"100%",borderRadius:"8px",color:C===e?"#FFFFFF":"#737791",outline:"none !important",textTransform:"capitalize",backgroundColor:"transparent",border:C===e?`1px solid ${W.palette.custom.borderColor}`:"none","&:hover":{backgroundColor:"transparent"}},children:e.charAt(0).toUpperCase()+e.slice(1).replace(/_/g," ")})},e)))})]}),o.jsxs(a,{children:[o.jsx(c,{variant:"h6",fontSize:"16px !important",marginBottom:2,fontWeight:500,children:"Time"}),o.jsx(h,{component:"fieldset",children:o.jsx(m,{value:v,onChange:e=>{z({...S,changedTime:!0,oldTime:v}),y(e.target.value)},children:["today","last_3_days","last_week","last_month","last_year","all_time"].map((e=>o.jsx(g,{value:e,control:o.jsx(x,{sx:{color:"#737791",opacity:.3,"&.Mui-checked":{color:"#FFFFFF",opacity:.5}}}),label:e.replace(/_/g," ").replace(/\b\w/g,(e=>e.toUpperCase())),sx:{margin:0,marginBottom:1,width:"fit-content",padding:"0 15px 0 0",border:`1px solid ${v===e?i("#FFFFFF",.5):W.palette.custom.borderColor}`,borderRadius:"8px","& .MuiTypography-root":{color:v===e?"#FFFFFF":"#737791",fontWeight:300}}},e)))})})]})]}),o.jsxs(a,{container:!0,gap:2,justifyContent:"space-between",children:[o.jsx(a,{children:o.jsx(d,{sx:{color:"#FFFFFF",textTransform:"none"},onClick:()=>{j([]),F([]),w("both"),y("all_time"),z({isChanged:!1,changedRoles:[],changedOrganizations:[],changedTime:!1,changedEmail:!1,oldEmail:"",oldTime:""}),p((e=>({...e,roles:null,organizations:null,hasEmail:null,created_after:null})))},children:"Clear filters"})}),o.jsx(a,{children:o.jsx(d,{sx:{color:"#FFFFFF",backgroundColor:W.palette.custom.mainBlue,"&:hover":{backgroundColor:W.palette.custom.mainBlue}},variant:"contained",onClick:()=>{(()=>{let e;switch(v){case"today":e=N().startOf("day").valueOf();break;case"last_3_days":e=N().subtract(3,"days").valueOf();break;case"last_week":e=N().subtract(7,"days").valueOf();break;case"last_month":e=N().subtract(30,"days").valueOf();break;case"last_year":e=N().subtract(365,"days").valueOf();break;default:e=null}p((o=>({...o,roles:b?.length?b:null,organizations:_?.length?_:null,hasEmail:"both"!==C&&C?"email"===C:null,created_after:e})))})(),z({isChanged:!1,changedRoles:[],changedOrganizations:[],changedTime:!1,changedEmail:!1,oldEmail:"",oldTime:""}),t(!1)},children:"Apply"})})]})]})})};function me({user:n,vessels:t,regionGroups:i,disabled:r}){const[s,a]=e.useState([]),[l,d]=e.useState(!1),c=e.useMemo((()=>t.filter((e=>e.region_group_id&&!1!==e.is_active)).map((e=>({...e,region_group_object:i.find((o=>o._id===e.region_group_id))}))).sort(((e,o)=>{const n=e.region_group_object?.name?.toLowerCase()||"",t=o.region_group_object?.name?.toLowerCase()||"";if(n<t)return-1;if(n>t)return 1;const i=e.name?.toLowerCase()||e.vessel_id,r=o.name?.toLowerCase()||o.vessel_id;return i.localeCompare(r)}))),[t,i]);e.useEffect((()=>{u()}),[n,t]);const u=()=>a(n.allowed_vessels||[]),h=e.useMemo((()=>{if(s.length!==(n.allowed_vessels||[]).length)return!0;const e=new Set(n.allowed_vessels||[]);return s.some((o=>!e.has(o)))}),[s,n]);return l?o.jsx(p,{}):o.jsx(J,{loading:0===t.length,options:c,value:s,disabled:r||l,multiple:!0,disableCloseOnSelect:!0,groupBy:e=>e.region_group_object?.name,label:`${(n.allowed_vessels||[]).length} selected`,getOptionLabel:e=>e.name,isOptionEqualToValue:(e,o)=>o.includes(e.vessel_id),renderTags:()=>null,onChange:(e,o)=>a(o.map((e=>"string"==typeof e?e:e.vessel_id))),onClose:()=>{h&&(()=>{d(!0);const e=s.filter((e=>t.find((o=>o.vessel_id===e))));D.patch(`/users/${n._id}/allowedVessels`,{allowed_vessels:e},{meta:{showSnackbar:!0}}).catch((e=>{u()})).finally((()=>{d(!1)}))})()}})}const ge=({updateOrganizationAnchorEl:e,setUpdateOrganizationAnchorEl:r,organizations:s,roles:a,selectedOrganizationUser:l,setUpdatingOrganization:d,onSuccess:c})=>{const u=Boolean(e),h=()=>{r(null)},m=e=>{const o=a.find((e=>e.role_id==l.role_id));return e._id===l.organization_id||e.is_miscellaneous&&!o?.denied_permissions.includes(I.manageUsers)};return o.jsx(o.Fragment,{children:o.jsx(n,{anchorEl:e,open:u,onClose:h,sx:{"& .MuiPaper-root":{minWidth:e?e.offsetWidth:"auto",backgroundColor:W.palette.primary.main}},anchorOrigin:{vertical:35,horizontal:0},children:s.map((e=>o.jsx(t,{disabled:m(e),onClick:()=>(async e=>{d(l._id),D.patch(`/users/${l._id}/organization`,{organization_id:e._id},{meta:{showSnackbar:!0}}).then((()=>c&&c())).catch(console.error).finally((()=>d())),h()})(e),sx:{gap:1,"&:hover":{backgroundColor:i(W.palette.custom.darkBlue,.3)+" !important"}},children:e.name},e._id)))})})};function xe({showAddUser:n,setShowAddUser:r,showFilterModal:s,setShowFilterModal:l,searchQuery:d}){const{user:m}=E(),{isMobile:g,timezone:x}=G(),F=T(),{vesselInfo:C,fetchVesselsInfo:w}=K(),{regions:v,fetchRegions:y}=Y(),[S,z]=e.useState({roles:null,organizations:null,vessels:null,hasEmail:null,full_name_or_email:null,created_after:null}),[R,k]=e.useState([]),[M,B]=e.useState([]),[A,O]=e.useState([]),[P,L]=e.useState([]),[H,V]=e.useState(!0),[q,Q]=e.useState(),[X,J]=e.useState(null),[ee,ne]=e.useState(),[te,ie]=e.useState(null),[re,se]=e.useState(),[ae,le]=e.useState(!1),[xe,pe]=e.useState([]),[fe,be]=e.useState(),[je,_e]=e.useState(),[Fe,Ce]=e.useState(1),[we,ve]=e.useState(10),[ye,Se]=e.useState(0),[ze,Re]=e.useState(!1),[ke,Me]=e.useState([]),Be=async(e,o=d)=>{V(!0),e&&Ce(1);const n=void 0!==o?o:d;try{const o=new URLSearchParams({...Object.fromEntries(Object.entries({...S,...n}).filter((([,e])=>null!==e&&""!==e))),page:e?1:Fe,rowsPerPage:we});if(ke.length>0){const e=ke[0];o.append("sort",e.field),o.append("sortOrder",e.sort)}const{data:t}=await D.get(`/v2/users?${o.toString()}`),{users:i,totalCount:r}=t;Array.isArray(i)&&i.length>0?(k(i),Se(r)):(k([]),Se(0),F("No data found for users",{variant:"warning"}))}catch(t){k([]),F("Something went wrong",{variant:"error"})}finally{V(!1)}};e.useEffect((()=>{Ee()}),[C]),e.useEffect((()=>{We()}),[v]),e.useEffect((()=>{const e=U(),o=()=>{Re(!0)};return e.on("users/changed",o),()=>{e.off("users/changed",o)}}),[]),e.useEffect((()=>{ze&&(Be(!1,d),Re(!1))}),[ze,Fe,S,we,d,ke]),e.useEffect((()=>{Ae(),Oe();const e=U();return e.on("roles/changed",Ae),()=>{e.off("roles/changed",Ae)}}),[]),e.useEffect((()=>{Array.isArray(R)&&R.length>0&&V(!1)}),[R]),e.useEffect((()=>{const e=Math.ceil(ye/we);Fe>e&&e>0&&Ce(1)}),[Fe,we,ye,R]),e.useEffect((()=>{Be(!0,d)}),[we,S,d,ke]),e.useEffect((()=>{Be(!1,d)}),[Fe]);const Ae=async()=>{try{const{data:e}=await D.get("/roles");Array.isArray(e)&&e.length>0?B(e):(B([]),F("No data found for roles",{variant:"warning"}))}catch(e){B([]),F("Something went wrong",{variant:"error"})}},Oe=async()=>{try{const{data:e}=await D.get("/organizations");Array.isArray(e)&&e.length>0?O(e):(O([]),F("No data found for organizations",{variant:"warning"}))}catch(e){O([]),F("Something went wrong",{variant:"error"})}},Ee=async()=>{try{if(C){const e=C.filter((e=>e.is_active));L(e)}else w()}catch(e){}},We=async()=>{v?pe(v):y()},Pe=({page:e,rowsPerPage:n,totalRows:r,onPageChange:s,onRowsPerPageChange:l})=>{const d=(e-1)*n+1,u=Math.min(e*n,r);return o.jsxs(a,{container:!0,justifyContent:{sm:"space-between",xs:"center"},alignItems:"center",padding:"10px",backgroundColor:i(W.palette.custom.offline,.08),gap:2,sx:{borderRadius:"5px"},children:[o.jsx(a,{padding:"10px 20px",size:"auto",children:o.jsx(c,{fontSize:{xs:"12px",lg:"14px"},fontWeight:600,children:`${0==u?0:d} - ${u} of ${r}`})}),o.jsx(a,{size:"auto",children:o.jsx(b,{count:Math.ceil(r/n),page:e,onChange:s,shape:"rounded",siblingCount:g?0:1,boundaryCount:1,sx:{"& .MuiButtonBase-root, .MuiPaginationItem-root":{color:"#FFFFFF",minHeight:"30px",fontSize:g?"9px":"14px",borderRadius:"8px",minWidth:"32px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:i(W.palette.custom.offline,.2)},"& .MuiButtonBase-root:hover, .MuiButtonBase-root.Mui-selected":{color:"#FFFFFF",backgroundColor:W.palette.custom.mainBlue}}})}),o.jsx(a,{justifyContent:"flex-end",display:"flex",size:"auto",children:o.jsx(h,{variant:"outlined",children:o.jsx(j,{value:n,onChange:l,sx:{"& .MuiOutlinedInput-notchedOutline":{border:"none"},"& .MuiSelect-select":{padding:"10px",fontSize:g?"12px":"16px",backgroundColor:W.palette.custom.mainBlue,borderRadius:"5px",color:"#FFFFFF",minWidth:g?0:"80px"}},children:[5,10,20].map((e=>o.jsx(t,{value:e,children:g?e:`${e} / Page`},e)))})})})]})},De=(e,o)=>{Ce(o)},Ie=e=>{ve(e.target.value),Ce(1)},Ne=e=>{if(m._id===e._id||m.role_id===e.role_id)return!0;const o=M.find((o=>o.role_id===e.role_id));return m.role?.hierarchy_number>=o?.hierarchy_number},Te=e=>{if(!e)return!0;if(m._id===e._id)return!0;const o=M.find((o=>o.role_id===e.role_id));return!o||(m.role?.hierarchy_number>=o?.hierarchy_number||!m.hasPermissions([I.manageUsers,I.manageOrganizations]))},Ue=e=>{if(m._id===e._id||m.role_id===e.role_id)return!0;const o=M.find((o=>o.role_id===e.role_id));return m.role?.hierarchy_number>=o?.hierarchy_number},$e=[{field:"name",headerName:"Name",minWidth:250,sortable:!0},{field:"email",headerName:"Email",minWidth:300,sortable:!0},{field:"role_id",headerName:"Role",minWidth:200,renderCell:e=>o.jsx(u,{disabled:Ne(e.row),onClick:o=>((e,o)=>{J(e.currentTarget),Q(o)})(o,e.row),sx:{paddingLeft:.5,paddingRight:2,display:"flex",flexDirection:"row-reverse",width:"fit-content",minWidth:{xs:"100%",xl:"50%"},borderRadius:"5px",justifyContent:"space-between"},icon:ee===e.row._id?o.jsx(p,{size:18}):o.jsx(_,{fontSize:"small",sx:{cursor:"pointer",opacity:.5}}),label:M.find((o=>o.role_id===e.row.role_id))?.role_name}),valueGetter:e=>M.find((o=>o.role_id===e))?.role_name||""},{field:"allowed_vessels",headerName:"Provisioned Vessels",minWidth:300,renderCell:({row:e})=>{const n=R.find((o=>o._id===e._id));if(!n)return null;const t=M.find((e=>e.role_id===n.role_id));return t?o.jsx("div",{style:{display:t.denied_permissions.includes(I.accessAllVessels)?"block":"none",maxWidth:200},children:o.jsx(me,{disabled:Ue(n),user:n,vessels:P,regionGroups:xe})}):null},sortable:!1,disableColumnMenu:!0},{field:"organization_id",headerName:"Organization",minWidth:200,renderCell:e=>o.jsx(u,{disabled:Te(e.row),onClick:o=>((e,o)=>{ie(e.currentTarget),se(o)})(o,e.row),sx:{paddingLeft:.5,paddingRight:2,display:"flex",flexDirection:"row-reverse",width:"fit-content",minWidth:{xs:"100%",xl:"50%"},borderRadius:"5px",justifyContent:"space-between"},icon:ae===e.row.organization_id?o.jsx(p,{size:18}):o.jsx(_,{fontSize:"small",sx:{cursor:"pointer",opacity:.5}}),label:A.find((o=>o._id===e.row.organization_id))?.name}),valueGetter:e=>A.find((o=>o._id===e))?.name||""},{field:"creation_timestamp",headerName:"Created",minWidth:150,valueGetter:e=>N(e).tz(x).format($.dateTimeFormat(m,{exclude_seconds:!0,exclude_hours:!0,exclude_minutes:!0})),sortable:!0},{field:"invited_by",headerName:"Created By",minWidth:150,valueGetter:e=>e?.name,sortable:!1,disableColumnMenu:!0},{field:"actions",headerName:"Actions",minWidth:150,renderCell:e=>o.jsx(a,{container:!0,children:o.jsx(a,{children:je===e.row._id?o.jsx(p,{size:18}):o.jsx(Z,{onClick:()=>be(e.row),disabled:Ne(e.row)})})}),sortable:!1,disableColumnMenu:!0}],Ge=e.useMemo((()=>[...$e.map((e=>({...e,filterable:!1,sortable:!1!==e.sortable,resizable:!1,disableColumnMenu:!1!==e.disableColumnMenu,disableReorder:!0,disableExport:!0,flex:1})))]),[$e]);return o.jsxs(a,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",children:[o.jsx(a,{overflow:"auto",size:"grow",children:o.jsx(oe,{loading:H,disableRowSelectionOnClick:!0,rows:R,columns:Ge,getRowId:e=>e._id,sortModel:ke,onSortModelChange:e=>{Me(e),Ce(1)},slots:{footer:()=>o.jsx(Pe,{page:Fe,rowsPerPage:we,totalRows:ye,onPageChange:De,onRowsPerPageChange:Ie}),noRowsOverlay:()=>o.jsxs(a,{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%",children:[o.jsx(f,{sx:{fontSize:"100px",color:W.palette.custom.borderColor}}),o.jsx(c,{variant:"h6",component:"div",gutterBottom:!0,color:W.palette.custom.borderColor,children:"No data available"})]})}})}),M&&re&&o.jsx(ge,{updateOrganizationAnchorEl:te,setUpdateOrganizationAnchorEl:ie,organizations:A,roles:M,selectedOrganizationUser:re,setUpdatingOrganization:le}),M&&q&&o.jsx(de,{updateRoleAnchorEl:X,setUpdateRoleAnchorEl:J,roles:M,selectedUser:q,setUpdatingRole:ne,organizations:A}),M&&o.jsx(ce,{roles:M,organizations:A,showAddUser:n,setShowAddUser:r,regionGroups:xe}),o.jsx(ue,{deleteUser:fe,setDeleteUser:be,setDeleting:_e}),o.jsx(he,{showFilterModal:s,setShowFilterModal:l,roles:M,organizations:A,setFilters:z})]})}const pe=({showAddOrganization:n,setShowAddOrganization:t,setAdding:i,onSuccess:r})=>{const[c,u]=e.useState(""),[h,m]=e.useState(""),g=()=>{t(!1),u(""),m("")};return o.jsx(s,{open:Boolean(n),onClose:g,children:o.jsx(Q,{title:"Add New Organization",onClose:g,children:o.jsxs(a,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:500},children:[o.jsx(a,{children:o.jsx(l,{value:c,onChange:e=>u(e.target.value),label:"Organization Name",variant:"filled",fullWidth:!0})}),o.jsx(a,{children:o.jsx(l,{value:h,onChange:e=>m(e.target.value),label:"Domain",variant:"filled",fullWidth:!0})}),o.jsx(a,{justifyContent:"center",display:"flex",children:o.jsx(d,{disabled:!c||!h,variant:"contained",onClick:()=>{i(!0),D.post("/organizations",{name:c,domain:h},{meta:{showSnackbar:!0}}).then((()=>{r&&r(),u(""),m(""),g()})).catch(console.error).finally((()=>i(!1)))},children:"Submit"})})]})})})},fe=({showDeleteModal:e,setShowDeleteModal:n,onDelete:t})=>{const i=()=>{n(!1)};return o.jsx(s,{open:Boolean(e),onClose:i,children:o.jsx(Q,{title:"Confirm Deletion",onClose:i,children:o.jsxs(a,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:500},children:[o.jsx(a,{children:o.jsx(c,{fontWeight:400,textAlign:"center",children:"Are you sure you want to delete this organization? This action cannot be undone."})}),o.jsxs(a,{container:!0,justifyContent:"center",gap:2,children:[o.jsx(d,{variant:"outlined",onClick:i,children:"Cancel"}),o.jsx(d,{variant:"contained",color:"error",onClick:()=>{t(),i()},children:"Delete"})]})]})})})},be=({showEditModal:n,setShowEditModal:t,organizationToEdit:i,setOrganizationToEdit:r,onSuccess:c})=>{const[u,h]=e.useState(""),[m,g]=e.useState("");e.useEffect((()=>{i&&(h(i.name||""),g(i.domain||""))}),[i]);const x=()=>{t(!1),h(""),g(""),r(null)};return o.jsx(s,{open:Boolean(n),onClose:x,children:o.jsx(Q,{title:"Edit Organization",onClose:x,children:o.jsxs(a,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:500},children:[o.jsx(a,{children:o.jsx(l,{value:u,onChange:e=>h(e.target.value),label:"Organization Name",variant:"filled",fullWidth:!0})}),o.jsx(a,{children:o.jsx(l,{value:m,onChange:e=>g(e.target.value),label:"Domain",variant:"filled",fullWidth:!0})}),o.jsx(a,{justifyContent:"center",display:"flex",children:o.jsx(d,{disabled:!u||!m,variant:"contained",onClick:()=>{D.patch(`/organizations/${i._id}`,{name:u,domain:m},{meta:{showSnackbar:!0}}).then((()=>{c&&c(),x()})).catch(console.error)},children:"Save Changes"})})]})})})};function je({showAddOrganization:n,setShowAddOrganization:r}){const{isMobile:s,timezone:l}=G(),{user:d}=E(),u=T(),[m,g]=e.useState([]),[x,p]=e.useState([]),[_,F]=e.useState(!0),[C,w]=e.useState(!1),[v,y]=e.useState(!1),[S,z]=e.useState(null),[R,k]=e.useState(!1),[M,B]=e.useState(null),[A,O]=e.useState(1),[P,I]=e.useState(10),U=async()=>{F(!0);try{const{data:e}=await D.get("/organizations");Array.isArray(e)&&e.length>0?p(e):(p([]),u("No data found for organizations",{variant:"warning"}))}catch(e){p([]),u("Something went wrong",{variant:"error"})}finally{F(!1)}};e.useEffect((()=>{U()}),[]),e.useEffect((()=>{g(x)}),[x]);const L=({page:e,rowsPerPage:n,totalRows:r,onPageChange:l,onRowsPerPageChange:d})=>{const u=(e-1)*n+1,m=Math.min(e*n,r);return o.jsx(o.Fragment,{children:o.jsxs(a,{container:!0,justifyContent:{sm:"space-between",xs:"center"},alignItems:"center",padding:"10px",backgroundColor:i(W.palette.custom.offline,.08),gap:2,sx:{borderRadius:"5px"},children:[o.jsx(a,{padding:"10px 20px",size:"auto",children:o.jsx(c,{fontSize:{xs:"12px",lg:"14px"},fontWeight:600,children:`${u} - ${m} of ${r}`})}),o.jsx(a,{size:"auto",children:o.jsx(b,{count:Math.ceil(r/n),page:e,onChange:l,shape:"rounded",siblingCount:s?0:1,boundaryCount:1,sx:{"& .MuiButtonBase-root, .MuiPaginationItem-root":{color:"#FFFFFF",minHeight:"30px",fontSize:s?"9px":"14px",borderRadius:"8px",minWidth:"32px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:i(W.palette.custom.offline,.2)},"& .MuiButtonBase-root:hover, .MuiButtonBase-root.Mui-selected":{color:"#FFFFFF",backgroundColor:W.palette.custom.mainBlue}}})}),o.jsx(a,{justifyContent:"flex-end",display:"flex",size:"auto",children:o.jsx(h,{variant:"outlined",children:o.jsx(j,{value:n,onChange:d,sx:{"& .MuiOutlinedInput-notchedOutline":{border:"none"},"& .MuiSelect-select":{padding:"10px",fontSize:s?"12px":"16px",backgroundColor:W.palette.custom.mainBlue,borderRadius:"5px",color:"#FFFFFF",minWidth:s?0:"80px"}},children:[5,10,20].map((e=>o.jsx(t,{value:e,children:s?e:`${e} / Page`},e)))})})})]})})},H=(e,o)=>{O(o)},V=e=>{I(e.target.value)},q=[...[{field:"name",headerName:"Name",minWidth:200},{field:"domain",headerName:"Domain",minWidth:200},{field:"user",headerName:"Created By",minWidth:200,valueGetter:e=>e?.name},{field:"user_count",headerName:"Total Users",minWidth:150},{field:"creation_timestamp",headerName:"Created",minWidth:200,valueGetter:e=>N(e).tz(l).format($.dateTimeFormat(d,{exclude_seconds:!0,exclude_hours:!0,exclude_minutes:!0}))},{field:"user",headerName:"Created By",valueGetter:e=>e?.name},{field:"actions",headerName:"Actions",minWidth:100,renderCell:e=>o.jsxs(a,{container:!0,justifyContent:"start",gap:1,children:[o.jsx(a,{children:o.jsx(ee,{onClick:()=>{return o=e.row,B(o),void k(!0);var o}})}),o.jsx(a,{children:o.jsx(Z,{onClick:()=>{z(e.row),y(!0)}})})]})}].map((e=>({...e,flex:1,filterable:!1,sortable:!0,resizable:!1,disableColumnMenu:!0,disableReorder:!0,disableExport:!0})))];return o.jsx(a,{container:!0,flexDirection:"column",height:"100%",children:o.jsxs(a,{overflow:"auto",size:"grow",children:[o.jsx(oe,{loading:_,disableRowSelectionOnClick:!0,rows:m.slice((A-1)*P,A*P),columns:q,getRowId:e=>e._id,slots:{footer:()=>o.jsx(L,{page:A,rowsPerPage:P,totalRows:m.length,onPageChange:H,onRowsPerPageChange:V}),noRowsOverlay:()=>o.jsxs(a,{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%",children:[o.jsx(f,{sx:{fontSize:"100px",color:W.palette.custom.borderColor}}),o.jsx(c,{variant:"h6",component:"div",gutterBottom:!0,color:W.palette.custom.borderColor,children:"No data available"})]})},getRowHeight:()=>50}),o.jsx(pe,{showAddOrganization:n,setShowAddOrganization:r,setAdding:w,onSuccess:U}),o.jsx(fe,{showDeleteModal:v,setShowDeleteModal:y,onDelete:async()=>{if(S)try{await D.delete(`/organizations/${S._id}`,{meta:{showSnackbar:!0}}),u("Organization deleted successfully",{variant:"success"}),U()}catch(e){u("Failed to delete organization",{variant:"error"})}finally{z(null)}}}),o.jsx(be,{showEditModal:R,setShowEditModal:k,organizationToEdit:M,setOrganizationToEdit:B,onSuccess:U})]})})}const _e=({showAddRole:n,setShowAddRole:t,setAdding:i,onSuccess:r})=>{const[c,u]=e.useState(""),h=()=>{t(!1),u("")};return o.jsx(s,{open:n,onClose:h,children:o.jsx(Q,{title:"Add New Role",onClose:h,children:o.jsxs(a,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:500},children:[o.jsx(a,{children:o.jsx(l,{value:c,onChange:e=>u(e.target.value),label:"Name",variant:"filled",fullWidth:!0})}),o.jsx(a,{justifyContent:"center",display:"flex",children:o.jsx(d,{disabled:!c,variant:"contained",onClick:()=>{h(),i(!0),D.post("/roles",{role_name:c},{meta:{showSnackbar:!0}}).then((()=>{r&&r(),u("")})).catch(console.error).finally((()=>i(!1)))},children:"Submit"})})]})})})},Fe=({deleteRole:e,setDeleteRole:n,setDeleting:t,onSuccess:i})=>{const r=()=>{n()};return o.jsx(s,{open:!!e,onClose:r,children:o.jsx(Q,{title:"Delete Role",onClose:r,children:o.jsxs(a,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:"auto"},children:[o.jsx(a,{children:o.jsxs(c,{children:['Are you sure you want to delete role "',e?.role_name,'"?']})}),o.jsxs(a,{container:!0,gap:1,justifyContent:"center",children:[o.jsx(a,{children:o.jsx(d,{variant:"contained",onClick:r,className:"btn-cancel",children:"Cancel"})}),o.jsx(a,{children:o.jsx(d,{variant:"contained",color:"error",onClick:()=>{r(),t(e.role_id),D.delete("/roles/"+e._id,{meta:{showSnackbar:!0}}).then((()=>i&&i())).catch(console.error).finally((()=>t()))},sx:{textTransform:"none",padding:"10px 24px"},children:"Delete"})})]})]})})})},Ce=({role:n,permissions:t,setRoles:r,setAllowReset:s,setAllowSave:d,setUpdatedRoles:h,setRowHeight:m})=>{const{user:g}=E(),x=e.useRef(),p=t.filter((e=>!n.denied_permissions.includes(e.permission_id))),[f,b]=e.useState(!1);e.useEffect((()=>{if(x.current){const e=x.current.offsetHeight+15;m(n._id,e)}}),[x.current?.offsetHeight]);const j=()=>g.role_id===n.role_id||!n.editable||g.role.hierarchy_number>=n.hierarchy_number,_=j();return o.jsx(F,{disabled:_,popupIcon:!(_||!n.editable||n.role_id===g.role_id)&&void 0,multiple:!0,options:t,getOptionLabel:e=>e.permission_name,value:p,open:!_&&f,onOpen:()=>!_&&b(!0),onClose:()=>b(!1),disableCloseOnSelect:!0,renderOption:(e,n,{selected:t})=>{const{key:r,...s}=e,l=!n.assignable||_;return o.jsxs(a,{...s,sx:{"&:hover":{backgroundColor:i(W.palette.custom.darkBlue,.3)+" !important"}},onMouseDown:e=>l&&e.preventDefault(),children:[o.jsx(w,{checked:t,sx:{marginRight:1},disabled:l}),o.jsx(c,{sx:{color:l?"gray":"inherit"},children:n.permission_name})]},r)},onChange:(e,o)=>{const i=o.filter((e=>t.some((o=>o._id===e._id&&o.assignable&&!j()))));d(!0),s(!0),r((e=>e.map((e=>(e._id===n._id&&(e.denied_permissions=t.filter((e=>!i.find((o=>o._id===e._id)))).map((e=>e.permission_id)),h((e=>e.includes(n._id)?e:[...e,n._id]))),e)))))},PopperComponent:e=>_?null:o.jsx(C,{...e,sx:{"& .MuiPaper-root":{backgroundColor:W.palette.primary.main}}}),renderTags:(e,n)=>e.map(((e,t)=>o.jsx(u,{label:e.permission_name,onDelete:_||!e.assignable?void 0:n({index:t}).onDelete,sx:{backgroundColor:"#1B1F2D",margin:.5,color:"#FFFFFF",padding:"4px",borderRadius:"5px",height:"auto",fontSize:"12px",fontWeight:"400","& .MuiChip-deleteIcon":{display:_||!e.assignable?"none":"inline-flex"},"&.Mui-disabled":{opacity:1}}},e._id||t))),renderInput:e=>o.jsx(l,{ref:x,...e,sx:{backgroundColor:W.palette.custom.darkBlue},variant:"outlined"}),label:"Add Permissions",sx:{padding:"10px","& .MuiFormControl-root":{border:`1px solid ${W.palette.custom.borderColor}`,borderRadius:"5px"}}})},we=({role:e,isDraggable:n})=>{const{attributes:t,listeners:i,setNodeRef:r,transform:s,transition:l}=ne({id:e._id,disabled:!n}),d={transform:te.Transform.toString(s),transition:l,padding:"8px",display:"flex",alignItems:"center",backgroundColor:W.palette.custom.darkBlue,border:`1px solid ${W.palette.custom.borderColor}`,borderRadius:"5px",color:n?"#FFFFFF":"grey"};return o.jsxs(a,{ref:r,style:d,container:!0,alignItems:"center",...t,...i,children:[n?o.jsx(v,{style:{marginRight:8,cursor:"grab"}}):o.jsx(y,{style:{marginRight:8,cursor:"not-allowed"}}),o.jsx(c,{sx:{minWidth:{xs:150,sm:300}},style:{borderRadius:"200px"},children:e.role_name})]})},ve=({open:n,onClose:t,roles:i,onReorder:r,fetchRoles:l,user:c})=>{const[u,h]=e.useState(i);e.useEffect((()=>{n&&h(i)}),[i,n]);const m=e=>c.role.hierarchy_number<e.hierarchy_number;return o.jsx(s,{open:n,onClose:t,children:o.jsxs(Q,{title:"Reorder Roles",onClose:t,children:[o.jsx(ie,{collisionDetection:re,onDragEnd:({active:e,over:o})=>{if(!o||e.id===o.id)return;const n=u.findIndex((o=>o._id===e.id)),t=u.findIndex((e=>e._id===o.id)),i=u[n],r=u[t];if(!(c.role.hierarchy_number>=r.hierarchy_number)&&i.hierarchy_number>c.role.hierarchy_number&&r.hierarchy_number>c.role.hierarchy_number){const e=ae(u,n,t);h(e)}},children:o.jsx(se,{items:u?.map((e=>e._id)),children:o.jsx(a,{container:!0,flexDirection:"column",gap:1,width:{xs:300,lg:500},children:u?.map((e=>o.jsx(we,{role:e,isDraggable:m(e)},e._id)))})})}),o.jsxs(a,{container:!0,justifyContent:"center",spacing:2,mt:2,children:[o.jsx(a,{children:o.jsx(d,{onClick:()=>{h(i),t()},variant:"outlined",sx:{mx:1},children:"Cancel"})}),o.jsx(a,{children:o.jsx(d,{onClick:async()=>{const e=u.map(((e,o)=>({_id:e._id,hierarchy_number:o+1})));try{await D.patch("/roles/reorder",{roles:e},{meta:{showSnackbar:!0}})}catch(o){}t()},variant:"contained",sx:{mx:1},children:"Save Order"})})]})]})})},ye=({showFilterModal:n,setShowFilterModal:t,permissions:r,roles:l,setFilteredRoles:h,setPage:m})=>{const[g,x]=e.useState([]),[p,f]=e.useState({isChanged:!1,changedPermissions:[]}),b=()=>{if(p.isChanged){const e=g.filter((e=>!p.changedPermissions.includes(e)));x(e),f({isChanged:!1,changedPermissions:[]})}t(!1)},j=()=>{let e=[...l];g.length>0&&(e=e.filter((e=>g.every((o=>!e.denied_permissions.includes(o)))))),h(e),f({isChanged:!1,changedPermissions:[]}),t(!1)};return e.useEffect((()=>{j()}),[l,r]),o.jsx(s,{open:Boolean(n),onClose:b,children:o.jsxs(Q,{title:"Filter",onClose:b,showDivider:!0,children:[o.jsx(a,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:500},maxHeight:"70vh",overflow:"auto",flexWrap:"nowrap",children:o.jsxs(a,{children:[o.jsx(c,{variant:"h6",fontSize:"16px !important",marginBottom:2,fontWeight:500,children:"Permissions"}),o.jsx(a,{container:!0,spacing:1,children:r.map((e=>o.jsx(a,{children:o.jsx(u,{label:e.permission_name,disableRipple:!0,onClick:()=>{return o=e.permission_id,x((e=>e.includes(o)?e.filter((e=>e!==o)):[...e,o])),void f({isChanged:!0,changedPermissions:[...p.changedPermissions,o]});var o},sx:{border:`1px solid ${g.includes(e.permission_id)?i("#FFFFFF",.5):W.palette.custom.borderColor}`,borderRadius:"8px",color:g.includes(e.permission_id)?"#FFFFFF":"#737791",backgroundColor:"transparent","&:hover":{backgroundColor:"transparent"}}})},e.permission_id)))})]})}),o.jsxs(a,{container:!0,gap:2,justifyContent:"space-between",marginTop:2,children:[o.jsx(a,{children:o.jsx(d,{sx:{color:"#FFFFFF",textTransform:"none"},onClick:()=>{x([]),h(l),m(1),f({isChanged:!1,changedPermissions:[]})},children:"Clear filters"})}),o.jsx(a,{children:o.jsx(d,{sx:{color:"#FFFFFF",backgroundColor:W.palette.custom.mainBlue,"&:hover":{backgroundColor:W.palette.custom.mainBlue}},variant:"contained",onClick:j,children:"Apply"})})]})]})})};function Se({showAddRole:n,setShowAddRole:r,showReorderModal:s,setShowReorderModal:l,showFilterModal:u,setShowFilterModal:m}){const{isMobile:g,timezone:x}=G(),_=T(),{user:F}=E(),[C,w]=e.useState([]),[v,y]=e.useState([]),[S,z]=e.useState([]),[R,k]=e.useState(!1),[M,B]=e.useState(!1),[A,O]=e.useState([]),[P,I]=e.useState(!1),[L,H]=e.useState(!0),[V,q]=e.useState(!1),[Q,X]=e.useState(null),[J,K]=e.useState(null),[Y,ee]=e.useState({}),[ne,te]=e.useState(1),[ie,re]=e.useState(10);e.useEffect((()=>{se(),ae();const e=U();return e.on("roles/changed",se),()=>{e.off("roles/changed",se)}}),[]),e.useEffect((()=>{Array.isArray(C)&&C.length>0&&H(!1)}),[C]),e.useEffect((()=>{const e=Math.ceil(v.length/ie);ne>e&&e>0&&te(1)}),[ne,ie,v]);const se=async()=>{try{const{data:e}=await D.get("/roles");Array.isArray(e)&&e.length>0?w(e.sort(((e,o)=>e&&void 0!==e.hierarchy_number?o&&void 0!==o.hierarchy_number?e.hierarchy_number-o.hierarchy_number:-1:1))):(w([]),_("No data found for roles",{variant:"warning"}))}catch(e){w([]),_("Something went wrong",{variant:"error"})}},ae=async()=>{try{const{data:e}=await D.get("/permissions");Array.isArray(e)&&e.length>0?z(e):(_("No data found for permissions",{variant:"warning"}),z([]))}catch(e){z([]),_("Something went wrong",{variant:"error"})}},le=()=>{se(),O([]),B(!1),k(!1)},de=()=>{I(!0);const e=C.filter((e=>!!A.includes(e._id)&&e));D.patch("roles/permissionUpdate",{roles_permissions:e},{meta:{showSnackbar:!0}}).then((()=>{O([]),B(!1),k(!1),I(!1)})).catch((e=>{I(!1),403===e.response?.status&&se()}))},ce=(e,o)=>{ee((n=>({...n,[e]:o})))},ue=({page:e,rowsPerPage:n,totalRows:r,onPageChange:s,onRowsPerPageChange:l})=>{const u=(e-1)*n+1,m=Math.min(e*n,r);return o.jsxs(o.Fragment,{children:[R&&M&&o.jsxs(a,{container:!0,justifyContent:"flex-end",alignItems:"center",marginBottom:2,padding:"10px",backgroundColor:i(W.palette.custom.offline,.08),gap:2,sx:{borderRadius:"5px"},children:[o.jsx(a,{children:o.jsx(d,{disabled:!M,onClick:le,variant:"outlined",sx:{border:`1px solid ${W.palette.custom.borderColor}`,color:"#9A9CA2",padding:"8px 40px","&:hover":{color:"#9A9CA2",border:`1px solid ${W.palette.custom.borderColor}`}},children:"Undo"})}),o.jsx(a,{children:o.jsx(d,{disabled:!R||P,startIcon:P&&o.jsx(p,{size:18}),variant:"contained",onClick:de,sx:{backgroundColor:W.palette.custom.mainBlue,color:"#FFFFFF",padding:"8px 40px","&:hover":{color:"#FFFFFF",backgroundColor:W.palette.custom.mainBlue}},children:"Save"})})]}),o.jsxs(a,{container:!0,justifyContent:{sm:"space-between",xs:"center"},alignItems:"center",padding:"10px",backgroundColor:i(W.palette.custom.offline,.08),gap:2,sx:{borderRadius:"5px"},children:[o.jsx(a,{padding:"10px 20px",size:"auto",children:o.jsx(c,{fontSize:{xs:"12px",lg:"14px"},fontWeight:600,children:`${0==m?0:u} - ${m} of ${r}`})}),o.jsx(a,{size:"auto",children:o.jsx(b,{count:Math.ceil(r/n),page:e,onChange:s,shape:"rounded",siblingCount:g?0:1,boundaryCount:1,sx:{"& .MuiButtonBase-root, .MuiPaginationItem-root":{color:"#FFFFFF",minHeight:"30px",fontSize:g?"9px":"14px",borderRadius:"8px",minWidth:"32px",display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:i(W.palette.custom.offline,.2)},"& .MuiButtonBase-root:hover, .MuiButtonBase-root.Mui-selected":{color:"#FFFFFF",backgroundColor:W.palette.custom.mainBlue}}})}),o.jsx(a,{justifyContent:"flex-end",display:"flex",size:"auto",children:o.jsx(h,{variant:"outlined",children:o.jsx(j,{value:n,onChange:l,sx:{"& .MuiOutlinedInput-notchedOutline":{border:"none"},"& .MuiSelect-select":{padding:"10px",fontSize:g?"12px":"16px",backgroundColor:W.palette.custom.mainBlue,borderRadius:"5px",color:"#FFFFFF",minWidth:g?0:"80px"}},children:[5,10,20].map((e=>o.jsx(t,{value:e,children:g?e:`${e} / Page`},e)))})})})]})]})},he=(e,o)=>{te(o)},me=e=>{re(e.target.value)},ge=[...[{field:"role_id",headerName:"ID",minWidth:100},{field:"role_name",headerName:"Role",minWidth:150},{field:"denied_permissions",headerName:"Permissions",flex:1,minWidth:300,renderCell:e=>S&&o.jsx(Ce,{setRowHeight:ce,setUpdatedRoles:O,setRoles:w,setAllowSave:k,setAllowReset:B,role:e.row,permissions:S}),valueGetter:e=>S?.filter((o=>!e.includes(o.permission_id))).map((e=>e.permission_name))||[]},{field:"creation_timestamp",headerName:"Created",minWidth:150,valueGetter:e=>N(e).tz(x).format($.dateTimeFormat(F,{exclude_seconds:!0,exclude_hours:!0,exclude_minutes:!0}))},{field:"user",headerName:"Created By",minWidth:150,valueGetter:e=>e?.name},{field:"actions",headerName:"",minWidth:50,renderCell:e=>{return o.jsx(a,{container:!0,justifyContent:"center",alignItems:"center",height:"inherit",children:o.jsx(Z,{onClick:()=>X(e.row),disabled:(n=e.row,F.role_id===n.role_id||!n.editable||F.role.hierarchy_number>=n.hierarchy_number)})});var n}}].map((e=>({...e,filterable:!1,sortable:!0,resizable:!1,disableColumnMenu:!0,disableReorder:!0,disableExport:!0})))];return o.jsxs(a,{container:!0,flexDirection:"column",height:"100%",children:[o.jsx(a,{overflow:"auto",size:"grow",children:o.jsx(oe,{loading:L,disableRowSelectionOnClick:!0,rows:v.slice((ne-1)*ie,ne*ie),columns:ge,getRowId:e=>e._id,slots:{footer:()=>o.jsx(ue,{page:ne,rowsPerPage:ie,totalRows:v.length,onPageChange:he,onRowsPerPageChange:me}),noRowsOverlay:()=>o.jsxs(a,{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",height:"100%",children:[o.jsx(f,{sx:{fontSize:"100px",color:W.palette.custom.borderColor}}),o.jsx(c,{variant:"h6",component:"div",gutterBottom:!0,color:W.palette.custom.borderColor,children:"No data available"})]})},getRowHeight:e=>Y[e.model._id]||52})}),o.jsx(_e,{showAddRole:n,setShowAddRole:r,setAdding:q,onSuccess:se}),o.jsx(Fe,{deleteRole:Q,setDeleteRole:X,setDeleting:K,onSuccess:se}),o.jsx(ve,{open:s,onClose:()=>l(!1),roles:C,onReorder:e=>{w(e)},fetchRoles:se,user:F}),o.jsx(ye,{showFilterModal:u,setShowFilterModal:m,permissions:S,roles:C,setFilteredRoles:y,setPage:te})]})}function ze(){const{user:n}=E(),[t,i]=e.useState(""),[r,s]=e.useState(!1),[l,c]=e.useState(!1),[u,h]=e.useState({}),[m,g]=e.useState(!1),[x,p]=e.useState(!1),[f,b]=e.useState(!1),[j,_]=e.useState(!1),{isMobile:F}=G(),[C,w]=S.useState(u.full_name_or_email||""),v=le(C,600);e.useEffect((()=>{void 0!==v&&v!==u.full_name_or_email&&P({target:{name:"full_name_or_email",value:v}})}),[v]),e.useEffect((()=>{u.full_name_or_email!==C&&w(u.full_name_or_email||"")}),[u.full_name_or_email]);const y=e.useMemo((()=>[{value:"users",label:"Users",component:o.jsx(xe,{showAddUser:r,setShowAddUser:s,showFilterModal:l,setShowFilterModal:c,searchQuery:u}),display:n?.hasPermissions([I.manageUsers])},{value:"roles",label:"Roles",component:o.jsx(Se,{showAddRole:m,setShowAddRole:g,showReorderModal:x,setShowReorderModal:p,showFilterModal:f,setShowFilterModal:b}),display:n?.hasPermissions([I.manageRoles])},{value:"organizations",label:"Organizations",component:o.jsx(je,{showAddOrganization:j,setShowAddOrganization:_}),display:n?.hasPermissions([I.manageOrganizations])}]),[n,r,l,m,x,f,u,j]),P=e=>{h({full_name_or_email:e.target.value})};return e.useEffect((()=>{t||i(y.find((e=>e.display))?.value||"")}),[t,y]),n&&y.some((e=>e.display))&&t&&o.jsxs(a,{container:!0,color:"#FFFFFF",flexDirection:"column",width:"100%",height:"100%",overflow:"auto",sx:{backgroundColor:W.palette.custom.darkBlue},children:[o.jsxs(a,{container:!0,padding:2,display:"flex",rowGap:2,justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",children:[o.jsx(a,{size:{xs:12,lg:3.9},children:o.jsx(z,{value:t,onChange:(e,o)=>i(o),sx:{width:"100%",padding:"4px",border:`2px solid ${W.palette.custom.borderColor}`,borderRadius:"8px",backgroundColor:"transparent","& .MuiTabs-flexContainer":{height:"100%"},"& .MuiButtonBase-root":{width:100/y.filter((e=>e.display)).length+"%",borderRadius:"8px"},"& .MuiButtonBase-root.Mui-selected":{backgroundColor:W.palette.custom.mainBlue}},children:y.filter((e=>e.display)).map((e=>o.jsx(R,{label:e.label,value:e.value,sx:{maxWidth:"none"}},e.value)))})}),o.jsxs(a,{container:!0,columnGap:2,justifyContent:"users"===t?"space-between":"flex-end",size:{xs:12,lg:8},children:["users"===t&&o.jsx(a,{size:{xs:"grow",lg:5.8},children:o.jsx(k,{type:"text",value:C,onChange:e=>{w(e.target.value)},startAdornment:o.jsx(M,{position:"start",children:o.jsx(B,{sx:{color:"#FFFFFF"}})}),placeholder:"Search by name or email",sx:{color:"#FFFFFF",width:"100%","& .MuiOutlinedInput-notchedOutline":{border:"2px solid",borderColor:W.palette.custom.borderColor+" !important",borderRadius:"8px"}}})}),"roles"===t&&o.jsxs(a,{alignItems:"center",display:"flex",justifyContent:"flex-end",gap:2,width:{xs:"100%",lg:"fit-content"},minHeight:50,size:"auto",children:[o.jsx(d,{fullWidth:F,variant:"outlined",startIcon:o.jsx("img",{src:"/icons/filter_icon.svg",width:20,height:20}),sx:{"&.MuiButtonBase-root":{borderColor:W.palette.custom.borderColor,color:"#FFFFFF",height:{xs:"100%",lg:"auto"},padding:{xs:"0",lg:"10px 20px"},fontWeight:"bold"},"& .MuiButton-icon":{marginRight:{xs:0,lg:"10px"}}},onClick:()=>b(!0),children:!F&&"Filter"}),o.jsx(d,{fullWidth:F,variant:"contained",sx:{"&.MuiButtonBase-root":{color:"#FFFFFF",height:{xs:"100%",lg:"auto"},padding:{xs:0,lg:"10px 20px"},backgroundColor:W.palette.custom.mainBlue,fontWeight:"bold"},"& .MuiButton-icon":{marginRight:{xs:0,lg:"10px"}}},startIcon:o.jsx(A,{}),onClick:()=>p(!0),children:!F&&"Reorder Roles"}),o.jsx(d,{fullWidth:F,variant:"contained",sx:{"&.MuiButtonBase-root":{color:"#FFFFFF",height:{xs:"100%",lg:"auto"},padding:{xs:0,lg:"10px 20px"},backgroundColor:W.palette.custom.mainBlue,fontWeight:"bold"},"& .MuiButton-icon":{marginRight:{xs:0,lg:"10px"}}},startIcon:o.jsx(O,{}),onClick:()=>g(!0),children:!F&&"Add New Role"})]}),"organizations"===t&&o.jsx(a,{alignItems:"center",display:"flex",justifyContent:"flex-end",gap:2,width:{xs:"100%",lg:"fit-content"},minHeight:50,size:"auto",children:o.jsx(d,{fullWidth:F,variant:"contained",sx:{"&.MuiButtonBase-root":{color:"#FFFFFF",height:{xs:"100%",lg:"auto"},padding:{xs:0,lg:"10px 20px"},backgroundColor:W.palette.custom.mainBlue,fontWeight:"bold"},"& .MuiButton-icon":{marginRight:{xs:0,lg:"10px"}}},startIcon:o.jsx(O,{}),onClick:()=>_((e=>!e)),children:!F&&"Add New Organization"})}),"users"===t&&o.jsxs(a,{alignItems:"center",display:"flex",justifyContent:"flex-end",gap:2,size:{xs:"auto",lg:5.8},children:[o.jsx(d,{variant:"outlined",startIcon:o.jsx("img",{src:"/icons/filter_icon.svg",width:20,height:20}),sx:{"&.MuiButtonBase-root":{borderColor:W.palette.custom.borderColor,height:{xs:"100%",lg:"auto"},color:"#FFFFFF",padding:{xs:"0",lg:"10px 20px"},fontWeight:"bold"},"& .MuiButton-icon":{marginRight:{xs:0,lg:"10px"}}},onClick:()=>c(!0),children:!F&&"Filter"}),o.jsx(d,{variant:"contained",sx:{"&.MuiButtonBase-root":{color:"#FFFFFF",height:{xs:"100%",lg:"auto"},padding:{xs:0,lg:"10px 20px"},backgroundColor:W.palette.custom.mainBlue,fontWeight:"bold"},"& .MuiButton-icon":{marginRight:{xs:0,lg:"10px"}}},startIcon:o.jsx(O,{}),onClick:()=>s(!0),children:!F&&"Invite User"})]})]})]}),y.filter((e=>e.display)).map((e=>o.jsx(a,{display:t!==e.value&&"none",paddingX:2,paddingBottom:2,width:"100%",size:"grow",children:e.component},e.value)))]})}export{ze as default};

const express = require("express");
const router = express.Router();
const { query } = require("express-validator");
const assignEndpointId = require("../middlewares/assignEndpointId");
const isAuthenticated = require("../middlewares/auth");
const { validateData } = require("../middlewares/validator");
const { validateError, canAccessVessel } = require("../utils/functions");
const { endpointIds } = require("../utils/endpointIds");
const limitPromise = require("../modules/pLimit");
const db = require("../modules/db");
const vesselService = require("../services/Vessel.service");
const { default: rateLimit } = require("express-rate-limit");
const compression = require("compression");
const { isValidObjectId } = require("mongoose");

const apiLimiter = rateLimit({
    windowMs: 5 * 1000,
    limit: 40,
    message: { message: "Too many requests from this IP" },
    standardHeaders: true,
    legacyHeaders: false,
    validate: {
        xForwardedForHeader: false,
        default: true,
    },
});

router.use("/", apiLimiter);
router.use(compression());

router.get(
    "/bulk",
    assignEndpointId.bind(this, endpointIds.FETCH_AUDIOS),
    isAuthenticated,
    validateData.bind(this, [
        query("vesselIds")
            .isString()
            .withMessage(`vesselIds is a required string`)
            .notEmpty()
            .withMessage(`vesselIds must be a comma-separated string`)
            .if(query("vesselIds").exists())
            .customSanitizer((v) => v.split(",").map((v) => v.trim()))
            .custom((v) => v.every((id) => isValidObjectId(id)))
            .withMessage(`vesselIds must be valid object IDs`),
        query("startTimestampISO").isISO8601().withMessage(`startTimestampISO must be a valid ISO 8601 timestamp`).optional(),
        query("endTimestampISO").isISO8601().withMessage(`endTimestampISO must be a valid ISO 8601 timestamp`).optional(),
    ]),
    async (req, res) => {
        const requestURL = req.get("Referer");
        const isSwagger = requestURL ? requestURL.includes("/docs") : false;
        let isClosed = false;

        const onClose = () => {
            isClosed = true;
        };

        res.on("close", onClose);

        try {
            const ts = new Date().getTime();
            const { vesselIds, startTimestampISO, endTimestampISO } = req.query;
            console.log(`/audio ${vesselIds}`, startTimestampISO, endTimestampISO);

            if (endTimestampISO && !startTimestampISO) {
                return res.status(400).json({ message: "startTimestampISO is required when endTimestampISO is provided" });
            }

            const vessels = await vesselService.find({ _id: { $in: vesselIds } });

            const assignedVessels = vessels.filter((vessel) => canAccessVessel(req, vessel));

            const query = {};
            query.onboard_vessel_id = { $in: assignedVessels.map((v) => v._id) };

            if (startTimestampISO) {
                const endTime = endTimestampISO || Date.now();
                query.timestamp = { $gte: new Date(startTimestampISO), $lte: new Date(endTime) };
            }
            query.host_location = { $ne: null };

            const audios = await limitPromise(async () => {
                if (isClosed) return res.end();

                console.log(`/audio querying DB`, query);

                const ts = new Date().getTime();

                const cursor = db.audio.collection("audio_files").find(query, {
                    projection: {
                        _id: 1,
                        frequency: 1,
                        timestamp: 1,
                        aws_region: 1,
                        bucket_name: 1,
                        host_location: 1,
                        onboard_vessel_id: 1,
                        audio_path: 1,
                    },
                });

                if (isSwagger) {
                    cursor.limit(20);
                }

                const result = await cursor.toArray();

                console.log(`/audio time taken to query ${new Date().getTime() - ts}`);

                const groupedAudios = result.reduce((acc, audio) => {
                    const vesselId = audio.onboard_vessel_id.toString();
                    if (!acc[vesselId]) {
                        acc[vesselId] = [];
                    }
                    acc[vesselId].push(audio);
                    return acc;
                }, {});

                vesselIds.forEach((vesselId) => {
                    if (!groupedAudios[vesselId]) {
                        groupedAudios[vesselId] = [];
                    }
                });

                return groupedAudios;
            });
            console.log(`/audio received ${audios?.length} audios`);

            if (isClosed) return res.end();

            res.json(audios);

            console.log(`/audio time taken to respond ${new Date().getTime() - ts}`);
        } catch (err) {
            validateError(err, res);
        } finally {
            res.removeListener("close", onClose);
        }
    },
);

module.exports = router;

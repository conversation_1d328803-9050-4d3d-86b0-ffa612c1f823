import { Box, Button, Grid, Typography } from "@mui/material";
import React, { useMemo, useState } from "react";
import { subMinutes } from "date-fns";
import CustomDateTimePicker from "../../../components/CustomDateTimePicker";
import dayjs from "dayjs";
import { generateReplayISOTime, userValues } from "../../../utils.js";
import { useUser } from "../../../hooks/UserHook.jsx";
import useVesselInfo from "../../../hooks/VesselInfoHook.jsx";
// import utc from 'dayjs/plugin/utc'
// import timezone from 'dayjs/plugin/timezone'

// dayjs.extend(utc)
// dayjs.extend(timezone)

const Replay = ({ handleReplayTime, onMenuUpdate, selectedStream }) => {
    const { vesselInfo } = useVesselInfo();
    const timezone = useMemo(() => {
        if (selectedStream && selectedStream.VesselId) {
            const vessel = vesselInfo.find((vessel) => vessel.vessel_id === selectedStream.VesselId);
            if (vessel && vessel.timezone) {
                console.log("Timezone: Found timezone for artifact", vessel, vessel.timezone);
                return vessel.timezone;
            } else {
                console.log("Timezone: No timezone found for artifact", vessel);
                return "UTC";
            }
        }
        return "UTC";
    }, [selectedStream, vesselInfo]);
    const { user } = useUser();
    const [fromDate, setFromDate] = useState(null);
    const [toDate, setToDate] = useState(null);
    const replays = [
        { title: "Last 1 Hour", interval: 1, time: generateReplayISOTime(60, timezone), tag: "1H", marksDuartion: 2 },
        { title: "Last 6 Hours", interval: 360, time: generateReplayISOTime(360, timezone), tag: "6H", marksDuartion: 12 },
        { title: "Last 12 Hours", interval: 720, time: generateReplayISOTime(720, timezone), tag: "12H", marksDuartion: 23 },
        { title: "Last 24 Hours", interval: 1440, time: generateReplayISOTime(1440, timezone), tag: "24H", marksDuartion: 45 },
        { title: "Last 7 Days", interval: 10080, time: generateReplayISOTime(10080, timezone), tag: "7D", marksDuartion: 315 },
        { title: "Last 30 Days", interval: 43200 - 180, time: generateReplayISOTime(43200 - 180, timezone), tag: "30D", marksDuartion: 1350 }, // Reduced 3 hours from 30 days to resolve a potential retention period issue
    ];

    const replayHours = (replay) => {
        handleReplayTime(replay);
    };
    const getMarksDuration = (durationInMinutes) => {
        // get the marks interval by dividing the totalMinutes by 30 minutes since we have 30 minutes marks duration in replays as well.
        return Math.ceil(durationInMinutes / 30);
    };
    const handleCustomReplay = () => {
        if (fromDate && toDate && dayjs(toDate).tz(timezone).diff(dayjs(fromDate).tz(timezone), "minute") <= 43200 - 180) {
            // Calculate duration and marks as before
            const duration = dayjs(toDate).tz(timezone).diff(dayjs(fromDate).tz(timezone), "minute");
            const marksDuration = getMarksDuration(duration);
            // Get ISO strings in the correct timezone
            const fromIso = dayjs(fromDate).tz(timezone).toISOString(); // ISO with offset
            const toIso = dayjs(toDate).tz(timezone).toISOString(); // ISO with offset
            handleReplayTime({
                interval: duration,
                time: fromIso,
                toDate: toIso,
                tag: `${dayjs(fromDate)
                    .tz(timezone)
                    .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))} to ${dayjs(toDate)
                    .tz(timezone)
                    .format(userValues.dateTimeFormat(user, { exclude_seconds: true }))}`,
                marksDuartion: marksDuration,
                type: "custom",
            });
            onMenuUpdate(null);
        }
    };

    const minDateTime = dayjs(subMinutes(new Date(), 43200 - 180));
    const maxDateTime = dayjs(new Date());

    return (
        <Box
            className="dashboard-step-13"
            sx={{
                minWidth: { xs: "0", md: "380px" },
                width: "100%",
                position: "relative",
            }}
        >
            {replays.map((replay, index) => (
                <Grid
                    key={index}
                    container
                    flexDirection={"row"}
                    justifyContent={"space-between"}
                    alignItems={"center"}
                    sx={{
                        display: "flex",
                        marginTop: "5px",
                        marginBottom: "5px",
                        borderBottom: "1px solid #282C39",
                        color: "#FFFFFF",
                        paddingY: 1,
                        paddingX: 2,
                    }}
                >
                    <Grid>
                        <Typography fontWeight={400} fontSize={"14px"}>
                            {replay.title}
                        </Typography>
                    </Grid>
                    <Grid>
                        <Button
                            variant="contained"
                            onClick={() => replayHours(replay)}
                            sx={{
                                fontSize: "12px",
                                padding: "5px 10px",
                                borderRadius: "4px",
                            }}
                        >
                            View Replay
                        </Button>
                    </Grid>
                </Grid>
            ))}
            <Grid
                container
                flexDirection={"column"}
                justifyContent={"space-between"}
                gap={2}
                sx={{ display: "flex", marginTop: "5px", marginBottom: "5px", color: "#FFFFFF", paddingY: 1, paddingX: 2 }}
            >
                <Grid>
                    <Grid container color={"#FFFFFF"} gap={1}>
                        <Grid container flexDirection={"column"} justifyContent={"center"} size="grow">
                            <Grid display={"flex"}>
                                <Typography
                                    textAlign={"center"}
                                    justifyContent={"center"}
                                    color={(theme) => theme.palette.custom.mediumGrey}
                                    fontSize={"16px"}
                                >
                                    From {timezone ? `(UTC ${timezone})` : ""}
                                </Typography>
                            </Grid>
                            <Grid>
                                <CustomDateTimePicker
                                    label="From Date"
                                    value={fromDate}
                                    onChange={(newValue) => setFromDate(newValue)}
                                    minDateTime={minDateTime}
                                    maxDateTime={maxDateTime}
                                />
                            </Grid>
                        </Grid>
                        <Grid container flexDirection={"column"} size="grow">
                            <Grid display={"flex"}>
                                <Typography
                                    textAlign={"center"}
                                    justifyContent={"center"}
                                    color={(theme) => theme.palette.custom.mediumGrey}
                                    fontSize={"16px"}
                                >
                                    To {timezone ? `(UTC ${timezone})` : ""}
                                </Typography>
                            </Grid>
                            <Grid>
                                <CustomDateTimePicker
                                    label="To Date"
                                    value={toDate}
                                    onChange={(newValue) => setToDate(newValue)}
                                    minDateTime={fromDate || minDateTime}
                                    maxDateTime={maxDateTime}
                                />
                            </Grid>
                        </Grid>
                    </Grid>
                </Grid>
                <Grid display={"flex"} justifyContent={"center"} size="grow">
                    <Button
                        variant="contained"
                        onClick={handleCustomReplay}
                        disabled={!(fromDate && toDate)}
                        fullWidth
                        sx={{
                            fontSize: "12px",
                            padding: "5px 10px",
                            borderRadius: "4px",
                        }}
                    >
                        View Custom Replay
                    </Button>
                </Grid>
            </Grid>
        </Box>
    );
};

export default Replay;

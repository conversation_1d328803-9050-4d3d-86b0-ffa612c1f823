require("dotenv").config();
const db = require("../modules/db");

// const collections = ["QSX0028_location", "QSX0030_location", "prototype-36_location"];
const match = { timestamp: { $gt: new Date("2025-08-15T09:00:00.000Z"), $lt: new Date("2025-08-18T11:00:00.000Z") } };

async function runTestPromises(collections) {
    console.log(`[runTestPromises] Running query...`);
    const ts = new Date().getTime();
    const results = (
        await Promise.all(
            collections.map((collection) =>
                db.locations
                    .collection(collection)
                    .find(match, { projection: { _id: 1, latitude: 1, longitude: 1, isStationary: 1, timestamp: 1 } })
                    // .withReadPreference('secondaryPreferred')
                    .toArray(),
            ),
        )
    ).flat();
    // console.log(results[0])
    console.log(`[runTestPromises] Time taken: ${new Date().getTime() - ts}ms`);
    console.log(`[runTestPromises] Received ${results.length} coordinates`);
}

async function runTestUnion(collections) {
    console.log(`[runTestUnion] Running query...`);
    const ts = new Date().getTime();

    const pipeline = [];

    pipeline.push({ $match: match });
    collections.forEach((collection, i) => {
        if (i === 0) return;
        pipeline.push({ $unionWith: { coll: collection, pipeline: [{ $match: match }] } });
    });
    pipeline.push({ $project: { _id: 1, latitude: 1, longitude: 1, isStationary: 1, timestamp: 1 } });

    const results = await db.locations.collection(collections[0]).aggregate(pipeline).toArray();

    console.log(`[runTestUnion] Time taken: ${new Date().getTime() - ts}ms`);
    console.log(`[runTestUnion] Received ${results.length} coordinates`);
}

async function runTestPromisesBatch(collections) {
    console.log(`[runTestPromisesBatch] Running query...`);
    const ts = Date.now();
    const results = [];

    await Promise.all(
        collections.map(async (collection) => {
            const cursor = db.locations
                .collection(collection)
                .find(match, {
                    projection: { _id: 1, latitude: 1, longitude: 1, isStationary: 1, timestamp: 1 },
                })
                .batchSize(5000); // smaller batches = faster handoff

            for await (const doc of cursor) {
                // process doc (e.g., push to an array, send to client stream, etc.)
                results.push(doc);
            }
        }),
    );

    console.log(`[runTestPromisesBatch] Time taken: ${Date.now() - ts}ms`);
    console.log(`[runTestPromisesBatch] Received ${results.length} coordinates`);
}

Promise.all([
    new Promise((resolve, reject) => {
        db.locations.once("open", resolve);
        db.locations.on("error", reject);
    }),
])
    .then(async () => {
        setTimeout(async () => {
            const collections = (await db.locations.db.listCollections().toArray()).map((c) => c.name).filter((c) => !c.startsWith("system."));
            console.log(collections);
            // return;
            await runTestUnion(collections);
            await runTestPromises(collections);
            await runTestPromisesBatch(collections);
            process.exit(0);
        }, 1000);
    })
    .catch((err) => {
        console.log(`❌ Database connection failed: ${err.message}`);
        process.exit(1);
    });

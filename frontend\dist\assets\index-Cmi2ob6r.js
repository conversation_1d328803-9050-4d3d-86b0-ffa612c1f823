const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/Login-B_LSyBV0.js","assets/vendor-B98I-pgv.js","assets/utils-D3r61PVZ.js","assets/maps--fsV2DPB.js","assets/charts-gTQAinvd.js","assets/VideoStream-CgWSEAbl.js","assets/AppHook-CvjturwY.js","assets/Aritfact.controller-B2VYOB-U.js","assets/Aritfact-BIOwxQGH.css","assets/index.esm-BO-krc_n.js","assets/PreviewMedia-CZ7OLmuB.js","assets/ModalContainer-CTYPbNwV.js","assets/ArtifactFlag.controller-BiYkhhNT.js","assets/S3.controller-BioajDex.js","assets/gps_socket-DcvwEa4U.js","assets/VesselInfoHook-D_QDbUmn.js","assets/GroupRegionHook-DlxDjEC4.js","assets/sortable.esm-NNrDZ8Px.js","assets/VideoStream-DAEe7WyE.css","assets/DashboardLayout-2osoQ31X.js","assets/ForgotPassword-LTV5Aymt.js","assets/ResetPassword-74pXi3yL.js","assets/HomeLayout-Bv9KfAqC.js","assets/FullMap-B5Rxz7Hm.js","assets/UserManagement-Bh8WTfyL.js","assets/ConfirmModal-C7H-ur5S.js","assets/validation-schemas-CuwDAm9M.js","assets/MultiSelect-sxo-kxyj.js","assets/EditButton-DWRezK7R.js","assets/DataGrid-BxMnRBo8.js","assets/useDebounce-BI6pGekf.js","assets/LogManagement-Bn1r1T7j.js","assets/CustomFooter-BBYHktSX.js","assets/ApiKeyManagement-Db3T96Sn.js","assets/StatisticsManagement-CA4F9GUq.js","assets/OTPInput-D_3mCMBS.js","assets/Settings-DNCNGvYf.js","assets/Signup-D5c_dVmk.js","assets/EventManagement-CX2JaNko.js","assets/EventManagement-TZGqC0AB.css","assets/NotificationManagement-BS7MozuC.js","assets/Subscription-DRiwJr0Q.js","assets/VesselManagement-CgKZPMW1.js"])))=>i.map(i=>d[i]);
import{b as e,g as t,p as n,l as r,c as i,d as o,e as s,f as a,h as c,t as l,o as u,i as d,k as h,m as f,n as p,q as m,s as g,u as b,v as y,w,x as v,y as x,r as E,z as M,j as k,A as S,B as T,_ as O,C as _,D as C,R as A,a as R,G as D,E as j,T as L,F as N,H as P,I as F,J as B,K as I,L as U,M as z,N as q,O as V,P as $,Q as W,S as H,U as Y}from"./vendor-B98I-pgv.js";import{r as G}from"./utils-D3r61PVZ.js";import{u as K}from"./maps--fsV2DPB.js";import"./charts-gTQAinvd.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var J,Z={};var X=function(){if(J)return Z;J=1;var t=e();return Z.createRoot=t.createRoot,Z.hydrateRoot=t.hydrateRoot,Z}();const Q=t(X),ee={},te=function(e,t,n){let r=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map((e=>Promise.resolve(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e}))))))};document.getElementsByTagName("link");const n=document.querySelector("meta[property=csp-nonce]"),i=n?.nonce||n?.getAttribute("nonce");r=e(t.map((e=>{if((e=function(e){return"/"+e}(e))in ee)return;ee[e]=!0;const t=e.endsWith(".css"),n=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${n}`))return;const r=document.createElement("link");return r.rel=t?"stylesheet":"modulepreload",t||(r.as="script"),r.crossOrigin="",r.href=e,i&&r.setAttribute("nonce",i),document.head.appendChild(r),t?new Promise(((t,n)=>{r.addEventListener("load",t),r.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function i(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return r.then((t=>{for(const e of t||[])"rejected"===e.status&&i(e.reason);return e().catch(i)}))};const ne=t(G()),re={VITE_API_URL:"http://localhost:5000",VITE_GOOGLE_MAPS_API_KEY:"AIzaSyCDZWBz858COlfPhlbLthSK8ZHVGSWFj_c",VITE_NODE_ENV:"dev",VITE_MICROSERVICE_GPS_SOCKET_URL:"https://microservices.quartermaster.us",stagingAndProduction:["staging","portal"],production:["portal"]};function ie(e,t){return function(){return e.apply(t,arguments)}}var oe={};const{toString:se}=Object.prototype,{getPrototypeOf:ae}=Object,{iterator:ce,toStringTag:le}=Symbol,ue=(e=>t=>{const n=se.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),de=e=>(e=e.toLowerCase(),t=>ue(t)===e),he=e=>t=>typeof t===e,{isArray:fe}=Array,pe=he("undefined");const me=de("ArrayBuffer");const ge=he("string"),be=he("function"),ye=he("number"),we=e=>null!==e&&"object"==typeof e,ve=e=>{if("object"!==ue(e))return!1;const t=ae(e);return!(null!==t&&t!==Object.prototype&&null!==Object.getPrototypeOf(t)||le in e||ce in e)},xe=de("Date"),Ee=de("File"),Me=de("Blob"),ke=de("FileList"),Se=de("URLSearchParams"),[Te,Oe,_e,Ce]=["ReadableStream","Request","Response","Headers"].map(de);function Ae(e,t,{allOwnKeys:n=!1}={}){if(null==e)return;let r,i;if("object"!=typeof e&&(e=[e]),fe(e))for(r=0,i=e.length;r<i;r++)t.call(null,e[r],r,e);else{const i=n?Object.getOwnPropertyNames(e):Object.keys(e),o=i.length;let s;for(r=0;r<o;r++)s=i[r],t.call(null,e[s],s,e)}}function Re(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,i=n.length;for(;i-- >0;)if(r=n[i],t===r.toLowerCase())return r;return null}const De="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:oe,je=e=>!pe(e)&&e!==De;const Le=(e=>t=>e&&t instanceof e)("undefined"!=typeof Uint8Array&&ae(Uint8Array)),Ne=de("HTMLFormElement"),Pe=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Fe=de("RegExp"),Be=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Ae(n,((n,i)=>{let o;!1!==(o=t(n,i,e))&&(r[i]=o||n)})),Object.defineProperties(e,r)};const Ie=de("AsyncFunction"),Ue=(ze="function"==typeof setImmediate,qe=be(De.postMessage),ze?setImmediate:qe?(Ve=`axios@${Math.random()}`,$e=[],De.addEventListener("message",(({source:e,data:t})=>{e===De&&t===Ve&&$e.length&&$e.shift()()}),!1),e=>{$e.push(e),De.postMessage(Ve,"*")}):e=>setTimeout(e));var ze,qe,Ve,$e;const We="undefined"!=typeof queueMicrotask?queueMicrotask.bind(De):"undefined"!=typeof process&&process.nextTick||Ue,He={isArray:fe,isArrayBuffer:me,isBuffer:function(e){return null!==e&&!pe(e)&&null!==e.constructor&&!pe(e.constructor)&&be(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"==typeof FormData&&e instanceof FormData||be(e.append)&&("formdata"===(t=ue(e))||"object"===t&&be(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&me(e.buffer),t},isString:ge,isNumber:ye,isBoolean:e=>!0===e||!1===e,isObject:we,isPlainObject:ve,isReadableStream:Te,isRequest:Oe,isResponse:_e,isHeaders:Ce,isUndefined:pe,isDate:xe,isFile:Ee,isBlob:Me,isRegExp:Fe,isFunction:be,isStream:e=>we(e)&&be(e.pipe),isURLSearchParams:Se,isTypedArray:Le,isFileList:ke,forEach:Ae,merge:function e(){const{caseless:t}=je(this)&&this||{},n={},r=(r,i)=>{const o=t&&Re(n,i)||i;ve(n[o])&&ve(r)?n[o]=e(n[o],r):ve(r)?n[o]=e({},r):fe(r)?n[o]=r.slice():n[o]=r};for(let i=0,o=arguments.length;i<o;i++)arguments[i]&&Ae(arguments[i],r);return n},extend:(e,t,n,{allOwnKeys:r}={})=>(Ae(t,((t,r)=>{n&&be(t)?e[r]=ie(t,n):e[r]=t}),{allOwnKeys:r}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let i,o,s;const a={};if(t=t||{},null==e)return t;do{for(i=Object.getOwnPropertyNames(e),o=i.length;o-- >0;)s=i[o],r&&!r(s,e,t)||a[s]||(t[s]=e[s],a[s]=!0);e=!1!==n&&ae(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:ue,kindOfTest:de,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(fe(e))return e;let t=e.length;if(!ye(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[ce]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:Ne,hasOwnProperty:Pe,hasOwnProp:Pe,reduceDescriptors:Be,freezeMethods:e=>{Be(e,((t,n)=>{if(be(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];be(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return fe(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:Re,global:De,isContextDefined:je,isSpecCompliantForm:function(e){return!!(e&&be(e.append)&&"FormData"===e[le]&&e[ce])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(we(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const i=fe(e)?[]:{};return Ae(e,((e,t)=>{const o=n(e,r+1);!pe(o)&&(i[t]=o)})),t[r]=void 0,i}}return e};return n(e,0)},isAsyncFn:Ie,isThenable:e=>e&&(we(e)||be(e))&&be(e.then)&&be(e.catch),setImmediate:Ue,asap:We,isIterable:e=>null!=e&&be(e[ce])};function Ye(e,t,n,r,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),i&&(this.response=i,this.status=i.status?i.status:null)}He.inherits(Ye,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:He.toJSONObject(this.config),code:this.code,status:this.status}}});const Ge=Ye.prototype,Ke={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{Ke[e]={value:e}})),Object.defineProperties(Ye,Ke),Object.defineProperty(Ge,"isAxiosError",{value:!0}),Ye.from=(e,t,n,r,i,o)=>{const s=Object.create(Ge);return He.toFlatObject(e,s,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),Ye.call(s,e.message,t,n,r,i),s.cause=e,s.name=e.name,o&&Object.assign(s,o),s};function Je(e){return He.isPlainObject(e)||He.isArray(e)}function Ze(e){return He.endsWith(e,"[]")?e.slice(0,-2):e}function Xe(e,t,n){return e?e.concat(t).map((function(e,t){return e=Ze(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const Qe=He.toFlatObject(He,{},null,(function(e){return/^is[A-Z]/.test(e)}));function et(e,t,n){if(!He.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=He.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!He.isUndefined(t[e])}))).metaTokens,i=n.visitor||l,o=n.dots,s=n.indexes,a=(n.Blob||"undefined"!=typeof Blob&&Blob)&&He.isSpecCompliantForm(t);if(!He.isFunction(i))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(He.isDate(e))return e.toISOString();if(!a&&He.isBlob(e))throw new Ye("Blob is not supported. Use a Buffer instead.");return He.isArrayBuffer(e)||He.isTypedArray(e)?a&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function l(e,n,i){let a=e;if(e&&!i&&"object"==typeof e)if(He.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(He.isArray(e)&&function(e){return He.isArray(e)&&!e.some(Je)}(e)||(He.isFileList(e)||He.endsWith(n,"[]"))&&(a=He.toArray(e)))return n=Ze(n),a.forEach((function(e,r){!He.isUndefined(e)&&null!==e&&t.append(!0===s?Xe([n],r,o):null===s?n:n+"[]",c(e))})),!1;return!!Je(e)||(t.append(Xe(i,n,o),c(e)),!1)}const u=[],d=Object.assign(Qe,{defaultVisitor:l,convertValue:c,isVisitable:Je});if(!He.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!He.isUndefined(n)){if(-1!==u.indexOf(n))throw Error("Circular reference detected in "+r.join("."));u.push(n),He.forEach(n,(function(n,o){!0===(!(He.isUndefined(n)||null===n)&&i.call(t,n,He.isString(o)?o.trim():o,r,d))&&e(n,r?r.concat(o):[o])})),u.pop()}}(e),t}function tt(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function nt(e,t){this._pairs=[],e&&et(e,this,t)}const rt=nt.prototype;function it(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function ot(e,t,n){if(!t)return e;const r=n&&n.encode||it;He.isFunction(n)&&(n={serialize:n});const i=n&&n.serialize;let o;if(o=i?i(t,n):He.isURLSearchParams(t)?t.toString():new nt(t,n).toString(r),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}rt.append=function(e,t){this._pairs.push([e,t])},rt.toString=function(e){const t=e?function(t){return e.call(this,t,tt)}:tt;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};class st{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){He.forEach(this.handlers,(function(t){null!==t&&e(t)}))}}const at={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ct={isBrowser:!0,classes:{URLSearchParams:"undefined"!=typeof URLSearchParams?URLSearchParams:nt,FormData:"undefined"!=typeof FormData?FormData:null,Blob:"undefined"!=typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},lt="undefined"!=typeof window&&"undefined"!=typeof document,ut="object"==typeof navigator&&navigator||void 0,dt=lt&&(!ut||["ReactNative","NativeScript","NS"].indexOf(ut.product)<0),ht="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,ft=lt&&window.location.href||"http://localhost",pt={...Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:lt,hasStandardBrowserEnv:dt,hasStandardBrowserWebWorkerEnv:ht,navigator:ut,origin:ft},Symbol.toStringTag,{value:"Module"})),...ct};function mt(e){function t(e,n,r,i){let o=e[i++];if("__proto__"===o)return!0;const s=Number.isFinite(+o),a=i>=e.length;if(o=!o&&He.isArray(r)?r.length:o,a)return He.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!s;r[o]&&He.isObject(r[o])||(r[o]=[]);return t(e,n,r[o],i)&&He.isArray(r[o])&&(r[o]=function(e){const t={},n=Object.keys(e);let r;const i=n.length;let o;for(r=0;r<i;r++)o=n[r],t[o]=e[o];return t}(r[o])),!s}if(He.isFormData(e)&&He.isFunction(e.entries)){const n={};return He.forEachEntry(e,((e,r)=>{t(function(e){return He.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null}const gt={transitional:at,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,i=He.isObject(e);i&&He.isHTMLForm(e)&&(e=new FormData(e));if(He.isFormData(e))return r?JSON.stringify(mt(e)):e;if(He.isArrayBuffer(e)||He.isBuffer(e)||He.isStream(e)||He.isFile(e)||He.isBlob(e)||He.isReadableStream(e))return e;if(He.isArrayBufferView(e))return e.buffer;if(He.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(i){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return et(e,new pt.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return pt.isNode&&He.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=He.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return et(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return i||r?(t.setContentType("application/json",!1),function(e,t,n){if(He.isString(e))try{return(t||JSON.parse)(e),He.trim(e)}catch(Io){if("SyntaxError"!==Io.name)throw Io}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||gt.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(He.isResponse(e)||He.isReadableStream(e))return e;if(e&&He.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(Io){if(n){if("SyntaxError"===Io.name)throw Ye.from(Io,Ye.ERR_BAD_RESPONSE,this,null,this.response);throw Io}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:pt.classes.FormData,Blob:pt.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};He.forEach(["delete","get","head","post","put","patch"],(e=>{gt.headers[e]={}}));const bt=He.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),yt=Symbol("internals");function wt(e){return e&&String(e).trim().toLowerCase()}function vt(e){return!1===e||null==e?e:He.isArray(e)?e.map(vt):String(e)}function xt(e,t,n,r,i){return He.isFunction(r)?r.call(this,t,n):(i&&(t=n),He.isString(t)?He.isString(r)?-1!==t.indexOf(r):He.isRegExp(r)?r.test(t):void 0:void 0)}let Et=class{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function i(e,t,n){const i=wt(t);if(!i)throw new Error("header name must be a non-empty string");const o=He.findKey(r,i);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||t]=vt(e))}const o=(e,t)=>He.forEach(e,((e,n)=>i(e,n,t)));if(He.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(He.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const t={};let n,r,i;return e&&e.split("\n").forEach((function(e){i=e.indexOf(":"),n=e.substring(0,i).trim().toLowerCase(),r=e.substring(i+1).trim(),!n||t[n]&&bt[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(He.isObject(e)&&He.isIterable(e)){let n,r,i={};for(const t of e){if(!He.isArray(t))throw TypeError("Object iterator must return a key-value pair");i[r=t[0]]=(n=i[r])?He.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}o(i,t)}else null!=e&&i(t,e,n);return this}get(e,t){if(e=wt(e)){const n=He.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(He.isFunction(t))return t.call(this,e,n);if(He.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=wt(e)){const n=He.findKey(this,e);return!(!n||void 0===this[n]||t&&!xt(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function i(e){if(e=wt(e)){const i=He.findKey(n,e);!i||t&&!xt(0,n[i],i,t)||(delete n[i],r=!0)}}return He.isArray(e)?e.forEach(i):i(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const i=t[n];e&&!xt(0,this[i],i,e,!0)||(delete this[i],r=!0)}return r}normalize(e){const t=this,n={};return He.forEach(this,((r,i)=>{const o=He.findKey(n,i);if(o)return t[o]=vt(r),void delete t[i];const s=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(i):String(i).trim();s!==i&&delete t[i],t[s]=vt(r),n[s]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const t=Object.create(null);return He.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&He.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,t])=>e+": "+t)).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...t){const n=new this(e);return t.forEach((e=>n.set(e))),n}static accessor(e){const t=(this[yt]=this[yt]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=wt(e);t[r]||(!function(e,t){const n=He.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,i){return this[r].call(this,t,e,n,i)},configurable:!0})}))}(n,e),t[r]=!0)}return He.isArray(e)?e.forEach(r):r(e),this}};function Mt(e,t){const n=this||gt,r=t||n,i=Et.from(r.headers);let o=r.data;return He.forEach(e,(function(e){o=e.call(n,o,i.normalize(),t?t.status:void 0)})),i.normalize(),o}function kt(e){return!(!e||!e.__CANCEL__)}function St(e,t,n){Ye.call(this,null==e?"canceled":e,Ye.ERR_CANCELED,t,n),this.name="CanceledError"}function Tt(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new Ye("Request failed with status code "+n.status,[Ye.ERR_BAD_REQUEST,Ye.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}Et.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),He.reduceDescriptors(Et.prototype,(({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(e){this[n]=e}}})),He.freezeMethods(Et),He.inherits(St,Ye,{__CANCEL__:!0});const Ot=(e,t,n=3)=>{let r=0;const i=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let i,o=0,s=0;return t=void 0!==t?t:1e3,function(a){const c=Date.now(),l=r[s];i||(i=c),n[o]=a,r[o]=c;let u=s,d=0;for(;u!==o;)d+=n[u++],u%=e;if(o=(o+1)%e,o===s&&(s=(s+1)%e),c-i<t)return;const h=l&&c-l;return h?Math.round(1e3*d/h):void 0}}(50,250);return function(e,t){let n,r,i=0,o=1e3/t;const s=(t,o=Date.now())=>{i=o,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[(...e)=>{const t=Date.now(),a=t-i;a>=o?s(e,t):(n=e,r||(r=setTimeout((()=>{r=null,s(n)}),o-a)))},()=>n&&s(n)]}((n=>{const o=n.loaded,s=n.lengthComputable?n.total:void 0,a=o-r,c=i(a);r=o;e({loaded:o,total:s,progress:s?o/s:void 0,bytes:a,rate:c||void 0,estimated:c&&s&&o<=s?(s-o)/c:void 0,event:n,lengthComputable:null!=s,[t?"download":"upload"]:!0})}),n)},_t=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},Ct=e=>(...t)=>He.asap((()=>e(...t))),At=pt.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,pt.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(pt.origin),pt.navigator&&/(msie|trident)/i.test(pt.navigator.userAgent)):()=>!0,Rt=pt.hasStandardBrowserEnv?{write(e,t,n,r,i,o){const s=[e+"="+encodeURIComponent(t)];He.isNumber(n)&&s.push("expires="+new Date(n).toGMTString()),He.isString(r)&&s.push("path="+r),He.isString(i)&&s.push("domain="+i),!0===o&&s.push("secure"),document.cookie=s.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Dt(e,t,n){let r=!function(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const jt=e=>e instanceof Et?{...e}:e;function Lt(e,t){t=t||{};const n={};function r(e,t,n,r){return He.isPlainObject(e)&&He.isPlainObject(t)?He.merge.call({caseless:r},e,t):He.isPlainObject(t)?He.merge({},t):He.isArray(t)?t.slice():t}function i(e,t,n,i){return He.isUndefined(t)?He.isUndefined(e)?void 0:r(void 0,e,0,i):r(e,t,0,i)}function o(e,t){if(!He.isUndefined(t))return r(void 0,t)}function s(e,t){return He.isUndefined(t)?He.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function a(n,i,o){return o in t?r(n,i):o in e?r(void 0,n):void 0}const c={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:a,headers:(e,t,n)=>i(jt(e),jt(t),0,!0)};return He.forEach(Object.keys(Object.assign({},e,t)),(function(r){const o=c[r]||i,s=o(e[r],t[r],r);He.isUndefined(s)&&o!==a||(n[r]=s)})),n}const Nt=e=>{const t=Lt({},e);let n,{data:r,withXSRFToken:i,xsrfHeaderName:o,xsrfCookieName:s,headers:a,auth:c}=t;if(t.headers=a=Et.from(a),t.url=ot(Dt(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),c&&a.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),He.isFormData(r))if(pt.hasStandardBrowserEnv||pt.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(n=a.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];a.setContentType([e||"multipart/form-data",...t].join("; "))}if(pt.hasStandardBrowserEnv&&(i&&He.isFunction(i)&&(i=i(t)),i||!1!==i&&At(t.url))){const e=o&&s&&Rt.read(s);e&&a.set(o,e)}return t},Pt="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=Nt(e);let i=r.data;const o=Et.from(r.headers).normalize();let s,a,c,l,u,{responseType:d,onUploadProgress:h,onDownloadProgress:f}=r;function p(){l&&l(),u&&u(),r.cancelToken&&r.cancelToken.unsubscribe(s),r.signal&&r.signal.removeEventListener("abort",s)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=Et.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());Tt((function(e){t(e),p()}),(function(e){n(e),p()}),{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new Ye("Request aborted",Ye.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new Ye("Network Error",Ye.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const i=r.transitional||at;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new Ye(t,i.clarifyTimeoutError?Ye.ETIMEDOUT:Ye.ECONNABORTED,e,m)),m=null},void 0===i&&o.setContentType(null),"setRequestHeader"in m&&He.forEach(o.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),He.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),f&&([c,u]=Ot(f,!0),m.addEventListener("progress",c)),h&&m.upload&&([a,l]=Ot(h),m.upload.addEventListener("progress",a),m.upload.addEventListener("loadend",l)),(r.cancelToken||r.signal)&&(s=t=>{m&&(n(!t||t.type?new St(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(s),r.signal&&(r.signal.aborted?s():r.signal.addEventListener("abort",s)));const b=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);b&&-1===pt.protocols.indexOf(b)?n(new Ye("Unsupported protocol "+b+":",Ye.ERR_BAD_REQUEST,e)):m.send(i||null)}))},Ft=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const i=function(e){if(!n){n=!0,s();const t=e instanceof Error?e:this.reason;r.abort(t instanceof Ye?t:new St(t instanceof Error?t.message:t))}};let o=t&&setTimeout((()=>{o=null,i(new Ye(`timeout ${t} of ms exceeded`,Ye.ETIMEDOUT))}),t);const s=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(i):e.removeEventListener("abort",i)})),e=null)};e.forEach((e=>e.addEventListener("abort",i)));const{signal:a}=r;return a.unsubscribe=()=>He.asap(s),a}},Bt=function*(e,t){let n=e.byteLength;if(n<t)return void(yield e);let r,i=0;for(;i<n;)r=i+t,yield e.slice(i,r),i=r},It=async function*(e){if(e[Symbol.asyncIterator])return void(yield*e);const t=e.getReader();try{for(;;){const{done:e,value:n}=await t.read();if(e)break;yield n}}finally{await t.cancel()}},Ut=(e,t,n,r)=>{const i=async function*(e,t){for await(const n of It(e))yield*Bt(n,t)}(e,t);let o,s=0,a=e=>{o||(o=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await i.next();if(t)return a(),void e.close();let o=r.byteLength;if(n){let e=s+=o;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw a(t),t}},cancel:e=>(a(e),i.return())},{highWaterMark:2})},zt="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,qt=zt&&"function"==typeof ReadableStream,Vt=zt&&("function"==typeof TextEncoder?($t=new TextEncoder,e=>$t.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var $t;const Wt=(e,...t)=>{try{return!!e(...t)}catch(Io){return!1}},Ht=qt&&Wt((()=>{let e=!1;const t=new Request(pt.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),Yt=qt&&Wt((()=>He.isReadableStream(new Response("").body))),Gt={stream:Yt&&(e=>e.body)};var Kt;zt&&(Kt=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!Gt[e]&&(Gt[e]=He.isFunction(Kt[e])?t=>t[e]():(t,n)=>{throw new Ye(`Response type '${e}' is not supported`,Ye.ERR_NOT_SUPPORT,n)})})));const Jt=async(e,t)=>{const n=He.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(He.isBlob(e))return e.size;if(He.isSpecCompliantForm(e)){const t=new Request(pt.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return He.isArrayBufferView(e)||He.isArrayBuffer(e)?e.byteLength:(He.isURLSearchParams(e)&&(e+=""),He.isString(e)?(await Vt(e)).byteLength:void 0)})(t):n},Zt=zt&&(async e=>{let{url:t,method:n,data:r,signal:i,cancelToken:o,timeout:s,onDownloadProgress:a,onUploadProgress:c,responseType:l,headers:u,withCredentials:d="same-origin",fetchOptions:h}=Nt(e);l=l?(l+"").toLowerCase():"text";let f,p=Ft([i,o&&o.toAbortSignal()],s);const m=p&&p.unsubscribe&&(()=>{p.unsubscribe()});let g;try{if(c&&Ht&&"get"!==n&&"head"!==n&&0!==(g=await Jt(u,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(He.isFormData(r)&&(e=n.headers.get("content-type"))&&u.setContentType(e),n.body){const[e,t]=_t(g,Ot(Ct(c)));r=Ut(n.body,65536,e,t)}}He.isString(d)||(d=d?"include":"omit");const i="credentials"in Request.prototype;f=new Request(t,{...h,signal:p,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:r,duplex:"half",credentials:i?d:void 0});let o=await fetch(f);const s=Yt&&("stream"===l||"response"===l);if(Yt&&(a||s&&m)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=o[t]}));const t=He.toFiniteNumber(o.headers.get("content-length")),[n,r]=a&&_t(t,Ot(Ct(a),!0))||[];o=new Response(Ut(o.body,65536,n,(()=>{r&&r(),m&&m()})),e)}l=l||"text";let b=await Gt[He.findKey(Gt,l)||"text"](o,e);return!s&&m&&m(),await new Promise(((t,n)=>{Tt(t,n,{data:b,headers:Et.from(o.headers),status:o.status,statusText:o.statusText,config:e,request:f})}))}catch(b){if(m&&m(),b&&"TypeError"===b.name&&/Load failed|fetch/i.test(b.message))throw Object.assign(new Ye("Network Error",Ye.ERR_NETWORK,e,f),{cause:b.cause||b});throw Ye.from(b,b&&b.code,e,f)}}),Xt={http:null,xhr:Pt,fetch:Zt};He.forEach(Xt,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(Io){}Object.defineProperty(e,"adapterName",{value:t})}}));const Qt=e=>`- ${e}`,en=e=>He.isFunction(e)||null===e||!1===e,tn=e=>{e=He.isArray(e)?e:[e];const{length:t}=e;let n,r;const i={};for(let o=0;o<t;o++){let t;if(n=e[o],r=n,!en(n)&&(r=Xt[(t=String(n)).toLowerCase()],void 0===r))throw new Ye(`Unknown adapter '${t}'`);if(r)break;i[t||"#"+o]=r}if(!r){const e=Object.entries(i).map((([e,t])=>`adapter ${e} `+(!1===t?"is not supported by the environment":"is not available in the build")));throw new Ye("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(Qt).join("\n"):" "+Qt(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r};function nn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new St(null,e)}function rn(e){nn(e),e.headers=Et.from(e.headers),e.data=Mt.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return tn(e.adapter||gt.adapter)(e).then((function(t){return nn(e),t.data=Mt.call(e,e.transformResponse,t),t.headers=Et.from(t.headers),t}),(function(t){return kt(t)||(nn(e),t&&t.response&&(t.response.data=Mt.call(e,e.transformResponse,t.response),t.response.headers=Et.from(t.response.headers))),Promise.reject(t)}))}const on="1.9.0",sn={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{sn[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const an={};sn.transitional=function(e,t,n){return(r,i,o)=>{if(!1===e)throw new Ye(function(e,t){return"[Axios v1.9.0] Transitional option '"+e+"'"+t+(n?". "+n:"")}(i," has been removed"+(t?" in "+t:"")),Ye.ERR_DEPRECATED);return t&&!an[i]&&(an[i]=!0),!e||e(r,i,o)}},sn.spelling=function(e){return(e,t)=>!0};const cn={assertOptions:function(e,t,n){if("object"!=typeof e)throw new Ye("options must be an object",Ye.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let i=r.length;for(;i-- >0;){const o=r[i],s=t[o];if(s){const t=e[o],n=void 0===t||s(t,o,e);if(!0!==n)throw new Ye("option "+o+" must be "+n,Ye.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new Ye("Unknown option "+o,Ye.ERR_BAD_OPTION)}},validators:sn},ln=cn.validators;let un=class{constructor(e){this.defaults=e||{},this.interceptors={request:new st,response:new st}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(Io){}}throw n}}_request(e,t){"string"==typeof e?(t=t||{}).url=e:t=e||{},t=Lt(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:i}=t;void 0!==n&&cn.assertOptions(n,{silentJSONParsing:ln.transitional(ln.boolean),forcedJSONParsing:ln.transitional(ln.boolean),clarifyTimeoutError:ln.transitional(ln.boolean)},!1),null!=r&&(He.isFunction(r)?t.paramsSerializer={serialize:r}:cn.assertOptions(r,{encode:ln.function,serialize:ln.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),cn.assertOptions(t,{baseUrl:ln.spelling("baseURL"),withXsrfToken:ln.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=i&&He.merge(i.common,i[t.method]);i&&He.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete i[e]})),t.headers=Et.concat(o,i);const s=[];let a=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(t)||(a=a&&e.synchronous,s.unshift(e.fulfilled,e.rejected))}));const c=[];let l;this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));let u,d=0;if(!a){const e=[rn.bind(this),void 0];for(e.unshift.apply(e,s),e.push.apply(e,c),u=e.length,l=Promise.resolve(t);d<u;)l=l.then(e[d++],e[d++]);return l}u=s.length;let h=t;for(d=0;d<u;){const e=s[d++],t=s[d++];try{h=e(h)}catch(f){t.call(this,f);break}}try{l=rn.call(this,h)}catch(f){return Promise.reject(f)}for(d=0,u=c.length;d<u;)l=l.then(c[d++],c[d++]);return l}getUri(e){return ot(Dt((e=Lt(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}};He.forEach(["delete","get","head","options"],(function(e){un.prototype[e]=function(t,n){return this.request(Lt(n||{},{method:e,url:t,data:(n||{}).data}))}})),He.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,i){return this.request(Lt(i||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}un.prototype[e]=t(),un.prototype[e+"Form"]=t(!0)}));const dn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(dn).forEach((([e,t])=>{dn[t]=e}));const hn=function e(t){const n=new un(t),r=ie(un.prototype.request,n);return He.extend(r,un.prototype,n,{allOwnKeys:!0}),He.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Lt(t,n))},r}(gt);hn.Axios=un,hn.CanceledError=St,hn.CancelToken=class e{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,i){n.reason||(n.reason=new St(e,r,i),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let t;const n=new e((function(e){t=e}));return{token:n,cancel:t}}},hn.isCancel=kt,hn.VERSION=on,hn.toFormData=et,hn.AxiosError=Ye,hn.Cancel=hn.CanceledError,hn.all=function(e){return Promise.all(e)},hn.spread=function(e){return function(t){return e.apply(null,t)}},hn.isAxiosError=function(e){return He.isObject(e)&&!0===e.isAxiosError},hn.mergeConfig=Lt,hn.AxiosHeaders=Et,hn.formToJSON=e=>mt(He.isHTMLForm(e)?new FormData(e):e),hn.getAdapter=tn,hn.HttpStatusCode=dn,hn.default=hn;const{Axios:fn,AxiosError:pn,CanceledError:mn,isCancel:gn,CancelToken:bn,VERSION:yn,all:wn,Cancel:vn,isAxiosError:xn,spread:En,toFormData:Mn,AxiosHeaders:kn,HttpStatusCode:Sn,formToJSON:Tn,getAdapter:On,mergeConfig:_n}=hn;let Cn=()=>{},An=()=>{},Rn=()=>{};const Dn=hn.create({baseURL:re.VITE_API_URL+"/api",withCredentials:!0});Dn.interceptors.request.use((e=>{if(!e.headers.Authorization){const t=localStorage.getItem("jwt_token");t&&(e.headers.Authorization=`Bearer ${t}`)}return e.signal&&e.signal.addEventListener("abort",(()=>{})),e}),(e=>Promise.reject(e))),Dn.interceptors.response.use((e=>{const{config:t}=e;return t.meta?.showSnackbar&&Rn&&Rn(e?.data?.message||"Success",{variant:"success"}),e}),(async e=>{const{config:t,response:n}=e;if("CanceledError"===e.name||"ERR_CANCELED"===e.code||e.message&&e.message.includes("canceled"))return Promise.reject(e);if(429===n?.status&&n?.headers.get("RateLimit-Reset")){const e=parseInt(n.headers.get("RateLimit-Reset"),10);return await new Promise((t=>setTimeout(t,1e3*e))),Dn(t)}if(!1!==t.meta?.showSnackbar){let t="ERR_NETWORK"===e.code?"Network Error. Please check your internet and try again":e.response?.data?.message||`Unexpected error occured (Code ${e.response?.status})`;if(n.data instanceof Blob&&"application/json"===n.data.type)try{const e=await n.data.text();t=JSON.parse(e).message||`Unexpected error occurred (Code ${n.status})`}catch{t=`Unexpected error occurred (Code ${n.status})`}An&&An(t,{variant:"error"})}return n&&401===n.status&&Cn&&Cn(),Promise.reject(e)}));const jn=e=>{Cn=e},Ln=73,Nn=79;function Pn(e){return e*(Math.PI/180)}function Fn(e){if(e<=84&&e>=72)return"X";if(e<72&&e>=-80){const t=8,n=-80;return"CDEFGHJKLMNPQRSTUVWX"[Math.floor((e-n)/t)]}return e>84||e<-80?"Z":void 0}let Bn=" ";class In{static get separator(){return Bn}static set separator(e){Bn=e}static parse(e){if(!isNaN(parseFloat(e))&&isFinite(e))return Number(e);const t=String(e).trim().replace(/^-/,"").replace(/[NSEW]$/i,"").split(/[^0-9.,]+/);if(""==t[t.length-1]&&t.splice(t.length-1),""==t)return NaN;let n=null;switch(t.length){case 3:n=t[0]/1+t[1]/60+t[2]/3600;break;case 2:n=t[0]/1+t[1]/60;break;case 1:n=t[0];break;default:return NaN}return/^-|[WS]$/i.test(e.trim())&&(n=-n),Number(n)}static toDms(e,t="d",n=void 0){if(isNaN(e))return null;if("string"==typeof e&&""==e.trim())return null;if("boolean"==typeof e)return null;if(e==1/0)return null;if(null==e)return null;if(void 0===n)switch(t){case"d":case"deg":n=4;break;case"dm":case"deg+min":n=2;break;case"dms":case"deg+min+sec":n=0;break;default:t="d",n=4}e=Math.abs(e);let r=null,i=null,o=null,s=null;switch(t){default:case"d":case"deg":i=e.toFixed(n),i<100&&(i="0"+i),i<10&&(i="0"+i),r=i+"°";break;case"dm":case"deg+min":i=Math.floor(e),o=(60*e%60).toFixed(n),60==o&&(o=(0).toFixed(n),i++),i=("000"+i).slice(-3),o<10&&(o="0"+o),r=i+"°"+In.separator+o+"′";break;case"dms":case"deg+min+sec":i=Math.floor(e),o=Math.floor(3600*e/60)%60,s=(3600*e%60).toFixed(n),60==s&&(s=(0).toFixed(n),o++),60==o&&(o=0,i++),i=("000"+i).slice(-3),o=("00"+o).slice(-2),s<10&&(s="0"+s),r=i+"°"+In.separator+o+"′"+In.separator+s+"″"}return r}static toLat(e,t,n){const r=In.toDms(In.wrap90(e),t,n);return null===r?"–":r.slice(1)+In.separator+(e<0?"S":"N")}static toLon(e,t,n){const r=In.toDms(In.wrap180(e),t,n);return null===r?"–":r+In.separator+(e<0?"W":"E")}static toBrng(e,t,n){const r=In.toDms(In.wrap360(e),t,n);return null===r?"–":r.replace("360","0")}static fromLocale(e){const t=123456.789.toLocaleString(),n={thousands:t.slice(3,4),decimal:t.slice(7,8)};return e.replace(n.thousands,"⁜").replace(n.decimal,".").replace("⁜",",")}static toLocale(e){const t=123456.789.toLocaleString(),n={thousands:t.slice(3,4),decimal:t.slice(7,8)};return e.replace(/,([0-9])/,"⁜$1").replace(".",n.decimal).replace("⁜",n.thousands)}static compassPoint(e,t=3){if(![1,2,3].includes(Number(t)))throw new RangeError(`invalid precision ‘${t}’`);e=In.wrap360(e);const n=4*2**(t-1);return["N","NNE","NE","ENE","E","ESE","SE","SSE","S","SSW","SW","WSW","W","WNW","NW","NNW"][Math.round(e*n/360)%n*16/n]}static wrap90(e){if(-90<=e&&e<=90)return e;const t=e,n=360;return 1*Math.abs(((t-90)%n+n)%n-180)-90}static wrap180(e){if(-180<=e&&e<=180)return e;const t=360;return((360*e/t-180)%t+t)%t-180}static wrap360(e){if(0<=e&&e<360)return e;const t=360;return(360*e/t%t+t)%t}}Number.prototype.toRadians=function(){return this*Math.PI/180},Number.prototype.toDegrees=function(){return 180*this/Math.PI};const Un=Math.PI;class zn{constructor(e,t){if(isNaN(e))throw new TypeError(`invalid lat ‘${e}’`);if(isNaN(t))throw new TypeError(`invalid lon ‘${t}’`);this._lat=In.wrap90(Number(e)),this._lon=In.wrap180(Number(t))}get lat(){return this._lat}get latitude(){return this._lat}set lat(e){if(this._lat=isNaN(e)?In.wrap90(In.parse(e)):In.wrap90(Number(e)),isNaN(this._lat))throw new TypeError(`invalid lat ‘${e}’`)}set latitude(e){if(this._lat=isNaN(e)?In.wrap90(In.parse(e)):In.wrap90(Number(e)),isNaN(this._lat))throw new TypeError(`invalid latitude ‘${e}’`)}get lon(){return this._lon}get lng(){return this._lon}get longitude(){return this._lon}set lon(e){if(this._lon=isNaN(e)?In.wrap180(In.parse(e)):In.wrap180(Number(e)),isNaN(this._lon))throw new TypeError(`invalid lon ‘${e}’`)}set lng(e){if(this._lon=isNaN(e)?In.wrap180(In.parse(e)):In.wrap180(Number(e)),isNaN(this._lon))throw new TypeError(`invalid lng ‘${e}’`)}set longitude(e){if(this._lon=isNaN(e)?In.wrap180(In.parse(e)):In.wrap180(Number(e)),isNaN(this._lon))throw new TypeError(`invalid longitude ‘${e}’`)}static get metresToKm(){return.001}static get metresToMiles(){return 1/1609.344}static get metresToNauticalMiles(){return 1/1852}static parse(...e){if(0==e.length)throw new TypeError("invalid (empty) point");if(null===e[0]||null===e[1])throw new TypeError("invalid (null) point");let t,n;if(2==e.length&&([t,n]=e,t=In.wrap90(In.parse(t)),n=In.wrap180(In.parse(n)),isNaN(t)||isNaN(n)))throw new TypeError(`invalid point ‘${e.toString()}’`);if(1==e.length&&"string"==typeof e[0]&&([t,n]=e[0].split(","),t=In.wrap90(In.parse(t)),n=In.wrap180(In.parse(n)),isNaN(t)||isNaN(n)))throw new TypeError(`invalid point ‘${e[0]}’`);if(1==e.length&&"object"==typeof e[0]){const r=e[0];if("Point"==r.type&&Array.isArray(r.coordinates)?[n,t]=r.coordinates:(null!=r.latitude&&(t=r.latitude),null!=r.lat&&(t=r.lat),null!=r.longitude&&(n=r.longitude),null!=r.lng&&(n=r.lng),null!=r.lon&&(n=r.lon),t=In.wrap90(In.parse(t)),n=In.wrap180(In.parse(n))),isNaN(t)||isNaN(n))throw new TypeError(`invalid point ‘${JSON.stringify(e[0])}’`)}if(isNaN(t)||isNaN(n))throw new TypeError(`invalid point ‘${e.toString()}’`);return new zn(t,n)}distanceTo(e,t=6371e3){if(e instanceof zn||(e=zn.parse(e)),isNaN(t))throw new TypeError(`invalid radius ‘${t}’`);const n=t,r=this.lat.toRadians(),i=this.lon.toRadians(),o=e.lat.toRadians(),s=o-r,a=e.lon.toRadians()-i,c=Math.sin(s/2)*Math.sin(s/2)+Math.cos(r)*Math.cos(o)*Math.sin(a/2)*Math.sin(a/2);return n*(2*Math.atan2(Math.sqrt(c),Math.sqrt(1-c)))}initialBearingTo(e){if(e instanceof zn||(e=zn.parse(e)),this.equals(e))return NaN;const t=this.lat.toRadians(),n=e.lat.toRadians(),r=(e.lon-this.lon).toRadians(),i=Math.cos(t)*Math.sin(n)-Math.sin(t)*Math.cos(n)*Math.cos(r),o=Math.sin(r)*Math.cos(n),s=Math.atan2(o,i).toDegrees();return In.wrap360(s)}finalBearingTo(e){e instanceof zn||(e=zn.parse(e));const t=e.initialBearingTo(this)+180;return In.wrap360(t)}midpointTo(e){e instanceof zn||(e=zn.parse(e));const t=this.lat.toRadians(),n=this.lon.toRadians(),r=e.lat.toRadians(),i=(e.lon-this.lon).toRadians(),o=Math.cos(t),s=0,a=Math.sin(t),c={x:o+Math.cos(r)*Math.cos(i),y:s+Math.cos(r)*Math.sin(i),z:a+Math.sin(r)},l=Math.atan2(c.z,Math.sqrt(c.x*c.x+c.y*c.y)),u=n+Math.atan2(c.y,c.x),d=l.toDegrees(),h=u.toDegrees();return new zn(d,h)}intermediatePointTo(e,t){if(e instanceof zn||(e=zn.parse(e)),this.equals(e))return new zn(this.lat,this.lon);const n=this.lat.toRadians(),r=this.lon.toRadians(),i=e.lat.toRadians(),o=e.lon.toRadians(),s=i-n,a=o-r,c=Math.sin(s/2)*Math.sin(s/2)+Math.cos(n)*Math.cos(i)*Math.sin(a/2)*Math.sin(a/2),l=2*Math.atan2(Math.sqrt(c),Math.sqrt(1-c)),u=Math.sin((1-t)*l)/Math.sin(l),d=Math.sin(t*l)/Math.sin(l),h=u*Math.cos(n)*Math.cos(r)+d*Math.cos(i)*Math.cos(o),f=u*Math.cos(n)*Math.sin(r)+d*Math.cos(i)*Math.sin(o),p=u*Math.sin(n)+d*Math.sin(i),m=Math.atan2(p,Math.sqrt(h*h+f*f)),g=Math.atan2(f,h),b=m.toDegrees(),y=g.toDegrees();return new zn(b,y)}destinationPoint(e,t,n=6371e3){const r=e/n,i=Number(t).toRadians(),o=this.lat.toRadians(),s=this.lon.toRadians(),a=Math.sin(o)*Math.cos(r)+Math.cos(o)*Math.sin(r)*Math.cos(i),c=Math.asin(a),l=Math.sin(i)*Math.sin(r)*Math.cos(o),u=Math.cos(r)-Math.sin(o)*a,d=s+Math.atan2(l,u),h=c.toDegrees(),f=d.toDegrees();return new zn(h,f)}static intersection(e,t,n,r){if(e instanceof zn||(e=zn.parse(e)),n instanceof zn||(n=zn.parse(n)),isNaN(t))throw new TypeError(`invalid brng1 ‘${t}’`);if(isNaN(r))throw new TypeError(`invalid brng2 ‘${r}’`);const i=e.lat.toRadians(),o=e.lon.toRadians(),s=n.lat.toRadians(),a=n.lon.toRadians(),c=Number(t).toRadians(),l=Number(r).toRadians(),u=s-i,d=a-o,h=2*Math.asin(Math.sqrt(Math.sin(u/2)*Math.sin(u/2)+Math.cos(i)*Math.cos(s)*Math.sin(d/2)*Math.sin(d/2)));if(Math.abs(h)<Number.EPSILON)return new zn(e.lat,e.lon);const f=(Math.sin(s)-Math.sin(i)*Math.cos(h))/(Math.sin(h)*Math.cos(i)),p=(Math.sin(i)-Math.sin(s)*Math.cos(h))/(Math.sin(h)*Math.cos(s)),m=Math.acos(Math.min(Math.max(f,-1),1)),g=Math.acos(Math.min(Math.max(p,-1),1)),b=c-(Math.sin(a-o)>0?m:2*Un-m),y=(Math.sin(a-o)>0?2*Un-g:g)-l;if(0==Math.sin(b)&&0==Math.sin(y))return null;if(Math.sin(b)*Math.sin(y)<0)return null;const w=-Math.cos(b)*Math.cos(y)+Math.sin(b)*Math.sin(y)*Math.cos(h),v=Math.atan2(Math.sin(h)*Math.sin(b)*Math.sin(y),Math.cos(y)+Math.cos(b)*w),x=Math.asin(Math.min(Math.max(Math.sin(i)*Math.cos(v)+Math.cos(i)*Math.sin(v)*Math.cos(c),-1),1)),E=o+Math.atan2(Math.sin(c)*Math.sin(v)*Math.cos(i),Math.cos(v)-Math.sin(i)*Math.sin(x)),M=x.toDegrees(),k=E.toDegrees();return new zn(M,k)}crossTrackDistanceTo(e,t,n=6371e3){e instanceof zn||(e=zn.parse(e)),t instanceof zn||(t=zn.parse(t));const r=n;if(this.equals(e))return 0;const i=e.distanceTo(this,r)/r,o=e.initialBearingTo(this).toRadians(),s=e.initialBearingTo(t).toRadians();return Math.asin(Math.sin(i)*Math.sin(o-s))*r}alongTrackDistanceTo(e,t,n=6371e3){e instanceof zn||(e=zn.parse(e)),t instanceof zn||(t=zn.parse(t));const r=n;if(this.equals(e))return 0;const i=e.distanceTo(this,r)/r,o=e.initialBearingTo(this).toRadians(),s=e.initialBearingTo(t).toRadians(),a=Math.asin(Math.sin(i)*Math.sin(o-s));return Math.acos(Math.cos(i)/Math.abs(Math.cos(a)))*Math.sign(Math.cos(s-o))*r}maxLatitude(e){const t=Number(e).toRadians(),n=this.lat.toRadians();return Math.acos(Math.abs(Math.sin(t)*Math.cos(n))).toDegrees()}static crossingParallels(e,t,n){if(e.equals(t))return null;const r=Number(n).toRadians(),i=e.lat.toRadians(),o=e.lon.toRadians(),s=t.lat.toRadians(),a=t.lon.toRadians()-o,c=Math.sin(i)*Math.cos(s)*Math.cos(r)*Math.sin(a),l=Math.sin(i)*Math.cos(s)*Math.cos(r)*Math.cos(a)-Math.cos(i)*Math.sin(s)*Math.cos(r),u=Math.cos(i)*Math.cos(s)*Math.sin(r)*Math.sin(a);if(u*u>c*c+l*l)return null;const d=Math.atan2(-l,c),h=Math.acos(u/Math.sqrt(c*c+l*l)),f=o+d+h,p=(o+d-h).toDegrees(),m=f.toDegrees();return{lon1:In.wrap180(p),lon2:In.wrap180(m)}}rhumbDistanceTo(e,t=6371e3){e instanceof zn||(e=zn.parse(e));const n=t,r=this.lat.toRadians(),i=e.lat.toRadians(),o=i-r;let s=Math.abs(e.lon-this.lon).toRadians();Math.abs(s)>Un&&(s=s>0?-(2*Un-s):2*Un+s);const a=Math.log(Math.tan(i/2+Un/4)/Math.tan(r/2+Un/4)),c=Math.abs(a)>1e-11?o/a:Math.cos(r);return Math.sqrt(o*o+c*c*s*s)*n}rhumbBearingTo(e){if(e instanceof zn||(e=zn.parse(e)),this.equals(e))return NaN;const t=this.lat.toRadians(),n=e.lat.toRadians();let r=(e.lon-this.lon).toRadians();Math.abs(r)>Un&&(r=r>0?-(2*Un-r):2*Un+r);const i=Math.log(Math.tan(n/2+Un/4)/Math.tan(t/2+Un/4)),o=Math.atan2(r,i).toDegrees();return In.wrap360(o)}rhumbDestinationPoint(e,t,n=6371e3){const r=this.lat.toRadians(),i=this.lon.toRadians(),o=Number(t).toRadians(),s=e/n,a=s*Math.cos(o);let c=r+a;Math.abs(c)>Un/2&&(c=c>0?Un-c:-Un-c);const l=Math.log(Math.tan(c/2+Un/4)/Math.tan(r/2+Un/4)),u=Math.abs(l)>1e-11?a/l:Math.cos(r),d=i+s*Math.sin(o)/u,h=c.toDegrees(),f=d.toDegrees();return new zn(h,f)}rhumbMidpointTo(e){e instanceof zn||(e=zn.parse(e));const t=this.lat.toRadians();let n=this.lon.toRadians();const r=e.lat.toRadians(),i=e.lon.toRadians();Math.abs(i-n)>Un&&(n+=2*Un);const o=(t+r)/2,s=Math.tan(Un/4+t/2),a=Math.tan(Un/4+r/2),c=Math.tan(Un/4+o/2);let l=((i-n)*Math.log(c)+n*Math.log(a)-i*Math.log(s))/Math.log(a/s);isFinite(l)||(l=(n+i)/2);const u=o.toDegrees(),d=l.toDegrees();return new zn(u,d)}static areaOf(e,t=6371e3){const n=t,r=e[0].equals(e[e.length-1]);r||e.push(e[0]);const i=e.length-1;let o=0;for(let a=0;a<i;a++){const t=e[a].lat.toRadians(),n=e[a+1].lat.toRadians(),r=(e[a+1].lon-e[a].lon).toRadians();o+=2*Math.atan2(Math.tan(r/2)*(Math.tan(t/2)+Math.tan(n/2)),1+Math.tan(t/2)*Math.tan(n/2))}(function(e){let t=0,n=e[0].initialBearingTo(e[1]);for(let i=0;i<e.length-1;i++){const r=e[i].initialBearingTo(e[i+1]),o=e[i].finalBearingTo(e[i+1]);t+=(r-n+540)%360-180,t+=(o-r+540)%360-180,n=o}const r=e[0].initialBearingTo(e[1]);t+=(r-n+540)%360-180;return Math.abs(t)<90})(e)&&(o=Math.abs(o)-2*Un);const s=Math.abs(o*n*n);return r||e.pop(),s}equals(e){return e instanceof zn||(e=zn.parse(e)),!(Math.abs(this.lat-e.lat)>Number.EPSILON)&&!(Math.abs(this.lon-e.lon)>Number.EPSILON)}toGeoJSON(){return{type:"Point",coordinates:[this.lon,this.lat]}}toString(e="d",t=void 0){if(!["d","dm","dms","n"].includes(e))throw new RangeError(`invalid format ‘${e}’`);if("n"==e)return null==t&&(t=4),`${this.lat.toFixed(t)},${this.lon.toFixed(t)}`;return`${In.toLat(this.lat,e,t)}, ${In.toLon(this.lon,e,t)}`}}const qn=ne("06/01/2024").valueOf(),Vn={"MM/DD/YYYY h:mm:ss A":"North America","DD/MM/YYYY h:mm:ss A":"Europe, Latin America, Australia/NZ, Africa, Asia","YYYY-MM-DD HH:mm:ss":"East Asia, Canada"},$n={dateTimeFormat:(e,{exclude_hours:t=!1,exclude_minutes:n=!1,exclude_seconds:r=!1}={})=>{const i=e?.date_time_format||Object.keys(Vn)[0],o=i.split(" ");if(o.length<2)return i;if(t)return o[0];if(n){const e=o[1].split(":");o[1]=e[0]}if(r){const e=o[1].split(":");o[1]=e[0]+":"+e[1]}return o.join(" ")}},Wn={zoom:3,interval:5,precision:5,journeyStart:ne(Date.now()-2592e5),journeyEnd:ne(Date.now()),datapointsDistance:3e3,polylineColors:{"683df46a073245cf0fd62bb5":g[700],"683df46b073245cf0fd62bb9":m[700],"683df46b073245cf0fd62bbc":p[700],"683df46c073245cf0fd62bbf":f[700],"683df46c073245cf0fd62bc2":h[600],"683df46f073245cf0fd62bd1":d[900],"683df470073245cf0fd62bd6":u[300],"683df471073245cf0fd62bd9":l[300],"683df473073245cf0fd62be2":c[300],"683df473073245cf0fd62be5":a[500],"683df473073245cf0fd62be8":s[500],"68402564e9b65fa69e0c042b":o[900],"684029a0e9b65fa69e0c051f":i.A700,"68554ba539865bb2cd834553":r[900],"6859570176620fae5f732a62":n[600]},insetMapUpdateInterval:1e3,dateTimeFormat:({exclude_hours:e=!1,exclude_minutes:t=!1,exclude_seconds:n=!1}={})=>`DD-MMM-YYYY${e?"":" HH"}${t?"":":mm"}${n?"":":ss"}`.trim(),timezone:"Asia/Shanghai",icons:{location:"M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7m0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5",image:"M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2M8.5 13.5l2.5 3.01L14.5 12l4.5 6H5z",video:"M10 3H14V5H19C20.1 5 21 5.9 21 7V17C21 18.1 20.1 19 19 19H14V21H10V19H5C3.9 19 3 18.1 3 17V7C3 5.9 3.9 5 5 5H10V3M17 12L13 9V15L17 12Z"},polylineTypes:{DOTTED:1,DASHED:2,SOLID:3},homePortsFilterModes:{ALL:1,ONLY_HOME_PORTS:2,ONLY_NON_HOME_PORTS:3},eventsInterval:864e5,HOME_PORT_RADIUS:500},Hn={manageRoles:100,manageUsers:200,accessAllVessels:300,viewSessionLogs:400,manageApiKeys:500,viewStatistics:600,manageNotificationsAlerts:700,manageRegionsGroups:800,additionalEmailAddressesPrivilege:900,manageOrganizations:1e3,manageVessels:1200,manageArtifacts:1300},Yn={super_admin:1,internal_admin:16},Gn=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];function Kn(e){return Gn[e]}function Jn(e){let t=1e4*Math.sin(e);return t-Math.floor(t)}function Zn(e,t=1){return`rgba(${Math.floor(256*Jn(e))}, ${Math.floor(256*Jn(e+1))}, ${Math.floor(256*Jn(e+2))}, ${t})`}const Xn=new Map;function Qn(e){if(Xn.has(e))return Xn.get(e);let t=0;for(let r=0;r<e.length;r++)t=e.charCodeAt(r)+((t<<5)-t);let n="#";for(let r=0;r<3;r++){n+=("00"+(t>>8*r&255).toString(16)).slice(-2)}return Xn.set(e,n),n}function er(e,t){return Array.from({length:(new Date(t)-new Date(e))/864e5+1},((t,n)=>new Date(new Date(e).setUTCDate(new Date(e).getUTCDate()+n)).toISOString().split("T")[0]))}function tr(e){const t=Object.entries(e).sort((([,e],[,t])=>t-e));return Object.fromEntries(t)}function nr(e){return e.includes(re.VITE_NODE_ENV)}const rr=(e,t=!1)=>{if(!e)return null;const[n,r]=e;return t?function(e,t){if(t="number"==typeof t?t:5,!Array.isArray(e))throw new TypeError("forward did not receive an array");if("string"==typeof e[0]||"string"==typeof e[1])throw new TypeError("forward received an array of strings, but it only accepts an array of numbers.");const[n,r]=e;if(n<-180||n>180)throw new TypeError("forward received an invalid longitude of "+n);if(r<-90||r>90)throw new TypeError("forward received an invalid latitude of "+r);if(r<-80||r>84)throw new TypeError(`forward received a latitude of ${r}, but this library does not support conversions of points in polar regions below 80°S and above 84°N`);return function(e,t){const n="00000"+e.easting,r="00000"+e.northing;return e.zoneNumber+e.zoneLetter+function(e,t,n){const r=function(e){let t=e%6;return 0===t&&(t=6),t}(n);return function(e,t,n){const r=n-1,i="AJSAJS".charCodeAt(r),o="AFAFAF".charCodeAt(r);let s=i+e-1,a=o+t,c=!1;return s>90&&(s=s-90+65-1,c=!0),(s===Ln||i<Ln&&s>Ln||(s>Ln||i<Ln)&&c)&&s++,(s===Nn||i<Nn&&s>Nn||(s>Nn||i<Nn)&&c)&&(s++,s===Ln&&s++),s>90&&(s=s-90+65-1),a>86?(a=a-86+65-1,c=!0):c=!1,(a===Ln||o<Ln&&a>Ln||(a>Ln||o<Ln)&&c)&&a++,(a===Nn||o<Nn&&a>Nn||(a>Nn||o<Nn)&&c)&&(a++,a===Ln&&a++),a>86&&(a=a-86+65-1),String.fromCharCode(s)+String.fromCharCode(a)}(Math.floor(e/1e5),Math.floor(t/1e5)%20,r)}(e.easting,e.northing,e.zoneNumber)+n.substr(n.length-5,t)+r.substr(r.length-5,t)}(function(e){const t=e.lat,n=e.lon,r=6378137,i=Pn(t),o=Pn(n);let s;s=Math.floor((n+180)/6)+1,180===n&&(s=60),t>=56&&t<64&&n>=3&&n<12&&(s=32),t>=72&&t<84&&(n>=0&&n<9?s=31:n>=9&&n<21?s=33:n>=21&&n<33?s=35:n>=33&&n<42&&(s=37));const a=Pn(6*(s-1)-180+3),c=r/Math.sqrt(1-.00669438*Math.sin(i)*Math.sin(i)),l=Math.tan(i)*Math.tan(i),u=.006739496752268451*Math.cos(i)*Math.cos(i),d=Math.cos(i)*(o-a),h=.9996*c*(d+(1-l+u)*d*d*d/6+(5-18*l+l*l+72*u-.39089081163157013)*d*d*d*d*d/120)+5e5;let f=.9996*(r*(.9983242984503243*i-.002514607064228144*Math.sin(2*i)+2639046602129982e-21*Math.sin(4*i)-3.418046101696858e-9*Math.sin(6*i))+c*Math.tan(i)*(d*d/2+(5-l+9*u+4*u*u)*d*d*d*d/24+(61-58*l+l*l+600*u-2.2240339282485886)*d*d*d*d*d*d/720));return t<0&&(f+=1e7),{northing:Math.trunc(f),easting:Math.trunc(h),zoneNumber:s,zoneLetter:Fn(t)}}({lat:r,lon:n}),t)}([n,r]):[r,n].map((e=>e?.toFixed(8))).join(", ")},ir=(e,t=void 0)=>{const n=document.createElement("a");n.href=URL.createObjectURL(e),t&&(n.download=t),n.click()},or=e=>{const t=e.headers["content-disposition"];let n="download.zip";if(t){const e=/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(t);null!=e&&e[1]&&(n=e[1].replace(/['"]/g,""))}ir(e.data,n)},sr=(e,t,n)=>{if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e))return"Invalid email format.";const r=e.split("@")[1],i=t.email.split("@")[1];if(!t?.hasPermissions([Hn.additionalEmailAddressesPrivilege])&&r!==i)return"Email domain does not match your domain.";return n.some((e=>e.domain===r))?"":"Email domain is not allowed."},ar=[{name:"GMT-12:00",offset:"-12:00",representative:"Etc/GMT-12"},{name:"GMT-11:00",offset:"-11:00",representative:"Pacific/Pago_Pago"},{name:"GMT-10:00",offset:"-10:00",representative:"Pacific/Honolulu"},{name:"GMT-09:00",offset:"-09:00",representative:"America/Anchorage"},{name:"GMT-08:00",offset:"-08:00",representative:"America/Los_Angeles"},{name:"GMT-07:00",offset:"-07:00",representative:"America/Denver"},{name:"GMT-06:00",offset:"-06:00",representative:"America/Costa_Rica"},{name:"GMT-05:00",offset:"-05:00",representative:"America/New_York"},{name:"GMT-04:00",offset:"-04:00",representative:"America/Santiago"},{name:"GMT-03:00",offset:"-03:00",representative:"America/Sao_Paulo"},{name:"GMT-02:00",offset:"-02:00",representative:"Atlantic/South_Georgia"},{name:"GMT-01:00",offset:"-01:00",representative:"Atlantic/Azores"},{name:"GMT+00:00",offset:"+00:00",representative:"UTC"},{name:"GMT+01:00",offset:"+01:00",representative:"Europe/Berlin"},{name:"GMT+02:00",offset:"+02:00",representative:"Africa/Cairo"},{name:"GMT+03:00",offset:"+03:00",representative:"Europe/Moscow"},{name:"GMT+04:00",offset:"+04:00",representative:"Asia/Dubai"},{name:"GMT+05:00",offset:"+05:00",representative:"Asia/Karachi"},{name:"GMT+05:30",offset:"+05:30",representative:"Asia/Kolkata"},{name:"GMT+06:00",offset:"+06:00",representative:"Asia/Dhaka"},{name:"GMT+07:00",offset:"+07:00",representative:"Asia/Bangkok"},{name:"GMT+08:00",offset:"+08:00",representative:"Asia/Shanghai"},{name:"GMT+09:00",offset:"+09:00",representative:"Asia/Tokyo"},{name:"GMT+10:00",offset:"+10:00",representative:"Australia/Sydney"},{name:"GMT+11:00",offset:"+11:00",representative:"Pacific/Noumea"},{name:"GMT+12:00",offset:"+12:00",representative:"Pacific/Auckland"}],cr=(e,t)=>e.length===t.length&&e.every(((e,n)=>e===t[n])),lr=(e,{timeInSeconds:t,totalDuration:n,referenceTime:r})=>{const i=n-t,o=Math.floor(i/86400),s=Math.floor(i%86400/3600),a=Math.floor(i%3600/60),c=Math.floor(i%60),l=ne(r.current-1e3*n).add(t,"second").format($n.dateTimeFormat(e));return{timeString:o>0?`- ${o}D ${s.toString().padStart(2,"0")}H ${a.toString().padStart(2,"0")}M ${c.toString().padStart(2,"0")}S`:`- ${s.toString().padStart(2,"0")}H ${a.toString().padStart(2,"0")}M ${c.toString().padStart(2,"0")}S`,formattedDate:l}},ur=(e,t)=>{const n=new zn(e.lat,e.lng),r=new zn(t.lat,t.lng);return n.distanceTo(r)},dr=(e,t={})=>{window.gtag&&window.gtag("event",e,t)},hr=e=>!1;function fr(e){const t=e[0],[n,r]=e.slice(1).split(":");if("00"===r)return`(${t}${parseInt(n,10)})`;{const e=parseInt(r,10)/60;return`(${t}${parseInt(n,10)+e})`}}function pr(e,t){if(e&&e.timestamp&&t.length>0){const n=t.find((t=>t.vessel_id===e.onboard_vessel_id));return n&&n.timezone?n.timezone:""}return""}const mr={FAVOURITE:1,SHARE:2,DOWNLOAD:3,FULLSCREEN:4,ARCHIVE:5,GROUP_ARCHIVE:6},gr=(e,t="UTC")=>ne().tz(t).subtract(e,"minute").toISOString(),br=(e,t)=>{const n=new Map(e.map((e=>[e._id,e]))),r=new Set,i=[];return t.forEach((e=>{if(e.length>1){const t=e.map((e=>n.get(e))).filter(Boolean);t.length&&(i.push({...t[0],isGroup:!0,groupArtifacts:t}),e.forEach((e=>r.add(e))))}else if(1===e.length){const t=n.get(e[0]);t&&(i.push({...t,isGroup:!1}),r.add(e[0]))}})),e.forEach((e=>{r.has(e._id)||i.push({...e,isGroup:!1})})),i},yr={grey:{dark:"#181818",main:"#1E293B",medium:"#BDBDBD",light:"#343B44",borderGrey:"#282C39"},green:{main:"#00CC1F"},blue:{main:"#3873E4",light:"#7090B0",dark:"#020716",secondary:p[800]},white:{main:"#FFFFFF",light:"#545454"},contrast:{primary:"#FFFFFF"}},wr='"Outfit", sans-serif',vr=b(),xr=y(b({...vr,breakpoints:{values:{xs:0,sm:600,md:900,lg:1200,xl:1536,"2xl":1900}},palette:{primary:{main:yr.grey.main,light:yr.grey.light},custom:{live:yr.green.main,offline:yr.blue.light,replay:yr.blue.secondary,unfocused:yr.white.light,mediumGrey:yr.grey.medium,darkBlue:yr.blue.dark,mainBlue:yr.blue.main,borderColor:yr.grey.borderGrey,text:yr.white.main},background:{paper:yr.grey.light,default:"#FFFFFF"}},typography:{fontFamily:wr,fontWeightLight:300,fontWeightRegular:700,fontWeightMedium:500},components:{MuiCssBaseline:{styleOverrides:{"::-webkit-scrollbar":{width:"10px",height:"10px",paddingLeft:.5},"::-webkit-scrollbar-track":{backgroundColor:"transparent"},"::-webkit-scrollbar-thumb":{backgroundColor:yr.grey.medium,borderRadius:"10px",border:"2px solid transparent",backgroundClip:"padding-box"}}},MuiFilledInput:{styleOverrides:{root:{"&:before":{borderBottomColor:yr.grey.main},"&:hover:not(.Mui-disabled):before":{borderBottomColor:yr.grey.light},"&:after":{borderBottomColor:yr.contrast.primary}}}},MuiTextField:{styleOverrides:{root:{"&:before":{borderBottomColor:"blue"},"&:after":{borderBottomColor:"blue"},"& .MuiInputLabel-root":{"&.Mui-focused":{color:yr.contrast.primary,opacity:.5}},"& .MuiInputLabel-filled":{color:yr.contrast.primary,opacity:.5},"& .MuiFilledInput-root":{color:yr.contrast.primary},"& .MuiOutlinedInput-root":{color:yr.contrast.primary},"& fieldset":{border:"none"},"&.MuiFilledInput-root":{backgroundColor:"transparent",border:`1px solid ${yr.grey.borderGrey}`,borderRadius:"8px"},"& .MuiFilledInput-input":{padding:"14px 24px"},"& .MuiFilledInput-root::after,.MuiFilledInput-root::before":{border:"none !important"},"& .MuiInputBase-root":{backgroundColor:"transparent",border:`1px solid ${yr.grey.borderGrey}`,borderRadius:"8px"},"& .MuiInputLabel-shrink":{display:"none"},"&.input-login":{borderRadius:"10px",height:"60px",border:"1px solid"+yr.grey.medium,backgroundColor:v(yr.blue.light,.08),[vr.breakpoints.up("sm")]:{height:"80px"},"& input:-webkit-autofill":{WebkitBoxShadow:`0 0 0 1000px ${v(yr.grey.main,.96)} inset`,WebkitTextFillColor:"#FFFFFF",borderRadius:0},"& .MuiInputBase-root":{paddingLeft:"20px",paddingRight:"20px",height:"80px",color:yr.contrast.primary,fontWeight:400,lineHeight:"30px",[vr.breakpoints.up("sm")]:{fontSize:"24px"}}},"&.input-signup":{borderRadius:"10px",height:"60px",border:"1px solid"+yr.grey.medium,backgroundColor:v(yr.blue.light,.08),[vr.breakpoints.up("sm")]:{height:"65px"},"& input:-webkit-autofill":{WebkitBoxShadow:`0 0 0 1000px ${v(yr.grey.main,.96)} inset`,WebkitTextFillColor:"#FFFFFF",borderRadius:0,padding:0},"& .MuiInputBase-root":{paddingLeft:"20px",paddingRight:"20px",height:"65px",color:yr.contrast.primary,fontWeight:400,lineHeight:"30px",[vr.breakpoints.up("sm")]:{fontSize:"24px"}},"& .MuiInputBase-input.Mui-disabled":{color:"white",WebkitTextFillColor:"white"}}}}},MuiButton:{defaultProps:{disableRipple:!0},styleOverrides:{root:{borderRadius:"10px",":disabled":{pointerEvents:"auto",cursor:"not-allowed"}},containedPrimary:{textTransform:"none",boxShadow:"none",backgroundColor:yr.blue.main,color:"#FFFFFF",padding:"10px 24px",":hover":{backgroundColor:yr.blue.main},":disabled":{color:yr.contrast.primary,backgroundColor:"#9A9CA2"},"&.btn-cancel":{background:"#FFFFFF",color:yr.grey.main,":hover":{background:"#FFFFFF",color:yr.grey.main}},"&.btn-login":{fontSize:"24px",lineHeight:"30px",color:yr.grey.main,fontWeight:"bold",backgroundColor:"#FFFFFF",":hover":{color:yr.contrast.primary,backgroundColor:yr.grey.light},height:"50px",[vr.breakpoints.up("sm")]:{height:"80px"}}},outlinedPrimary:{color:"#737791",padding:"10px 24px",borderColor:yr.grey.borderGrey,":disabled":{opacity:.5},":hover":{borderColor:yr.grey.borderGrey}}}},MuiSelect:{styleOverrides:{root:{color:yr.contrast.primary,borderColor:"red","& .MuiInputBase-root":{color:yr.contrast.primary,borderColor:"#FFFFFF"}},icon:{color:yr.contrast.primary}}},MuiOutlinedInput:{styleOverrides:{root:{"&.Mui-focused .MuiOutlinedInput-notchedOutline":{borderColor:yr.contrast.primary},"&:hover .MuiOutlinedInput-notchedOutline":{borderColor:yr.contrast.primary},"& .MuiOutlinedInput-notchedOutline":{borderColor:yr.contrast.primary}}}},MuiAccordion:{styleOverrides:{root:{fontFamily:wr,borderRadius:0}}},MuiAccordionSummary:{styleOverrides:{root:{fontSize:"15px",lineHeight:"15px",fontWeight:700,color:yr.contrast.primary,"& .MuiSvgIcon-root":{color:yr.contrast.primary}}}},MuiMenuItem:{styleOverrides:{root:{color:yr.contrast.primary}}},MuiSkeleton:{styleOverrides:{root:{backgroundColor:w[800]}},defaultProps:{animation:"wave"}},MuiCircularProgress:{styleOverrides:{root:{color:"inherit"}},defaultProps:{size:28}},MuiSlider:{styleOverrides:{root:{color:yr.contrast.primary,height:8},track:{border:"none",transition:"all 0.5s ease !important"},thumb:{backgroundColor:"#FFFFFF",transition:"all 0.5s ease !important",height:15,width:15},markLabel:{color:yr.grey.medium,fontSize:"12px"}}},MuiPickersDay:{styleOverrides:{root:{color:yr.contrast.primary,"&.Mui-selected":{backgroundColor:yr.grey.dark,border:"1px solid black"},"&.Mui-disabled":{color:`${yr.grey.light} !important`}}}},MuiDateCalendar:{styleOverrides:{root:{"& .MuiSvgIcon-root":{color:yr.contrast.primary}}}},MuiPickersCalendarHeader:{styleOverrides:{label:{color:yr.contrast.primary},"& .MuiSvgIcon-root":{color:yr.contrast.primary}}},MuiDayCalendar:{styleOverrides:{weekDayLabel:{color:yr.contrast.primary}}},MuiPickersYear:{styleOverrides:{yearButton:{color:yr.contrast.primary}}},MuiCheckbox:{styleOverrides:{root:{color:"inherit","&.Mui-checked":{color:yr.contrast.primary},"&.Mui-disabled":{opacity:.5}},indeterminate:{color:yr.contrast.primary+" !important"}}},MuiFormControlLabel:{styleOverrides:{label:{color:"inherit","&.Mui-disabled":{color:"inherit",opacity:.5}}}},MuiIconButton:{styleOverrides:{root:{color:"inherit","&.Mui-disabled":{color:yr.grey.light,opacity:.5}}},defaultProps:{disableRipple:!0}},MuiDateTimePicker:{defaultProps:{format:Wn.dateTimeFormat({exclude_seconds:!0})}},MuiPickersLayout:{styleOverrides:{root:{"& .MuiPickersToolbar-root":{"& .MuiTypography-root":{color:yr.contrast.primary}},"& .MuiDialogActions-root":{"& .MuiButtonBase-root":{backgroundColor:yr.contrast.primary}}},contentWrapper:{"& .MuiTimeClock-root":{"& .MuiButtonBase-root":{color:yr.contrast.primary}}}}},MuiClock:{styleOverrides:{clock:{backgroundColor:yr.contrast.primary}}},MuiTab:{styleOverrides:{root:{color:yr.contrast.primary,"&.Mui-selected":{color:yr.contrast.primary,backgroundColor:yr.grey.light}}},defaultProps:{disableRipple:!0}},MuiTabs:{styleOverrides:{root:{backgroundColor:yr.grey.main}},defaultProps:{TabIndicatorProps:{style:{display:"none"}}}},MuiDataGrid:{styleOverrides:{root:{"--DataGrid-containerBackground":"transparent","--DataGrid-rowBorderColor":"transparent",color:"inherit",borderColor:yr.grey.light,borderRadius:"10px",padding:"10px 15px",fontWeight:"400","& .MuiDataGrid-main":{display:"grid"},"& .MuiDataGrid-columnHeaderTitle":{color:yr.blue.main},[`& .${x.selectLabel}`]:{display:"block"},[`& .${x.input}`]:{display:"inline-flex"},"& .MuiDataGrid-cell:focus,.MuiDataGrid-columnHeader:focus,.MuiDataGrid-columnHeader:focus-within,.MuiDataGrid-cell:focus-within":{outline:"none"},"& .MuiDataGrid-columnSeparator":{display:"none"},"& .MuiDataGrid-columnHeaders":{backgroundColor:yr.blue.dark}},panelContent:{color:yr.contrast.primary,"& .MuiInputLabel-root":{color:"inherit"},"& .MuiFormLabel-root":{paddingLeft:"10px",paddingTop:"10px"},"& .MuiInputBase-root":{paddingLeft:"10px",paddingTop:"5px",color:yr.contrast.primary,"& .MuiButtonBase-root":{borderColor:yr.grey.main,color:yr.contrast.primary},"& .MuiChip-root":{color:yr.contrast.primary}}},columnsManagementHeader:{"& .MuiInputBase-root":{fontSize:" 14px !important",height:"auto !important"}},columnsManagement:{color:yr.contrast.primary,"& .MuiCheckbox-root":{marginLeft:4}},columnsManagementFooter:{color:yr.contrast.primary,"& .MuiButtonBase-root":{color:yr.contrast.primary,"&.MuiButton-text":{color:yr.grey.dark}},"& .MuiCheckbox-root":{marginLeft:4}},overlay:{backgroundColor:yr.grey.dark},withBorderColor:{borderColor:yr.grey.light},cell:{color:"inherit"},footerContainer:{backgroundColor:yr.grey.main,"& .MuiTablePagination-root":{color:"inherit"},"& .MuiButtonBase-root":{color:"inherit"}}}},MuiChip:{defaultProps:{color:"primary"}},MuiList:{styleOverrides:{root:{"& .MuiListItemIcon-root .MuiSvgIcon-root":{color:yr.contrast.primary}}}},MuiAutocomplete:{styleOverrides:{popper:{"& .MuiAutocomplete-listbox":{color:yr.contrast.primary}}}}}})),Er=E.createContext();var Mr,kr,Sr={},Tr={exports:{}},Or=Tr.exports;function _r(){return Mr||(Mr=1,e=Tr,t=Tr.exports,function(n,r){var i="function",o="undefined",s="object",a="string",c="major",l="model",u="name",d="type",h="vendor",f="version",p="architecture",m="console",g="mobile",b="tablet",y="smarttv",w="wearable",v="embedded",x="Amazon",E="Apple",M="ASUS",k="BlackBerry",S="Browser",T="Chrome",O="Firefox",_="Google",C="Huawei",A="LG",R="Microsoft",D="Motorola",j="Opera",L="Samsung",N="Sharp",P="Sony",F="Xiaomi",B="Zebra",I="Facebook",U="Chromium OS",z="Mac OS",q=" Browser",V=function(e){for(var t={},n=0;n<e.length;n++)t[e[n].toUpperCase()]=e[n];return t},$=function(e,t){return typeof e===a&&-1!==W(t).indexOf(W(e))},W=function(e){return e.toLowerCase()},H=function(e,t){if(typeof e===a)return e=e.replace(/^\s\s*/,""),typeof t===o?e:e.substring(0,500)},Y=function(e,t){for(var n,o,a,c,l,u,d=0;d<t.length&&!l;){var h=t[d],f=t[d+1];for(n=o=0;n<h.length&&!l&&h[n];)if(l=h[n++].exec(e))for(a=0;a<f.length;a++)u=l[++o],typeof(c=f[a])===s&&c.length>0?2===c.length?typeof c[1]==i?this[c[0]]=c[1].call(this,u):this[c[0]]=c[1]:3===c.length?typeof c[1]!==i||c[1].exec&&c[1].test?this[c[0]]=u?u.replace(c[1],c[2]):r:this[c[0]]=u?c[1].call(this,u,c[2]):r:4===c.length&&(this[c[0]]=u?c[3].call(this,u.replace(c[1],c[2])):r):this[c]=u||r;d+=2}},G=function(e,t){for(var n in t)if(typeof t[n]===s&&t[n].length>0){for(var i=0;i<t[n].length;i++)if($(t[n][i],e))return"?"===n?r:n}else if($(t[n],e))return"?"===n?r:n;return t.hasOwnProperty("*")?t["*"]:e},K={ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"},J={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[f,[u,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[f,[u,"Edge"]],[/(opera mini)\/([-\w\.]+)/i,/(opera [mobiletab]{3,6})\b.+version\/([-\w\.]+)/i,/(opera)(?:.+version\/|[\/ ]+)([\w\.]+)/i],[u,f],[/opios[\/ ]+([\w\.]+)/i],[f,[u,j+" Mini"]],[/\bop(?:rg)?x\/([\w\.]+)/i],[f,[u,j+" GX"]],[/\bopr\/([\w\.]+)/i],[f,[u,j]],[/\bb[ai]*d(?:uhd|[ub]*[aekoprswx]{5,6})[\/ ]?([\w\.]+)/i],[f,[u,"Baidu"]],[/\b(?:mxbrowser|mxios|myie2)\/?([-\w\.]*)\b/i],[f,[u,"Maxthon"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer|sleipnir)[\/ ]?([\w\.]*)/i,/(avant|iemobile|slim(?:browser|boat|jet))[\/ ]?([\d\.]*)/i,/(?:ms|\()(ie) ([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|qupzilla|falkon|rekonq|puffin|brave|whale(?!.+naver)|qqbrowserlite|duckduckgo|klar|helio|(?=comodo_)?dragon)\/([-\w\.]+)/i,/(heytap|ovi|115)browser\/([\d\.]+)/i,/(weibo)__([\d\.]+)/i],[u,f],[/quark(?:pc)?\/([-\w\.]+)/i],[f,[u,"Quark"]],[/\bddg\/([\w\.]+)/i],[f,[u,"DuckDuckGo"]],[/(?:\buc? ?browser|(?:juc.+)ucweb)[\/ ]?([\w\.]+)/i],[f,[u,"UC"+S]],[/microm.+\bqbcore\/([\w\.]+)/i,/\bqbcore\/([\w\.]+).+microm/i,/micromessenger\/([\w\.]+)/i],[f,[u,"WeChat"]],[/konqueror\/([\w\.]+)/i],[f,[u,"Konqueror"]],[/trident.+rv[: ]([\w\.]{1,9})\b.+like gecko/i],[f,[u,"IE"]],[/ya(?:search)?browser\/([\w\.]+)/i],[f,[u,"Yandex"]],[/slbrowser\/([\w\.]+)/i],[f,[u,"Smart Lenovo "+S]],[/(avast|avg)\/([\w\.]+)/i],[[u,/(.+)/,"$1 Secure "+S],f],[/\bfocus\/([\w\.]+)/i],[f,[u,O+" Focus"]],[/\bopt\/([\w\.]+)/i],[f,[u,j+" Touch"]],[/coc_coc\w+\/([\w\.]+)/i],[f,[u,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[f,[u,"Dolphin"]],[/coast\/([\w\.]+)/i],[f,[u,j+" Coast"]],[/miuibrowser\/([\w\.]+)/i],[f,[u,"MIUI"+q]],[/fxios\/([\w\.-]+)/i],[f,[u,O]],[/\bqihoobrowser\/?([\w\.]*)/i],[f,[u,"360"]],[/\b(qq)\/([\w\.]+)/i],[[u,/(.+)/,"$1Browser"],f],[/(oculus|sailfish|huawei|vivo|pico)browser\/([\w\.]+)/i],[[u,/(.+)/,"$1"+q],f],[/samsungbrowser\/([\w\.]+)/i],[f,[u,L+" Internet"]],[/metasr[\/ ]?([\d\.]+)/i],[f,[u,"Sogou Explorer"]],[/(sogou)mo\w+\/([\d\.]+)/i],[[u,"Sogou Mobile"],f],[/(electron)\/([\w\.]+) safari/i,/(tesla)(?: qtcarbrowser|\/(20\d\d\.[-\w\.]+))/i,/m?(qqbrowser|2345(?=browser|chrome|explorer))\w*[\/ ]?v?([\w\.]+)/i],[u,f],[/(lbbrowser|rekonq)/i,/\[(linkedin)app\]/i],[u],[/ome\/([\w\.]+) \w* ?(iron) saf/i,/ome\/([\w\.]+).+qihu (360)[es]e/i],[f,u],[/((?:fban\/fbios|fb_iab\/fb4a)(?!.+fbav)|;fbav\/([\w\.]+);)/i],[[u,I],f],[/(Klarna)\/([\w\.]+)/i,/(kakao(?:talk|story))[\/ ]([\w\.]+)/i,/(naver)\(.*?(\d+\.[\w\.]+).*\)/i,/safari (line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(alipay)client\/([\w\.]+)/i,/(twitter)(?:and| f.+e\/([\w\.]+))/i,/(chromium|instagram|snapchat)[\/ ]([-\w\.]+)/i],[u,f],[/\bgsa\/([\w\.]+) .*safari\//i],[f,[u,"GSA"]],[/musical_ly(?:.+app_?version\/|_)([\w\.]+)/i],[f,[u,"TikTok"]],[/headlesschrome(?:\/([\w\.]+)| )/i],[f,[u,T+" Headless"]],[/ wv\).+(chrome)\/([\w\.]+)/i],[[u,T+" WebView"],f],[/droid.+ version\/([\w\.]+)\b.+(?:mobile safari|safari)/i],[f,[u,"Android "+S]],[/(chrome|omniweb|arora|[tizenoka]{5} ?browser)\/v?([\w\.]+)/i],[u,f],[/version\/([\w\.\,]+) .*mobile\/\w+ (safari)/i],[f,[u,"Mobile Safari"]],[/version\/([\w(\.|\,)]+) .*(mobile ?safari|safari)/i],[f,u],[/webkit.+?(mobile ?safari|safari)(\/[\w\.]+)/i],[u,[f,G,{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}]],[/(webkit|khtml)\/([\w\.]+)/i],[u,f],[/(navigator|netscape\d?)\/([-\w\.]+)/i],[[u,"Netscape"],f],[/(wolvic|librewolf)\/([\w\.]+)/i],[u,f],[/mobile vr; rv:([\w\.]+)\).+firefox/i],[f,[u,O+" Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo browser|minimo|conkeror)[\/ ]?([\w\.\+]+)/i,/(seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([-\w\.]+)$/i,/(firefox)\/([\w\.]+)/i,/(mozilla)\/([\w\.]+) .+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|obigo|mosaic|(?:go|ice|up)[\. ]?browser)[-\/ ]?v?([\w\.]+)/i,/(links) \(([\w\.]+)/i],[u,[f,/_/g,"."]],[/(cobalt)\/([\w\.]+)/i],[u,[f,/master.|lts./,""]]],cpu:[[/(?:(amd|x(?:(?:86|64)[-_])?|wow|win)64)[;\)]/i],[[p,"amd64"]],[/(ia32(?=;))/i],[[p,W]],[/((?:i[346]|x)86)[;\)]/i],[[p,"ia32"]],[/\b(aarch64|arm(v?8e?l?|_?64))\b/i],[[p,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[p,"armhf"]],[/windows (ce|mobile); ppc;/i],[[p,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?: mac|;|\))/i],[[p,/ower/,"",W]],[/(sun4\w)[;\)]/i],[[p,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?=v(?:[1-7]|[5-7]1)l?|;|eabi)|(?=atmel )avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[p,W]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[ptx]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus 10)/i],[l,[h,L],[d,b]],[/\b((?:s[cgp]h|gt|sm)-(?![lr])\w+|sc[g-]?[\d]+a?|galaxy nexus)/i,/samsung[- ]((?!sm-[lr])[-\w]+)/i,/sec-(sgh\w+)/i],[l,[h,L],[d,g]],[/(?:\/|\()(ip(?:hone|od)[\w, ]*)(?:\/|;)/i],[l,[h,E],[d,g]],[/\((ipad);[-\w\),; ]+apple/i,/applecoremedia\/[\w\.]+ \((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[l,[h,E],[d,b]],[/(macintosh);/i],[l,[h,E]],[/\b(sh-?[altvz]?\d\d[a-ekm]?)/i],[l,[h,N],[d,g]],[/(?:honor)([-\w ]+)[;\)]/i],[l,[h,"Honor"],[d,g]],[/\b((?:ag[rs][23]?|bah2?|sht?|btv)-a?[lw]\d{2})\b(?!.+d\/s)/i],[l,[h,C],[d,b]],[/(?:huawei)([-\w ]+)[;\)]/i,/\b(nexus 6p|\w{2,4}e?-[atu]?[ln][\dx][012359c][adn]?)\b(?!.+d\/s)/i],[l,[h,C],[d,g]],[/\b(poco[\w ]+|m2\d{3}j\d\d[a-z]{2})(?: bui|\))/i,/\b; (\w+) build\/hm\1/i,/\b(hm[-_ ]?note?[_ ]?(?:\d\w)?) bui/i,/\b(redmi[\-_ ]?(?:note|k)?[\w_ ]+)(?: bui|\))/i,/oid[^\)]+; (m?[12][0-389][01]\w{3,6}[c-y])( bui|; wv|\))/i,/\b(mi[-_ ]?(?:a\d|one|one[_ ]plus|note lte|max|cc)?[_ ]?(?:\d?\w?)[_ ]?(?:plus|se|lite|pro)?)(?: bui|\))/i],[[l,/_/g," "],[h,F],[d,g]],[/oid[^\)]+; (2\d{4}(283|rpbf)[cgl])( bui|\))/i,/\b(mi[-_ ]?(?:pad)(?:[\w_ ]+))(?: bui|\))/i],[[l,/_/g," "],[h,F],[d,b]],[/; (\w+) bui.+ oppo/i,/\b(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007|a101op)\b/i],[l,[h,"OPPO"],[d,g]],[/\b(opd2\d{3}a?) bui/i],[l,[h,"OPPO"],[d,b]],[/vivo (\w+)(?: bui|\))/i,/\b(v[12]\d{3}\w?[at])(?: bui|;)/i],[l,[h,"Vivo"],[d,g]],[/\b(rmx[1-3]\d{3})(?: bui|;|\))/i],[l,[h,"Realme"],[d,g]],[/\b(milestone|droid(?:[2-4x]| (?:bionic|x2|pro|razr))?:?( 4g)?)\b[\w ]+build\//i,/\bmot(?:orola)?[- ](\w*)/i,/((?:moto[\w\(\) ]+|xt\d{3,4}|nexus 6)(?= bui|\)))/i],[l,[h,D],[d,g]],[/\b(mz60\d|xoom[2 ]{0,2}) build\//i],[l,[h,D],[d,b]],[/((?=lg)?[vl]k\-?\d{3}) bui| 3\.[-\w; ]{10}lg?-([06cv9]{3,4})/i],[l,[h,A],[d,b]],[/(lm(?:-?f100[nv]?|-[\w\.]+)(?= bui|\))|nexus [45])/i,/\blg[-e;\/ ]+((?!browser|netcast|android tv)\w+)/i,/\blg-?([\d\w]+) bui/i],[l,[h,A],[d,g]],[/(ideatab[-\w ]+)/i,/lenovo ?(s[56]000[-\w]+|tab(?:[\w ]+)|yt[-\d\w]{6}|tb[-\d\w]{6})/i],[l,[h,"Lenovo"],[d,b]],[/(?:maemo|nokia).*(n900|lumia \d+)/i,/nokia[-_ ]?([-\w\.]*)/i],[[l,/_/g," "],[h,"Nokia"],[d,g]],[/(pixel c)\b/i],[l,[h,_],[d,b]],[/droid.+; (pixel[\daxl ]{0,6})(?: bui|\))/i],[l,[h,_],[d,g]],[/droid.+; (a?\d[0-2]{2}so|[c-g]\d{4}|so[-gl]\w+|xq-a\w[4-7][12])(?= bui|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[l,[h,P],[d,g]],[/sony tablet [ps]/i,/\b(?:sony)?sgp\w+(?: bui|\))/i],[[l,"Xperia Tablet"],[h,P],[d,b]],[/ (kb2005|in20[12]5|be20[12][59])\b/i,/(?:one)?(?:plus)? (a\d0\d\d)(?: b|\))/i],[l,[h,"OnePlus"],[d,g]],[/(alexa)webm/i,/(kf[a-z]{2}wi|aeo(?!bc)\w\w)( bui|\))/i,/(kf[a-z]+)( bui|\)).+silk\//i],[l,[h,x],[d,b]],[/((?:sd|kf)[0349hijorstuw]+)( bui|\)).+silk\//i],[[l,/(.+)/g,"Fire Phone $1"],[h,x],[d,g]],[/(playbook);[-\w\),; ]+(rim)/i],[l,h,[d,b]],[/\b((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10; (\w+)/i],[l,[h,k],[d,g]],[/(?:\b|asus_)(transfo[prime ]{4,10} \w+|eeepc|slider \w+|nexus 7|padfone|p00[cj])/i],[l,[h,M],[d,b]],[/ (z[bes]6[027][012][km][ls]|zenfone \d\w?)\b/i],[l,[h,M],[d,g]],[/(nexus 9)/i],[l,[h,"HTC"],[d,b]],[/(htc)[-;_ ]{1,2}([\w ]+(?=\)| bui)|\w+)/i,/(zte)[- ]([\w ]+?)(?: bui|\/|\))/i,/(alcatel|geeksphone|nexian|panasonic(?!(?:;|\.))|sony(?!-bra))[-_ ]?([-\w]*)/i],[h,[l,/_/g," "],[d,g]],[/droid [\w\.]+; ((?:8[14]9[16]|9(?:0(?:48|60|8[01])|1(?:3[27]|66)|2(?:6[69]|9[56])|466))[gqswx])\w*(\)| bui)/i],[l,[h,"TCL"],[d,b]],[/(itel) ((\w+))/i],[[h,W],l,[d,G,{tablet:["p10001l","w7001"],"*":"mobile"}]],[/droid.+; ([ab][1-7]-?[0178a]\d\d?)/i],[l,[h,"Acer"],[d,b]],[/droid.+; (m[1-5] note) bui/i,/\bmz-([-\w]{2,})/i],[l,[h,"Meizu"],[d,g]],[/; ((?:power )?armor(?:[\w ]{0,8}))(?: bui|\))/i],[l,[h,"Ulefone"],[d,g]],[/; (energy ?\w+)(?: bui|\))/i,/; energizer ([\w ]+)(?: bui|\))/i],[l,[h,"Energizer"],[d,g]],[/; cat (b35);/i,/; (b15q?|s22 flip|s48c|s62 pro)(?: bui|\))/i],[l,[h,"Cat"],[d,g]],[/((?:new )?andromax[\w- ]+)(?: bui|\))/i],[l,[h,"Smartfren"],[d,g]],[/droid.+; (a(?:015|06[35]|142p?))/i],[l,[h,"Nothing"],[d,g]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron|infinix|tecno|micromax|advan)[-_ ]?([-\w]*)/i,/; (imo) ((?!tab)[\w ]+?)(?: bui|\))/i,/(hp) ([\w ]+\w)/i,/(asus)-?(\w+)/i,/(microsoft); (lumia[\w ]+)/i,/(lenovo)[-_ ]?([-\w]+)/i,/(jolla)/i,/(oppo) ?([\w ]+) bui/i],[h,l,[d,g]],[/(imo) (tab \w+)/i,/(kobo)\s(ereader|touch)/i,/(archos) (gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/(nook)[\w ]+build\/(\w+)/i,/(dell) (strea[kpr\d ]*[\dko])/i,/(le[- ]+pan)[- ]+(\w{1,9}) bui/i,/(trinity)[- ]*(t\d{3}) bui/i,/(gigaset)[- ]+(q\w{1,9}) bui/i,/(vodafone) ([\w ]+)(?:\)| bui)/i],[h,l,[d,b]],[/(surface duo)/i],[l,[h,R],[d,b]],[/droid [\d\.]+; (fp\du?)(?: b|\))/i],[l,[h,"Fairphone"],[d,g]],[/(u304aa)/i],[l,[h,"AT&T"],[d,g]],[/\bsie-(\w*)/i],[l,[h,"Siemens"],[d,g]],[/\b(rct\w+) b/i],[l,[h,"RCA"],[d,b]],[/\b(venue[\d ]{2,7}) b/i],[l,[h,"Dell"],[d,b]],[/\b(q(?:mv|ta)\w+) b/i],[l,[h,"Verizon"],[d,b]],[/\b(?:barnes[& ]+noble |bn[rt])([\w\+ ]*) b/i],[l,[h,"Barnes & Noble"],[d,b]],[/\b(tm\d{3}\w+) b/i],[l,[h,"NuVision"],[d,b]],[/\b(k88) b/i],[l,[h,"ZTE"],[d,b]],[/\b(nx\d{3}j) b/i],[l,[h,"ZTE"],[d,g]],[/\b(gen\d{3}) b.+49h/i],[l,[h,"Swiss"],[d,g]],[/\b(zur\d{3}) b/i],[l,[h,"Swiss"],[d,b]],[/\b((zeki)?tb.*\b) b/i],[l,[h,"Zeki"],[d,b]],[/\b([yr]\d{2}) b/i,/\b(dragon[- ]+touch |dt)(\w{5}) b/i],[[h,"Dragon Touch"],l,[d,b]],[/\b(ns-?\w{0,9}) b/i],[l,[h,"Insignia"],[d,b]],[/\b((nxa|next)-?\w{0,9}) b/i],[l,[h,"NextBook"],[d,b]],[/\b(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05])) b/i],[[h,"Voice"],l,[d,g]],[/\b(lvtel\-)?(v1[12]) b/i],[[h,"LvTel"],l,[d,g]],[/\b(ph-1) /i],[l,[h,"Essential"],[d,g]],[/\b(v(100md|700na|7011|917g).*\b) b/i],[l,[h,"Envizen"],[d,b]],[/\b(trio[-\w\. ]+) b/i],[l,[h,"MachSpeed"],[d,b]],[/\btu_(1491) b/i],[l,[h,"Rotor"],[d,b]],[/(shield[\w ]+) b/i],[l,[h,"Nvidia"],[d,b]],[/(sprint) (\w+)/i],[h,l,[d,g]],[/(kin\.[onetw]{3})/i],[[l,/\./g," "],[h,R],[d,g]],[/droid.+; (cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[l,[h,B],[d,b]],[/droid.+; (ec30|ps20|tc[2-8]\d[kx])\)/i],[l,[h,B],[d,g]],[/smart-tv.+(samsung)/i],[h,[d,y]],[/hbbtv.+maple;(\d+)/i],[[l,/^/,"SmartTV"],[h,L],[d,y]],[/(nux; netcast.+smarttv|lg (netcast\.tv-201\d|android tv))/i],[[h,A],[d,y]],[/(apple) ?tv/i],[h,[l,E+" TV"],[d,y]],[/crkey/i],[[l,T+"cast"],[h,_],[d,y]],[/droid.+aft(\w+)( bui|\))/i],[l,[h,x],[d,y]],[/\(dtv[\);].+(aquos)/i,/(aquos-tv[\w ]+)\)/i],[l,[h,N],[d,y]],[/(bravia[\w ]+)( bui|\))/i],[l,[h,P],[d,y]],[/(mitv-\w{5}) bui/i],[l,[h,F],[d,y]],[/Hbbtv.*(technisat) (.*);/i],[h,l,[d,y]],[/\b(roku)[\dx]*[\)\/]((?:dvp-)?[\d\.]*)/i,/hbbtv\/\d+\.\d+\.\d+ +\([\w\+ ]*; *([\w\d][^;]*);([^;]*)/i],[[h,H],[l,H],[d,y]],[/\b(android tv|smart[- ]?tv|opera tv|tv; rv:)\b/i],[[d,y]],[/(ouya)/i,/(nintendo) ([wids3utch]+)/i],[h,l,[d,m]],[/droid.+; (shield) bui/i],[l,[h,"Nvidia"],[d,m]],[/(playstation [345portablevi]+)/i],[l,[h,P],[d,m]],[/\b(xbox(?: one)?(?!; xbox))[\); ]/i],[l,[h,R],[d,m]],[/\b(sm-[lr]\d\d[05][fnuw]?s?)\b/i],[l,[h,L],[d,w]],[/((pebble))app/i],[h,l,[d,w]],[/(watch)(?: ?os[,\/]|\d,\d\/)[\d\.]+/i],[l,[h,E],[d,w]],[/droid.+; (glass) \d/i],[l,[h,_],[d,w]],[/droid.+; (wt63?0{2,3})\)/i],[l,[h,B],[d,w]],[/droid.+; (glass) \d/i],[l,[h,_],[d,w]],[/(pico) (4|neo3(?: link|pro)?)/i],[h,l,[d,w]],[/; (quest( \d| pro)?)/i],[l,[h,I],[d,w]],[/(tesla)(?: qtcarbrowser|\/[-\w\.]+)/i],[h,[d,v]],[/(aeobc)\b/i],[l,[h,x],[d,v]],[/droid .+?; ([^;]+?)(?: bui|; wv\)|\) applew).+? mobile safari/i],[l,[d,g]],[/droid .+?; ([^;]+?)(?: bui|\) applew).+?(?! mobile) safari/i],[l,[d,b]],[/\b((tablet|tab)[;\/]|focus\/\d(?!.+mobile))/i],[[d,b]],[/(phone|mobile(?:[;\/]| [ \w\/\.]*safari)|pda(?=.+windows ce))/i],[[d,g]],[/(android[-\w\. ]{0,9});.+buil/i],[l,[h,"Generic"]]],engine:[[/windows.+ edge\/([\w\.]+)/i],[f,[u,"EdgeHTML"]],[/(arkweb)\/([\w\.]+)/i],[u,f],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[f,[u,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna|servo)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/ ]\(?([\w\.]+)/i,/(icab)[\/ ]([23]\.[\d\.]+)/i,/\b(libweb)/i],[u,f],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[f,u]],os:[[/microsoft (windows) (vista|xp)/i],[u,f],[/(windows (?:phone(?: os)?|mobile))[\/ ]?([\d\.\w ]*)/i],[u,[f,G,K]],[/windows nt 6\.2; (arm)/i,/windows[\/ ]?([ntce\d\. ]+\w)(?!.+xbox)/i,/(?:win(?=3|9|n)|win 9x )([nt\d\.]+)/i],[[f,G,K],[u,"Windows"]],[/ip[honead]{2,4}\b(?:.*os ([\w]+) like mac|; opera)/i,/(?:ios;fbsv\/|iphone.+ios[\/ ])([\d\.]+)/i,/cfnetwork\/.+darwin/i],[[f,/_/g,"."],[u,"iOS"]],[/(mac os x) ?([\w\. ]*)/i,/(macintosh|mac_powerpc\b)(?!.+haiku)/i],[[u,z],[f,/_/g,"."]],[/droid ([\w\.]+)\b.+(android[- ]x86|harmonyos)/i],[f,u],[/(android|webos|qnx|bada|rim tablet os|maemo|meego|sailfish|openharmony)[-\/ ]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/ ]([\w\.]+)/i,/\((series40);/i],[u,f],[/\(bb(10);/i],[f,[u,k]],[/(?:symbian ?os|symbos|s60(?=;)|series60)[-\/ ]?([\w\.]*)/i],[f,[u,"Symbian"]],[/mozilla\/[\d\.]+ \((?:mobile|tablet|tv|mobile; [\w ]+); rv:.+ gecko\/([\w\.]+)/i],[f,[u,O+" OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[f,[u,"webOS"]],[/watch(?: ?os[,\/]|\d,\d\/)([\d\.]+)/i],[f,[u,"watchOS"]],[/crkey\/([\d\.]+)/i],[f,[u,T+"cast"]],[/(cros) [\w]+(?:\)| ([\w\.]+)\b)/i],[[u,U],f],[/panasonic;(viera)/i,/(netrange)mmh/i,/(nettv)\/(\d+\.[\w\.]+)/i,/(nintendo|playstation) ([wids345portablevuch]+)/i,/(xbox); +xbox ([^\);]+)/i,/\b(joli|palm)\b ?(?:os)?\/?([\w\.]*)/i,/(mint)[\/\(\) ]?(\w*)/i,/(mageia|vectorlinux)[; ]/i,/([kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?= linux)|slackware|fedora|mandriva|centos|pclinuxos|red ?hat|zenwalk|linpus|raspbian|plan 9|minix|risc os|contiki|deepin|manjaro|elementary os|sabayon|linspire)(?: gnu\/linux)?(?: enterprise)?(?:[- ]linux)?(?:-gnu)?[-\/ ]?(?!chrom|package)([-\w\.]*)/i,/(hurd|linux) ?([\w\.]*)/i,/(gnu) ?([\w\.]*)/i,/\b([-frentopcghs]{0,5}bsd|dragonfly)[\/ ]?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku) (\w+)/i],[u,f],[/(sunos) ?([\w\.\d]*)/i],[[u,"Solaris"],f],[/((?:open)?solaris)[-\/ ]?([\w\.]*)/i,/(aix) ((\d)(?=\.|\)| )[\w\.])*/i,/\b(beos|os\/2|amigaos|morphos|openvms|fuchsia|hp-ux|serenityos)/i,/(unix) ?([\w\.]*)/i],[u,f]]},Z=function(e,t){if(typeof e===s&&(t=e,e=r),!(this instanceof Z))return new Z(e,t).getResult();var m=typeof n!==o&&n.navigator?n.navigator:r,y=e||(m&&m.userAgent?m.userAgent:""),w=m&&m.userAgentData?m.userAgentData:r,v=t?function(e,t){var n={};for(var r in e)t[r]&&t[r].length%2==0?n[r]=t[r].concat(e[r]):n[r]=e[r];return n}(J,t):J,x=m&&m.userAgent==y;return this.getBrowser=function(){var e={};return e[u]=r,e[f]=r,Y.call(e,y,v.browser),e[c]=function(e){return typeof e===a?e.replace(/[^\d\.]/g,"").split(".")[0]:r}(e[f]),x&&m&&m.brave&&typeof m.brave.isBrave==i&&(e[u]="Brave"),e},this.getCPU=function(){var e={};return e[p]=r,Y.call(e,y,v.cpu),e},this.getDevice=function(){var e={};return e[h]=r,e[l]=r,e[d]=r,Y.call(e,y,v.device),x&&!e[d]&&w&&w.mobile&&(e[d]=g),x&&"Macintosh"==e[l]&&m&&typeof m.standalone!==o&&m.maxTouchPoints&&m.maxTouchPoints>2&&(e[l]="iPad",e[d]=b),e},this.getEngine=function(){var e={};return e[u]=r,e[f]=r,Y.call(e,y,v.engine),e},this.getOS=function(){var e={};return e[u]=r,e[f]=r,Y.call(e,y,v.os),x&&!e[u]&&w&&w.platform&&"Unknown"!=w.platform&&(e[u]=w.platform.replace(/chrome os/i,U).replace(/macos/i,z)),e},this.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},this.getUA=function(){return y},this.setUA=function(e){return y=typeof e===a&&e.length>500?H(e,500):e,this},this.setUA(y),this};Z.VERSION="1.0.40",Z.BROWSER=V([u,f,c]),Z.CPU=V([p]),Z.DEVICE=V([l,h,d,m,g,y,b,w,v]),Z.ENGINE=Z.OS=V([u,f]),e.exports&&(t=e.exports=Z),t.UAParser=Z;var X=typeof n!==o&&(n.jQuery||n.Zepto);if(X&&!X.ua){var Q=new Z;X.ua=Q.getResult(),X.ua.get=function(){return Q.getUA()},X.ua.set=function(e){Q.setUA(e);var t=Q.getResult();for(var n in t)X.ua[n]=t[n]}}}("object"==typeof window?window:Or)),Tr.exports;var e,t}var Cr=function(){if(kr)return Sr;kr=1,Object.defineProperty(Sr,"__esModule",{value:!0});var e,t=M(),n=(e=t)&&"object"==typeof e&&"default"in e?e.default:e,r=_r(),i=new r,o=i.getBrowser(),s=i.getCPU(),a=i.getDevice(),c=i.getEngine(),l=i.getOS(),u=i.getUA(),d=function(e){return i.setUA(e)},h=function(e){if(e){var t=new r(e);return{UA:t,browser:t.getBrowser(),cpu:t.getCPU(),device:t.getDevice(),engine:t.getEngine(),os:t.getOS(),ua:t.getUA(),setUserAgent:function(e){return t.setUA(e)}}}},f=Object.freeze({ClientUAInstance:i,browser:o,cpu:s,device:a,engine:c,os:l,ua:u,setUa:d,parseUserAgent:h});function p(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function m(e){return m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},m(e)}function g(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(){return y=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},y.apply(this,arguments)}function w(e){return w=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},w(e)}function v(e,t){return v=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},v(e,t)}function x(e,t){if(null==e)return{};var n,r,i=function(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(i[n]=e[n])}return i}function E(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function k(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==n)return;var r,i,o=[],s=!0,a=!1;try{for(n=n.call(e);!(s=(r=n.next()).done)&&(o.push(r.value),!t||o.length!==t);s=!0);}catch(c){a=!0,i=c}finally{try{s||null==n.return||n.return()}finally{if(a)throw i}}return o}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return S(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return S(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function S(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var T="mobile",O="tablet",_="smarttv",C="console",A="wearable",R="embedded",D=void 0,j={Chrome:"Chrome",Firefox:"Firefox",Opera:"Opera",Yandex:"Yandex",Safari:"Safari",InternetExplorer:"Internet Explorer",Edge:"Edge",Chromium:"Chromium",Ie:"IE",MobileSafari:"Mobile Safari",EdgeChromium:"Edge Chromium",MIUI:"MIUI Browser",SamsungBrowser:"Samsung Browser"},L={IOS:"iOS",Android:"Android",WindowsPhone:"Windows Phone",Windows:"Windows",MAC_OS:"Mac OS"},N={isMobile:!1,isTablet:!1,isBrowser:!1,isSmartTV:!1,isConsole:!1,isWearable:!1},P=function(e){return e||(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"none")},F=function(){return!("undefined"==typeof window||!window.navigator&&!navigator)&&(window.navigator||navigator)},B=function(e){var t=F();return t&&t.platform&&(-1!==t.platform.indexOf(e)||"MacIntel"===t.platform&&t.maxTouchPoints>1&&!window.MSStream)},I=function(e,t,n,r){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?p(Object(n),!0).forEach((function(t){b(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):p(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},e,{vendor:P(t.vendor),model:P(t.model),os:P(n.name),osVersion:P(n.version),ua:P(r)})},U=function(e){return e.type===T},z=function(e){return e.type===O},q=function(e){var t=e.type;return t===T||t===O},V=function(e){return e.type===_},$=function(e){return e.type===D},W=function(e){return e.type===A},H=function(e){return e.type===C},Y=function(e){return e.type===R},G=function(e){var t=e.vendor;return P(t)},K=function(e){var t=e.model;return P(t)},J=function(e){var t=e.type;return P(t,"browser")},Z=function(e){return e.name===L.Android},X=function(e){return e.name===L.Windows},Q=function(e){return e.name===L.MAC_OS},ee=function(e){return e.name===L.WindowsPhone},te=function(e){return e.name===L.IOS},ne=function(e){var t=e.version;return P(t)},re=function(e){var t=e.name;return P(t)},ie=function(e){return e.name===j.Chrome},oe=function(e){return e.name===j.Firefox},se=function(e){return e.name===j.Chromium},ae=function(e){return e.name===j.Edge},ce=function(e){return e.name===j.Yandex},le=function(e){var t=e.name;return t===j.Safari||t===j.MobileSafari},ue=function(e){return e.name===j.MobileSafari},de=function(e){return e.name===j.Opera},he=function(e){var t=e.name;return t===j.InternetExplorer||t===j.Ie},fe=function(e){return e.name===j.MIUI},pe=function(e){return e.name===j.SamsungBrowser},me=function(e){var t=e.version;return P(t)},ge=function(e){var t=e.major;return P(t)},be=function(e){var t=e.name;return P(t)},ye=function(e){var t=e.name;return P(t)},we=function(e){var t=e.version;return P(t)},ve=function(){var e=F(),t=e&&e.userAgent&&e.userAgent.toLowerCase();return"string"==typeof t&&/electron/.test(t)},xe=function(e){return"string"==typeof e&&-1!==e.indexOf("Edg/")},Ee=function(){var e=F();return e&&(/iPad|iPhone|iPod/.test(e.platform)||"MacIntel"===e.platform&&e.maxTouchPoints>1)&&!window.MSStream},Me=function(){return B("iPad")},ke=function(){return B("iPhone")},Se=function(){return B("iPod")},Te=function(e){return P(e)};function Oe(e){var t=e||f,n=t.device,r=t.browser,i=t.os,o=t.engine,s=t.ua;return{isSmartTV:V(n),isConsole:H(n),isWearable:W(n),isEmbedded:Y(n),isMobileSafari:ue(r)||Me(),isChromium:se(r),isMobile:q(n)||Me(),isMobileOnly:U(n),isTablet:z(n)||Me(),isBrowser:$(n),isDesktop:$(n),isAndroid:Z(i),isWinPhone:ee(i),isIOS:te(i)||Me(),isChrome:ie(r),isFirefox:oe(r),isSafari:le(r),isOpera:de(r),isIE:he(r),osVersion:ne(i),osName:re(i),fullBrowserVersion:me(r),browserVersion:ge(r),browserName:be(r),mobileVendor:G(n),mobileModel:K(n),engineName:ye(o),engineVersion:we(o),getUA:Te(s),isEdge:ae(r)||xe(s),isYandex:ce(r),deviceType:J(n),isIOS13:Ee(),isIPad13:Me(),isIPhone13:ke(),isIPod13:Se(),isElectron:ve(),isEdgeChromium:xe(s),isLegacyEdge:ae(r)&&!xe(s),isWindows:X(i),isMacOs:Q(i),isMIUI:fe(r),isSamsungBrowser:pe(r)}}var _e=V(a),Ce=H(a),Ae=W(a),Re=Y(a),De=ue(o)||Me(),je=se(o),Le=q(a)||Me(),Ne=U(a),Pe=z(a)||Me(),Fe=$(a),Be=$(a),Ie=Z(l),Ue=ee(l),ze=te(l)||Me(),qe=ie(o),Ve=oe(o),$e=le(o),We=de(o),He=he(o),Ye=ne(l),Ge=re(l),Ke=me(o),Je=ge(o),Ze=be(o),Xe=G(a),Qe=K(a),et=ye(c),tt=we(c),nt=Te(u),rt=ae(o)||xe(u),it=ce(o),ot=J(a),st=Ee(),at=Me(),ct=ke(),lt=Se(),ut=ve(),dt=xe(u),ht=ae(o)&&!xe(u),ft=X(l),pt=Q(l),mt=fe(o),gt=pe(o);function bt(e){var t=e||window.navigator.userAgent;return h(t)}return Sr.AndroidView=function(e){var r=e.renderWithFragment,i=e.children,o=x(e,["renderWithFragment","children"]);return Ie?r?n.createElement(t.Fragment,null,i):n.createElement("div",o,i):null},Sr.BrowserTypes=j,Sr.BrowserView=function(e){var r=e.renderWithFragment,i=e.children,o=x(e,["renderWithFragment","children"]);return Fe?r?n.createElement(t.Fragment,null,i):n.createElement("div",o,i):null},Sr.ConsoleView=function(e){var r=e.renderWithFragment,i=e.children,o=x(e,["renderWithFragment","children"]);return Ce?r?n.createElement(t.Fragment,null,i):n.createElement("div",o,i):null},Sr.CustomView=function(e){var r=e.renderWithFragment,i=e.children;e.viewClassName,e.style;var o=e.condition,s=x(e,["renderWithFragment","children","viewClassName","style","condition"]);return o?r?n.createElement(t.Fragment,null,i):n.createElement("div",s,i):null},Sr.IEView=function(e){var r=e.renderWithFragment,i=e.children,o=x(e,["renderWithFragment","children"]);return He?r?n.createElement(t.Fragment,null,i):n.createElement("div",o,i):null},Sr.IOSView=function(e){var r=e.renderWithFragment,i=e.children,o=x(e,["renderWithFragment","children"]);return ze?r?n.createElement(t.Fragment,null,i):n.createElement("div",o,i):null},Sr.MobileOnlyView=function(e){var r=e.renderWithFragment,i=e.children;e.viewClassName,e.style;var o=x(e,["renderWithFragment","children","viewClassName","style"]);return Ne?r?n.createElement(t.Fragment,null,i):n.createElement("div",o,i):null},Sr.MobileView=function(e){var r=e.renderWithFragment,i=e.children,o=x(e,["renderWithFragment","children"]);return Le?r?n.createElement(t.Fragment,null,i):n.createElement("div",o,i):null},Sr.OsTypes=L,Sr.SmartTVView=function(e){var r=e.renderWithFragment,i=e.children,o=x(e,["renderWithFragment","children"]);return _e?r?n.createElement(t.Fragment,null,i):n.createElement("div",o,i):null},Sr.TabletView=function(e){var r=e.renderWithFragment,i=e.children,o=x(e,["renderWithFragment","children"]);return Pe?r?n.createElement(t.Fragment,null,i):n.createElement("div",o,i):null},Sr.WearableView=function(e){var r=e.renderWithFragment,i=e.children,o=x(e,["renderWithFragment","children"]);return Ae?r?n.createElement(t.Fragment,null,i):n.createElement("div",o,i):null},Sr.WinPhoneView=function(e){var r=e.renderWithFragment,i=e.children,o=x(e,["renderWithFragment","children"]);return Ue?r?n.createElement(t.Fragment,null,i):n.createElement("div",o,i):null},Sr.browserName=Ze,Sr.browserVersion=Je,Sr.deviceDetect=function(e){var t=e?h(e):f,n=t.device,r=t.browser,i=t.engine,o=t.os,s=t.ua,a=function(e){switch(e){case T:return{isMobile:!0};case O:return{isTablet:!0};case _:return{isSmartTV:!0};case C:return{isConsole:!0};case A:return{isWearable:!0};case D:return{isBrowser:!0};case R:return{isEmbedded:!0};default:return N}}(n.type),c=a.isBrowser,l=a.isMobile,u=a.isTablet,d=a.isSmartTV,p=a.isConsole,m=a.isWearable,g=a.isEmbedded;return c?function(e,t,n,r,i){return{isBrowser:e,browserMajorVersion:P(t.major),browserFullVersion:P(t.version),browserName:P(t.name),engineName:P(n.name),engineVersion:P(n.version),osName:P(r.name),osVersion:P(r.version),userAgent:P(i)}}(c,r,i,o,s):d?function(e,t,n,r){return{isSmartTV:e,engineName:P(t.name),engineVersion:P(t.version),osName:P(n.name),osVersion:P(n.version),userAgent:P(r)}}(d,i,o,s):p?function(e,t,n,r){return{isConsole:e,engineName:P(t.name),engineVersion:P(t.version),osName:P(n.name),osVersion:P(n.version),userAgent:P(r)}}(p,i,o,s):l||u?I(a,n,o,s):m?function(e,t,n,r){return{isWearable:e,engineName:P(t.name),engineVersion:P(t.version),osName:P(n.name),osVersion:P(n.version),userAgent:P(r)}}(m,i,o,s):g?function(e,t,n,r,i){return{isEmbedded:e,vendor:P(t.vendor),model:P(t.model),engineName:P(n.name),engineVersion:P(n.version),osName:P(r.name),osVersion:P(r.version),userAgent:P(i)}}(g,n,i,o,s):void 0},Sr.deviceType=ot,Sr.engineName=et,Sr.engineVersion=tt,Sr.fullBrowserVersion=Ke,Sr.getSelectorsByUserAgent=function(e){if(e&&"string"==typeof e){var t=h(e);return Oe({device:t.device,browser:t.browser,os:t.os,engine:t.engine,ua:t.ua})}},Sr.getUA=nt,Sr.isAndroid=Ie,Sr.isBrowser=Fe,Sr.isChrome=qe,Sr.isChromium=je,Sr.isConsole=Ce,Sr.isDesktop=Be,Sr.isEdge=rt,Sr.isEdgeChromium=dt,Sr.isElectron=ut,Sr.isEmbedded=Re,Sr.isFirefox=Ve,Sr.isIE=He,Sr.isIOS=ze,Sr.isIOS13=st,Sr.isIPad13=at,Sr.isIPhone13=ct,Sr.isIPod13=lt,Sr.isLegacyEdge=ht,Sr.isMIUI=mt,Sr.isMacOs=pt,Sr.isMobile=Le,Sr.isMobileOnly=Ne,Sr.isMobileSafari=De,Sr.isOpera=We,Sr.isSafari=$e,Sr.isSamsungBrowser=gt,Sr.isSmartTV=_e,Sr.isTablet=Pe,Sr.isWearable=Ae,Sr.isWinPhone=Ue,Sr.isWindows=ft,Sr.isYandex=it,Sr.mobileModel=Qe,Sr.mobileVendor=Xe,Sr.osName=Ge,Sr.osVersion=Ye,Sr.parseUserAgent=h,Sr.setUserAgent=function(e){return d(e)},Sr.useDeviceData=bt,Sr.useDeviceSelectors=function(e){var t=bt(e||window.navigator.userAgent);return[Oe(t),t]},Sr.useMobileOrientation=function(){var e=k(t.useState((function(){var e=window.innerWidth>window.innerHeight?90:0;return{isPortrait:0===e,isLandscape:90===e,orientation:0===e?"portrait":"landscape"}})),2),n=e[0],r=e[1],i=t.useCallback((function(){var e=window.innerWidth>window.innerHeight?90:0,t={isPortrait:0===e,isLandscape:90===e,orientation:0===e?"portrait":"landscape"};n.orientation!==t.orientation&&r(t)}),[n.orientation]);return t.useEffect((function(){return void 0!==("undefined"==typeof window?"undefined":m(window))&&Le&&(i(),window.addEventListener("load",i,!1),window.addEventListener("resize",i,!1)),function(){window.removeEventListener("resize",i,!1),window.removeEventListener("load",i,!1)}}),[i]),n},Sr.withOrientationChange=function(e){return function(t){function r(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,r),(t=function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return E(e)}(this,w(r).call(this,e))).isEventListenerAdded=!1,t.handleOrientationChange=t.handleOrientationChange.bind(E(t)),t.onOrientationChange=t.onOrientationChange.bind(E(t)),t.onPageLoad=t.onPageLoad.bind(E(t)),t.state={isLandscape:!1,isPortrait:!1},t}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&v(e,t)}(r,t),g(r,[{key:"handleOrientationChange",value:function(){this.isEventListenerAdded||(this.isEventListenerAdded=!0);var e=window.innerWidth>window.innerHeight?90:0;this.setState({isPortrait:0===e,isLandscape:90===e})}},{key:"onOrientationChange",value:function(){this.handleOrientationChange()}},{key:"onPageLoad",value:function(){this.handleOrientationChange()}},{key:"componentDidMount",value:function(){void 0!==("undefined"==typeof window?"undefined":m(window))&&Le&&(this.isEventListenerAdded?window.removeEventListener("load",this.onPageLoad,!1):(this.handleOrientationChange(),window.addEventListener("load",this.onPageLoad,!1)),window.addEventListener("resize",this.onOrientationChange,!1))}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this.onOrientationChange,!1)}},{key:"render",value:function(){return n.createElement(e,y({},this.props,{isLandscape:this.state.isLandscape,isPortrait:this.state.isPortrait}))}}]),r}(n.Component)},Sr}();const Ar=Object.create(null);Ar.open="0",Ar.close="1",Ar.ping="2",Ar.pong="3",Ar.message="4",Ar.upgrade="5",Ar.noop="6";const Rr=Object.create(null);Object.keys(Ar).forEach((e=>{Rr[Ar[e]]=e}));const Dr={type:"error",data:"parser error"},jr="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),Lr="function"==typeof ArrayBuffer,Nr=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,Pr=({type:e,data:t},n,r)=>jr&&t instanceof Blob?n?r(t):Fr(t,r):Lr&&(t instanceof ArrayBuffer||Nr(t))?n?r(t):Fr(new Blob([t]),r):r(Ar[e]+(t||"")),Fr=(e,t)=>{const n=new FileReader;return n.onload=function(){const e=n.result.split(",")[1];t("b"+(e||""))},n.readAsDataURL(e)};function Br(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let Ir;const Ur="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",zr="undefined"==typeof Uint8Array?[]:new Uint8Array(256);for(let qc=0;qc<64;qc++)zr[Ur.charCodeAt(qc)]=qc;const qr="function"==typeof ArrayBuffer,Vr=(e,t)=>{if("string"!=typeof e)return{type:"message",data:Wr(e,t)};const n=e.charAt(0);if("b"===n)return{type:"message",data:$r(e.substring(1),t)};return Rr[n]?e.length>1?{type:Rr[n],data:e.substring(1)}:{type:Rr[n]}:Dr},$r=(e,t)=>{if(qr){const n=(e=>{let t,n,r,i,o,s=.75*e.length,a=e.length,c=0;"="===e[e.length-1]&&(s--,"="===e[e.length-2]&&s--);const l=new ArrayBuffer(s),u=new Uint8Array(l);for(t=0;t<a;t+=4)n=zr[e.charCodeAt(t)],r=zr[e.charCodeAt(t+1)],i=zr[e.charCodeAt(t+2)],o=zr[e.charCodeAt(t+3)],u[c++]=n<<2|r>>4,u[c++]=(15&r)<<4|i>>2,u[c++]=(3&i)<<6|63&o;return l})(e);return Wr(n,t)}return{base64:!0,data:e}},Wr=(e,t)=>"blob"===t?e instanceof Blob?e:new Blob([e]):e instanceof ArrayBuffer?e:e.buffer,Hr=String.fromCharCode(30);function Yr(){return new TransformStream({transform(e,t){!function(e,t){jr&&e.data instanceof Blob?e.data.arrayBuffer().then(Br).then(t):Lr&&(e.data instanceof ArrayBuffer||Nr(e.data))?t(Br(e.data)):Pr(e,!1,(e=>{Ir||(Ir=new TextEncoder),t(Ir.encode(e))}))}(e,(n=>{const r=n.length;let i;if(r<126)i=new Uint8Array(1),new DataView(i.buffer).setUint8(0,r);else if(r<65536){i=new Uint8Array(3);const e=new DataView(i.buffer);e.setUint8(0,126),e.setUint16(1,r)}else{i=new Uint8Array(9);const e=new DataView(i.buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(r))}e.data&&"string"!=typeof e.data&&(i[0]|=128),t.enqueue(i),t.enqueue(n)}))}})}let Gr;function Kr(e){return e.reduce(((e,t)=>e+t.length),0)}function Jr(e,t){if(e[0].length===t)return e.shift();const n=new Uint8Array(t);let r=0;for(let i=0;i<t;i++)n[i]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),n}function Zr(e){if(e)return function(e){for(var t in Zr.prototype)e[t]=Zr.prototype[t];return e}(e)}Zr.prototype.on=Zr.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},Zr.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},Zr.prototype.off=Zr.prototype.removeListener=Zr.prototype.removeAllListeners=Zr.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var i=0;i<r.length;i++)if((n=r[i])===t||n.fn===t){r.splice(i,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},Zr.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){r=0;for(var i=(n=n.slice(0)).length;r<i;++r)n[r].apply(this,t)}return this},Zr.prototype.emitReserved=Zr.prototype.emit,Zr.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},Zr.prototype.hasListeners=function(e){return!!this.listeners(e).length};const Xr="function"==typeof Promise&&"function"==typeof Promise.resolve?e=>Promise.resolve().then(e):(e,t)=>t(e,0),Qr="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function ei(e,...t){return t.reduce(((t,n)=>(e.hasOwnProperty(n)&&(t[n]=e[n]),t)),{})}const ti=Qr.setTimeout,ni=Qr.clearTimeout;function ri(e,t){t.useNativeTimers?(e.setTimeoutFn=ti.bind(Qr),e.clearTimeoutFn=ni.bind(Qr)):(e.setTimeoutFn=Qr.setTimeout.bind(Qr),e.clearTimeoutFn=Qr.clearTimeout.bind(Qr))}function ii(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class oi extends Error{constructor(e,t,n){super(e),this.description=t,this.context=n,this.type="TransportError"}}class si extends Zr{constructor(e){super(),this.writable=!1,ri(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,n){return super.emitReserved("error",new oi(e,t,n)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState&&this.write(e)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){const t=Vr(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){const e=this.opts.hostname;return-1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){const t=function(e){let t="";for(let n in e)e.hasOwnProperty(n)&&(t.length&&(t+="&"),t+=encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t}(e);return t.length?"?"+t:""}}class ai extends si{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";const t=()=>{this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(e++,this.once("pollComplete",(function(){--e||t()}))),this.writable||(e++,this.once("drain",(function(){--e||t()})))}else t()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){((e,t)=>{const n=e.split(Hr),r=[];for(let i=0;i<n.length;i++){const e=Vr(n[i],t);if(r.push(e),"error"===e.type)break}return r})(e,this.socket.binaryType).forEach((e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)})),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){const e=()=>{this.write([{type:"close"}])};"open"===this.readyState?e():this.once("open",e)}write(e){this.writable=!1,((e,t)=>{const n=e.length,r=new Array(n);let i=0;e.forEach(((e,o)=>{Pr(e,!1,(e=>{r[o]=e,++i===n&&t(r.join(Hr))}))}))})(e,(e=>{this.doWrite(e,(()=>{this.writable=!0,this.emitReserved("drain")}))}))}uri(){const e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=ii()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let ci=!1;try{ci="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(zc){}const li=ci;function ui(){}class di extends ai{constructor(e){if(super(e),"undefined"!=typeof location){const t="https:"===location.protocol;let n=location.port;n||(n=t?"443":"80"),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||n!==e.port}}doWrite(e,t){const n=this.request({method:"POST",data:e});n.on("success",t),n.on("error",((e,t)=>{this.onError("xhr post error",e,t)}))}doPoll(){const e=this.request();e.on("data",this.onData.bind(this)),e.on("error",((e,t)=>{this.onError("xhr poll error",e,t)})),this.pollXhr=e}}let hi=class e extends Zr{constructor(e,t,n){super(),this.createRequest=e,ri(this,n),this._opts=n,this._method=n.method||"GET",this._uri=t,this._data=void 0!==n.data?n.data:null,this._create()}_create(){var t;const n=ei(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");n.xdomain=!!this._opts.xd;const r=this._xhr=this.createRequest(n);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0);for(let e in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&r.setRequestHeader(e,this._opts.extraHeaders[e])}}catch(Io){}if("POST"===this._method)try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(Io){}try{r.setRequestHeader("Accept","*/*")}catch(Io){}null===(t=this._opts.cookieJar)||void 0===t||t.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var e;3===r.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(r.getResponseHeader("set-cookie"))),4===r.readyState&&(200===r.status||1223===r.status?this._onLoad():this.setTimeoutFn((()=>{this._onError("number"==typeof r.status?r.status:0)}),0))},r.send(this._data)}catch(Io){return void this.setTimeoutFn((()=>{this._onError(Io)}),0)}"undefined"!=typeof document&&(this._index=e.requestsCount++,e.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(t){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=ui,t)try{this._xhr.abort()}catch(Io){}"undefined"!=typeof document&&delete e.requests[this._index],this._xhr=null}}_onLoad(){const e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}};if(hi.requestsCount=0,hi.requests={},"undefined"!=typeof document)if("function"==typeof attachEvent)attachEvent("onunload",fi);else if("function"==typeof addEventListener){addEventListener("onpagehide"in Qr?"pagehide":"unload",fi,!1)}function fi(){for(let e in hi.requests)hi.requests.hasOwnProperty(e)&&hi.requests[e].abort()}const pi=function(){const e=mi({xdomain:!1});return e&&null!==e.responseType}();function mi(e){const t=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!t||li))return new XMLHttpRequest}catch(Io){}if(!t)try{return new(Qr[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(Io){}}const gi="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class bi extends si{get name(){return"websocket"}doOpen(){const e=this.uri(),t=this.opts.protocols,n=gi?{}:ei(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,n)}catch(zc){return this.emitReserved("error",zc)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],r=t===e.length-1;Pr(n,this.supportsBinary,(e=>{try{this.doWrite(n,e)}catch(Io){}r&&Xr((()=>{this.writable=!0,this.emitReserved("drain")}),this.setTimeoutFn)}))}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=ii()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}const yi=Qr.WebSocket||Qr.MozWebSocket;const wi={websocket:class extends bi{createSocket(e,t,n){return gi?new yi(e,t,n):t?new yi(e,t):new yi(e)}doWrite(e,t){this.ws.send(t)}},webtransport:class extends si{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(zc){return this.emitReserved("error",zc)}this._transport.closed.then((()=>{this.onClose()})).catch((e=>{this.onError("webtransport error",e)})),this._transport.ready.then((()=>{this._transport.createBidirectionalStream().then((e=>{const t=function(e,t){Gr||(Gr=new TextDecoder);const n=[];let r=0,i=-1,o=!1;return new TransformStream({transform(s,a){for(n.push(s);;){if(0===r){if(Kr(n)<1)break;const e=Jr(n,1);o=!(128&~e[0]),i=127&e[0],r=i<126?3:126===i?1:2}else if(1===r){if(Kr(n)<2)break;const e=Jr(n,2);i=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),r=3}else if(2===r){if(Kr(n)<8)break;const e=Jr(n,8),t=new DataView(e.buffer,e.byteOffset,e.length),o=t.getUint32(0);if(o>Math.pow(2,21)-1){a.enqueue(Dr);break}i=o*Math.pow(2,32)+t.getUint32(4),r=3}else{if(Kr(n)<i)break;const e=Jr(n,i);a.enqueue(Vr(o?e:Gr.decode(e),t)),r=0}if(0===i||i>e){a.enqueue(Dr);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),n=e.readable.pipeThrough(t).getReader(),r=Yr();r.readable.pipeTo(e.writable),this._writer=r.writable.getWriter();const i=()=>{n.read().then((({done:e,value:t})=>{e||(this.onPacket(t),i())})).catch((e=>{}))};i();const o={type:"open"};this.query.sid&&(o.data=`{"sid":"${this.query.sid}"}`),this._writer.write(o).then((()=>this.onOpen()))}))}))}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],r=t===e.length-1;this._writer.write(n).then((()=>{r&&Xr((()=>{this.writable=!0,this.emitReserved("drain")}),this.setTimeoutFn)}))}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}},polling:class extends di{constructor(e){super(e);const t=e&&e.forceBase64;this.supportsBinary=pi&&!t}request(e={}){return Object.assign(e,{xd:this.xd},this.opts),new hi(mi,this.uri(),e)}}},vi=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,xi=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function Ei(e){if(e.length>8e3)throw"URI too long";const t=e,n=e.indexOf("["),r=e.indexOf("]");-1!=n&&-1!=r&&(e=e.substring(0,n)+e.substring(n,r).replace(/:/g,";")+e.substring(r,e.length));let i=vi.exec(e||""),o={},s=14;for(;s--;)o[xi[s]]=i[s]||"";return-1!=n&&-1!=r&&(o.source=t,o.host=o.host.substring(1,o.host.length-1).replace(/;/g,":"),o.authority=o.authority.replace("[","").replace("]","").replace(/;/g,":"),o.ipv6uri=!0),o.pathNames=function(e,t){const n=/\/{2,9}/g,r=t.replace(n,"/").split("/");"/"!=t.slice(0,1)&&0!==t.length||r.splice(0,1);"/"==t.slice(-1)&&r.splice(r.length-1,1);return r}(0,o.path),o.queryKey=function(e,t){const n={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,(function(e,t,r){t&&(n[t]=r)})),n}(0,o.query),o}const Mi="function"==typeof addEventListener&&"function"==typeof removeEventListener,ki=[];Mi&&addEventListener("offline",(()=>{ki.forEach((e=>e()))}),!1);class Si extends Zr{constructor(e,t){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"==typeof e&&(t=e,e=null),e){const n=Ei(e);t.hostname=n.host,t.secure="https"===n.protocol||"wss"===n.protocol,t.port=n.port,n.query&&(t.query=n.query)}else t.host&&(t.hostname=Ei(t.host).host);ri(this,t),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach((e=>{const t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e})),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(e){let t={},n=e.split("&");for(let r=0,i=n.length;r<i;r++){let e=n[r].split("=");t[decodeURIComponent(e[0])]=decodeURIComponent(e[1])}return t}(this.opts.query)),Mi&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},ki.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(e){const t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);const n=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](n)}_open(){if(0===this.transports.length)return void this.setTimeoutFn((()=>{this.emitReserved("error","No transports available")}),0);const e=this.opts.rememberUpgrade&&Si.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";const t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",(e=>this._onClose("transport close",e)))}onOpen(){this.readyState="open",Si.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const t=new Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn((()=>{this._onClose("ping timeout")}),e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let n=0;n<this.writeBuffer.length;n++){const r=this.writeBuffer[n].data;if(r&&(e+="string"==typeof(t=r)?function(e){let t=0,n=0;for(let r=0,i=e.length;r<i;r++)t=e.charCodeAt(r),t<128?n+=1:t<2048?n+=2:t<55296||t>=57344?n+=3:(r++,n+=4);return n}(t):Math.ceil(1.33*(t.byteLength||t.size))),n>0&&e>this._maxPayload)return this.writeBuffer.slice(0,n);e+=2}var t;return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,Xr((()=>{this._onClose("ping timeout")}),this.setTimeoutFn)),e}write(e,t,n){return this._sendPacket("message",e,t,n),this}send(e,t,n){return this._sendPacket("message",e,t,n),this}_sendPacket(e,t,n,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof n&&(r=n,n=null),"closing"===this.readyState||"closed"===this.readyState)return;(n=n||{}).compress=!1!==n.compress;const i={type:e,data:t,options:n};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),r&&this.once("flush",r),this.flush()}close(){const e=()=>{this._onClose("forced close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},n=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",this.writeBuffer.length?this.once("drain",(()=>{this.upgrading?n():e()})):this.upgrading?n():e()),this}_onError(e){if(Si.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),Mi&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const e=ki.indexOf(this._offlineEventListener);-1!==e&&ki.splice(e,1)}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}Si.protocol=4;class Ti extends Si{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}_probe(e){let t=this.createTransport(e),n=!1;Si.priorWebsocketSuccess=!1;const r=()=>{n||(t.send([{type:"ping",data:"probe"}]),t.once("packet",(e=>{if(!n)if("pong"===e.type&&"probe"===e.data){if(this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;Si.priorWebsocketSuccess="websocket"===t.name,this.transport.pause((()=>{n||"closed"!==this.readyState&&(l(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())}))}else{const e=new Error("probe error");e.transport=t.name,this.emitReserved("upgradeError",e)}})))};function i(){n||(n=!0,l(),t.close(),t=null)}const o=e=>{const n=new Error("probe error: "+e);n.transport=t.name,i(),this.emitReserved("upgradeError",n)};function s(){o("transport closed")}function a(){o("socket closed")}function c(e){t&&e.name!==t.name&&i()}const l=()=>{t.removeListener("open",r),t.removeListener("error",o),t.removeListener("close",s),this.off("close",a),this.off("upgrading",c)};t.once("open",r),t.once("error",o),t.once("close",s),this.once("close",a),this.once("upgrading",c),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn((()=>{n||t.open()}),200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){const t=[];for(let n=0;n<e.length;n++)~this.transports.indexOf(e[n])&&t.push(e[n]);return t}}let Oi=class extends Ti{constructor(e,t={}){const n="object"==typeof e?e:t;(!n.transports||n.transports&&"string"==typeof n.transports[0])&&(n.transports=(n.transports||["polling","websocket","webtransport"]).map((e=>wi[e])).filter((e=>!!e))),super(e,n)}};const _i="function"==typeof ArrayBuffer,Ci=Object.prototype.toString,Ai="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Ci.call(Blob),Ri="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===Ci.call(File);function Di(e){return _i&&(e instanceof ArrayBuffer||(e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer)(e))||Ai&&e instanceof Blob||Ri&&e instanceof File}function ji(e,t){if(!e||"object"!=typeof e)return!1;if(Array.isArray(e)){for(let t=0,n=e.length;t<n;t++)if(ji(e[t]))return!0;return!1}if(Di(e))return!0;if(e.toJSON&&"function"==typeof e.toJSON&&1===arguments.length)return ji(e.toJSON(),!0);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&ji(e[n]))return!0;return!1}function Li(e){const t=[],n=e.data,r=e;return r.data=Ni(n,t),r.attachments=t.length,{packet:r,buffers:t}}function Ni(e,t){if(!e)return e;if(Di(e)){const n={_placeholder:!0,num:t.length};return t.push(e),n}if(Array.isArray(e)){const n=new Array(e.length);for(let r=0;r<e.length;r++)n[r]=Ni(e[r],t);return n}if("object"==typeof e&&!(e instanceof Date)){const n={};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=Ni(e[r],t));return n}return e}function Pi(e,t){return e.data=Fi(e.data,t),delete e.attachments,e}function Fi(e,t){if(!e)return e;if(e&&!0===e._placeholder){if("number"==typeof e.num&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=Fi(e[n],t);else if("object"==typeof e)for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=Fi(e[n],t));return e}const Bi=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"];var Ii;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(Ii||(Ii={}));function Ui(e){return"[object Object]"===Object.prototype.toString.call(e)}class zi extends Zr{constructor(e){super(),this.reviver=e}add(e){let t;if("string"==typeof e){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");t=this.decodeString(e);const n=t.type===Ii.BINARY_EVENT;n||t.type===Ii.BINARY_ACK?(t.type=n?Ii.EVENT:Ii.ACK,this.reconstructor=new qi(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else{if(!Di(e)&&!e.base64)throw new Error("Unknown type: "+e);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");t=this.reconstructor.takeBinaryData(e),t&&(this.reconstructor=null,super.emitReserved("decoded",t))}}decodeString(e){let t=0;const n={type:Number(e.charAt(0))};if(void 0===Ii[n.type])throw new Error("unknown packet type "+n.type);if(n.type===Ii.BINARY_EVENT||n.type===Ii.BINARY_ACK){const r=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);const i=e.substring(r,t);if(i!=Number(i)||"-"!==e.charAt(t))throw new Error("Illegal attachments");n.attachments=Number(i)}if("/"===e.charAt(t+1)){const r=t+1;for(;++t;){if(","===e.charAt(t))break;if(t===e.length)break}n.nsp=e.substring(r,t)}else n.nsp="/";const r=e.charAt(t+1);if(""!==r&&Number(r)==r){const r=t+1;for(;++t;){const n=e.charAt(t);if(null==n||Number(n)!=n){--t;break}if(t===e.length)break}n.id=Number(e.substring(r,t+1))}if(e.charAt(++t)){const r=this.tryParse(e.substr(t));if(!zi.isPayloadValid(n.type,r))throw new Error("invalid payload");n.data=r}return n}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(Io){return!1}}static isPayloadValid(e,t){switch(e){case Ii.CONNECT:return Ui(t);case Ii.DISCONNECT:return void 0===t;case Ii.CONNECT_ERROR:return"string"==typeof t||Ui(t);case Ii.EVENT:case Ii.BINARY_EVENT:return Array.isArray(t)&&("number"==typeof t[0]||"string"==typeof t[0]&&-1===Bi.indexOf(t[0]));case Ii.ACK:case Ii.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class qi{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){const e=Pi(this.reconPack,this.buffers);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Vi=Object.freeze(Object.defineProperty({__proto__:null,Decoder:zi,Encoder:class{constructor(e){this.replacer=e}encode(e){return e.type!==Ii.EVENT&&e.type!==Ii.ACK||!ji(e)?[this.encodeAsString(e)]:this.encodeAsBinary({type:e.type===Ii.EVENT?Ii.BINARY_EVENT:Ii.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id})}encodeAsString(e){let t=""+e.type;return e.type!==Ii.BINARY_EVENT&&e.type!==Ii.BINARY_ACK||(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),t}encodeAsBinary(e){const t=Li(e),n=this.encodeAsString(t.packet),r=t.buffers;return r.unshift(n),r}},get PacketType(){return Ii},protocol:5},Symbol.toStringTag,{value:"Module"}));function $i(e,t,n){return e.on(t,n),function(){e.off(t,n)}}const Wi=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Hi extends Zr{constructor(e,t,n){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,n&&n.auth&&(this.auth=n.auth),this._opts=Object.assign({},n),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const e=this.io;this.subs=[$i(e,"open",this.onopen.bind(this)),$i(e,"packet",this.onpacket.bind(this)),$i(e,"error",this.onerror.bind(this)),$i(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var n,r,i;if(Wi.hasOwnProperty(e))throw new Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;const o={type:Ii.EVENT,data:t,options:{}};if(o.options.compress=!1!==this.flags.compress,"function"==typeof t[t.length-1]){const e=this.ids++,n=t.pop();this._registerAckCallback(e,n),o.id=e}const s=null===(r=null===(n=this.io.engine)||void 0===n?void 0:n.transport)||void 0===r?void 0:r.writable,a=this.connected&&!(null===(i=this.io.engine)||void 0===i?void 0:i._hasPingExpired());return this.flags.volatile&&!s||(a?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o)),this.flags={},this}_registerAckCallback(e,t){var n;const r=null!==(n=this.flags.timeout)&&void 0!==n?n:this._opts.ackTimeout;if(void 0===r)return void(this.acks[e]=t);const i=this.io.setTimeoutFn((()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&this.sendBuffer.splice(t,1);t.call(this,new Error("operation has timed out"))}),r),o=(...e)=>{this.io.clearTimeoutFn(i),t.apply(this,e)};o.withError=!0,this.acks[e]=o}emitWithAck(e,...t){return new Promise(((n,r)=>{const i=(e,t)=>e?r(e):n(t);i.withError=!0,t.push(i),this.emit(e,...t)}))}_addToQueue(e){let t;"function"==typeof e[e.length-1]&&(t=e.pop());const n={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push(((e,...r)=>{if(n!==this._queue[0])return;return null!==e?n.tryCount>this._opts.retries&&(this._queue.shift(),t&&t(e)):(this._queue.shift(),t&&t(null,...r)),n.pending=!1,this._drainQueue()})),this._queue.push(n),this._drainQueue()}_drainQueue(e=!1){if(!this.connected||0===this._queue.length)return;const t=this._queue[0];t.pending&&!e||(t.pending=!0,t.tryCount++,this.flags=t.flags,this.emit.apply(this,t.args))}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){"function"==typeof this.auth?this.auth((e=>{this._sendConnectPacket(e)})):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:Ii.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach((e=>{if(!this.sendBuffer.some((t=>String(t.id)===e))){const t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,new Error("socket has been disconnected"))}}))}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case Ii.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case Ii.EVENT:case Ii.BINARY_EVENT:this.onevent(e);break;case Ii.ACK:case Ii.BINARY_ACK:this.onack(e);break;case Ii.DISCONNECT:this.ondisconnect();break;case Ii.CONNECT_ERROR:this.destroy();const t=new Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){const t=e.data||[];null!=e.id&&t.push(this.ack(e.id)),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length){const t=this._anyListeners.slice();for(const n of t)n.apply(this,e)}super.emit.apply(this,e),this._pid&&e.length&&"string"==typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){const t=this;let n=!1;return function(...r){n||(n=!0,t.packet({type:Ii.ACK,id:e,data:r}))}}onack(e){const t=this.acks[e.id];"function"==typeof t&&(delete this.acks[e.id],t.withError&&e.data.unshift(null),t.apply(this,e.data))}onconnect(e,t){this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach((e=>this.emitEvent(e))),this.receiveBuffer=[],this.sendBuffer.forEach((e=>{this.notifyOutgoingListeners(e),this.packet(e)})),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach((e=>e())),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:Ii.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){const t=this._anyListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){const t=this._anyOutgoingListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const t=this._anyOutgoingListeners.slice();for(const n of t)n.apply(this,e.data)}}}function Yi(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}Yi.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=1&Math.floor(10*t)?e+n:e-n}return 0|Math.min(e,this.max)},Yi.prototype.reset=function(){this.attempts=0},Yi.prototype.setMin=function(e){this.ms=e},Yi.prototype.setMax=function(e){this.max=e},Yi.prototype.setJitter=function(e){this.jitter=e};class Gi extends Zr{constructor(e,t){var n;super(),this.nsps={},this.subs=[],e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.opts=t,ri(this,t),this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(n=t.randomizationFactor)&&void 0!==n?n:.5),this.backoff=new Yi({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this._readyState="closed",this.uri=e;const r=t.parser||Vi;this.encoder=new r.Encoder,this.decoder=new r.Decoder,this._autoConnect=!1!==t.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(~this._readyState.indexOf("open"))return this;this.engine=new Oi(this.uri,this.opts);const t=this.engine,n=this;this._readyState="opening",this.skipReconnect=!1;const r=$i(t,"open",(function(){n.onopen(),e&&e()})),i=t=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},o=$i(t,"error",i);if(!1!==this._timeout){const e=this._timeout,n=this.setTimeoutFn((()=>{r(),i(new Error("timeout")),t.close()}),e);this.opts.autoUnref&&n.unref(),this.subs.push((()=>{this.clearTimeoutFn(n)}))}return this.subs.push(r),this.subs.push(o),this}connect(e){return this.open(e)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const e=this.engine;this.subs.push($i(e,"ping",this.onping.bind(this)),$i(e,"data",this.ondata.bind(this)),$i(e,"error",this.onerror.bind(this)),$i(e,"close",this.onclose.bind(this)),$i(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(Io){this.onclose("parse error",Io)}}ondecoded(e){Xr((()=>{this.emitReserved("packet",e)}),this.setTimeoutFn)}onerror(e){this.emitReserved("error",e)}socket(e,t){let n=this.nsps[e];return n?this._autoConnect&&!n.active&&n.connect():(n=new Hi(this,e,t),this.nsps[e]=n),n}_destroy(e){const t=Object.keys(this.nsps);for(const n of t){if(this.nsps[n].active)return}this._close()}_packet(e){const t=this.encoder.encode(e);for(let n=0;n<t.length;n++)this.engine.write(t[n],e.options)}cleanup(){this.subs.forEach((e=>e())),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var n;this.cleanup(),null===(n=this.engine)||void 0===n||n.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const t=this.backoff.duration();this._reconnecting=!0;const n=this.setTimeoutFn((()=>{e.skipReconnect||(this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open((t=>{t?(e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):e.onreconnect()})))}),t);this.opts.autoUnref&&n.unref(),this.subs.push((()=>{this.clearTimeoutFn(n)}))}}onreconnect(){const e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}const Ki={};function Ji(e,t){"object"==typeof e&&(t=e,e=void 0);const n=function(e,t="",n){let r=e;n=n||"undefined"!=typeof location&&location,null==e&&(e=n.protocol+"//"+n.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?n.protocol+e:n.host+e),/^(https?|wss?):\/\//.test(e)||(e=void 0!==n?n.protocol+"//"+e:"https://"+e),r=Ei(e)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const i=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+i+":"+r.port+t,r.href=r.protocol+"://"+i+(n&&n.port===r.port?"":":"+r.port),r}(e,(t=t||{}).path||"/socket.io"),r=n.source,i=n.id,o=n.path,s=Ki[i]&&o in Ki[i].nsps;let a;return t.forceNew||t["force new connection"]||!1===t.multiplex||s?a=new Gi(r,t):(Ki[i]||(Ki[i]=new Gi(r,t)),a=Ki[i]),n.query&&!t.query&&(t.query=n.queryKey),a.socket(n.path,t)}Object.assign(Ji,{Manager:Gi,Socket:Hi,io:Ji,connect:Ji});let Zi=null;const Xi=()=>Ji(re.VITE_API_URL,{autoConnect:!0,auth:{jwt_token:localStorage.getItem("jwt_token"),device:`${Cr.osName} ${Cr.osVersion}`,browser:Cr.browserName}}),Qi=()=>(Zi||(Zi=Xi()),Zi);class eo extends Error{}function to(e){let t=e.replace(/-/g,"+").replace(/_/g,"/");switch(t.length%4){case 0:break;case 2:t+="==";break;case 3:t+="=";break;default:throw new Error("base64 string is not of the correct length")}try{return function(e){return decodeURIComponent(atob(e).replace(/(.)/g,((e,t)=>{let n=t.charCodeAt(0).toString(16).toUpperCase();return n.length<2&&(n="0"+n),"%"+n})))}(t)}catch(zc){return atob(t)}}function no(e,t){if("string"!=typeof e)throw new eo("Invalid token specified: must be a string");t||(t={});const n=!0===t.header?0:1,r=e.split(".")[n];if("string"!=typeof r)throw new eo(`Invalid token specified: missing part #${n+1}`);let i;try{i=to(r)}catch(Io){throw new eo(`Invalid token specified: invalid base64 for part #${n+1} (${Io.message})`)}try{return JSON.parse(i)}catch(Io){throw new eo(`Invalid token specified: invalid json for part #${n+1} (${Io.message})`)}}eo.prototype.name="InvalidTokenError";const ro=({children:e})=>{const[t,n]=E.useState(!1),[r,i]=E.useState(null),[o,s]=E.useState(!1);E.useEffect((()=>{const e=e=>{e._id===r._id&&a()},t=e=>{e.role_id===r.role_id&&a()},n=Qi();return n.on("users/changed",e),n.on("roles/changed",t),()=>{n.off("users/changed",e),n.off("roles/changed",t)}}),[r]);const a=()=>new Promise(((e,t)=>{const r=localStorage.getItem("jwt_token");if(!r)return n(!0),t("No token found");const o=no(r);if(new Date(1e3*o.exp).getTime()<(new Date).getTime())return n(!0),t("Your session has expired");Dn.get("/users/user",{meta:{showSnackbar:!1}}).then((t=>{const r=t.data;r.hasPermissions=function(e,t="AND"){return"AND"===t?e.every((e=>this.permissions.find((t=>t.permission_id===e)))):e.some((e=>this.permissions.find((t=>t.permission_id===e))))},i(r),n(!0),e(r)})).catch((e=>{localStorage.removeItem("jwt_token"),t(e)}))}));return k.jsx(Er.Provider,{value:{user:r,userFetched:t,login:async({username:e,password:t})=>new Promise(((n,r)=>{Dn.get("/users/auth",{headers:{Authorization:`Basic ${btoa(`${e}:${t}`)}`},meta:{showSnackbar:!1}}).then((e=>{localStorage.setItem("jwt_token",e.data.jwt_token),Zi&&Zi.disconnect(),Zi=Xi(),a().then((e=>n(e))).catch(r)})).catch(r)})),logout:e=>{localStorage.removeItem("jwt_token"),localStorage.removeItem("showIDs"),Zi&&(Zi.disconnect(),Zi=null),setTimeout((()=>{i(null),e&&e()}),500)},fetchUser:a,sessionExpired:o,setSessionExpired:s},children:e})},io=E.createContext(),oo=re.VITE_GOOGLE_MAPS_API_KEY,so=["geometry","places"],ao=({children:e})=>{const[t,n]=E.useState(Wn.timezone),[r,i]=E.useState(!0),[o,s]=E.useState(null),[a,c]=E.useState(window.innerHeight),[l,u]=E.useState(!(!localStorage.getItem("devMode")||!Number(localStorage.getItem("devMode")))),[d,h]=E.useState((()=>{const e=localStorage.getItem("showIDs");return null!==e&&"true"===e})),f=S(),{isLoaded:p}=K({id:"google-map-script",googleMapsApiKey:oo,libraries:so}),m={xs:T(f.breakpoints.down("sm")),sm:T(f.breakpoints.down("md")),md:T(f.breakpoints.down("lg")),lg:T(f.breakpoints.down("xl"))},g=E.useMemo((()=>p&&window.google),[p]),b=!!(m.xs||m.sm||m.md);return E.useEffect((()=>{localStorage.setItem("devMode",l?1:0)}),[l]),E.useEffect((()=>{const e=()=>{c(window.innerHeight)},t=()=>{document.hidden?i(!1):i(!0)};return window.addEventListener("resize",e),document.addEventListener("visibilitychange",t),()=>{window.removeEventListener("resize",e),document.removeEventListener("visibilitychange",t)}}),[]),k.jsx(io.Provider,{value:{timezone:t,setTimezone:n,google:g,isMobile:b,screenSize:m,selectedVessel:o,setSelectedVessel:s,deviceHeight:a,isTabActive:r,devMode:l,setDevMode:u,showIDs:d,setShowIDs:h},children:e})},co=["localeText"],lo=E.createContext(null),uo=function(e){const{localeText:t}=e,n=O(e,co),{utils:r,localeText:i}=E.useContext(lo)??{utils:void 0,localeText:void 0},o=_({props:n,name:"MuiLocalizationProvider"}),{children:s,dateAdapter:a,dateFormats:c,dateLibInstance:l,adapterLocale:u,localeText:d}=o,h=E.useMemo((()=>C({},d,i,t)),[d,i,t]),f=E.useMemo((()=>{if(!a)return r||null;const e=new a({locale:u,formats:c,instance:l});if(!e.isMUIAdapter)throw new Error(["MUI X: The date adapter should be imported from `@mui/x-date-pickers` or `@mui/x-date-pickers-pro`, not from `@date-io`","For example, `import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs'` instead of `import AdapterDayjs from '@date-io/dayjs'`","More information on the installation documentation: https://mui.com/x/react-date-pickers/getting-started/#installation"].join("\n"));return e}),[a,u,c,l,r]),p=E.useMemo((()=>f?{minDate:f.date("1900-01-01T00:00:00.000"),maxDate:f.date("2099-12-31T00:00:00.000")}:null),[f]),m=E.useMemo((()=>({utils:f,defaultDates:p,localeText:h})),[p,f,h]);return k.jsx(lo.Provider,{value:m,children:s})};var ho,fo={exports:{}};var po=(ho||(ho=1,fo.exports=function(){var e="week",t="year";return function(n,r,i){var o=r.prototype;o.week=function(n){if(void 0===n&&(n=null),null!==n)return this.add(7*(n-this.week()),"day");var r=this.$locale().yearStart||1;if(11===this.month()&&this.date()>25){var o=i(this).startOf(t).add(1,t).date(r),s=i(this).endOf(e);if(o.isBefore(s))return 1}var a=i(this).startOf(t).date(r).startOf(e).subtract(1,"millisecond"),c=this.diff(a,e,!0);return c<0?i(this).startOf("week").week():Math.ceil(c)},o.weeks=function(e){return void 0===e&&(e=null),this.week(e)}}}()),fo.exports);const mo=t(po);var go,bo={exports:{}};var yo=(go||(go=1,bo.exports=function(){var e={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"},t=/(\[[^[]*\])|([-_:/.,()\s]+)|(A|a|Q|YYYY|YY?|ww?|MM?M?M?|Do|DD?|hh?|HH?|mm?|ss?|S{1,3}|z|ZZ?)/g,n=/\d/,r=/\d\d/,i=/\d\d?/,o=/\d*[^-_:/,()\s\d]+/,s={},a=function(e){return(e=+e)+(e>68?1900:2e3)},c=function(e){return function(t){this[e]=+t}},l=[/[+-]\d\d:?(\d\d)?|Z/,function(e){(this.zone||(this.zone={})).offset=function(e){if(!e)return 0;if("Z"===e)return 0;var t=e.match(/([+-]|\d\d)/g),n=60*t[1]+(+t[2]||0);return 0===n?0:"+"===t[0]?-n:n}(e)}],u=function(e){var t=s[e];return t&&(t.indexOf?t:t.s.concat(t.f))},d=function(e,t){var n,r=s.meridiem;if(r){for(var i=1;i<=24;i+=1)if(e.indexOf(r(i,0,t))>-1){n=i>12;break}}else n=e===(t?"pm":"PM");return n},h={A:[o,function(e){this.afternoon=d(e,!1)}],a:[o,function(e){this.afternoon=d(e,!0)}],Q:[n,function(e){this.month=3*(e-1)+1}],S:[n,function(e){this.milliseconds=100*+e}],SS:[r,function(e){this.milliseconds=10*+e}],SSS:[/\d{3}/,function(e){this.milliseconds=+e}],s:[i,c("seconds")],ss:[i,c("seconds")],m:[i,c("minutes")],mm:[i,c("minutes")],H:[i,c("hours")],h:[i,c("hours")],HH:[i,c("hours")],hh:[i,c("hours")],D:[i,c("day")],DD:[r,c("day")],Do:[o,function(e){var t=s.ordinal,n=e.match(/\d+/);if(this.day=n[0],t)for(var r=1;r<=31;r+=1)t(r).replace(/\[|\]/g,"")===e&&(this.day=r)}],w:[i,c("week")],ww:[r,c("week")],M:[i,c("month")],MM:[r,c("month")],MMM:[o,function(e){var t=u("months"),n=(u("monthsShort")||t.map((function(e){return e.slice(0,3)}))).indexOf(e)+1;if(n<1)throw new Error;this.month=n%12||n}],MMMM:[o,function(e){var t=u("months").indexOf(e)+1;if(t<1)throw new Error;this.month=t%12||t}],Y:[/[+-]?\d+/,c("year")],YY:[r,function(e){this.year=a(e)}],YYYY:[/\d{4}/,c("year")],Z:l,ZZ:l};function f(n){var r,i;r=n,i=s&&s.formats;for(var o=(n=r.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(t,n,r){var o=r&&r.toUpperCase();return n||i[r]||e[r]||i[o].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,(function(e,t,n){return t||n.slice(1)}))}))).match(t),a=o.length,c=0;c<a;c+=1){var l=o[c],u=h[l],d=u&&u[0],f=u&&u[1];o[c]=f?{regex:d,parser:f}:l.replace(/^\[|\]$/g,"")}return function(e){for(var t={},n=0,r=0;n<a;n+=1){var i=o[n];if("string"==typeof i)r+=i.length;else{var s=i.regex,c=i.parser,l=e.slice(r),u=s.exec(l)[0];c.call(t,u),e=e.replace(u,"")}}return function(e){var t=e.afternoon;if(void 0!==t){var n=e.hours;t?n<12&&(e.hours+=12):12===n&&(e.hours=0),delete e.afternoon}}(t),t}}return function(e,t,n){n.p.customParseFormat=!0,e&&e.parseTwoDigitYear&&(a=e.parseTwoDigitYear);var r=t.prototype,i=r.parse;r.parse=function(e){var t=e.date,r=e.utc,o=e.args;this.$u=r;var a=o[1];if("string"==typeof a){var c=!0===o[2],l=!0===o[3],u=c||l,d=o[2];l&&(d=o[2]),s=this.$locale(),!c&&d&&(s=n.Ls[d]),this.$d=function(e,t,n,r){try{if(["x","X"].indexOf(t)>-1)return new Date(("X"===t?1e3:1)*e);var i=f(t)(e),o=i.year,s=i.month,a=i.day,c=i.hours,l=i.minutes,u=i.seconds,d=i.milliseconds,h=i.zone,p=i.week,m=new Date,g=a||(o||s?1:m.getDate()),b=o||m.getFullYear(),y=0;o&&!s||(y=s>0?s-1:m.getMonth());var w,v=c||0,x=l||0,E=u||0,M=d||0;return h?new Date(Date.UTC(b,y,g,v,x,E,M+60*h.offset*1e3)):n?new Date(Date.UTC(b,y,g,v,x,E,M)):(w=new Date(b,y,g,v,x,E,M),p&&(w=r(w).week(p).toDate()),w)}catch(k){return new Date("")}}(t,a,r,n),this.init(),d&&!0!==d&&(this.$L=this.locale(d).$L),u&&t!=this.format(a)&&(this.$d=new Date("")),s={}}else if(a instanceof Array)for(var h=a.length,p=1;p<=h;p+=1){o[1]=a[p-1];var m=n.apply(this,o);if(m.isValid()){this.$d=m.$d,this.$L=m.$L,this.init();break}p===h&&(this.$d=new Date(""))}else i.call(this,e)}}}()),bo.exports);const wo=t(yo);var vo,xo={exports:{}};var Eo=(vo||(vo=1,xo.exports=function(){var e={LTS:"h:mm:ss A",LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY h:mm A",LLLL:"dddd, MMMM D, YYYY h:mm A"};return function(t,n,r){var i=n.prototype,o=i.format;r.en.formats=e,i.format=function(t){void 0===t&&(t="YYYY-MM-DDTHH:mm:ssZ");var n,r=this.$locale().formats,i=(n=void 0===r?{}:r,t.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,(function(t,r,i){var o=i&&i.toUpperCase();return r||n[i]||e[i]||n[o].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,(function(e,t,n){return t||n.slice(1)}))})));return o.call(this,i)}}}()),xo.exports);const Mo=t(Eo);var ko,So={exports:{}};var To=(ko||(ko=1,So.exports=function(e,t,n){t.prototype.isBetween=function(e,t,r,i){var o=n(e),s=n(t),a="("===(i=i||"()")[0],c=")"===i[1];return(a?this.isAfter(o,r):!this.isBefore(o,r))&&(c?this.isBefore(s,r):!this.isAfter(s,r))||(a?this.isBefore(o,r):!this.isAfter(o,r))&&(c?this.isAfter(s,r):!this.isBefore(s,r))}}),So.exports);const Oo=t(To);var _o,Co={exports:{}};var Ao=(_o||(_o=1,Co.exports=function(e,t){var n=t.prototype,r=n.format;n.format=function(e){var t=this,n=this.$locale();if(!this.isValid())return r.bind(this)(e);var i=this.$utils(),o=(e||"YYYY-MM-DDTHH:mm:ssZ").replace(/\[([^\]]+)]|Q|wo|ww|w|WW|W|zzz|z|gggg|GGGG|Do|X|x|k{1,2}|S/g,(function(e){switch(e){case"Q":return Math.ceil((t.$M+1)/3);case"Do":return n.ordinal(t.$D);case"gggg":return t.weekYear();case"GGGG":return t.isoWeekYear();case"wo":return n.ordinal(t.week(),"W");case"w":case"ww":return i.s(t.week(),"w"===e?1:2,"0");case"W":case"WW":return i.s(t.isoWeek(),"W"===e?1:2,"0");case"k":case"kk":return i.s(String(0===t.$H?24:t.$H),"k"===e?1:2,"0");case"X":return Math.floor(t.$d.getTime()/1e3);case"x":return t.$d.getTime();case"z":return"["+t.offsetName()+"]";case"zzz":return"["+t.offsetName("long")+"]";default:return e}}));return r.bind(this)(o)}}),Co.exports);const Ro=t(Ao);ne.extend(Mo),ne.extend(mo),ne.extend(Oo),ne.extend(Ro);const Do={YY:"year",YYYY:{sectionType:"year",contentType:"digit",maxLength:4},M:{sectionType:"month",contentType:"digit",maxLength:2},MM:"month",MMM:{sectionType:"month",contentType:"letter"},MMMM:{sectionType:"month",contentType:"letter"},D:{sectionType:"day",contentType:"digit",maxLength:2},DD:"day",Do:{sectionType:"day",contentType:"digit-with-letter"},d:{sectionType:"weekDay",contentType:"digit",maxLength:2},dd:{sectionType:"weekDay",contentType:"letter"},ddd:{sectionType:"weekDay",contentType:"letter"},dddd:{sectionType:"weekDay",contentType:"letter"},A:"meridiem",a:"meridiem",H:{sectionType:"hours",contentType:"digit",maxLength:2},HH:"hours",h:{sectionType:"hours",contentType:"digit",maxLength:2},hh:"hours",m:{sectionType:"minutes",contentType:"digit",maxLength:2},mm:"minutes",s:{sectionType:"seconds",contentType:"digit",maxLength:2},ss:"seconds"},jo={year:"YYYY",month:"MMMM",monthShort:"MMM",dayOfMonth:"D",dayOfMonthFull:"Do",weekday:"dddd",weekdayShort:"dd",hours24h:"HH",hours12h:"hh",meridiem:"A",minutes:"mm",seconds:"ss",fullDate:"ll",keyboardDate:"L",shortDate:"MMM D",normalDate:"D MMMM",normalDateWithWeekday:"ddd, MMM D",fullTime:"LT",fullTime12h:"hh:mm A",fullTime24h:"HH:mm",keyboardDateTime:"L LT",keyboardDateTime12h:"L hh:mm A",keyboardDateTime24h:"L HH:mm"},Lo=["Missing UTC plugin","To be able to use UTC or timezones, you have to enable the `utc` plugin","Find more information on https://mui.com/x/react-date-pickers/timezone/#day-js-and-utc"].join("\n"),No=["Missing timezone plugin","To be able to use timezones, you have to enable both the `utc` and the `timezone` plugin","Find more information on https://mui.com/x/react-date-pickers/timezone/#day-js-and-timezone"].join("\n");class Po{constructor({locale:e,formats:t}={}){this.isMUIAdapter=!0,this.isTimezoneCompatible=!0,this.lib="dayjs",this.dayjs=void 0,this.locale=void 0,this.formats=void 0,this.escapedCharacters={start:"[",end:"]"},this.formatTokenMap=Do,this.setLocaleToValue=e=>{const t=this.getCurrentLocaleCode();return t===e.locale()?e:e.locale(t)},this.hasUTCPlugin=()=>void 0!==ne.utc,this.hasTimezonePlugin=()=>void 0!==ne.tz,this.isSame=(e,t,n)=>{const r=this.setTimezone(t,this.getTimezone(e));return e.format(n)===r.format(n)},this.cleanTimezone=e=>{switch(e){case"default":return;case"system":return ne.tz.guess();default:return e}},this.createSystemDate=e=>{if(this.hasUTCPlugin()&&this.hasTimezonePlugin()){const t=ne.tz.guess();return"UTC"!==t?ne.tz(e,t):ne(e)}return ne(e)},this.createUTCDate=e=>{if(!this.hasUTCPlugin())throw new Error(Lo);return ne.utc(e)},this.createTZDate=(e,t)=>{if(!this.hasUTCPlugin())throw new Error(Lo);if(!this.hasTimezonePlugin())throw new Error(No);const n=void 0!==e&&!e.endsWith("Z");return ne(e).tz(this.cleanTimezone(t),n)},this.getLocaleFormats=()=>{const e=ne.Ls;let t=e[this.locale||"en"];return void 0===t&&(t=e.en),t.formats},this.adjustOffset=e=>{if(!this.hasTimezonePlugin())return e;const t=this.getTimezone(e);if("UTC"!==t){const n=e.tz(this.cleanTimezone(t),!0);if(n.$offset===(e.$offset??0))return e;e.$offset=n.$offset}return e},this.date=(e,t="default")=>{if(null===e)return null;let n;return n="UTC"===t?this.createUTCDate(e):"system"===t||"default"===t&&!this.hasTimezonePlugin()?this.createSystemDate(e):this.createTZDate(e,t),void 0===this.locale?n:n.locale(this.locale)},this.getInvalidDate=()=>ne(new Date("Invalid date")),this.getTimezone=e=>{if(this.hasTimezonePlugin()){const t=e.$x?.$timezone;if(t)return t}return this.hasUTCPlugin()&&e.isUTC()?"UTC":"system"},this.setTimezone=(e,t)=>{if(this.getTimezone(e)===t)return e;if("UTC"===t){if(!this.hasUTCPlugin())throw new Error(Lo);return e.utc()}if("system"===t)return e.local();if(!this.hasTimezonePlugin()){if("default"===t)return e;throw new Error(No)}return ne.tz(e,this.cleanTimezone(t))},this.toJsDate=e=>e.toDate(),this.parse=(e,t)=>""===e?null:this.dayjs(e,t,this.locale,!0),this.getCurrentLocaleCode=()=>this.locale||"en",this.is12HourCycleInCurrentLocale=()=>/A|a/.test(this.getLocaleFormats().LT||""),this.expandFormat=e=>{const t=this.getLocaleFormats();return e.replace(/(\[[^\]]+])|(LTS?|l{1,4}|L{1,4})/g,((e,n,r)=>{const i=r&&r.toUpperCase();return n||t[r]||t[i].replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g,((e,t,n)=>t||n.slice(1)))}))},this.isValid=e=>null!=e&&e.isValid(),this.format=(e,t)=>this.formatByString(e,this.formats[t]),this.formatByString=(e,t)=>this.dayjs(e).format(t),this.formatNumber=e=>e,this.isEqual=(e,t)=>null===e&&null===t||null!==e&&null!==t&&e.toDate().getTime()===t.toDate().getTime(),this.isSameYear=(e,t)=>this.isSame(e,t,"YYYY"),this.isSameMonth=(e,t)=>this.isSame(e,t,"YYYY-MM"),this.isSameDay=(e,t)=>this.isSame(e,t,"YYYY-MM-DD"),this.isSameHour=(e,t)=>e.isSame(t,"hour"),this.isAfter=(e,t)=>e>t,this.isAfterYear=(e,t)=>this.hasUTCPlugin()?!this.isSameYear(e,t)&&e.utc()>t.utc():e.isAfter(t,"year"),this.isAfterDay=(e,t)=>this.hasUTCPlugin()?!this.isSameDay(e,t)&&e.utc()>t.utc():e.isAfter(t,"day"),this.isBefore=(e,t)=>e<t,this.isBeforeYear=(e,t)=>this.hasUTCPlugin()?!this.isSameYear(e,t)&&e.utc()<t.utc():e.isBefore(t,"year"),this.isBeforeDay=(e,t)=>this.hasUTCPlugin()?!this.isSameDay(e,t)&&e.utc()<t.utc():e.isBefore(t,"day"),this.isWithinRange=(e,[t,n])=>e>=t&&e<=n,this.startOfYear=e=>this.adjustOffset(e.startOf("year")),this.startOfMonth=e=>this.adjustOffset(e.startOf("month")),this.startOfWeek=e=>this.adjustOffset(this.setLocaleToValue(e).startOf("week")),this.startOfDay=e=>this.adjustOffset(e.startOf("day")),this.endOfYear=e=>this.adjustOffset(e.endOf("year")),this.endOfMonth=e=>this.adjustOffset(e.endOf("month")),this.endOfWeek=e=>this.adjustOffset(this.setLocaleToValue(e).endOf("week")),this.endOfDay=e=>this.adjustOffset(e.endOf("day")),this.addYears=(e,t)=>this.adjustOffset(t<0?e.subtract(Math.abs(t),"year"):e.add(t,"year")),this.addMonths=(e,t)=>this.adjustOffset(t<0?e.subtract(Math.abs(t),"month"):e.add(t,"month")),this.addWeeks=(e,t)=>this.adjustOffset(t<0?e.subtract(Math.abs(t),"week"):e.add(t,"week")),this.addDays=(e,t)=>this.adjustOffset(t<0?e.subtract(Math.abs(t),"day"):e.add(t,"day")),this.addHours=(e,t)=>this.adjustOffset(t<0?e.subtract(Math.abs(t),"hour"):e.add(t,"hour")),this.addMinutes=(e,t)=>this.adjustOffset(t<0?e.subtract(Math.abs(t),"minute"):e.add(t,"minute")),this.addSeconds=(e,t)=>this.adjustOffset(t<0?e.subtract(Math.abs(t),"second"):e.add(t,"second")),this.getYear=e=>e.year(),this.getMonth=e=>e.month(),this.getDate=e=>e.date(),this.getHours=e=>e.hour(),this.getMinutes=e=>e.minute(),this.getSeconds=e=>e.second(),this.getMilliseconds=e=>e.millisecond(),this.setYear=(e,t)=>this.adjustOffset(e.set("year",t)),this.setMonth=(e,t)=>this.adjustOffset(e.set("month",t)),this.setDate=(e,t)=>this.adjustOffset(e.set("date",t)),this.setHours=(e,t)=>this.adjustOffset(e.set("hour",t)),this.setMinutes=(e,t)=>this.adjustOffset(e.set("minute",t)),this.setSeconds=(e,t)=>this.adjustOffset(e.set("second",t)),this.setMilliseconds=(e,t)=>this.adjustOffset(e.set("millisecond",t)),this.getDaysInMonth=e=>e.daysInMonth(),this.getWeekArray=e=>{const t=this.startOfWeek(this.startOfMonth(e)),n=this.endOfWeek(this.endOfMonth(e));let r=0,i=t;const o=[];for(;i<n;){const e=Math.floor(r/7);o[e]=o[e]||[],o[e].push(i),i=this.addDays(i,1),r+=1}return o},this.getWeekNumber=e=>e.week(),this.getYearRange=([e,t])=>{const n=this.startOfYear(e),r=this.endOfYear(t),i=[];let o=n;for(;this.isBefore(o,r);)i.push(o),o=this.addYears(o,1);return i},this.dayjs=((e,t)=>t?(...n)=>e(...n).locale(t):e)(ne,e),this.locale=e,this.formats=C({},jo,t),ne.extend(wo)}getDayOfWeek(e){return e.day()+1}}function Fo(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(n=Fo(e[t]))&&(r&&(r+=" "),r+=n);else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}function Bo(){for(var e,t,n=0,r="";n<arguments.length;)(e=arguments[n++])&&(t=Fo(e))&&(r&&(r+=" "),r+=t);return r}let Io={data:""},Uo=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||Io,zo=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,qo=/\/\*[^]*?\*\/|  +/g,Vo=/\n+/g,$o=(e,t)=>{let n="",r="",i="";for(let o in e){let s=e[o];"@"==o[0]?"i"==o[1]?n=o+" "+s+";":r+="f"==o[1]?$o(s,o):o+"{"+$o(s,"k"==o[1]?"":t)+"}":"object"==typeof s?r+=$o(s,t?t.replace(/([^,])+/g,(e=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)))):o):null!=s&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),i+=$o.p?$o.p(o,s):o+":"+s+";")}return n+(t&&i?t+"{"+i+"}":i)+r},Wo={},Ho=e=>{if("object"==typeof e){let t="";for(let n in e)t+=n+Ho(e[n]);return t}return e},Yo=(e,t,n,r,i)=>{let o=Ho(e),s=Wo[o]||(Wo[o]=(e=>{let t=0,n=11;for(;t<e.length;)n=101*n+e.charCodeAt(t++)>>>0;return"go"+n})(o));if(!Wo[s]){let t=o!==e?e:(e=>{let t,n,r=[{}];for(;t=zo.exec(e.replace(qo,""));)t[4]?r.shift():t[3]?(n=t[3].replace(Vo," ").trim(),r.unshift(r[0][n]=r[0][n]||{})):r[0][t[1]]=t[2].replace(Vo," ").trim();return r[0]})(e);Wo[s]=$o(i?{["@keyframes "+s]:t}:t,n?"":"."+s)}let a=n&&Wo.g?Wo.g:null;return n&&(Wo.g=Wo[s]),((e,t,n,r)=>{r?t.data=t.data.replace(r,e):-1===t.data.indexOf(e)&&(t.data=n?e+t.data:t.data+e)})(Wo[s],t,r,a),s};function Go(e){let t=this||{},n=e.call?e(t.p):e;return Yo(n.unshift?n.raw?((e,t,n)=>e.reduce(((e,r,i)=>{let o=t[i];if(o&&o.call){let e=o(n),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;o=t?"."+t:e&&"object"==typeof e?e.props?"":$o(e,""):!1===e?"":e}return e+r+(null==o?"":o)}),""))(n,[].slice.call(arguments,1),t.p):n.reduce(((e,n)=>Object.assign(e,n&&n.call?n(t.p):n)),{}):n,Uo(t.target),t.g,t.o,t.k)}function Ko(e,t,n){return t&&function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}function Jo(){return Jo=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Jo.apply(this,arguments)}function Zo(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,e.__proto__=t}function Xo(e,t){if(null==e)return{};var n,r,i={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(i[n]=e[n]);return i}function Qo(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}Go.bind({g:1}),Go.bind({k:1});var es=function(){return""},ts=A.createContext({enqueueSnackbar:es,closeSnackbar:es}),ns="@media (max-width:599.95px)",rs="@media (min-width:600px)",is=function(e){return e.charAt(0).toUpperCase()+e.slice(1)},os=function(e){return""+is(e.vertical)+is(e.horizontal)},ss=function(e){return!!e||0===e},as="unmounted",cs="exited",ls="entering",us="entered",ds="exiting",hs=function(e){function t(t){var n;n=e.call(this,t)||this;var r,i=t.appear;return n.appearStatus=null,t.in?i?(r=cs,n.appearStatus=ls):r=us:r=t.unmountOnExit||t.mountOnEnter?as:cs,n.state={status:r},n.nextCallback=null,n}Zo(t,e),t.getDerivedStateFromProps=function(e,t){return e.in&&t.status===as?{status:cs}:null};var n=t.prototype;return n.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},n.componentDidUpdate=function(e){var t=null;if(e!==this.props){var n=this.state.status;this.props.in?n!==ls&&n!==us&&(t=ls):n!==ls&&n!==us||(t=ds)}this.updateStatus(!1,t)},n.componentWillUnmount=function(){this.cancelNextCallback()},n.getTimeouts=function(){var e=this.props.timeout,t=e,n=e;return null!=e&&"number"!=typeof e&&"string"!=typeof e&&(n=e.exit,t=e.enter),{exit:n,enter:t}},n.updateStatus=function(e,t){void 0===e&&(e=!1),null!==t?(this.cancelNextCallback(),t===ls?this.performEnter(e):this.performExit()):this.props.unmountOnExit&&this.state.status===cs&&this.setState({status:as})},n.performEnter=function(e){var t=this,n=this.props.enter,r=e,i=this.getTimeouts();e||n?(this.props.onEnter&&this.props.onEnter(this.node,r),this.safeSetState({status:ls},(function(){t.props.onEntering&&t.props.onEntering(t.node,r),t.onTransitionEnd(i.enter,(function(){t.safeSetState({status:us},(function(){t.props.onEntered&&t.props.onEntered(t.node,r)}))}))}))):this.safeSetState({status:us},(function(){t.props.onEntered&&t.props.onEntered(t.node,r)}))},n.performExit=function(){var e=this,t=this.props.exit,n=this.getTimeouts();t?(this.props.onExit&&this.props.onExit(this.node),this.safeSetState({status:ds},(function(){e.props.onExiting&&e.props.onExiting(e.node),e.onTransitionEnd(n.exit,(function(){e.safeSetState({status:cs},(function(){e.props.onExited&&e.props.onExited(e.node)}))}))}))):this.safeSetState({status:cs},(function(){e.props.onExited&&e.props.onExited(e.node)}))},n.cancelNextCallback=function(){null!==this.nextCallback&&this.nextCallback.cancel&&(this.nextCallback.cancel(),this.nextCallback=null)},n.safeSetState=function(e,t){t=this.setNextCallback(t),this.setState(e,t)},n.setNextCallback=function(e){var t=this,n=!0;return this.nextCallback=function(){n&&(n=!1,t.nextCallback=null,e())},this.nextCallback.cancel=function(){n=!1},this.nextCallback},n.onTransitionEnd=function(e,t){this.setNextCallback(t);var n=null==e&&!this.props.addEndListener;this.node&&!n?(this.props.addEndListener&&this.props.addEndListener(this.node,this.nextCallback),null!=e&&setTimeout(this.nextCallback,e)):setTimeout(this.nextCallback,0)},n.render=function(){var e=this.state.status;if(e===as)return null;var t=this.props;return(0,t.children)(e,Xo(t,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]))},Ko(t,[{key:"node",get:function(){var e,t=null===(e=this.props.nodeRef)||void 0===e?void 0:e.current;if(!t)throw new Error("notistack - Custom snackbar is not refForwarding");return t}}]),t}(A.Component);function fs(){}function ps(e,t){"function"==typeof e?e(t):e&&(e.current=t)}function ms(e,t){return E.useMemo((function(){return null==e&&null==t?null:function(n){ps(e,n),ps(t,n)}}),[e,t])}function gs(e){var t=e.timeout,n=e.style,r=void 0===n?{}:n,i=e.mode;return{duration:"object"==typeof t?t[i]||0:t,easing:r.transitionTimingFunction,delay:r.transitionDelay}}hs.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:fs,onEntering:fs,onEntered:fs,onExit:fs,onExiting:fs,onExited:fs};var bs="cubic-bezier(0.4, 0, 0.2, 1)",ys="cubic-bezier(0.0, 0, 0.2, 1)",ws="cubic-bezier(0.4, 0, 0.6, 1)",vs=function(e){e.scrollTop=e.scrollTop},xs=function(e){return Math.round(e)+"ms"};function Es(e,t){void 0===e&&(e=["all"]);var n=t||{},r=n.duration,i=void 0===r?300:r,o=n.easing,s=void 0===o?bs:o,a=n.delay,c=void 0===a?0:a;return(Array.isArray(e)?e:[e]).map((function(e){var t="string"==typeof i?i:xs(i),n="string"==typeof c?c:xs(c);return e+" "+t+" "+s+" "+n})).join(",")}function Ms(e){var t=function(e){return e&&e.ownerDocument||document}(e);return t.defaultView||window}function ks(e,t){if(t){var n=function(e,t){var n,r=t.getBoundingClientRect(),i=Ms(t);if(t.fakeTransform)n=t.fakeTransform;else{var o=i.getComputedStyle(t);n=o.getPropertyValue("-webkit-transform")||o.getPropertyValue("transform")}var s=0,a=0;if(n&&"none"!==n&&"string"==typeof n){var c=n.split("(")[1].split(")")[0].split(",");s=parseInt(c[4],10),a=parseInt(c[5],10)}switch(e){case"left":return"translateX("+(i.innerWidth+s-r.left)+"px)";case"right":return"translateX(-"+(r.left+r.width-s)+"px)";case"up":return"translateY("+(i.innerHeight+a-r.top)+"px)";default:return"translateY(-"+(r.top+r.height-a)+"px)"}}(e,t);n&&(t.style.webkitTransform=n,t.style.transform=n)}}var Ss=E.forwardRef((function(e,t){var n=e.children,r=e.direction,i=void 0===r?"down":r,o=e.in,s=e.style,a=e.timeout,c=void 0===a?0:a,l=e.onEnter,u=e.onEntered,d=e.onExit,h=e.onExited,f=Xo(e,["children","direction","in","style","timeout","onEnter","onEntered","onExit","onExited"]),p=E.useRef(null),m=ms(n.ref,p),g=ms(m,t),b=E.useCallback((function(){p.current&&ks(i,p.current)}),[i]);return E.useEffect((function(){if(!o&&"down"!==i&&"right"!==i){var e=function(e,t){var n;function r(){for(var r=this,i=arguments.length,o=new Array(i),s=0;s<i;s++)o[s]=arguments[s];clearTimeout(n),n=setTimeout((function(){e.apply(r,o)}),t)}return void 0===t&&(t=166),r.clear=function(){clearTimeout(n)},r}((function(){p.current&&ks(i,p.current)})),t=Ms(p.current);return t.addEventListener("resize",e),function(){e.clear(),t.removeEventListener("resize",e)}}}),[i,o]),E.useEffect((function(){o||b()}),[o,b]),E.createElement(hs,Object.assign({appear:!0,nodeRef:p,onEnter:function(e,t){ks(i,e),vs(e),l&&l(e,t)},onEntered:u,onEntering:function(e){var t=(null==s?void 0:s.transitionTimingFunction)||ys,n=gs({timeout:c,mode:"enter",style:Jo({},s,{transitionTimingFunction:t})});e.style.webkitTransition=Es("-webkit-transform",n),e.style.transition=Es("transform",n),e.style.webkitTransform="none",e.style.transform="none"},onExit:function(e){var t=(null==s?void 0:s.transitionTimingFunction)||ws,n=gs({timeout:c,mode:"exit",style:Jo({},s,{transitionTimingFunction:t})});e.style.webkitTransition=Es("-webkit-transform",n),e.style.transition=Es("transform",n),ks(i,e),d&&d(e)},onExited:function(e){e.style.webkitTransition="",e.style.transition="",h&&h(e)},in:o,timeout:c},f),(function(e,t){return E.cloneElement(n,Jo({ref:g,style:Jo({visibility:"exited"!==e||o?void 0:"hidden"},s,{},n.props.style)},t))}))}));Ss.displayName="Slide";var Ts=function(e){return A.createElement("svg",Object.assign({viewBox:"0 0 24 24",focusable:"false",style:{fontSize:20,marginInlineEnd:8,userSelect:"none",width:"1em",height:"1em",display:"inline-block",fill:"currentColor",flexShrink:0}},e))},Os=function(){return A.createElement(Ts,null,A.createElement("path",{d:"M12 2C6.5 2 2 6.5 2 12S6.5 22 12 22 22 17.5 22 12 17.5 2 12 2M10 17L5 12L6.41\n        10.59L10 14.17L17.59 6.58L19 8L10 17Z"}))},_s=function(){return A.createElement(Ts,null,A.createElement("path",{d:"M13,14H11V10H13M13,18H11V16H13M1,21H23L12,2L1,21Z"}))},Cs=function(){return A.createElement(Ts,null,A.createElement("path",{d:"M12,2C17.53,2 22,6.47 22,12C22,17.53 17.53,22 12,22C6.47,22 2,17.53 2,12C2,\n        6.47 6.47,2 12,2M15.59,7L12,10.59L8.41,7L7,8.41L10.59,12L7,15.59L8.41,17L12,\n        13.41L15.59,17L17,15.59L13.41,12L17,8.41L15.59,7Z"}))},As=function(){return A.createElement(Ts,null,A.createElement("path",{d:"M13,9H11V7H13M13,17H11V11H13M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,\n        0 22,12A10,10 0 0,0 12,2Z"}))},Rs={maxSnack:3,persist:!1,hideIconVariant:!1,disableWindowBlurListener:!1,variant:"default",autoHideDuration:5e3,iconVariant:{default:void 0,success:A.createElement(Os,null),warning:A.createElement(_s,null),error:A.createElement(Cs,null),info:A.createElement(As,null)},anchorOrigin:{vertical:"bottom",horizontal:"left"},TransitionComponent:Ss,transitionDuration:{enter:225,exit:195}},Ds=function(e,t){return function(n,r){return void 0===r&&(r=!1),r?Jo({},Rs[n],{},t[n],{},e[n]):"autoHideDuration"===n?(i=e.autoHideDuration,o=t.autoHideDuration,(s=function(e){return"number"==typeof e||null===e})(i)?i:s(o)?o:Rs.autoHideDuration):"transitionDuration"===n?function(e,t){var n=function(e,t){return t.some((function(t){return typeof e===t}))};return n(e,["string","number"])?e:n(e,["object"])?Jo({},Rs.transitionDuration,{},n(t,["object"])&&t,{},e):n(t,["string","number"])?t:n(t,["object"])?Jo({},Rs.transitionDuration,{},t):Rs.transitionDuration}(e.transitionDuration,t.transitionDuration):e[n]||t[n]||Rs[n];var i,o,s}};function js(e){return Object.entries(e).reduce((function(e,t){var n,r=t[0],i=t[1];return Jo({},e,((n={})[r]=Go(i),n))}),{})}var Ls="notistack-SnackbarContainer",Ns="notistack-Snackbar",Ps="notistack-CollapseWrapper",Fs="notistack-MuiContent",Bs=function(e){return"notistack-MuiContent-"+e},Is=js({root:{height:0},entered:{height:"auto"}}),Us="0px",zs=E.forwardRef((function(e,t){var n=e.children,r=e.in,i=e.onExited,o=E.useRef(null),s=E.useRef(null),a=ms(t,s),c=function(){return o.current?o.current.clientHeight:0};return E.createElement(hs,{in:r,unmountOnExit:!0,onEnter:function(e){e.style.height=Us},onEntered:function(e){e.style.height="auto"},onEntering:function(e){var t=c(),n=gs({timeout:175,mode:"enter"}),r=n.duration,i=n.easing;e.style.transitionDuration="string"==typeof r?r:r+"ms",e.style.height=t+"px",e.style.transitionTimingFunction=i||""},onExit:function(e){e.style.height=c()+"px"},onExited:i,onExiting:function(e){vs(e);var t=gs({timeout:175,mode:"exit"}),n=t.duration,r=t.easing;e.style.transitionDuration="string"==typeof n?n:n+"ms",e.style.height=Us,e.style.transitionTimingFunction=r||""},nodeRef:s,timeout:175},(function(e,t){return E.createElement("div",Object.assign({ref:a,className:Bo(Is.root,"entered"===e&&Is.entered),style:Jo({pointerEvents:"all",overflow:"hidden",minHeight:Us,transition:Es("height")},"entered"===e&&{overflow:"visible"},{},"exited"===e&&!r&&{visibility:"hidden"})},t),E.createElement("div",{ref:o,className:Ps,style:{display:"flex",width:"100%"}},n))}))}));zs.displayName="Collapse";var qs={right:"left",left:"right",bottom:"up",top:"down"},Vs=function(e){return"anchorOrigin"+os(e)},$s=function(){};function Ws(e,t){return e.reduce((function(e,n){return null==n?e:function(){for(var r=arguments.length,i=new Array(r),o=0;o<r;o++)i[o]=arguments[o];var s=[].concat(i);t&&-1===s.indexOf(t)&&s.push(t),e.apply(this,s),n.apply(this,s)}}),$s)}var Hs="undefined"!=typeof window?E.useLayoutEffect:E.useEffect;function Ys(e){var t=E.useRef(e);return Hs((function(){t.current=e})),E.useCallback((function(){return t.current.apply(void 0,arguments)}),[])}var Gs,Ks=E.forwardRef((function(e,t){var n=e.children,r=e.className,i=e.autoHideDuration,o=e.disableWindowBlurListener,s=void 0!==o&&o,a=e.onClose,c=e.id,l=e.open,u=e.SnackbarProps,d=void 0===u?{}:u,h=E.useRef(),f=Ys((function(){a&&a.apply(void 0,arguments)})),p=Ys((function(e){a&&null!=e&&(h.current&&clearTimeout(h.current),h.current=setTimeout((function(){f(null,"timeout",c)}),e))}));E.useEffect((function(){return l&&p(i),function(){h.current&&clearTimeout(h.current)}}),[l,i,p]);var m=function(){h.current&&clearTimeout(h.current)},g=E.useCallback((function(){null!=i&&p(.5*i)}),[i,p]);return E.useEffect((function(){if(!s&&l)return window.addEventListener("focus",g),window.addEventListener("blur",m),function(){window.removeEventListener("focus",g),window.removeEventListener("blur",m)}}),[s,g,l]),E.createElement("div",Object.assign({ref:t},d,{className:Bo(Ns,r),onMouseEnter:function(e){d.onMouseEnter&&d.onMouseEnter(e),m()},onMouseLeave:function(e){d.onMouseLeave&&d.onMouseLeave(e),g()}}),n)}));Ks.displayName="Snackbar";var Js=js({root:(Gs={display:"flex",flexWrap:"wrap",flexGrow:1},Gs[rs]={flexGrow:"initial",minWidth:"288px"},Gs)}),Zs=E.forwardRef((function(e,t){var n=e.className,r=Xo(e,["className"]);return A.createElement("div",Object.assign({ref:t,className:Bo(Js.root,n)},r))}));Zs.displayName="SnackbarContent";var Xs=js({root:{backgroundColor:"#313131",fontSize:"0.875rem",lineHeight:1.43,letterSpacing:"0.01071em",color:"#fff",alignItems:"center",padding:"6px 16px",borderRadius:"4px",boxShadow:"0px 3px 5px -1px rgba(0,0,0,0.2),0px 6px 10px 0px rgba(0,0,0,0.14),0px 1px 18px 0px rgba(0,0,0,0.12)"},lessPadding:{paddingLeft:"20px"},default:{backgroundColor:"#313131"},success:{backgroundColor:"#43a047"},error:{backgroundColor:"#d32f2f"},warning:{backgroundColor:"#ff9800"},info:{backgroundColor:"#2196f3"},message:{display:"flex",alignItems:"center",padding:"8px 0"},action:{display:"flex",alignItems:"center",marginLeft:"auto",paddingLeft:"16px",marginRight:"-8px"}}),Qs="notistack-snackbar",ea=E.forwardRef((function(e,t){var n=e.id,r=e.message,i=e.action,o=e.iconVariant,s=e.variant,a=e.hideIconVariant,c=e.style,l=e.className,u=o[s],d=i;return"function"==typeof d&&(d=d(n)),A.createElement(Zs,{ref:t,role:"alert","aria-describedby":Qs,style:c,className:Bo(Fs,Bs(s),Xs.root,Xs[s],l,!a&&u&&Xs.lessPadding)},A.createElement("div",{id:Qs,className:Xs.message},a?null:u,r),d&&A.createElement("div",{className:Xs.action},d))}));ea.displayName="MaterialDesignContent";var ta,na,ra,ia,oa,sa=E.memo(ea),aa=js({wrappedRoot:{width:"100%",position:"relative",transform:"translateX(0)",top:0,right:0,bottom:0,left:0,minWidth:"288px"}}),ca=function(e){var t=E.useRef(),n=E.useState(!0),r=n[0],i=n[1],o=Ws([e.snack.onClose,e.onClose]),s=E.useCallback((function(){t.current=setTimeout((function(){i((function(e){return!e}))}),125)}),[]);E.useEffect((function(){return function(){t.current&&clearTimeout(t.current)}}),[]);var a,c=e.snack,l=e.classes,u=e.Component,d=void 0===u?sa:u,h=E.useMemo((function(){return function(e){void 0===e&&(e={});var t={containerRoot:!0,containerAnchorOriginTopCenter:!0,containerAnchorOriginBottomCenter:!0,containerAnchorOriginTopRight:!0,containerAnchorOriginBottomRight:!0,containerAnchorOriginTopLeft:!0,containerAnchorOriginBottomLeft:!0};return Object.keys(e).filter((function(e){return!t[e]})).reduce((function(t,n){var r;return Jo({},t,((r={})[n]=e[n],r))}),{})}(l)}),[l]),f=c.open,p=c.SnackbarProps,m=c.TransitionComponent,g=c.TransitionProps,b=c.transitionDuration,y=c.disableWindowBlurListener,w=c.content,v=Xo(c,["open","SnackbarProps","TransitionComponent","TransitionProps","transitionDuration","disableWindowBlurListener","content","entered","requestClose","onEnter","onEntered","onExit","onExited"]),x=Jo({direction:(a=v.anchorOrigin,"center"!==a.horizontal?qs[a.horizontal]:qs[a.vertical]),timeout:b},g),M=w;"function"==typeof M&&(M=M(v.id,v.message));var k=["onEnter","onEntered","onExit","onExited"].reduce((function(t,n){var r;return Jo({},t,((r={})[n]=Ws([e.snack[n],e[n]],v.id),r))}),{});return A.createElement(zs,{in:r,onExited:k.onExited},A.createElement(Ks,{open:f,id:v.id,disableWindowBlurListener:y,autoHideDuration:v.autoHideDuration,className:Bo(aa.wrappedRoot,h.root,h[Vs(v.anchorOrigin)]),SnackbarProps:p,onClose:o},A.createElement(m,Object.assign({},x,{appear:!0,in:f,onExit:k.onExit,onExited:s,onEnter:k.onEnter,onEntered:Ws([k.onEntered,function(){e.snack.requestClose&&o(null,"instructed",e.snack.id)}],v.id)}),M||A.createElement(d,Object.assign({},v)))))},la={default:20},ua={default:6,dense:2},da="."+Ps,ha=js({root:(ta={boxSizing:"border-box",display:"flex",maxHeight:"100%",position:"fixed",zIndex:1400,height:"auto",width:"auto",transition:Es(["top","right","bottom","left","max-width"],{duration:300,easing:"ease"}),pointerEvents:"none"},ta[da]={padding:ua.default+"px 0px",transition:"padding 300ms ease 0ms"},ta.maxWidth="calc(100% - "+2*la.default+"px)",ta[ns]={width:"100%",maxWidth:"calc(100% - 32px)"},ta),rootDense:(na={},na[da]={padding:ua.dense+"px 0px"},na),top:{top:la.default-ua.default+"px",flexDirection:"column"},bottom:{bottom:la.default-ua.default+"px",flexDirection:"column-reverse"},left:(ra={left:la.default+"px"},ra[rs]={alignItems:"flex-start"},ra[ns]={left:"16px"},ra),right:(ia={right:la.default+"px"},ia[rs]={alignItems:"flex-end"},ia[ns]={right:"16px"},ia),center:(oa={left:"50%",transform:"translateX(-50%)"},oa[rs]={alignItems:"center"},oa)}),fa=function(e){var t=e.classes,n=void 0===t?{}:t,r=e.anchorOrigin,i=e.dense,o=e.children,s=Bo(Ls,ha[r.vertical],ha[r.horizontal],ha.root,n.containerRoot,n["containerAnchorOrigin"+os(r)],i&&ha.rootDense);return A.createElement("div",{className:s},o)},pa=E.memo(fa),ma=function(e){return!("string"==typeof e||E.isValidElement(e))},ga=function(e){function t(t){var n;return(n=e.call(this,t)||this).enqueueSnackbar=function(e,t){if(void 0===t&&(t={}),null==e)throw new Error("enqueueSnackbar called with invalid argument");var r=ma(e)?e:t,i=ma(e)?e.message:e,o=r.key,s=r.preventDuplicate,a=Xo(r,["key","preventDuplicate"]),c=ss(o),l=c?o:(new Date).getTime()+Math.random(),u=Ds(a,n.props),d=Jo({id:l},a,{message:i,open:!0,entered:!1,requestClose:!1,persist:u("persist"),action:u("action"),content:u("content"),variant:u("variant"),anchorOrigin:u("anchorOrigin"),disableWindowBlurListener:u("disableWindowBlurListener"),autoHideDuration:u("autoHideDuration"),hideIconVariant:u("hideIconVariant"),TransitionComponent:u("TransitionComponent"),transitionDuration:u("transitionDuration"),TransitionProps:u("TransitionProps",!0),iconVariant:u("iconVariant",!0),style:u("style",!0),SnackbarProps:u("SnackbarProps",!0),className:Bo(n.props.className,a.className)});return d.persist&&(d.autoHideDuration=void 0),n.setState((function(e){if(void 0===s&&n.props.preventDuplicate||s){var t=function(e){return c?e.id===l:e.message===i},r=e.queue.findIndex(t)>-1,o=e.snacks.findIndex(t)>-1;if(r||o)return e}return n.handleDisplaySnack(Jo({},e,{queue:[].concat(e.queue,[d])}))})),l},n.handleDisplaySnack=function(e){return e.snacks.length>=n.maxSnack?n.handleDismissOldest(e):n.processQueue(e)},n.processQueue=function(e){var t=e.queue,n=e.snacks;return t.length>0?Jo({},e,{snacks:[].concat(n,[t[0]]),queue:t.slice(1,t.length)}):e},n.handleDismissOldest=function(e){if(e.snacks.some((function(e){return!e.open||e.requestClose})))return e;var t=!1,r=!1;e.snacks.reduce((function(e,t){return e+(t.open&&t.persist?1:0)}),0)===n.maxSnack&&(r=!0);var i=e.snacks.map((function(e){return t||e.persist&&!r?Jo({},e):(t=!0,e.entered?(e.onClose&&e.onClose(null,"maxsnack",e.id),n.props.onClose&&n.props.onClose(null,"maxsnack",e.id),Jo({},e,{open:!1})):Jo({},e,{requestClose:!0}))}));return Jo({},e,{snacks:i})},n.handleEnteredSnack=function(e,t,r){if(!ss(r))throw new Error("handleEnteredSnack Cannot be called with undefined key");n.setState((function(e){return{snacks:e.snacks.map((function(e){return e.id===r?Jo({},e,{entered:!0}):Jo({},e)}))}}))},n.handleCloseSnack=function(e,t,r){n.props.onClose&&n.props.onClose(e,t,r);var i=void 0===r;n.setState((function(e){var t=e.snacks,n=e.queue;return{snacks:t.map((function(e){return i||e.id===r?e.entered?Jo({},e,{open:!1}):Jo({},e,{requestClose:!0}):Jo({},e)})),queue:n.filter((function(e){return e.id!==r}))}}))},n.closeSnackbar=function(e){var t=n.state.snacks.find((function(t){return t.id===e}));ss(e)&&t&&t.onClose&&t.onClose(null,"instructed",e),n.handleCloseSnack(null,"instructed",e)},n.handleExitedSnack=function(e,t){if(!ss(t))throw new Error("handleExitedSnack Cannot be called with undefined key");n.setState((function(e){var r=n.processQueue(Jo({},e,{snacks:e.snacks.filter((function(e){return e.id!==t}))}));return 0===r.queue.length?r:n.handleDismissOldest(r)}))},n.enqueueSnackbar,n.closeSnackbar,n.state={snacks:[],queue:[],contextValue:{enqueueSnackbar:n.enqueueSnackbar.bind(Qo(n)),closeSnackbar:n.closeSnackbar.bind(Qo(n))}},n}return Zo(t,e),t.prototype.render=function(){var e=this,t=this.state.contextValue,n=this.props,r=n.domRoot,i=n.children,o=n.dense,s=void 0!==o&&o,a=n.Components,c=void 0===a?{}:a,l=n.classes,u=this.state.snacks.reduce((function(e,t){var n,r=os(t.anchorOrigin),i=e[r]||[];return Jo({},e,((n={})[r]=[].concat(i,[t]),n))}),{}),d=Object.keys(u).map((function(t){var n=u[t],r=n[0];return A.createElement(pa,{key:t,dense:s,anchorOrigin:r.anchorOrigin,classes:l},n.map((function(t){return A.createElement(ca,{key:t.id,snack:t,classes:l,Component:c[t.variant],onClose:e.handleCloseSnack,onEnter:e.props.onEnter,onExit:e.props.onExit,onExited:Ws([e.handleExitedSnack,e.props.onExited],t.id),onEntered:Ws([e.handleEnteredSnack,e.props.onEntered],t.id)})})))}));return A.createElement(ts.Provider,{value:t},i,r?R.createPortal(d,r):d)},Ko(t,[{key:"maxSnack",get:function(){return this.props.maxSnack||Rs.maxSnack}}]),t}(E.Component);const ba=()=>{const{enqueueSnackbar:e}=E.useContext(ts);return(t,n={})=>{if(!t)return;const r={hideIconVariant:!0,preventDuplicate:!0,...n};e(t,r)}},ya="2.13.7",wa=(e,t)=>t.some((t=>e instanceof t));let va,xa;const Ea=new WeakMap,Ma=new WeakMap,ka=new WeakMap;let Sa={get(e,t,n){if(e instanceof IDBTransaction){if("done"===t)return Ea.get(e);if("store"===t)return n.objectStoreNames[1]?void 0:n.objectStore(n.objectStoreNames[0])}return Ca(e[t])},set:(e,t,n)=>(e[t]=n,!0),has:(e,t)=>e instanceof IDBTransaction&&("done"===t||"store"===t)||t in e};function Ta(e){Sa=e(Sa)}function Oa(e){return(xa||(xa=[IDBCursor.prototype.advance,IDBCursor.prototype.continue,IDBCursor.prototype.continuePrimaryKey])).includes(e)?function(...t){return e.apply(Aa(this),t),Ca(this.request)}:function(...t){return Ca(e.apply(Aa(this),t))}}function _a(e){return"function"==typeof e?Oa(e):(e instanceof IDBTransaction&&function(e){if(Ea.has(e))return;const t=new Promise(((t,n)=>{const r=()=>{e.removeEventListener("complete",i),e.removeEventListener("error",o),e.removeEventListener("abort",o)},i=()=>{t(),r()},o=()=>{n(e.error||new DOMException("AbortError","AbortError")),r()};e.addEventListener("complete",i),e.addEventListener("error",o),e.addEventListener("abort",o)}));Ea.set(e,t)}(e),wa(e,va||(va=[IDBDatabase,IDBObjectStore,IDBIndex,IDBCursor,IDBTransaction]))?new Proxy(e,Sa):e)}function Ca(e){if(e instanceof IDBRequest)return function(e){const t=new Promise(((t,n)=>{const r=()=>{e.removeEventListener("success",i),e.removeEventListener("error",o)},i=()=>{t(Ca(e.result)),r()},o=()=>{n(e.error),r()};e.addEventListener("success",i),e.addEventListener("error",o)}));return ka.set(t,e),t}(e);if(Ma.has(e))return Ma.get(e);const t=_a(e);return t!==e&&(Ma.set(e,t),ka.set(t,e)),t}const Aa=e=>ka.get(e);function Ra(e,t,{blocked:n,upgrade:r,blocking:i,terminated:o}={}){const s=indexedDB.open(e,t),a=Ca(s);return r&&s.addEventListener("upgradeneeded",(e=>{r(Ca(s.result),e.oldVersion,e.newVersion,Ca(s.transaction),e)})),n&&s.addEventListener("blocked",(e=>n(e.oldVersion,e.newVersion,e))),a.then((e=>{o&&e.addEventListener("close",(()=>o())),i&&e.addEventListener("versionchange",(e=>i(e.oldVersion,e.newVersion,e)))})).catch((()=>{})),a}const Da=["get","getKey","getAll","getAllKeys","count"],ja=["put","add","delete","clear"],La=new Map;function Na(e,t){if(!(e instanceof IDBDatabase)||t in e||"string"!=typeof t)return;if(La.get(t))return La.get(t);const n=t.replace(/FromIndex$/,""),r=t!==n,i=ja.includes(n);if(!(n in(r?IDBIndex:IDBObjectStore).prototype)||!i&&!Da.includes(n))return;const o=async function(e,...t){const o=this.transaction(e,i?"readwrite":"readonly");let s=o.store;return r&&(s=s.index(t.shift())),(await Promise.all([s[n](...t),i&&o.done]))[0]};return La.set(t,o),o}Ta((e=>({...e,get:(t,n,r)=>Na(t,n)||e.get(t,n,r),has:(t,n)=>!!Na(t,n)||e.has(t,n)})));const Pa=["continue","continuePrimaryKey","advance"],Fa={},Ba=new WeakMap,Ia=new WeakMap,Ua={get(e,t){if(!Pa.includes(t))return e[t];let n=Fa[t];return n||(n=Fa[t]=function(...e){Ba.set(this,Ia.get(this)[t](...e))}),n}};async function*za(...e){let t=this;if(t instanceof IDBCursor||(t=await t.openCursor(...e)),!t)return;const n=new Proxy(t,Ua);for(Ia.set(n,t),ka.set(n,Aa(t));t;)yield n,t=await(Ba.get(n)||t.continue()),Ba.delete(n)}function qa(e,t){return t===Symbol.asyncIterator&&wa(e,[IDBIndex,IDBObjectStore,IDBCursor])||"iterate"===t&&wa(e,[IDBIndex,IDBObjectStore])}Ta((e=>({...e,get:(t,n,r)=>qa(t,n)?za:e.get(t,n,r),has:(t,n)=>qa(t,n)||e.has(t,n)})));const Va="quartermaster";var $a,Wa=Promise.resolve();const Ha=async()=>(Wa=Wa.then((async()=>{($a=await Ra(Va)).onversionchange=async()=>{$a.close(),Ha()},$a.onerror=async(e,t)=>{$a.close(),Ha()}})),await Wa),Ya=async e=>{const t=$a.version+1;$a.close(),($a=await Ra(Va,t,{upgrade(t){t.objectStoreNames.contains(e)||t.createObjectStore(e,{keyPath:"_id"})}})).onversionchange=async()=>{$a.close(),Ha()},$a.onerror=async(e,t)=>{$a.close(),Ha()}},Ga={initDB:Ha,addItems:async(e,t)=>(Wa=Wa.then((async()=>{$a.objectStoreNames.contains(e)||await Ya(e);const n=$a.transaction(e,"readwrite"),r=n.objectStore(e);for(const e of t){await r.get(e._id)||await r.add(e)}await n.done})),await Wa),getItem:async(e,t)=>(Wa=Wa.then((async()=>{if(!$a.objectStoreNames.contains(e))return null;const n=$a.transaction(e,"readonly"),r=await n.objectStore(e).get(t);return await n.done,r||null})),await Wa),getItems:async(e,t)=>(Wa=Wa.then((async()=>{if(!$a.objectStoreNames.contains(e))return[];const n=$a.transaction(e,"readonly"),r=await n.objectStore(e).getAll();return await n.done,t?r.filter(t):r})),await Wa),deleteItem:async(e,t)=>(Wa=Wa.then((async()=>{const n=$a.transaction(e,"readwrite");await n.objectStore(e).delete(t),await n.done})),await Wa),clearIndexedDB:async()=>(Wa=Wa.then((async()=>{const e=await indexedDB.databases();e&&e.forEach((async e=>{indexedDB.deleteDatabase(e.name)}))})),await Wa),updateItem:async(e,t,n)=>(Wa=Wa.then((async()=>{if(!$a.objectStoreNames.contains(e))return[];const r=$a.transaction(e,"readwrite"),i=r.objectStore(e),o=await i.get(t);if(!o)return[];const s={...o,...n};return await i.put(s),await r.done,s})),await Wa)};class Ka extends E.Component{constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null,errorPath:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){this.setState({error:e,errorInfo:t})}handleRefresh=()=>{this.setState({hasError:!1,error:null,errorInfo:null}),window.location.reload()};render(){const{hasError:e,error:t,errorInfo:n}=this.state;return e?k.jsx(D,{container:!0,direction:"column",justifyContent:"center",alignItems:"center",sx:{height:"100%",background:xr.palette.custom.darkBlue},children:k.jsxs(D,{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",children:[k.jsx(j,{sx:{fontSize:100,color:"#E60000"}}),k.jsx(L,{fontSize:24,color:"#FFFFFF",children:"Something went wrong!"}),k.jsx(L,{fontWeight:400,color:"#FFFFFF",marginBottom:2,children:"We apologize for the inconvenience. Please try refreshing the page."}),k.jsxs(N,{disableGutters:!0,sx:{background:"#1E293B",maxWidth:500},children:[k.jsx(P,{expandIcon:k.jsx(F,{}),children:k.jsx(L,{color:"#FFFFFF",children:"Error Details"})}),k.jsxs(B,{sx:{backgroundColor:"primary.main",marginBottom:2},children:[k.jsx(L,{color:"#FFFFFF",fontWeight:400,children:t&&t.toString()}),k.jsx(I,{component:"span",sx:{display:"block",padding:1,border:"1px solid",borderColor:"#FFFFFF",borderRadius:1,maxHeight:300,overflow:"auto"},children:k.jsx(L,{color:"#FFFFFF",fontWeight:400,fontSize:12,children:n&&n.componentStack})})]})]}),k.jsx(U,{variant:"contained",disableRipple:!0,color:"primary",sx:{textTransform:"none",marginTop:2},onClick:this.handleRefresh,children:"Reload Page"})]})}):this.props.children}}const Ja=E.createContext();const Za=new class{async fetchAll({region_groups:e}){return(await Dn.get("/vessels/info",{params:{region_groups:e}})).data}},Xa=()=>{const e=E.useContext(Er);if(void 0===e)throw new Error("useUser must be used within a UserProvider");return e},Qa=({children:e})=>{const[t,n]=E.useState([]),{user:r}=Xa(),i=async()=>{try{const e=await Za.fetchAll({});return n(e),e}catch(zc){}};return E.useEffect((()=>{r&&i()}),[r]),k.jsx(Ja.Provider,{value:{vesselInfo:t,fetchVesselsInfo:i},children:e})},ec=E.createContext();const tc=new class{async fetchAll(){return(await Dn.get("/regionGroups")).data}async create({name:e,timezone:t}){return(await Dn.post("/regionGroups",{name:e,timezone:t},{meta:{showSnackbar:!0}})).data}async update({id:e,name:t,timezone:n}){return(await Dn.post(`/regionGroups/${e}`,{name:t,timezone:n},{meta:{showSnackbar:!0}})).data}async delete({id:e}){return(await Dn.delete(`/regionGroups/${e}`,{meta:{showSnackbar:!0}})).data}},nc=({children:e})=>{const[t,n]=E.useState([]),{user:r}=Xa(),i=async()=>{try{const e=await tc.fetchAll();n(e)}catch(zc){}};return E.useEffect((()=>{r&&i()}),[r]),k.jsx(ec.Provider,{value:{regions:t,fetchRegions:i},children:e})},rc={display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",width:"100vw",height:"100vh",backgroundColor:"#f8f8f8",color:"#333",fontSize:24,lineHeight:"normal",fontWeight:"bold",fontFamily:"Arial, sans-serif"},ic={animation:"rotate 2s linear infinite",width:"100px"};function oc(){return k.jsxs("div",{style:rc,children:[k.jsx("style",{children:"\n@keyframes rotate {\n  0% { transform: rotate(0deg);}\n  100% { transform: rotate(360deg);}\n}\n"}),k.jsx("img",{src:"/quartermaster-logo-dark.svg",style:ic,alt:"Quartermaster Logo"}),k.jsx("p",{children:"Loading..."})]})}const sc=E.createContext();const ac=new class{async addFavouriteArtifact(e){try{return await Dn.post("/artifactFavourites",e,{meta:{showSnackbar:!0}})}catch(t){return t.response.data}}async removeFavouriteArtifact(e){try{return await Dn.delete("/artifactFavourites",{data:e,meta:{showSnackbar:!0}})}catch(t){return t.response.data}}async getUserFavouriteArtifacts(){try{return(await Dn.get("/artifactFavourites")).data}catch(e){return e.response.data}}},cc=({children:e})=>{const[t,n]=E.useState([]),[r,i]=E.useState([]),{user:o}=Xa(),s=async()=>{try{const{favourites:e,artifacts:t}=await ac.getUserFavouriteArtifacts(),r=e.map((e=>e.artifact_id));n(r),i(t),localStorage.setItem("favouritesArtifacts",JSON.stringify(r)),window.dispatchEvent(new CustomEvent("localStorageChange",{detail:{key:"favouritesArtifacts",newValue:JSON.stringify(r)}}))}catch(zc){}};return E.useEffect((()=>{o&&s()}),[o]),E.useEffect((()=>{if(!o)return;const e=Qi(),t=()=>{s()};return e.on("favourites/changed",t),()=>{e.off("favourites/changed",t)}}),[o]),k.jsx(sc.Provider,{value:{favouritesArtifacts:t,fetchFavouritesArtifacts:s,setFavouritesArtifacts:n,artifactsFavourites:r},children:e})},lc=E.lazy((()=>te((()=>import("./Login-B_LSyBV0.js")),__vite__mapDeps([0,1,2,3,4])))),uc=E.lazy((()=>te((()=>import("./VideoStream-CgWSEAbl.js")),__vite__mapDeps([5,1,6,7,8,9,10,11,12,13,3,4,14,15,2,16,17,18])))),dc=E.lazy((()=>te((()=>import("./DashboardLayout-2osoQ31X.js")),__vite__mapDeps([19,1,6,14,13,12,2,3,4])))),hc=E.lazy((()=>te((()=>import("./ForgotPassword-LTV5Aymt.js")),__vite__mapDeps([20,1,2,3,4])))),fc=E.lazy((()=>te((()=>import("./ResetPassword-74pXi3yL.js")),__vite__mapDeps([21,1,2,3,4])))),pc=E.lazy((()=>te((()=>import("./HomeLayout-Bv9KfAqC.js")),__vite__mapDeps([22,1,2,3,4])))),mc=E.lazy((()=>te((()=>import("./FullMap-B5Rxz7Hm.js")),__vite__mapDeps([23,1,7,6,8,3,4,10,11,12,13,15,16,2])))),gc=E.lazy((()=>te((()=>import("./UserManagement-Bh8WTfyL.js")),__vite__mapDeps([24,1,6,25,11,2,26,27,15,16,28,29,17,30,3,4])))),bc=E.lazy((()=>te((()=>import("./LogManagement-Bn1r1T7j.js")),__vite__mapDeps([31,1,6,11,9,32,30,2,3,4])))),yc=E.lazy((()=>te((()=>import("./ApiKeyManagement-Db3T96Sn.js")),__vite__mapDeps([33,1,6,11,15,16,28,27,29,2,3,4])))),wc=E.lazy((()=>te((()=>import("./StatisticsManagement-CA4F9GUq.js")),__vite__mapDeps([34,1,6,4,15,29,2,3])))),vc=E.lazy((()=>te((()=>import("./OTPInput-D_3mCMBS.js")),__vite__mapDeps([35,1,2,3,4])))),xc=E.lazy((()=>te((()=>import("./Settings-DNCNGvYf.js")),__vite__mapDeps([36,1,25,11,6,2,3,4])))),Ec=E.lazy((()=>te((()=>import("./Signup-D5c_dVmk.js")),__vite__mapDeps([37,1,2,26,3,4])))),Mc=E.lazy((()=>te((()=>import("./EventManagement-CX2JaNko.js")),__vite__mapDeps([38,1,6,11,10,12,15,2,9,13,3,4,39])))),kc=E.lazy((()=>te((()=>import("./NotificationManagement-BS7MozuC.js")),__vite__mapDeps([40,1,6,11,28,29,15,2,3,4])))),Sc=E.lazy((()=>te((()=>import("./Subscription-DRiwJr0Q.js")),__vite__mapDeps([41,1])))),Tc=E.lazy((()=>te((()=>import("./VesselManagement-CgKZPMW1.js")),__vite__mapDeps([42,1,6,11,13,28,32,29,16,2,26,15,3,4])))),Oc=e=>k.jsx(Ka,{children:k.jsx(e,{})}),_c=({path:e})=>k.jsxs(D,{container:!0,display:"block",width:"100%",height:"100%",children:[k.jsx(D,{display:"stream"===e?"block":"none",width:"100%",height:"100%",children:Oc(uc)}),k.jsx(D,{display:"map"===e?"block":"none",width:"100%",height:"100%",children:Oc(mc)}),k.jsx(D,{display:"users"===e?"block":"none",width:"100%",height:"100%",children:Oc(gc)}),k.jsx(D,{display:"logs"===e?"block":"none",width:"100%",height:"100%",children:Oc(bc)}),k.jsx(D,{display:"api-keys"===e?"block":"none",width:"100%",height:"100%",children:Oc(yc)}),k.jsx(D,{display:"statistics"===e?"block":"none",width:"100%",height:"100%",children:Oc(wc)}),k.jsx(D,{display:"settings"===e?"block":"none",width:"100%",height:"100%",children:Oc(xc)}),k.jsx(D,{display:"events"===e?"block":"none",width:"100%",height:"100%",children:Oc(Mc)}),k.jsx(D,{display:"notification"===e?"block":"none",width:"100%",height:"100%",children:Oc(kc)}),k.jsx(D,{item:!0,display:"vessel-management"===e?"block":"none",width:"100%",height:"100%",children:Oc(Tc)})]}),Cc=z(q(k.jsxs(H,{children:[k.jsxs(H,{path:"/",element:k.jsx(pc,{}),children:[k.jsx(H,{index:!0,element:k.jsx(lc,{})}),k.jsx(H,{path:"/login",element:k.jsx(lc,{})}),k.jsx(H,{path:"/signup",element:k.jsx(Ec,{})}),k.jsx(H,{path:"/otp",element:k.jsx(vc,{})}),k.jsx(H,{path:"/forgot-password",element:k.jsx(hc,{})}),k.jsx(H,{path:"/reset-password/:token",element:k.jsx(fc,{})})]}),k.jsx(H,{path:"/subscription",element:k.jsx(Sc,{})}),k.jsxs(H,{path:"/dashboard",element:k.jsx(dc,{}),children:[k.jsx(H,{index:!0,element:k.jsx(Y,{to:"stream"})}),k.jsx(H,{path:"stream",element:k.jsx(_c,{path:"stream"})}),k.jsx(H,{path:"map",element:k.jsx(_c,{path:"map"})}),k.jsx(H,{path:"users",element:k.jsx(_c,{path:"users"})}),k.jsx(H,{path:"logs",element:k.jsx(_c,{path:"logs"})}),k.jsx(H,{path:"api-keys",element:k.jsx(_c,{path:"api-keys"})}),k.jsx(H,{path:"statistics",element:k.jsx(_c,{path:"statistics"})}),k.jsx(H,{path:"settings",element:k.jsx(_c,{path:"settings"})}),k.jsx(H,{path:"events/:id?",element:k.jsx(_c,{path:"events"})}),k.jsx(H,{path:"notification",element:k.jsx(_c,{path:"notification"})}),k.jsx(H,{path:"vessel-management",element:k.jsx(_c,{path:"vessel-management"})})]},"test"),k.jsx(H,{path:"*",element:k.jsx(Y,{to:"/"})})]}))),Ac=()=>{const e=ba();return E.useEffect((()=>{An=(t,n)=>e(t,n),(e=>{Rn=e})(((t,n)=>e(t,n)))}),[]),k.jsx(E.Suspense,{fallback:k.jsx(oc,{}),children:k.jsx(W,{router:Cc})})};function Rc(){const[e,t]=E.useState(!1),[n,r]=E.useState(!1);E.useEffect((()=>{(async()=>{try{const e=localStorage.getItem("QMA_Version");e&&e===ya||(localStorage.setItem("QMA_Version",ya),await Ga.clearIndexedDB()),await Ga.initDB()}catch(zc){}return!0})().catch(console.error).finally((()=>t(!0)))}),[]);const i=E.useRef();return E.useEffect((()=>{clearTimeout(i.current),e||(i.current=setTimeout((()=>{r(!0)}),3e3))}),[e]),!e&&n?k.jsx("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh"},children:k.jsx("p",{style:{fontWeight:"bold",fontSize:22},children:"If page does not load, please try closing any other tabs for the website"})}):e?k.jsxs(V,{theme:xr,children:[k.jsx($,{}),k.jsx(ao,{children:k.jsx(ro,{children:k.jsx(cc,{children:k.jsx(Qa,{children:k.jsx(nc,{children:k.jsx(uo,{dateAdapter:Po,children:k.jsx(ga,{maxSnack:3,disableWindowBlurListener:!0,autoHideDuration:3e3,children:k.jsx(Ac,{})})})})})})})})]}):null}var Dc,jc={exports:{}};var Lc=(Dc||(Dc=1,jc.exports=function(){var e="minute",t=/[+-]\d\d(?::?\d\d)?/g,n=/([+-]|\d\d)/g;return function(r,i,o){var s=i.prototype;o.utc=function(e){return new i({date:e,utc:!0,args:arguments})},s.utc=function(t){var n=o(this.toDate(),{locale:this.$L,utc:!0});return t?n.add(this.utcOffset(),e):n},s.local=function(){return o(this.toDate(),{locale:this.$L,utc:!1})};var a=s.parse;s.parse=function(e){e.utc&&(this.$u=!0),this.$utils().u(e.$offset)||(this.$offset=e.$offset),a.call(this,e)};var c=s.init;s.init=function(){if(this.$u){var e=this.$d;this.$y=e.getUTCFullYear(),this.$M=e.getUTCMonth(),this.$D=e.getUTCDate(),this.$W=e.getUTCDay(),this.$H=e.getUTCHours(),this.$m=e.getUTCMinutes(),this.$s=e.getUTCSeconds(),this.$ms=e.getUTCMilliseconds()}else c.call(this)};var l=s.utcOffset;s.utcOffset=function(r,i){var o=this.$utils().u;if(o(r))return this.$u?0:o(this.$offset)?l.call(this):this.$offset;if("string"==typeof r&&null===(r=function(e){void 0===e&&(e="");var r=e.match(t);if(!r)return null;var i=(""+r[0]).match(n)||["-",0,0],o=i[0],s=60*+i[1]+ +i[2];return 0===s?0:"+"===o?s:-s}(r)))return this;var s=Math.abs(r)<=16?60*r:r,a=this;if(i)return a.$offset=s,a.$u=0===r,a;if(0!==r){var c=this.$u?this.toDate().getTimezoneOffset():-1*this.utcOffset();(a=this.local().add(s+c,e)).$offset=s,a.$x.$localOffset=c}else a=this.utc();return a};var u=s.format;s.format=function(e){var t=e||(this.$u?"YYYY-MM-DDTHH:mm:ss[Z]":"");return u.call(this,t)},s.valueOf=function(){var e=this.$utils().u(this.$offset)?0:this.$offset+(this.$x.$localOffset||this.$d.getTimezoneOffset());return this.$d.valueOf()-6e4*e},s.isUTC=function(){return!!this.$u},s.toISOString=function(){return this.toDate().toISOString()},s.toString=function(){return this.toDate().toUTCString()};var d=s.toDate;s.toDate=function(e){return"s"===e&&this.$offset?o(this.format("YYYY-MM-DD HH:mm:ss:SSS")).toDate():d.call(this)};var h=s.diff;s.diff=function(e,t,n){if(e&&this.$u===e.$u)return h.call(this,e,t,n);var r=this.local(),i=o(e).local();return h.call(r,i,t,n)}}}()),jc.exports);const Nc=t(Lc);var Pc,Fc={exports:{}};var Bc=(Pc||(Pc=1,Fc.exports=function(){var e={year:0,month:1,day:2,hour:3,minute:4,second:5},t={};return function(n,r,i){var o,s=function(e,n,r){void 0===r&&(r={});var i=new Date(e);return function(e,n){void 0===n&&(n={});var r=n.timeZoneName||"short",i=e+"|"+r,o=t[i];return o||(o=new Intl.DateTimeFormat("en-US",{hour12:!1,timeZone:e,year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:r}),t[i]=o),o}(n,r).formatToParts(i)},a=function(t,n){for(var r=s(t,n),o=[],a=0;a<r.length;a+=1){var c=r[a],l=c.type,u=c.value,d=e[l];d>=0&&(o[d]=parseInt(u,10))}var h=o[3],f=24===h?0:h,p=o[0]+"-"+o[1]+"-"+o[2]+" "+f+":"+o[4]+":"+o[5]+":000",m=+t;return(i.utc(p).valueOf()-(m-=m%1e3))/6e4},c=r.prototype;c.tz=function(e,t){void 0===e&&(e=o);var n,r=this.utcOffset(),s=this.toDate(),a=s.toLocaleString("en-US",{timeZone:e}),c=Math.round((s-new Date(a))/1e3/60),l=15*-Math.round(s.getTimezoneOffset()/15)-c;if(Number(l)){if(n=i(a,{locale:this.$L}).$set("millisecond",this.$ms).utcOffset(l,!0),t){var u=n.utcOffset();n=n.add(r-u,"minute")}}else n=this.utcOffset(0,t);return n.$x.$timezone=e,n},c.offsetName=function(e){var t=this.$x.$timezone||i.tz.guess(),n=s(this.valueOf(),t,{timeZoneName:e}).find((function(e){return"timezonename"===e.type.toLowerCase()}));return n&&n.value};var l=c.startOf;c.startOf=function(e,t){if(!this.$x||!this.$x.$timezone)return l.call(this,e,t);var n=i(this.format("YYYY-MM-DD HH:mm:ss:SSS"),{locale:this.$L});return l.call(n,e,t).tz(this.$x.$timezone,!0)},i.tz=function(e,t,n){var r=n&&t,s=n||t||o,c=a(+i(),s);if("string"!=typeof e)return i(e).tz(s);var l=function(e,t,n){var r=e-60*t*1e3,i=a(r,n);if(t===i)return[r,t];var o=a(r-=60*(i-t)*1e3,n);return i===o?[r,i]:[e-60*Math.min(i,o)*1e3,Math.max(i,o)]}(i.utc(e,r).valueOf(),c,s),u=l[0],d=l[1],h=i(u).utcOffset(d);return h.$x.$timezone=s,h},i.tz.guess=function(){return Intl.DateTimeFormat().resolvedOptions().timeZone},i.tz.setDefault=function(e){o=e}}}()),Fc.exports);const Ic=t(Bc),Uc=console.error;ne.extend(Nc),ne.extend(Ic),nr(re.production)&&(console.log=()=>{},console.error=function(...e){const t=e[0];"string"==typeof t&&["MUI X: useResizeContainer - The parent DOM element of the Data Grid has an empty height"].some((e=>t.includes(e)))||Uc.apply(console,e)}),function(){let e="";nr(["portal"])?e="sbslb655o6":nr(["dev"])&&(e="sbaw0nicou"),function(e,t,n,r,i,o,s){e[n]=e[n]||function(){(e[n].q=e[n].q||[]).push(arguments)},(o=t.createElement(r)).async=1,o.src="https://www.clarity.ms/tag/"+i,(s=t.getElementsByTagName(r)[0]).parentNode.insertBefore(o,s)}(window,document,"clarity","script",e)}(),function(){let e="";if(nr(["portal"])?e="G-7STH6DSMLK":nr(["dev"])&&(e="G-N0QMBKZ3LX"),e){let t=function(){window.dataLayer.push(arguments)};const n=document.createElement("script");n.async=!0,n.src=`https://www.googletagmanager.com/gtag/js?id=${e}`,document.head.appendChild(n),window.dataLayer=window.dataLayer||[],window.gtag=t,t("js",new Date),t("config",e)}}(),Q.createRoot(document.getElementById("root")).render(k.jsx(Rc,{}),function(){const e=document.getElementById("splash");e&&(e.style.transition="opacity 0.5s",e.style.opacity="0",e.remove())}());export{Qn as A,ur as B,qn as C,ba as D,Yn as E,tr as F,Zn as G,er as H,Kn as I,ir as J,Vn as K,uo as L,lo as M,br as N,sc as O,mr as P,ac as Q,sr as R,cc as S,cr as T,ro as U,tc as V,hr as W,ec as X,Ja as Y,io as Z,ne as a,$n as b,Wn as c,rr as d,Dn as e,lr as f,gr as g,re as h,Ga as i,Nc as j,Ic as k,nr as l,Qi as m,ar as n,dr as o,Hn as p,or as q,no as r,fr as s,xr as t,Xa as u,ya as v,jn as w,Ji as x,pr as y,X as z};

import React, { useEffect, useRef, useState } from "react";
import { UserProvider } from "../../../providers/UserProvider";
import dayjs from "dayjs";
import { defaultValues, displayCoordinates } from "../../../utils";
import s3Controller from "../../../controllers/S3.controller";
import { Grid, Typography, IconButton, Skeleton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import KeyboardArrowRightIcon from "@mui/icons-material/KeyboardArrowRight";
import AudioPlayer from "../../../components/AudioPlayer";

const ClusterAudioInfoWindow = ({ markers, currentAudioClusterInfoWindow, user }) => {
    const [currentIndex, setCurrentIndex] = useState(0);
    const artifactAudios = markers.map((marker) => marker.audioArtifactData);
    const audio = artifactAudios[currentIndex];
    const [src, setSrc] = useState(null);
    const [loading, setLoading] = useState(true);
    const audioRef = useRef(null);

    console.log("AudioData: cluster", audio, markers, artifactAudios);
    useEffect(() => {
        if (audio) {
            fetchSignedURL({ audio });
        }
    }, [audio]);

    const fetchSignedURL = async ({ audio }) => {
        try {
            const url = await s3Controller.getSignedUrl({
                bucket_name: audio.bucket_name,
                key: audio.audio_path,
                region: audio.aws_region,
            });
            setSrc(url);
            if (url) {
                setLoading(false);
            }
        } catch (err) {
            console.log("Error in fetch SignedUrl", err);
        }
    };

    const handleNavigation = (direction) => {
        if (audioRef.current) {
            audioRef.current.pause();
            audioRef.current.currentTime = 0;
        }
        setLoading(true);
        setCurrentIndex((prevIndex) => {
            if (direction === "prev") {
                return prevIndex > 0 ? prevIndex - 1 : artifactAudios.length - 1;
            } else {
                return (prevIndex + 1) % artifactAudios.length;
            }
        });
    };

    const handleClose = () => {
        currentAudioClusterInfoWindow.close();
    };

    const hasLocation = audio && audio.host_location && Array.isArray(audio.host_location.coordinates) && audio.host_location.coordinates.length > 0;

    return (
        <Grid container direction="column" style={{ color: "white", padding: "20px", background: "#343B44", maxWidth: "330px", height: "auto" }}>
            <style>
                {`
                    .gm-style-iw-chr, .gm-style-iw-tc {
                        display: none !important;
                    }
                    .gm-style .gm-style-iw-c {
                        background-color: #343B44 !important;
                        outline: none;
                        padding: 0;
                    }
                    .gm-style .gm-style-iw-d {
                        overflow: auto !important;
                    }
                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {
                        background-color: #fff !important;
                    }
                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {
                        background: #343B44 !important;
                    }
                `}
            </style>
            <Grid sx={{ height: "auto" }}>
                <Grid container justifyContent="space-between" alignItems="center" style={{ marginBottom: "10px" }}>
                    <Typography variant="h6">
                        Artifact Audio {artifactAudios.length > 1 ? `${currentIndex + 1} / ${artifactAudios.length}` : ""}
                    </Typography>
                    <IconButton
                        onClick={handleClose}
                        sx={{
                            color: "white",
                            border: "1px solid white",
                            "&:hover": {
                                backgroundColor: "white",
                                color: "#4F5968",
                            },
                        }}
                    >
                        <CloseIcon sx={{ fontSize: "16px" }} />
                    </IconButton>
                </Grid>
                <Grid
                    sx={{
                        position: "relative",
                        backgroundColor: "#343B44",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                        // height: 200,
                        borderRadius: 1,
                    }}
                >
                    {!loading ? (
                        <AudioPlayer src={src} />
                    ) : (
                        <Skeleton
                            sx={{
                                width: "100%",
                                height: "50px",
                            }}
                        />
                    )}
                </Grid>
            </Grid>
            <Grid sx={{ height: "auto", display: "flex", flexDirection: "column" }}>
                <Typography>
                    <strong>Frequency:</strong> {audio.frequency || "Not available"}
                </Typography>
                {hasLocation && (
                    <Typography>
                        <strong>Location:</strong> {displayCoordinates(audio.host_location.coordinates, !!(user && user.use_MGRS))}
                    </Typography>
                )}
                <Typography>
                    <strong>Timestamp</strong>:{" "}
                    {audio.timestamp ? dayjs(audio.timestamp).format(defaultValues.dateTimeFormat()) : "Not available"}{" "}
                </Typography>
            </Grid>
            <Grid sx={{ height: "auto" }}>
                {artifactAudios.length > 1 && (
                    <Grid container justifyContent="space-between" sx={{ marginBottom: "1px" }}>
                        <IconButton
                            onClick={() => handleNavigation("prev")}
                            sx={{
                                color: "white",
                                fontSize: "24px",
                                fontWeight: "bold",
                                "&:hover": {
                                    backgroundColor: "white",
                                    color: "#4F5968",
                                },
                            }}
                        >
                            <ChevronLeftIcon sx={{ fontSize: "24px" }} />
                        </IconButton>
                        <IconButton
                            onClick={() => handleNavigation("next")}
                            sx={{
                                color: "white",
                                fontWeight: "bold",
                                "&:hover": {
                                    backgroundColor: "white",
                                    color: "#4F5968",
                                },
                            }}
                        >
                            <KeyboardArrowRightIcon sx={{ fontSize: "24px" }} />
                        </IconButton>
                    </Grid>
                )}
            </Grid>
        </Grid>
    );
};

const WrappedAudioClusterInfoWindow = (props) => {
    return (
        <UserProvider>
            <ClusterAudioInfoWindow {...props} />
        </UserProvider>
    );
};

export default WrappedAudioClusterInfoWindow;

import axios from "axios";
import environment from "../environment";

// Create a global event emitter or use a method to trigger navigation
let onSessionExpiry = () => {};
let onError = () => {};
let onSuccess = () => {};

const axiosInstance = axios.create({
    baseURL: environment.VITE_API_URL + "/api",
    withCredentials: true,
});

axiosInstance.interceptors.request.use(
    (config) => {
        if (!config.headers["Authorization"]) {
            const token = localStorage.getItem("jwt_token");
            if (token) {
                config.headers["Authorization"] = `Bearer ${token}`;
            }
        }

        // Ensure signal is properly passed through for request cancellation
        if (config.signal) {
            // Add event listener to handle abort
            config.signal.addEventListener("abort", () => {
                console.log("Request aborted:", config.url);
            });
        }

        return config;
    },
    (error) => {
        return Promise.reject(error);
    },
);

axiosInstance.interceptors.response.use(
    (response) => {
        const { config } = response;
        if (config.meta?.showSnackbar) {
            onSuccess && onSuccess(response?.data?.message || `Success`, { variant: "success" });
        }
        return response;
    },
    async (error) => {
        const { config, response } = error;

        // Handle aborted requests
        if (error.name === "CanceledError" || error.code === "ERR_CANCELED" || (error.message && error.message.includes("canceled"))) {
            console.log("Request was canceled:", config?.url);
            return Promise.reject(error);
        }

        // If RateLimit-Reset header is present and the status code is 429 (Too Many Requests)
        if (response?.status === 429 && response?.headers.get("RateLimit-Reset")) {
            console.warn("rate-limit detected");
            const retryAfter = parseInt(response.headers.get("RateLimit-Reset"), 10); // in seconds

            // Delay the retry according to the RateLimit-Reset header value
            await new Promise((resolve) => setTimeout(resolve, retryAfter * 1000)); // Convert seconds to milliseconds

            // Retry the original request
            return axiosInstance(config);
        }

        if (config.meta?.showSnackbar !== false) {
            let errorMessage =
                error.code === "ERR_NETWORK"
                    ? "Network Error. Please check your internet and try again"
                    : error.response?.data?.message || `Unexpected error occured (Code ${error.response?.status})`;

            if (response.data instanceof Blob && response.data.type === "application/json") {
                try {
                    const text = await response.data.text();
                    const jsonData = JSON.parse(text);
                    errorMessage = jsonData.message || `Unexpected error occurred (Code ${response.status})`;
                } catch {
                    errorMessage = `Unexpected error occurred (Code ${response.status})`;
                }
            }

            onError && onError(errorMessage, { variant: "error" });
        }

        if (response && response.status === 401) {
            onSessionExpiry && onSessionExpiry();
        }

        return Promise.reject(error);
    },
);

// Method to register the unauthorized callback
export const registerSessionExpiryCallback = (callback) => {
    onSessionExpiry = callback;
};

export const registerErrorCallback = (callback) => {
    onError = callback;
};

export const registerSuccessCallback = (callback) => {
    onSuccess = callback;
};

export default axiosInstance;

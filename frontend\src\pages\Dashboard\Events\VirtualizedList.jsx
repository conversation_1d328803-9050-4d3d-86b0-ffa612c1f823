import { memo } from "react";
import { VariableSizeList } from "react-window";

const VirtualizedList = memo(
    ({ listRef, containerHeight, getItemSize, handleScroll, Row, items, columnCount, rowData }) => {
        return (
            <VariableSizeList
                ref={listRef}
                height={containerHeight}
                width="100%"
                itemCount={Math.ceil(items.length / columnCount)}
                itemSize={getItemSize}
                overscanCount={3}
                onScroll={handleScroll}
                itemData={{ items, columnCount, ...(rowData || {}) }}
            >
                {Row}
            </VariableSizeList>
        );
    },
    (prev, next) => prev.items.length === next.items.length && prev.containerHeight === next.containerHeight && prev.columnCount === next.columnCount,
);

VirtualizedList.displayName = "VirtualizedList";
export default VirtualizedList;

import{r as e,j as t,bE as n,G as r,K as o,a0 as a,ao as i,T as s,bp as l,Y as c,b3 as d,x as p,c8 as u,R as f,c9 as h,a as m,a5 as g,aM as v,L as y,bb as x,bK as w,b5 as b,be as D,W as C,am as S,bH as k,b2 as _,ca as M,X as E,bN as F,bv as T,ay as P,ag as R,af as j,aR as I}from"./vendor-B98I-pgv.js";import{u as N}from"./AppHook-CvjturwY.js";import{u as A,p as O,d as L,t as Y,a as W,b as H,o as B,m as V,e as z,N as K,O as U,P as Q,l as G,h as q}from"./index-Cmi2ob6r.js";import{M as $}from"./ModalContainer-CTYPbNwV.js";import{P as X}from"./PreviewMedia-CZ7OLmuB.js";import{u as Z}from"./VesselInfoHook-D_QDbUmn.js";import{i as J,c as ee,d as te,e as ne,g as re,f as oe,h as ae,j as ie,k as se,l as le,p as ce,t as de,m as pe,n as ue,o as fe,q as he,u as me,v as ge,w as ve,x as ye,y as xe,z as we,A as be,B as De,C as Ce,D as Se,E as ke,G as _e,H as Me,I as Ee,J as Fe,K as Te,L as Pe,M as Re,N as je,O as Ie,P as Ne,Q as Ae,R as Oe,S as Le,T as Ye,U as We,V as He,W as Be,X as Ve,Y as ze,Z as Ke,_ as Ue,$ as Qe,a0 as Ge,a1 as qe,a2 as $e,a3 as Xe,a4 as Ze,a5 as Je,a6 as et,a7 as tt,a8 as nt,a9 as rt,aa as ot,ab as at}from"./utils-D3r61PVZ.js";import{V as it}from"./index.esm-BO-krc_n.js";import{s as st}from"./S3.controller-BioajDex.js";import{a as lt}from"./ArtifactFlag.controller-BiYkhhNT.js";import"./maps--fsV2DPB.js";import"./charts-gTQAinvd.js";function ct({showDetailModal:h,setShowDetailModal:m,selectedCard:g,setSelectedCard:v,id:y,signedUrls:x}){const{screenSize:w}=N(),{user:b}=A(),{vesselInfo:D}=Z(),[C,S]=e.useState(null),[k,_]=e.useState(g?.currentGroupIndex||0),[M,E]=e.useState(!0),[F,T]=e.useState(!1),P=b?.hasPermissions([O.manageArtifacts]),R=g?.isGroup&&g?.groupArtifacts?.length>1,j=R?g.groupArtifacts.length:1,I=e.useMemo((()=>R&&g.groupArtifacts[k]||g),[g,k,R]),B=e.useMemo((()=>D.find((e=>e.vessel_id===I?.onboard_vessel_id))),[D,I?.onboard_vessel_id]),V=I?.location?.coordinates&&L(I.location.coordinates,!!b?.use_MGRS),z=e.useCallback((()=>{k>0&&(E(!0),T(!1),_(k-1))}),[k]),K=e.useCallback((()=>{k<j-1&&(E(!0),T(!1),_(k+1))}),[k,j]),U=()=>{v(null),m(!1),_(0),E(!0),T(!1)};e.useEffect((()=>{if(!I)return;E(!0),T(!1);const e=Boolean(I.video_path),t=x.get(`${I._id}:image`),n=x.get(`${I._id}:video`);if(e)S(n),E(!1);else{const e=new Image;e.onload=()=>{S(t),E(!1),T(!1)},e.onerror=()=>{E(!1),T(!0)},e.src=t}}),[I]),e.useEffect((()=>{void 0!==g?.currentGroupIndex&&_(g.currentGroupIndex)}),[g]);const Q=[{label:"Location",value:V},{label:"Category",value:I?.super_category||"Unspecified category"},{label:"Sub Category",value:I?.category},{label:"Weapons",value:I?.weapons},{label:"Size",value:I?.size},{label:"Color",value:I?.color},{label:"Imo Number",value:I?.imo_number},{label:"Country Flag",value:I?.country_flag},{label:"Text Detected",value:Array.isArray(I?.text_extraction)&&I.text_extraction.length>0?I.text_extraction.map((e=>e.text)).slice(0,5).join(", "):null},{label:"Description",value:I?.others}],G=["Text Detected","Description"];return t.jsx(n,{open:Boolean(h),onClose:U,children:t.jsx($,{title:"Event Details",onClose:U,showDivider:!0,children:t.jsxs(r,{container:!0,gap:1,maxHeight:"70vh",overflow:"auto",minWidth:{xs:300,sm:500},maxWidth:800,children:[t.jsxs(r,{size:12,position:"relative",children:[M&&t.jsxs(t.Fragment,{children:[t.jsx(o,{sx:{display:"flex",justifyContent:"center",alignItems:"center",position:"absolute",top:0,left:0,width:"100%",height:"300px",zIndex:1,borderRadius:2},children:t.jsx(a,{})}),t.jsx(i,{variant:"rectangular",width:"100%",height:"300px",sx:{borderRadius:2}})]}),F&&!M&&t.jsx(o,{height:"300px",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"rgba(0,0,0,0.2)",borderRadius:2,children:t.jsx(s,{color:"text.secondary",variant:"body1",children:"Failed to load media"})}),!M&&!F&&I&&t.jsx(l,{in:!0,timeout:300,unmountOnExit:!0,children:t.jsx(o,{children:t.jsx(X,{thumbnailLink:C,originalLink:C,cardId:y||I._id,isImage:!I.video_path,style:{borderRadius:8,height:300,objectFit:"contain",backgroundColor:"#000"},skeletonStyle:{height:300,width:"100%",borderRadius:2},showFullscreenIconForMap:!I.video_path,showArchiveButton:P,isGrouped:R,groupArtifacts:g?.groupArtifacts})})})]}),R&&t.jsx(r,{size:12,container:!0,justifyContent:"center",children:t.jsxs(o,{sx:{display:"flex",alignItems:"center",padding:"4px 8px",gap:1},onClick:e=>e.stopPropagation(),children:[t.jsx(c,{size:"small",onClick:z,disabled:0===k,sx:{color:"white",padding:"4px",background:p(Y.palette.custom.borderColor,.8)+" !important","&:disabled":{color:"rgba(255,255,255,0.3)"}},children:t.jsx(d,{fontSize:"small"})}),t.jsxs(s,{variant:"caption",sx:{color:"white",fontWeight:500,minWidth:"40px",textAlign:"center",padding:"5px 17px",borderRadius:"100px",background:p(Y.palette.primary.main,.8)},children:[String(k+1).padStart(2,"0"),"/",String(j).padStart(2,"0")]}),t.jsx(c,{size:"small",onClick:K,disabled:k===j-1,sx:{color:"white",padding:"4px",background:p(Y.palette.custom.borderColor,.8)+" !important","&:disabled":{color:"rgba(255,255,255,0.3)"}},children:t.jsx(u,{fontSize:"small"})})]})}),t.jsxs(r,{display:"flex",justifyContent:w.xs?"flex-start":"space-between",alignItems:w.xs?"flex-start":"center",paddingX:1,flexDirection:w.xs?"column":"row",size:12,children:[w.xs&&t.jsx(s,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,color:Y.palette.custom.mainBlue,children:"Name"}),t.jsx(s,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,children:B?.name||B?.vessel_id||"Unknown Vessel"}),w.xs&&t.jsx(s,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,color:Y.palette.custom.mainBlue,children:"Timestamp"}),t.jsx(s,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,children:W(I?.timestamp).format(H.dateTimeFormat(b,{exclude_seconds:!0}))})]}),Q.map((({label:e,value:n},o)=>t.jsx(f.Fragment,{children:t.jsxs(r,{display:"flex",alignItems:{xs:"flex-start",sm:o%2==0||G.includes(e)?"flex-start":"flex-end"},paddingX:1,flexDirection:"column",size:{xs:12,sm:G.includes(e)?12:5.9},children:[t.jsx(s,{fontSize:"16px",fontWeight:500,color:Y.palette.custom.mainBlue,children:e}),t.jsx(s,{fontSize:"16px",fontWeight:500,children:n??"--"})]})},o)))]})})})}function dt(){return"undefined"!=typeof window}function pt(e){return ht(e)?(e.nodeName||"").toLowerCase():"#document"}function ut(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function ft(e){var t;return null==(t=(ht(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ht(e){return!!dt()&&(e instanceof Node||e instanceof ut(e).Node)}function mt(e){return!!dt()&&(e instanceof Element||e instanceof ut(e).Element)}function gt(e){return!!dt()&&(e instanceof HTMLElement||e instanceof ut(e).HTMLElement)}function vt(e){return!(!dt()||"undefined"==typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof ut(e).ShadowRoot)}function yt(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=St(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function xt(e){return["table","td","th"].includes(pt(e))}function wt(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(n){return!1}}))}function bt(e){const t=Dt(),n=mt(e)?St(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!n[e]&&"none"!==n[e]))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function Dt(){return!("undefined"==typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function Ct(e){return["html","body","#document"].includes(pt(e))}function St(e){return ut(e).getComputedStyle(e)}function kt(e){return mt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function _t(e){if("html"===pt(e))return e;const t=e.assignedSlot||e.parentNode||vt(e)&&e.host||ft(e);return vt(t)?t.host:t}function Mt(e){const t=_t(e);return Ct(t)?e.ownerDocument?e.ownerDocument.body:e.body:gt(t)&&yt(t)?t:Mt(t)}function Et(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=Mt(e),a=o===(null==(r=e.ownerDocument)?void 0:r.body),i=ut(o);if(a){const e=Ft(i);return t.concat(i,i.visualViewport||[],yt(o)?o:[],e&&n?Et(e):[])}return t.concat(o,Et(o,[],n))}function Ft(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}const Tt=Math.min,Pt=Math.max,Rt=Math.round,jt=Math.floor,It=e=>({x:e,y:e}),Nt={left:"right",right:"left",bottom:"top",top:"bottom"},At={start:"end",end:"start"};function Ot(e,t,n){return Pt(e,Tt(t,n))}function Lt(e,t){return"function"==typeof e?e(t):e}function Yt(e){return e.split("-")[0]}function Wt(e){return e.split("-")[1]}function Ht(e){return"y"===e?"height":"width"}function Bt(e){return["top","bottom"].includes(Yt(e))?"y":"x"}function Vt(e){return"x"===Bt(e)?"y":"x"}function zt(e){return e.replace(/start|end/g,(e=>At[e]))}function Kt(e){return e.replace(/left|right|bottom|top/g,(e=>Nt[e]))}function Ut(e){return"number"!=typeof e?function(e){return{top:0,right:0,bottom:0,left:0,...e}}(e):{top:e,right:e,bottom:e,left:e}}function Qt(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}var Gt="undefined"!=typeof document?e.useLayoutEffect:e.useEffect;const qt={...h}.useInsertionEffect||(e=>e());function $t(e,t,n){let{reference:r,floating:o}=e;const a=Bt(t),i=Vt(t),s=Ht(i),l=Yt(t),c="y"===a,d=r.x+r.width/2-o.width/2,p=r.y+r.height/2-o.height/2,u=r[s]/2-o[s]/2;let f;switch(l){case"top":f={x:d,y:r.y-o.height};break;case"bottom":f={x:d,y:r.y+r.height};break;case"right":f={x:r.x+r.width,y:p};break;case"left":f={x:r.x-o.width,y:p};break;default:f={x:r.x,y:r.y}}switch(Wt(t)){case"start":f[i]-=u*(n&&c?-1:1);break;case"end":f[i]+=u*(n&&c?-1:1)}return f}function Xt(e){const t=St(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=gt(e),a=o?e.offsetWidth:n,i=o?e.offsetHeight:r,s=Rt(n)!==a||Rt(r)!==i;return s&&(n=a,r=i),{width:n,height:r,$:s}}function Zt(e){return mt(e)?e:e.contextElement}function Jt(e){const t=Zt(e);if(!gt(t))return It(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:a}=Xt(t);let i=(a?Rt(n.width):n.width)/r,s=(a?Rt(n.height):n.height)/o;return i&&Number.isFinite(i)||(i=1),s&&Number.isFinite(s)||(s=1),{x:i,y:s}}const en=It(0);function tn(e){const t=ut(e);return Dt()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:en}function nn(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),a=Zt(e);let i=It(1);t&&(r?mt(r)&&(i=Jt(r)):i=Jt(e));const s=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==ut(e))&&t}(a,n,r)?tn(a):It(0);let l=(o.left+s.x)/i.x,c=(o.top+s.y)/i.y,d=o.width/i.x,p=o.height/i.y;if(a){const e=ut(a),t=r&&mt(r)?ut(r):r;let n=e,o=Ft(n);for(;o&&r&&t!==n;){const e=Jt(o),t=o.getBoundingClientRect(),r=St(o),a=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,i=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;l*=e.x,c*=e.y,d*=e.x,p*=e.y,l+=a,c+=i,n=ut(o),o=Ft(n)}}return Qt({width:d,height:p,x:l,y:c})}function rn(e,t){const n=kt(e).scrollLeft;return t?t.left+n:nn(ft(e)).left+n}function on(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:rn(e,r)),y:r.top+t.scrollTop}}function an(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=ut(e),r=ft(e),o=n.visualViewport;let a=r.clientWidth,i=r.clientHeight,s=0,l=0;if(o){a=o.width,i=o.height;const e=Dt();(!e||e&&"fixed"===t)&&(s=o.offsetLeft,l=o.offsetTop)}return{width:a,height:i,x:s,y:l}}(e,n);else if("document"===t)r=function(e){const t=ft(e),n=kt(e),r=e.ownerDocument.body,o=Pt(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=Pt(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+rn(e);const s=-n.scrollTop;return"rtl"===St(r).direction&&(i+=Pt(t.clientWidth,r.clientWidth)-o),{width:o,height:a,x:i,y:s}}(ft(e));else if(mt(t))r=function(e,t){const n=nn(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,a=gt(e)?Jt(e):It(1);return{width:e.clientWidth*a.x,height:e.clientHeight*a.y,x:o*a.x,y:r*a.y}}(t,n);else{const n=tn(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return Qt(r)}function sn(e,t){const n=_t(e);return!(n===t||!mt(n)||Ct(n))&&("fixed"===St(n).position||sn(n,t))}function ln(e,t,n){const r=gt(t),o=ft(t),a="fixed"===n,i=nn(e,!0,a,t);let s={scrollLeft:0,scrollTop:0};const l=It(0);if(r||!r&&!a)if(("body"!==pt(t)||yt(o))&&(s=kt(t)),r){const e=nn(t,!0,a,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else o&&(l.x=rn(o));const c=!o||r||a?It(0):on(o,s);return{x:i.left+s.scrollLeft-l.x-c.x,y:i.top+s.scrollTop-l.y-c.y,width:i.width,height:i.height}}function cn(e){return"static"===St(e).position}function dn(e,t){if(!gt(e)||"fixed"===St(e).position)return null;if(t)return t(e);let n=e.offsetParent;return ft(e)===n&&(n=n.ownerDocument.body),n}function pn(e,t){const n=ut(e);if(wt(e))return n;if(!gt(e)){let t=_t(e);for(;t&&!Ct(t);){if(mt(t)&&!cn(t))return t;t=_t(t)}return n}let r=dn(e,t);for(;r&&xt(r)&&cn(r);)r=dn(r,t);return r&&Ct(r)&&cn(r)&&!bt(r)?n:r||function(e){let t=_t(e);for(;gt(t)&&!Ct(t);){if(bt(t))return t;if(wt(t))return null;t=_t(t)}return null}(e)||n}const un={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const a="fixed"===o,i=ft(r),s=!!t&&wt(t.floating);if(r===i||s&&a)return n;let l={scrollLeft:0,scrollTop:0},c=It(1);const d=It(0),p=gt(r);if((p||!p&&!a)&&(("body"!==pt(r)||yt(i))&&(l=kt(r)),gt(r))){const e=nn(r);c=Jt(r),d.x=e.x+r.clientLeft,d.y=e.y+r.clientTop}const u=!i||p||a?It(0):on(i,l,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+d.x+u.x,y:n.y*c.y-l.scrollTop*c.y+d.y+u.y}},getDocumentElement:ft,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const a=[..."clippingAncestors"===n?wt(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=Et(e,[],!1).filter((e=>mt(e)&&"body"!==pt(e))),o=null;const a="fixed"===St(e).position;let i=a?_t(e):e;for(;mt(i)&&!Ct(i);){const t=St(i),n=bt(i);n||"fixed"!==t.position||(o=null),(a?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||yt(i)&&!n&&sn(e,i))?r=r.filter((e=>e!==i)):o=t,i=_t(i)}return t.set(e,r),r}(t,this._c):[].concat(n),r],i=a[0],s=a.reduce(((e,n)=>{const r=an(t,n,o);return e.top=Pt(r.top,e.top),e.right=Tt(r.right,e.right),e.bottom=Tt(r.bottom,e.bottom),e.left=Pt(r.left,e.left),e}),an(t,i,o));return{width:s.right-s.left,height:s.bottom-s.top,x:s.left,y:s.top}},getOffsetParent:pn,getElementRects:async function(e){const t=this.getOffsetParent||pn,n=this.getDimensions,r=await n(e.floating);return{reference:ln(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=Xt(e);return{width:t,height:n}},getScale:Jt,isElement:mt,isRTL:function(e){return"rtl"===St(e).direction}};function fn(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function hn(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:i="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:l=!1}=r,c=Zt(e),d=o||a?[...c?Et(c):[],...Et(t)]:[];d.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)}));const p=c&&s?function(e,t){let n,r=null;const o=ft(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function i(s,l){void 0===s&&(s=!1),void 0===l&&(l=1),a();const c=e.getBoundingClientRect(),{left:d,top:p,width:u,height:f}=c;if(s||t(),!u||!f)return;const h={rootMargin:-jt(p)+"px "+-jt(o.clientWidth-(d+u))+"px "+-jt(o.clientHeight-(p+f))+"px "+-jt(d)+"px",threshold:Pt(0,Tt(1,l))||1};let m=!0;function g(t){const r=t[0].intersectionRatio;if(r!==l){if(!m)return i();r?i(!1,r):n=setTimeout((()=>{i(!1,1e-7)}),1e3)}1!==r||fn(c,e.getBoundingClientRect())||i(),m=!1}try{r=new IntersectionObserver(g,{...h,root:o.ownerDocument})}catch(v){r=new IntersectionObserver(g,h)}r.observe(e)}(!0),a}(c,n):null;let u,f=-1,h=null;i&&(h=new ResizeObserver((e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(f),f=requestAnimationFrame((()=>{var e;null==(e=h)||e.observe(t)}))),n()})),c&&!l&&h.observe(c),h.observe(t));let m=l?nn(e):null;return l&&function t(){const r=nn(e);m&&!fn(m,r)&&n();m=r,u=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach((e=>{o&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)})),null==p||p(),null==(e=h)||e.disconnect(),h=null,l&&cancelAnimationFrame(u)}}const mn=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:a,placement:i,middlewareData:s}=t,l=await async function(e,t){const{placement:n,platform:r,elements:o}=e,a=await(null==r.isRTL?void 0:r.isRTL(o.floating)),i=Yt(n),s=Wt(n),l="y"===Bt(n),c=["left","top"].includes(i)?-1:1,d=a&&l?-1:1,p=Lt(t,e);let{mainAxis:u,crossAxis:f,alignmentAxis:h}="number"==typeof p?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:p.mainAxis||0,crossAxis:p.crossAxis||0,alignmentAxis:p.alignmentAxis};return s&&"number"==typeof h&&(f="end"===s?-1*h:h),l?{x:f*d,y:u*c}:{x:u*c,y:f*d}}(t,e);return i===(null==(n=s.offset)?void 0:n.placement)&&null!=(r=s.arrow)&&r.alignmentOffset?{}:{x:o+l.x,y:a+l.y,data:{...l,placement:i}}}}},gn=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:a,rects:i,initialPlacement:s,platform:l,elements:c}=t,{mainAxis:d=!0,crossAxis:p=!0,fallbackPlacements:u,fallbackStrategy:f="bestFit",fallbackAxisSideDirection:h="none",flipAlignment:m=!0,...g}=Lt(e,t);if(null!=(n=a.arrow)&&n.alignmentOffset)return{};const v=Yt(o),y=Bt(s),x=Yt(s)===s,w=await(null==l.isRTL?void 0:l.isRTL(c.floating)),b=u||(x||!m?[Kt(s)]:function(e){const t=Kt(e);return[zt(e),t,zt(t)]}(s)),D="none"!==h;!u&&D&&b.push(...function(e,t,n,r){const o=Wt(e);let a=function(e,t,n){const r=["left","right"],o=["right","left"],a=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?a:i;default:return[]}}(Yt(e),"start"===n,r);return o&&(a=a.map((e=>e+"-"+o)),t&&(a=a.concat(a.map(zt)))),a}(s,m,h,w));const C=[s,...b],S=await async function(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:a,rects:i,elements:s,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:d="viewport",elementContext:p="floating",altBoundary:u=!1,padding:f=0}=Lt(t,e),h=Ut(f),m=s[u?"floating"===p?"reference":"floating":p],g=Qt(await a.getClippingRect({element:null==(n=await(null==a.isElement?void 0:a.isElement(m)))||n?m:m.contextElement||await(null==a.getDocumentElement?void 0:a.getDocumentElement(s.floating)),boundary:c,rootBoundary:d,strategy:l})),v="floating"===p?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,y=await(null==a.getOffsetParent?void 0:a.getOffsetParent(s.floating)),x=await(null==a.isElement?void 0:a.isElement(y))&&await(null==a.getScale?void 0:a.getScale(y))||{x:1,y:1},w=Qt(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:s,rect:v,offsetParent:y,strategy:l}):v);return{top:(g.top-w.top+h.top)/x.y,bottom:(w.bottom-g.bottom+h.bottom)/x.y,left:(g.left-w.left+h.left)/x.x,right:(w.right-g.right+h.right)/x.x}}(t,g),k=[];let _=(null==(r=a.flip)?void 0:r.overflows)||[];if(d&&k.push(S[v]),p){const e=function(e,t,n){void 0===n&&(n=!1);const r=Wt(e),o=Vt(e),a=Ht(o);let i="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[a]>t.floating[a]&&(i=Kt(i)),[i,Kt(i)]}(o,i,w);k.push(S[e[0]],S[e[1]])}if(_=[..._,{placement:o,overflows:k}],!k.every((e=>e<=0))){var M,E;const e=((null==(M=a.flip)?void 0:M.index)||0)+1,t=C[e];if(t)return{data:{index:e,overflows:_},reset:{placement:t}};let n=null==(E=_.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:E.placement;if(!n)switch(f){case"bestFit":{var F;const e=null==(F=_.filter((e=>{if(D){const t=Bt(e.placement);return t===y||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:F[0];e&&(n=e);break}case"initialPlacement":n=s}if(o!==n)return{reset:{placement:n}}}return{}}}},vn=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:a,platform:i,elements:s,middlewareData:l}=t,{element:c,padding:d=0}=Lt(e,t)||{};if(null==c)return{};const p=Ut(d),u={x:n,y:r},f=Vt(o),h=Ht(f),m=await i.getDimensions(c),g="y"===f,v=g?"top":"left",y=g?"bottom":"right",x=g?"clientHeight":"clientWidth",w=a.reference[h]+a.reference[f]-u[f]-a.floating[h],b=u[f]-a.reference[f],D=await(null==i.getOffsetParent?void 0:i.getOffsetParent(c));let C=D?D[x]:0;C&&await(null==i.isElement?void 0:i.isElement(D))||(C=s.floating[x]||a.floating[h]);const S=w/2-b/2,k=C/2-m[h]/2-1,_=Tt(p[v],k),M=Tt(p[y],k),E=_,F=C-m[h]-M,T=C/2-m[h]/2+S,P=Ot(E,T,F),R=!l.arrow&&null!=Wt(o)&&T!==P&&a.reference[h]/2-(T<E?_:M)-m[h]/2<0,j=R?T<E?T-E:T-F:0;return{[f]:u[f]+j,data:{[f]:P,centerOffset:T-P-j,...R&&{alignmentOffset:j}},reset:R}}}),yn=(e,t,n)=>{const r=new Map,o={platform:un,...n},a={...o.platform,_c:r};return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:i}=n,s=a.filter(Boolean),l=await(null==i.isRTL?void 0:i.isRTL(t));let c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:p}=$t(c,r,l),u=r,f={},h=0;for(let m=0;m<s.length;m++){const{name:n,fn:a}=s[m],{x:g,y:v,data:y,reset:x}=await a({x:d,y:p,initialPlacement:r,placement:u,strategy:o,middlewareData:f,rects:c,platform:i,elements:{reference:e,floating:t}});d=null!=g?g:d,p=null!=v?v:p,f={...f,[n]:{...f[n],...y}},x&&h<=50&&(h++,"object"==typeof x&&(x.placement&&(u=x.placement),x.rects&&(c=!0===x.rects?await i.getElementRects({reference:e,floating:t,strategy:o}):x.rects),({x:d,y:p}=$t(c,u,l))),m=-1)}return{x:d,y:p,placement:u,strategy:o,middlewareData:f}})(e,t,{...o,platform:a})};var xn="undefined"!=typeof document?e.useLayoutEffect:e.useEffect;function wn(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!=r--;)if(!wn(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!wn(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function bn(e){if("undefined"==typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Dn(e,t){const n=bn(e);return Math.round(t*n)/n}function Cn(t){const n=e.useRef(t);return xn((()=>{n.current=t})),n}const Sn=e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"==typeof e?e(t):e;return n&&(o=n,{}.hasOwnProperty.call(o,"current"))?null!=n.current?vn({element:n.current,padding:r}).fn(t):{}:n?vn({element:n,padding:r}).fn(t):{};var o}}),kn=(e,t)=>({...mn(e),options:[e,t]}),_n=(e,t)=>({...Sn(e),options:[e,t]}),Mn={...h};let En=!1,Fn=0;const Tn=()=>"floating-ui-"+Math.random().toString(36).slice(2,6)+Fn++;const Pn=Mn.useId||function(){const[t,n]=e.useState((()=>En?Tn():void 0));return Gt((()=>{null==t&&n(Tn())}),[]),e.useEffect((()=>{En=!0}),[]),t},Rn=e.forwardRef((function(n,r){const{context:{placement:o,elements:{floating:a},middlewareData:{arrow:i,shift:s}},width:l=14,height:c=7,tipRadius:d=0,strokeWidth:p=0,staticOffset:u,stroke:f,d:h,style:{transform:m,...g}={},...v}=n,y=Pn(),[x,w]=e.useState(!1);if(Gt((()=>{if(!a)return;"rtl"===St(a).direction&&w(!0)}),[a]),!a)return null;const[b,D]=o.split("-"),C="top"===b||"bottom"===b;let S=u;(C&&null!=s&&s.x||!C&&null!=s&&s.y)&&(S=null);const k=2*p,_=k/2,M=l/2*(d/-8+1),E=c/2*d/4,F=!!h,T=S&&"end"===D?"bottom":"top";let P=S&&"end"===D?"right":"left";S&&x&&(P="end"===D?"left":"right");const R=null!=(null==i?void 0:i.x)?S||i.x:"",j=null!=(null==i?void 0:i.y)?S||i.y:"",I=h||"M0,0 H"+l+" L"+(l-M)+","+(c-E)+" Q"+l/2+","+c+" "+M+","+(c-E)+" Z",N={top:F?"rotate(180deg)":"",left:F?"rotate(90deg)":"rotate(-90deg)",bottom:F?"":"rotate(180deg)",right:F?"rotate(-90deg)":"rotate(90deg)"}[b];return t.jsxs("svg",{...v,"aria-hidden":!0,ref:r,width:F?l:l+k,height:l,viewBox:"0 0 "+l+" "+(c>l?c:l),style:{position:"absolute",pointerEvents:"none",[P]:R,[T]:j,[b]:C||F?"100%":"calc(100% - "+k/2+"px)",transform:[N,m].filter((e=>!!e)).join(" "),...g},children:[k>0&&t.jsx("path",{clipPath:"url(#"+y+")",fill:"none",stroke:f,strokeWidth:k+(h?0:1),d:I}),t.jsx("path",{stroke:k&&!h?v.fill:"none",d:I}),t.jsx("clipPath",{id:y,children:t.jsx("rect",{x:-_,y:_*(F?-1:1),width:l+k,height:l})})]})}));const jn=e.createContext(null),In=e.createContext(null);function Nn(t){const{open:n=!1,onOpenChange:r,elements:o}=t,a=Pn(),i=e.useRef({}),[s]=e.useState((()=>function(){const e=new Map;return{emit(t,n){var r;null==(r=e.get(t))||r.forEach((e=>e(n)))},on(t,n){e.has(t)||e.set(t,new Set),e.get(t).add(n)},off(t,n){var r;null==(r=e.get(t))||r.delete(n)}}}())),l=null!=((null==(c=e.useContext(jn))?void 0:c.id)||null);var c;const[d,p]=e.useState(o.reference),u=function(t){const n=e.useRef((()=>{}));return qt((()=>{n.current=t})),e.useCallback((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return null==n.current?void 0:n.current(...t)}),[])}(((e,t,n)=>{i.current.openEvent=e?t:void 0,s.emit("openchange",{open:e,event:t,reason:n,nested:l}),null==r||r(e,t,n)})),f=e.useMemo((()=>({setPositionReference:p})),[]),h=e.useMemo((()=>({reference:d||o.reference||null,floating:o.floating||null,domReference:o.reference})),[d,o.reference,o.floating]);return e.useMemo((()=>({dataRef:i,open:n,onOpenChange:u,elements:h,events:s,floatingId:a,refs:f})),[n,u,h,s,a,f])}function An(t){void 0===t&&(t={});const{nodeId:n}=t,r=Nn({...t,elements:{reference:null,floating:null,...t.elements}}),o=t.rootContext||r,a=o.elements,[i,s]=e.useState(null),[l,c]=e.useState(null),d=(null==a?void 0:a.domReference)||i,p=e.useRef(null),u=e.useContext(In);Gt((()=>{d&&(p.current=d)}),[d]);const f=function(t){void 0===t&&(t={});const{placement:n="bottom",strategy:r="absolute",middleware:o=[],platform:a,elements:{reference:i,floating:s}={},transform:l=!0,whileElementsMounted:c,open:d}=t,[p,u]=e.useState({x:0,y:0,strategy:r,placement:n,middlewareData:{},isPositioned:!1}),[f,h]=e.useState(o);wn(f,o)||h(o);const[g,v]=e.useState(null),[y,x]=e.useState(null),w=e.useCallback((e=>{e!==S.current&&(S.current=e,v(e))}),[]),b=e.useCallback((e=>{e!==k.current&&(k.current=e,x(e))}),[]),D=i||g,C=s||y,S=e.useRef(null),k=e.useRef(null),_=e.useRef(p),M=null!=c,E=Cn(c),F=Cn(a),T=Cn(d),P=e.useCallback((()=>{if(!S.current||!k.current)return;const e={placement:n,strategy:r,middleware:f};F.current&&(e.platform=F.current),yn(S.current,k.current,e).then((e=>{const t={...e,isPositioned:!1!==T.current};R.current&&!wn(_.current,t)&&(_.current=t,m.flushSync((()=>{u(t)})))}))}),[f,n,r,F,T]);xn((()=>{!1===d&&_.current.isPositioned&&(_.current.isPositioned=!1,u((e=>({...e,isPositioned:!1}))))}),[d]);const R=e.useRef(!1);xn((()=>(R.current=!0,()=>{R.current=!1})),[]),xn((()=>{if(D&&(S.current=D),C&&(k.current=C),D&&C){if(E.current)return E.current(D,C,P);P()}}),[D,C,P,E,M]);const j=e.useMemo((()=>({reference:S,floating:k,setReference:w,setFloating:b})),[w,b]),I=e.useMemo((()=>({reference:D,floating:C})),[D,C]),N=e.useMemo((()=>{const e={position:r,left:0,top:0};if(!I.floating)return e;const t=Dn(I.floating,p.x),n=Dn(I.floating,p.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...bn(I.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}}),[r,l,I.floating,p.x,p.y]);return e.useMemo((()=>({...p,update:P,refs:j,elements:I,floatingStyles:N})),[p,P,j,I,N])}({...t,elements:{...a,...l&&{reference:l}}}),h=e.useCallback((e=>{const t=mt(e)?{getBoundingClientRect:()=>e.getBoundingClientRect(),getClientRects:()=>e.getClientRects(),contextElement:e}:e;c(t),f.refs.setReference(t)}),[f.refs]),g=e.useCallback((e=>{(mt(e)||null===e)&&(p.current=e,s(e)),(mt(f.refs.reference.current)||null===f.refs.reference.current||null!==e&&!mt(e))&&f.refs.setReference(e)}),[f.refs]),v=e.useMemo((()=>({...f.refs,setReference:g,setPositionReference:h,domReference:p})),[f.refs,g,h]),y=e.useMemo((()=>({...f.elements,domReference:d})),[f.elements,d]),x=e.useMemo((()=>({...f,...o,refs:v,elements:y,nodeId:n})),[f,v,y,n,o]);return Gt((()=>{o.dataRef.current.floatingContext=x;const e=null==u?void 0:u.nodesRef.current.find((e=>e.id===n));e&&(e.context=x)})),e.useMemo((()=>({...f,context:x,refs:v,elements:y})),[f,v,y,x])}
/*!
  react-datepicker v7.6.0
  https://github.com/Hacker0x01/react-datepicker
  Released under the MIT License.
*/var On=function(e,t){return(On=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])})(e,t)};function Ln(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}On(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var Yn=function(){return Yn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},Yn.apply(this,arguments)};function Wn(e,t,n){if(n||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}"function"==typeof SuppressedError&&SuppressedError;var Hn,Bn,Vn=function(e){var t=e.showTimeSelectOnly,n=void 0!==t&&t,r=e.showTime,o=void 0!==r&&r,a=e.className,i=e.children,s=n?"Choose Time":"Choose Date".concat(o?" and Time":"");return f.createElement("div",{className:a,role:"dialog","aria-label":s,"aria-modal":"true"},i)},zn=function(t){var n=t.children,r=t.onClickOutside,o=t.className,a=t.containerRef,i=t.style,s=function(t,n){var r=e.useRef(null),o=e.useRef(t);o.current=t;var a=e.useCallback((function(e){var t;r.current&&!r.current.contains(e.target)&&(n&&e.target instanceof HTMLElement&&e.target.classList.contains(n)||null===(t=o.current)||void 0===t||t.call(o,e))}),[n]);return e.useEffect((function(){return document.addEventListener("mousedown",a),function(){document.removeEventListener("mousedown",a)}}),[a]),r}(r,t.ignoreClass);return f.createElement("div",{className:o,style:i,ref:function(e){s.current=e,a&&(a.current=e)}},n)};function Kn(){return"undefined"!=typeof window?window:globalThis}(Bn=Hn||(Hn={})).ArrowUp="ArrowUp",Bn.ArrowDown="ArrowDown",Bn.ArrowLeft="ArrowLeft",Bn.ArrowRight="ArrowRight",Bn.PageUp="PageUp",Bn.PageDown="PageDown",Bn.Home="Home",Bn.End="End",Bn.Enter="Enter",Bn.Space=" ",Bn.Tab="Tab",Bn.Escape="Escape",Bn.Backspace="Backspace",Bn.X="x";var Un=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;function Qn(e){if(null==e)return new Date;var t="string"==typeof e?ce(e):de(e);return qn(t)?t:new Date}function Gn(e,t,n,r,o){var a,i=null,s=fr(n)||fr(ur()),l=!0;if(Array.isArray(t))return t.forEach((function(t){var a=ve(e,t,new Date,{locale:s,useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0});r&&(l=qn(a,o)&&e===$n(a,t,n)),qn(a,o)&&l&&(i=a)})),i;if(i=ve(e,t,new Date,{locale:s,useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0}),r)l=qn(i)&&e===$n(i,t,n);else if(!qn(i)){var c=(null!==(a=t.match(Un))&&void 0!==a?a:[]).map((function(e){var t=e[0];if("p"===t||"P"===t){var n=ye[t];return s?n(e,s.formatLong):t}return e})).join("");e.length>0&&(i=ve(e,c.slice(0,e.length),new Date,{useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0})),qn(i)||(i=new Date(e))}return qn(i)&&l?i:null}function qn(e,t){return pe(e)&&!J(e,null!=t?t:new Date("1/1/1800"))}function $n(e,t,n){if("en"===n)return Ae(e,t,{useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0});var r=n?fr(n):void 0;return!r&&ur()&&fr(ur())&&(r=fr(ur())),Ae(e,t,{locale:r,useAdditionalWeekYearTokens:!0,useAdditionalDayOfYearTokens:!0})}function Xn(e,t){var n=t.dateFormat,r=t.locale,o=Array.isArray(n)&&n.length>0?n[0]:n;return e&&$n(e,o,r)||""}function Zn(e,t){var n=t.hour,r=void 0===n?0:n,o=t.minute,a=void 0===o?0:o,i=t.second;return ke(_e(Me(e,void 0===i?0:i),a),r)}function Jn(e){return me(e)}function er(e,t,n){var r=fr(t||ur());return Be(e,{locale:r,weekStartsOn:n})}function tr(e){return Se(e)}function nr(e){return De(e)}function rr(e){return et(e)}function or(){return me(Qn())}function ar(e){return ge(e)}function ir(e,t){return e&&t?Oe(e,t):!e&&!t}function sr(e,t){return e&&t?Le(e,t):!e&&!t}function lr(e,t){return e&&t?Je(e,t):!e&&!t}function cr(e,t){return e&&t?we(e,t):!e&&!t}function dr(e,t){return e&&t?le(e,t):!e&&!t}function pr(e,t,n){var r,o=me(t),a=ge(n);try{r=xe(e,{start:o,end:a})}catch(i){r=!1}return r}function ur(){return Kn().__localeId__}function fr(e){if("string"==typeof e){var t=Kn();return t.__localeData__?t.__localeData__[e]:void 0}return e}function hr(e,t){return $n(je(Qn(),e),"LLLL",t)}function mr(e,t){return $n(je(Qn(),e),"LLL",t)}function gr(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.maxDate,a=n.excludeDates,i=n.excludeDateIntervals,s=n.includeDates,l=n.includeDateIntervals,c=n.filterDate;return kr(e,{minDate:r,maxDate:o})||a&&a.some((function(t){return t instanceof Date?cr(e,t):cr(e,t.date)}))||i&&i.some((function(t){var n=t.start,r=t.end;return xe(e,{start:n,end:r})}))||s&&!s.some((function(t){return cr(e,t)}))||l&&!l.some((function(t){var n=t.start,r=t.end;return xe(e,{start:n,end:r})}))||c&&!c(Qn(e))||!1}function vr(e,t){var n=void 0===t?{}:t,r=n.excludeDates,o=n.excludeDateIntervals;return o&&o.length>0?o.some((function(t){var n=t.start,r=t.end;return xe(e,{start:n,end:r})})):r&&r.some((function(t){var n;return t instanceof Date?cr(e,t):cr(e,null!==(n=t.date)&&void 0!==n?n:new Date)}))||!1}function yr(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.maxDate,a=n.excludeDates,i=n.includeDates,s=n.filterDate;return kr(e,{minDate:r?Se(r):void 0,maxDate:o?Ce(o):void 0})||(null==a?void 0:a.some((function(t){return sr(e,t instanceof Date?t:t.date)})))||i&&!i.some((function(t){return sr(e,t)}))||s&&!s(Qn(e))||!1}function xr(e,t,n,r){var o=ie(e),a=se(e),i=ie(t),s=se(t),l=ie(r);return o===i&&o===l?a<=n&&n<=s:o<i&&(l===o&&a<=n||l===i&&s>=n||l<i&&l>o)}function wr(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.maxDate,a=n.excludeDates,i=n.includeDates;return kr(e,{minDate:r,maxDate:o})||a&&a.some((function(t){return sr(t instanceof Date?t:t.date,e)}))||i&&!i.some((function(t){return sr(t,e)}))||!1}function br(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.maxDate,a=n.excludeDates,i=n.includeDates,s=n.filterDate;return kr(e,{minDate:r,maxDate:o})||(null==a?void 0:a.some((function(t){return lr(e,t instanceof Date?t:t.date)})))||i&&!i.some((function(t){return lr(e,t)}))||s&&!s(Qn(e))||!1}function Dr(e,t,n){if(!t||!n)return!1;if(!pe(t)||!pe(n))return!1;var r=ie(t),o=ie(n);return r<=e&&o>=e}function Cr(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.maxDate,a=n.excludeDates,i=n.includeDates,s=n.filterDate,l=new Date(e,0,1);return kr(l,{minDate:r?De(r):void 0,maxDate:o?be(o):void 0})||(null==a?void 0:a.some((function(e){return ir(l,e instanceof Date?e:e.date)})))||i&&!i.some((function(e){return ir(l,e)}))||s&&!s(Qn(l))||!1}function Sr(e,t,n,r){var o=ie(e),a=$e(e),i=ie(t),s=$e(t),l=ie(r);return o===i&&o===l?a<=n&&n<=s:o<i&&(l===o&&a<=n||l===i&&s>=n||l<i&&l>o)}function kr(e,t){var n,r=void 0===t?{}:t,o=r.minDate,a=r.maxDate;return null!==(n=o&&ue(e,o)<0||a&&ue(e,a)>0)&&void 0!==n&&n}function _r(e,t){return t.some((function(t){return ae(t)===ae(e)&&oe(t)===oe(e)&&re(t)===re(e)}))}function Mr(e,t){var n=void 0===t?{}:t,r=n.excludeTimes,o=n.includeTimes,a=n.filterTime;return r&&_r(e,r)||o&&!_r(e,o)||a&&!a(e)||!1}function Er(e,t){var n=t.minTime,r=t.maxTime;if(!n||!r)throw new Error("Both minTime and maxTime props required");var o=Qn();o=ke(o,ae(e)),o=_e(o,oe(e)),o=Me(o,re(e));var a=Qn();a=ke(a,ae(n)),a=_e(a,oe(n)),a=Me(a,re(n));var i,s=Qn();s=ke(s,ae(r)),s=_e(s,oe(r)),s=Me(s,re(r));try{i=!xe(o,{start:a,end:s})}catch(l){i=!1}return i}function Fr(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.includeDates,a=Fe(e,1);return r&&Ve(r,a)>0||o&&o.every((function(e){return Ve(e,a)>0}))||!1}function Tr(e,t){var n=void 0===t?{}:t,r=n.maxDate,o=n.includeDates,a=Ee(e,1);return r&&Ve(a,r)>0||o&&o.every((function(e){return Ve(a,e)>0}))||!1}function Pr(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.includeDates,a=Ie(e,1);return r&&Ue(r,a)>0||o&&o.every((function(e){return Ue(e,a)>0}))||!1}function Rr(e,t){var n=void 0===t?{}:t,r=n.maxDate,o=n.includeDates,a=Ne(e,1);return r&&Ue(a,r)>0||o&&o.every((function(e){return Ue(a,e)>0}))||!1}function jr(e){var t=e.minDate,n=e.includeDates;if(n&&t){var r=n.filter((function(e){return ue(e,t)>=0}));return fe(r)}return n?fe(n):t}function Ir(e){var t=e.maxDate,n=e.includeDates;if(n&&t){var r=n.filter((function(e){return ue(e,t)<=0}));return he(r)}return n?he(n):t}function Nr(e,t){var n;void 0===e&&(e=[]),void 0===t&&(t="react-datepicker__day--highlighted");for(var r=new Map,o=0,a=e.length;o<a;o++){var i=e[o];if(te(i)){var s=$n(i,"MM.dd.yyyy");(f=r.get(s)||[]).includes(t)||(f.push(t),r.set(s,f))}else if("object"==typeof i){var l=null!==(n=Object.keys(i)[0])&&void 0!==n?n:"",c=i[l];if("string"==typeof l&&Array.isArray(c))for(var d=0,p=c.length;d<p;d++){var u=c[d];if(u){var f;s=$n(u,"MM.dd.yyyy");(f=r.get(s)||[]).includes(l)||(f.push(l),r.set(s,f))}}}}return r}function Ar(e,t){void 0===e&&(e=[]),void 0===t&&(t="react-datepicker__day--holidays");var n=new Map;return e.forEach((function(e){var r=e.date,o=e.holidayName;if(te(r)){var a=$n(r,"MM.dd.yyyy"),i=n.get(a)||{className:"",holidayNames:[]};if(!("className"in i)||i.className!==t||(s=i.holidayNames,l=[o],s.length!==l.length||!s.every((function(e,t){return e===l[t]})))){var s,l;i.className=t;var c=i.holidayNames;i.holidayNames=c?Wn(Wn([],c,!0),[o],!1):[o],n.set(a,i)}}})),n}function Or(e,t,n,r,o){for(var a=o.length,i=[],s=0;s<a;s++){var l=e,c=o[s];c&&(l=tt(l,ae(c)),l=Xe(l,oe(c)),l=nt(l,re(c)));var d=Xe(e,(n+1)*r);ee(l,t)&&J(l,d)&&null!=c&&i.push(c)}return i}function Lr(e){return e<10?"0".concat(e):"".concat(e)}function Yr(e,t){void 0===t&&(t=12);var n=Math.ceil(ie(e)/t)*t;return{startPeriod:n-(t-1),endPeriod:n}}function Wr(e){var t=e.getSeconds(),n=e.getMilliseconds();return de(e.getTime()-1e3*t-n)}function Hr(e){if(!te(e))throw new Error("Invalid date");var t=new Date(e);return t.setHours(0,0,0,0),t}function Br(e,t){if(!te(e)||!te(t))throw new Error("Invalid date received");var n=Hr(e),r=Hr(t);return J(n,r)}function Vr(e){return e.key===Hn.Space}var zr,Kr=function(t){function n(n){var r=t.call(this,n)||this;return r.inputRef=f.createRef(),r.onTimeChange=function(e){var t,n;r.setState({time:e});var o=r.props.date,a=o instanceof Date&&!isNaN(+o)?o:new Date;if(null==e?void 0:e.includes(":")){var i=e.split(":"),s=i[0],l=i[1];a.setHours(Number(s)),a.setMinutes(Number(l))}null===(n=(t=r.props).onChange)||void 0===n||n.call(t,a)},r.renderTimeInput=function(){var t=r.state.time,n=r.props,o=n.date,a=n.timeString,i=n.customTimeInput;return i?e.cloneElement(i,{date:o,value:t,onChange:r.onTimeChange}):f.createElement("input",{type:"time",className:"react-datepicker-time__input",placeholder:"Time",name:"time-input",ref:r.inputRef,onClick:function(){var e;null===(e=r.inputRef.current)||void 0===e||e.focus()},required:!0,value:t,onChange:function(e){r.onTimeChange(e.target.value||a)}})},r.state={time:r.props.timeString},r}return Ln(n,t),n.getDerivedStateFromProps=function(e,t){return e.timeString!==t.time?{time:e.timeString}:null},n.prototype.render=function(){return f.createElement("div",{className:"react-datepicker__input-time-container"},f.createElement("div",{className:"react-datepicker-time__caption"},this.props.timeInputLabel),f.createElement("div",{className:"react-datepicker-time__input-container"},f.createElement("div",{className:"react-datepicker-time__input"},this.renderTimeInput())))},n}(e.Component),Ur=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.dayEl=e.createRef(),n.handleClick=function(e){!n.isDisabled()&&n.props.onClick&&n.props.onClick(e)},n.handleMouseEnter=function(e){!n.isDisabled()&&n.props.onMouseEnter&&n.props.onMouseEnter(e)},n.handleOnKeyDown=function(e){var t,r;e.key===Hn.Space&&(e.preventDefault(),e.key=Hn.Enter),null===(r=(t=n.props).handleOnKeyDown)||void 0===r||r.call(t,e)},n.isSameDay=function(e){return cr(n.props.day,e)},n.isKeyboardSelected=function(){var e;if(n.props.disabledKeyboardNavigation)return!1;var t=n.props.selectsMultiple?null===(e=n.props.selectedDates)||void 0===e?void 0:e.some((function(e){return n.isSameDayOrWeek(e)})):n.isSameDayOrWeek(n.props.selected),r=n.props.preSelection&&n.isDisabled(n.props.preSelection);return!t&&n.isSameDayOrWeek(n.props.preSelection)&&!r},n.isDisabled=function(e){return void 0===e&&(e=n.props.day),gr(e,{minDate:n.props.minDate,maxDate:n.props.maxDate,excludeDates:n.props.excludeDates,excludeDateIntervals:n.props.excludeDateIntervals,includeDateIntervals:n.props.includeDateIntervals,includeDates:n.props.includeDates,filterDate:n.props.filterDate})},n.isExcluded=function(){return vr(n.props.day,{excludeDates:n.props.excludeDates,excludeDateIntervals:n.props.excludeDateIntervals})},n.isStartOfWeek=function(){return cr(n.props.day,er(n.props.day,n.props.locale,n.props.calendarStartDay))},n.isSameWeek=function(e){return n.props.showWeekPicker&&cr(e,er(n.props.day,n.props.locale,n.props.calendarStartDay))},n.isSameDayOrWeek=function(e){return n.isSameDay(e)||n.isSameWeek(e)},n.getHighLightedClass=function(){var e=n.props,t=e.day,r=e.highlightDates;if(!r)return!1;var o=$n(t,"MM.dd.yyyy");return r.get(o)},n.getHolidaysClass=function(){var e,t=n.props,r=t.day,o=t.holidays;if(!o)return[void 0];var a=$n(r,"MM.dd.yyyy");return o.has(a)?[null===(e=o.get(a))||void 0===e?void 0:e.className]:[void 0]},n.isInRange=function(){var e=n.props,t=e.day,r=e.startDate,o=e.endDate;return!(!r||!o)&&pr(t,r,o)},n.isInSelectingRange=function(){var e,t=n.props,r=t.day,o=t.selectsStart,a=t.selectsEnd,i=t.selectsRange,s=t.selectsDisabledDaysInRange,l=t.startDate,c=t.endDate,d=null!==(e=n.props.selectingDate)&&void 0!==e?e:n.props.preSelection;return!(!(o||a||i)||!d||!s&&n.isDisabled())&&(o&&c&&(J(d,c)||dr(d,c))?pr(r,d,c):(a&&l&&(ee(d,l)||dr(d,l))||!(!i||!l||c||!ee(d,l)&&!dr(d,l)))&&pr(r,l,d))},n.isSelectingRangeStart=function(){var e;if(!n.isInSelectingRange())return!1;var t=n.props,r=t.day,o=t.startDate,a=t.selectsStart,i=null!==(e=n.props.selectingDate)&&void 0!==e?e:n.props.preSelection;return cr(r,a?i:o)},n.isSelectingRangeEnd=function(){var e;if(!n.isInSelectingRange())return!1;var t=n.props,r=t.day,o=t.endDate,a=t.selectsEnd,i=t.selectsRange,s=null!==(e=n.props.selectingDate)&&void 0!==e?e:n.props.preSelection;return cr(r,a||i?s:o)},n.isRangeStart=function(){var e=n.props,t=e.day,r=e.startDate,o=e.endDate;return!(!r||!o)&&cr(r,t)},n.isRangeEnd=function(){var e=n.props,t=e.day,r=e.startDate,o=e.endDate;return!(!r||!o)&&cr(o,t)},n.isWeekend=function(){var e=ot(n.props.day);return 0===e||6===e},n.isAfterMonth=function(){return void 0!==n.props.month&&(n.props.month+1)%12===se(n.props.day)},n.isBeforeMonth=function(){return void 0!==n.props.month&&(se(n.props.day)+1)%12===n.props.month},n.isCurrentDay=function(){return n.isSameDay(Qn())},n.isSelected=function(){var e;return n.props.selectsMultiple?null===(e=n.props.selectedDates)||void 0===e?void 0:e.some((function(e){return n.isSameDayOrWeek(e)})):n.isSameDayOrWeek(n.props.selected)},n.getClassNames=function(e){var t,r=n.props.dayClassName?n.props.dayClassName(e):void 0;return g("react-datepicker__day",r,"react-datepicker__day--"+$n(n.props.day,"ddd",t),{"react-datepicker__day--disabled":n.isDisabled(),"react-datepicker__day--excluded":n.isExcluded(),"react-datepicker__day--selected":n.isSelected(),"react-datepicker__day--keyboard-selected":n.isKeyboardSelected(),"react-datepicker__day--range-start":n.isRangeStart(),"react-datepicker__day--range-end":n.isRangeEnd(),"react-datepicker__day--in-range":n.isInRange(),"react-datepicker__day--in-selecting-range":n.isInSelectingRange(),"react-datepicker__day--selecting-range-start":n.isSelectingRangeStart(),"react-datepicker__day--selecting-range-end":n.isSelectingRangeEnd(),"react-datepicker__day--today":n.isCurrentDay(),"react-datepicker__day--weekend":n.isWeekend(),"react-datepicker__day--outside-month":n.isAfterMonth()||n.isBeforeMonth()},n.getHighLightedClass(),n.getHolidaysClass())},n.getAriaLabel=function(){var e=n.props,t=e.day,r=e.ariaLabelPrefixWhenEnabled,o=void 0===r?"Choose":r,a=e.ariaLabelPrefixWhenDisabled,i=void 0===a?"Not available":a,s=n.isDisabled()||n.isExcluded()?i:o;return"".concat(s," ").concat($n(t,"PPPP",n.props.locale))},n.getTitle=function(){var e=n.props,t=e.day,r=e.holidays,o=void 0===r?new Map:r,a=e.excludeDates,i=$n(t,"MM.dd.yyyy"),s=[];return o.has(i)&&s.push.apply(s,o.get(i).holidayNames),n.isExcluded()&&s.push(null==a?void 0:a.filter((function(e){return e instanceof Date?cr(e,t):cr(null==e?void 0:e.date,t)})).map((function(e){if(!(e instanceof Date))return null==e?void 0:e.message}))),s.join(", ")},n.getTabIndex=function(){var e=n.props.selected,t=n.props.preSelection;return(!n.props.showWeekPicker||!n.props.showWeekNumber&&n.isStartOfWeek())&&(n.isKeyboardSelected()||n.isSameDay(e)&&cr(t,e))?0:-1},n.handleFocusDay=function(){var e;n.shouldFocusDay()&&(null===(e=n.dayEl.current)||void 0===e||e.focus({preventScroll:!0}))},n.renderDayContents=function(){return n.props.monthShowsDuplicateDaysEnd&&n.isAfterMonth()||n.props.monthShowsDuplicateDaysStart&&n.isBeforeMonth()?null:n.props.renderDayContents?n.props.renderDayContents(at(n.props.day),n.props.day):at(n.props.day)},n.render=function(){return f.createElement("div",{ref:n.dayEl,className:n.getClassNames(n.props.day),onKeyDown:n.handleOnKeyDown,onClick:n.handleClick,onMouseEnter:n.props.usePointerEvent?void 0:n.handleMouseEnter,onPointerEnter:n.props.usePointerEvent?n.handleMouseEnter:void 0,tabIndex:n.getTabIndex(),"aria-label":n.getAriaLabel(),role:"option",title:n.getTitle(),"aria-disabled":n.isDisabled(),"aria-current":n.isCurrentDay()?"date":void 0,"aria-selected":n.isSelected()||n.isInRange()},n.renderDayContents(),""!==n.getTitle()&&f.createElement("span",{className:"overlay"},n.getTitle()))},n}return Ln(n,t),n.prototype.componentDidMount=function(){this.handleFocusDay()},n.prototype.componentDidUpdate=function(){this.handleFocusDay()},n.prototype.shouldFocusDay=function(){var e=!1;return 0===this.getTabIndex()&&this.isSameDay(this.props.preSelection)&&(document.activeElement&&document.activeElement!==document.body||(e=!0),this.props.inline&&!this.props.shouldFocusDayInline&&(e=!1),this.isDayActiveElement()&&(e=!0),this.isDuplicateDay()&&(e=!1)),e},n.prototype.isDayActiveElement=function(){var e,t,n;return(null===(t=null===(e=this.props.containerRef)||void 0===e?void 0:e.current)||void 0===t?void 0:t.contains(document.activeElement))&&(null===(n=document.activeElement)||void 0===n?void 0:n.classList.contains("react-datepicker__day"))},n.prototype.isDuplicateDay=function(){return this.props.monthShowsDuplicateDaysEnd&&this.isAfterMonth()||this.props.monthShowsDuplicateDaysStart&&this.isBeforeMonth()},n}(e.Component),Qr=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.weekNumberEl=e.createRef(),n.handleClick=function(e){n.props.onClick&&n.props.onClick(e)},n.handleOnKeyDown=function(e){var t,r;e.key===Hn.Space&&(e.preventDefault(),e.key=Hn.Enter),null===(r=(t=n.props).handleOnKeyDown)||void 0===r||r.call(t,e)},n.isKeyboardSelected=function(){return!n.props.disabledKeyboardNavigation&&!cr(n.props.date,n.props.selected)&&cr(n.props.date,n.props.preSelection)},n.getTabIndex=function(){return n.props.showWeekPicker&&n.props.showWeekNumber&&(n.isKeyboardSelected()||cr(n.props.date,n.props.selected)&&cr(n.props.preSelection,n.props.selected))?0:-1},n.handleFocusWeekNumber=function(e){var t=!1;0===n.getTabIndex()&&!(null==e?void 0:e.isInputFocused)&&cr(n.props.date,n.props.preSelection)&&(document.activeElement&&document.activeElement!==document.body||(t=!0),n.props.inline&&!n.props.shouldFocusDayInline&&(t=!1),n.props.containerRef&&n.props.containerRef.current&&n.props.containerRef.current.contains(document.activeElement)&&document.activeElement&&document.activeElement.classList.contains("react-datepicker__week-number")&&(t=!0)),t&&n.weekNumberEl.current&&n.weekNumberEl.current.focus({preventScroll:!0})},n}return Ln(n,t),Object.defineProperty(n,"defaultProps",{get:function(){return{ariaLabelPrefix:"week "}},enumerable:!1,configurable:!0}),n.prototype.componentDidMount=function(){this.handleFocusWeekNumber()},n.prototype.componentDidUpdate=function(e){this.handleFocusWeekNumber(e)},n.prototype.render=function(){var e=this.props,t=e.weekNumber,r=e.isWeekDisabled,o=e.ariaLabelPrefix,a=void 0===o?n.defaultProps.ariaLabelPrefix:o,i=e.onClick,s={"react-datepicker__week-number":!0,"react-datepicker__week-number--clickable":!!i&&!r,"react-datepicker__week-number--selected":!!i&&cr(this.props.date,this.props.selected)};return f.createElement("div",{ref:this.weekNumberEl,className:g(s),"aria-label":"".concat(a," ").concat(this.props.weekNumber),onClick:this.handleClick,onKeyDown:this.handleOnKeyDown,tabIndex:this.getTabIndex()},t)},n}(e.Component),Gr=function(e){function t(){var n=null!==e&&e.apply(this,arguments)||this;return n.isDisabled=function(e){return gr(e,{minDate:n.props.minDate,maxDate:n.props.maxDate,excludeDates:n.props.excludeDates,excludeDateIntervals:n.props.excludeDateIntervals,includeDateIntervals:n.props.includeDateIntervals,includeDates:n.props.includeDates,filterDate:n.props.filterDate})},n.handleDayClick=function(e,t){n.props.onDayClick&&n.props.onDayClick(e,t)},n.handleDayMouseEnter=function(e){n.props.onDayMouseEnter&&n.props.onDayMouseEnter(e)},n.handleWeekClick=function(e,r,o){for(var a,i,s,l=new Date(e),c=0;c<7;c++){var d=new Date(e);if(d.setDate(d.getDate()+c),!n.isDisabled(d)){l=d;break}}"function"==typeof n.props.onWeekSelect&&n.props.onWeekSelect(l,r,o),n.props.showWeekPicker&&n.handleDayClick(l,o),(null!==(a=n.props.shouldCloseOnSelect)&&void 0!==a?a:t.defaultProps.shouldCloseOnSelect)&&(null===(s=(i=n.props).setOpen)||void 0===s||s.call(i,!1))},n.formatWeekNumber=function(e){return n.props.formatWeekNumber?n.props.formatWeekNumber(e):function(e){return rt(e)}(e)},n.isWeekDisabled=function(){for(var e=n.startOfWeek(),t=Re(e,6),r=new Date(e);r<=t;){if(!n.isDisabled(r))return!1;r=Re(r,1)}return!0},n.renderDays=function(){var e=n.startOfWeek(),r=[],o=n.formatWeekNumber(e);if(n.props.showWeekNumber){var a=n.props.onWeekSelect||n.props.showWeekPicker?n.handleWeekClick.bind(n,e,o):void 0;r.push(f.createElement(Qr,Yn({key:"W"},t.defaultProps,n.props,{weekNumber:o,isWeekDisabled:n.isWeekDisabled(),date:e,onClick:a})))}return r.concat([0,1,2,3,4,5,6].map((function(r){var o=Re(e,r);return f.createElement(Ur,Yn({},t.defaultProps,n.props,{ariaLabelPrefixWhenEnabled:n.props.chooseDayAriaLabelPrefix,ariaLabelPrefixWhenDisabled:n.props.disabledDayAriaLabelPrefix,key:o.valueOf(),day:o,onClick:n.handleDayClick.bind(n,o),onMouseEnter:n.handleDayMouseEnter.bind(n,o)}))})))},n.startOfWeek=function(){return er(n.props.day,n.props.locale,n.props.calendarStartDay)},n.isKeyboardSelected=function(){return!n.props.disabledKeyboardNavigation&&!cr(n.startOfWeek(),n.props.selected)&&cr(n.startOfWeek(),n.props.preSelection)},n}return Ln(t,e),Object.defineProperty(t,"defaultProps",{get:function(){return{shouldCloseOnSelect:!0}},enumerable:!1,configurable:!0}),t.prototype.render=function(){var e={"react-datepicker__week":!0,"react-datepicker__week--selected":cr(this.startOfWeek(),this.props.selected),"react-datepicker__week--keyboard-selected":this.isKeyboardSelected()};return f.createElement("div",{className:g(e)},this.renderDays())},t}(e.Component),qr="two_columns",$r="three_columns",Xr="four_columns",Zr=((zr={})[qr]={grid:[[0,1],[2,3],[4,5],[6,7],[8,9],[10,11]],verticalNavigationOffset:2},zr[$r]={grid:[[0,1,2],[3,4,5],[6,7,8],[9,10,11]],verticalNavigationOffset:3},zr[Xr]={grid:[[0,1,2,3],[4,5,6,7],[8,9,10,11]],verticalNavigationOffset:4},zr);function Jr(e,t){return e?Xr:t?qr:$r}var eo=function(t){function n(){var n=null!==t&&t.apply(this,arguments)||this;return n.MONTH_REFS=Wn([],Array(12),!0).map((function(){return e.createRef()})),n.QUARTER_REFS=Wn([],Array(4),!0).map((function(){return e.createRef()})),n.isDisabled=function(e){return gr(e,{minDate:n.props.minDate,maxDate:n.props.maxDate,excludeDates:n.props.excludeDates,excludeDateIntervals:n.props.excludeDateIntervals,includeDateIntervals:n.props.includeDateIntervals,includeDates:n.props.includeDates,filterDate:n.props.filterDate})},n.isExcluded=function(e){return vr(e,{excludeDates:n.props.excludeDates,excludeDateIntervals:n.props.excludeDateIntervals})},n.handleDayClick=function(e,t){var r,o;null===(o=(r=n.props).onDayClick)||void 0===o||o.call(r,e,t,n.props.orderInDisplay)},n.handleDayMouseEnter=function(e){var t,r;null===(r=(t=n.props).onDayMouseEnter)||void 0===r||r.call(t,e)},n.handleMouseLeave=function(){var e,t;null===(t=(e=n.props).onMouseLeave)||void 0===t||t.call(e)},n.isRangeStartMonth=function(e){var t=n.props,r=t.day,o=t.startDate,a=t.endDate;return!(!o||!a)&&sr(je(r,e),o)},n.isRangeStartQuarter=function(e){var t=n.props,r=t.day,o=t.startDate,a=t.endDate;return!(!o||!a)&&lr(qe(r,e),o)},n.isRangeEndMonth=function(e){var t=n.props,r=t.day,o=t.startDate,a=t.endDate;return!(!o||!a)&&sr(je(r,e),a)},n.isRangeEndQuarter=function(e){var t=n.props,r=t.day,o=t.startDate,a=t.endDate;return!(!o||!a)&&lr(qe(r,e),a)},n.isInSelectingRangeMonth=function(e){var t,r=n.props,o=r.day,a=r.selectsStart,i=r.selectsEnd,s=r.selectsRange,l=r.startDate,c=r.endDate,d=null!==(t=n.props.selectingDate)&&void 0!==t?t:n.props.preSelection;return!(!(a||i||s)||!d)&&(a&&c?xr(d,c,e,o):(i&&l||!(!s||!l||c))&&xr(l,d,e,o))},n.isSelectingMonthRangeStart=function(e){var t;if(!n.isInSelectingRangeMonth(e))return!1;var r=n.props,o=r.day,a=r.startDate,i=r.selectsStart,s=je(o,e),l=null!==(t=n.props.selectingDate)&&void 0!==t?t:n.props.preSelection;return sr(s,i?l:a)},n.isSelectingMonthRangeEnd=function(e){var t;if(!n.isInSelectingRangeMonth(e))return!1;var r=n.props,o=r.day,a=r.endDate,i=r.selectsEnd,s=r.selectsRange,l=je(o,e),c=null!==(t=n.props.selectingDate)&&void 0!==t?t:n.props.preSelection;return sr(l,i||s?c:a)},n.isInSelectingRangeQuarter=function(e){var t,r=n.props,o=r.day,a=r.selectsStart,i=r.selectsEnd,s=r.selectsRange,l=r.startDate,c=r.endDate,d=null!==(t=n.props.selectingDate)&&void 0!==t?t:n.props.preSelection;return!(!(a||i||s)||!d)&&(a&&c?Sr(d,c,e,o):(i&&l||!(!s||!l||c))&&Sr(l,d,e,o))},n.isWeekInMonth=function(e){var t=n.props.day,r=Re(e,6);return sr(e,t)||sr(r,t)},n.isCurrentMonth=function(e,t){return ie(e)===ie(Qn())&&t===se(Qn())},n.isCurrentQuarter=function(e,t){return ie(e)===ie(Qn())&&t===$e(Qn())},n.isSelectedMonth=function(e,t,n){return se(n)===t&&ie(e)===ie(n)},n.isSelectMonthInList=function(e,t,r){return r.some((function(r){return n.isSelectedMonth(e,t,r)}))},n.isSelectedQuarter=function(e,t,n){return $e(e)===t&&ie(e)===ie(n)},n.renderWeeks=function(){for(var e,t,r=[],o=n.props.fixedHeight,a=0,i=!1,s=er(tr(n.props.day),n.props.locale,n.props.calendarStartDay),l=n.props.selected?(e=n.props.selected,n.props.showWeekPicker?er(e,n.props.locale,n.props.calendarStartDay):n.props.selected):void 0,c=n.props.preSelection?(t=n.props.preSelection,n.props.showWeekPicker?er(t,n.props.locale,n.props.calendarStartDay):n.props.preSelection):void 0;r.push(f.createElement(Gr,Yn({},n.props,{ariaLabelPrefix:n.props.weekAriaLabelPrefix,key:a,day:s,month:se(n.props.day),onDayClick:n.handleDayClick,onDayMouseEnter:n.handleDayMouseEnter,selected:l,preSelection:c,showWeekNumber:n.props.showWeekNumbers}))),!i;){a++,s=Ye(s,1);var d=o&&a>=6,p=!o&&!n.isWeekInMonth(s);if(d||p){if(!n.props.peekNextMonth)break;i=!0}}return r},n.onMonthClick=function(e,t){var r=n.isMonthDisabledForLabelDate(t),o=r.isDisabled,a=r.labelDate;o||n.handleDayClick(tr(a),e)},n.onMonthMouseEnter=function(e){var t=n.isMonthDisabledForLabelDate(e),r=t.isDisabled,o=t.labelDate;r||n.handleDayMouseEnter(tr(o))},n.handleMonthNavigation=function(e,t){var r,o,a,i;null===(o=(r=n.props).setPreSelection)||void 0===o||o.call(r,t),null===(i=null===(a=n.MONTH_REFS[e])||void 0===a?void 0:a.current)||void 0===i||i.focus()},n.handleKeyboardNavigation=function(e,t,r){var o,a=n.props,i=a.selected,s=a.preSelection,l=a.setPreSelection,c=a.minDate,d=a.maxDate,p=a.showFourColumnMonthYearPicker,u=a.showTwoColumnMonthYearPicker;if(s){var f=Jr(p,u),h=n.getVerticalOffset(f),m=null===(o=Zr[f])||void 0===o?void 0:o.grid,g=function(e,t,n){var r,o,a=t,i=n;switch(e){case Hn.ArrowRight:a=Ee(t,1),i=11===n?0:n+1;break;case Hn.ArrowLeft:a=Fe(t,1),i=0===n?11:n-1;break;case Hn.ArrowUp:a=Fe(t,h),i=(null===(r=null==m?void 0:m[0])||void 0===r?void 0:r.includes(n))?n+12-h:n-h;break;case Hn.ArrowDown:a=Ee(t,h),i=(null===(o=null==m?void 0:m[m.length-1])||void 0===o?void 0:o.includes(n))?n-12+h:n+h}return{newCalculatedDate:a,newCalculatedMonth:i}};if(t!==Hn.Enter){var v=function(e,t,r){for(var o=e,a=!1,i=0,s=g(o,t,r),l=s.newCalculatedDate,p=s.newCalculatedMonth;!a;){if(i>=40){l=t,p=r;break}var u;if(c&&l<c)o=Hn.ArrowRight,l=(u=g(o,l,p)).newCalculatedDate,p=u.newCalculatedMonth;if(d&&l>d)o=Hn.ArrowLeft,l=(u=g(o,l,p)).newCalculatedDate,p=u.newCalculatedMonth;if(wr(l,n.props))l=(u=g(o,l,p)).newCalculatedDate,p=u.newCalculatedMonth;else a=!0;i++}return{newCalculatedDate:l,newCalculatedMonth:p}}(t,s,r),y=v.newCalculatedDate,x=v.newCalculatedMonth;switch(t){case Hn.ArrowRight:case Hn.ArrowLeft:case Hn.ArrowUp:case Hn.ArrowDown:n.handleMonthNavigation(x,y)}}else n.isMonthDisabled(r)||(n.onMonthClick(e,r),null==l||l(i))}},n.getVerticalOffset=function(e){var t,n;return null!==(n=null===(t=Zr[e])||void 0===t?void 0:t.verticalNavigationOffset)&&void 0!==n?n:0},n.onMonthKeyDown=function(e,t){var r=n.props,o=r.disabledKeyboardNavigation,a=r.handleOnMonthKeyDown,i=e.key;i!==Hn.Tab&&e.preventDefault(),o||n.handleKeyboardNavigation(e,i,t),a&&a(e)},n.onQuarterClick=function(e,t){var r=qe(n.props.day,t);br(r,n.props)||n.handleDayClick(rr(r),e)},n.onQuarterMouseEnter=function(e){var t=qe(n.props.day,e);br(t,n.props)||n.handleDayMouseEnter(rr(t))},n.handleQuarterNavigation=function(e,t){var r,o,a,i;n.isDisabled(t)||n.isExcluded(t)||(null===(o=(r=n.props).setPreSelection)||void 0===o||o.call(r,t),null===(i=null===(a=n.QUARTER_REFS[e-1])||void 0===a?void 0:a.current)||void 0===i||i.focus())},n.onQuarterKeyDown=function(e,t){var r,o,a=e.key;if(!n.props.disabledKeyboardNavigation)switch(a){case Hn.Enter:n.onQuarterClick(e,t),null===(o=(r=n.props).setPreSelection)||void 0===o||o.call(r,n.props.selected);break;case Hn.ArrowRight:if(!n.props.preSelection)break;n.handleQuarterNavigation(4===t?1:t+1,Qe(n.props.preSelection,1));break;case Hn.ArrowLeft:if(!n.props.preSelection)break;n.handleQuarterNavigation(1===t?4:t-1,ze(n.props.preSelection))}},n.isMonthDisabledForLabelDate=function(e){var t,r=n.props,o=r.day,a=r.minDate,i=r.maxDate,s=r.excludeDates,l=r.includeDates,c=je(o,e);return{isDisabled:null!==(t=(a||i||s||l)&&yr(c,n.props))&&void 0!==t&&t,labelDate:c}},n.isMonthDisabled=function(e){return n.isMonthDisabledForLabelDate(e).isDisabled},n.getMonthClassNames=function(e){var t=n.props,r=t.day,o=t.startDate,a=t.endDate,i=t.preSelection,s=t.monthClassName,l=s?s(je(r,e)):void 0,c=n.getSelection();return g("react-datepicker__month-text","react-datepicker__month-".concat(e),l,{"react-datepicker__month-text--disabled":n.isMonthDisabled(e),"react-datepicker__month-text--selected":c?n.isSelectMonthInList(r,e,c):void 0,"react-datepicker__month-text--keyboard-selected":!n.props.disabledKeyboardNavigation&&i&&n.isSelectedMonth(r,e,i)&&!n.isMonthDisabled(e),"react-datepicker__month-text--in-selecting-range":n.isInSelectingRangeMonth(e),"react-datepicker__month-text--in-range":o&&a?xr(o,a,e,r):void 0,"react-datepicker__month-text--range-start":n.isRangeStartMonth(e),"react-datepicker__month-text--range-end":n.isRangeEndMonth(e),"react-datepicker__month-text--selecting-range-start":n.isSelectingMonthRangeStart(e),"react-datepicker__month-text--selecting-range-end":n.isSelectingMonthRangeEnd(e),"react-datepicker__month-text--today":n.isCurrentMonth(r,e)})},n.getTabIndex=function(e){if(null==n.props.preSelection)return"-1";var t=se(n.props.preSelection),r=n.isMonthDisabledForLabelDate(t).isDisabled;return e!==t||r||n.props.disabledKeyboardNavigation?"-1":"0"},n.getQuarterTabIndex=function(e){if(null==n.props.preSelection)return"-1";var t=$e(n.props.preSelection),r=br(n.props.day,n.props);return e!==t||r||n.props.disabledKeyboardNavigation?"-1":"0"},n.getAriaLabel=function(e){var t=n.props,r=t.chooseDayAriaLabelPrefix,o=void 0===r?"Choose":r,a=t.disabledDayAriaLabelPrefix,i=void 0===a?"Not available":a,s=t.day,l=t.locale,c=je(s,e),d=n.isDisabled(c)||n.isExcluded(c)?i:o;return"".concat(d," ").concat($n(c,"MMMM yyyy",l))},n.getQuarterClassNames=function(e){var t=n.props,r=t.day,o=t.startDate,a=t.endDate,i=t.selected,s=t.minDate,l=t.maxDate,c=t.excludeDates,d=t.includeDates,p=t.filterDate,u=t.preSelection,f=t.disabledKeyboardNavigation,h=(s||l||c||d||p)&&br(qe(r,e),n.props);return g("react-datepicker__quarter-text","react-datepicker__quarter-".concat(e),{"react-datepicker__quarter-text--disabled":h,"react-datepicker__quarter-text--selected":i?n.isSelectedQuarter(r,e,i):void 0,"react-datepicker__quarter-text--keyboard-selected":!f&&u&&n.isSelectedQuarter(r,e,u)&&!h,"react-datepicker__quarter-text--in-selecting-range":n.isInSelectingRangeQuarter(e),"react-datepicker__quarter-text--in-range":o&&a?Sr(o,a,e,r):void 0,"react-datepicker__quarter-text--range-start":n.isRangeStartQuarter(e),"react-datepicker__quarter-text--range-end":n.isRangeEndQuarter(e)})},n.getMonthContent=function(e){var t=n.props,r=t.showFullMonthYearPicker,o=t.renderMonthContent,a=t.locale,i=t.day,s=mr(e,a),l=hr(e,a);return o?o(e,s,l,i):r?l:s},n.getQuarterContent=function(e){var t,r=n.props,o=r.renderQuarterContent,a=function(e,t){return $n(qe(Qn(),e),"QQQ",t)}(e,r.locale);return null!==(t=null==o?void 0:o(e,a))&&void 0!==t?t:a},n.renderMonths=function(){var e,t=n.props,r=t.showTwoColumnMonthYearPicker,o=t.showFourColumnMonthYearPicker,a=t.day,i=t.selected,s=null===(e=Zr[Jr(o,r)])||void 0===e?void 0:e.grid;return null==s?void 0:s.map((function(e,t){return f.createElement("div",{className:"react-datepicker__month-wrapper",key:t},e.map((function(e,t){return f.createElement("div",{ref:n.MONTH_REFS[e],key:t,onClick:function(t){n.onMonthClick(t,e)},onKeyDown:function(t){Vr(t)&&(t.preventDefault(),t.key=Hn.Enter),n.onMonthKeyDown(t,e)},onMouseEnter:n.props.usePointerEvent?void 0:function(){return n.onMonthMouseEnter(e)},onPointerEnter:n.props.usePointerEvent?function(){return n.onMonthMouseEnter(e)}:void 0,tabIndex:Number(n.getTabIndex(e)),className:n.getMonthClassNames(e),"aria-disabled":n.isMonthDisabled(e),role:"option","aria-label":n.getAriaLabel(e),"aria-current":n.isCurrentMonth(a,e)?"date":void 0,"aria-selected":i?n.isSelectedMonth(a,e,i):void 0},n.getMonthContent(e))})))}))},n.renderQuarters=function(){var e=n.props,t=e.day,r=e.selected;return f.createElement("div",{className:"react-datepicker__quarter-wrapper"},[1,2,3,4].map((function(e,o){return f.createElement("div",{key:o,ref:n.QUARTER_REFS[o],role:"option",onClick:function(t){n.onQuarterClick(t,e)},onKeyDown:function(t){n.onQuarterKeyDown(t,e)},onMouseEnter:n.props.usePointerEvent?void 0:function(){return n.onQuarterMouseEnter(e)},onPointerEnter:n.props.usePointerEvent?function(){return n.onQuarterMouseEnter(e)}:void 0,className:n.getQuarterClassNames(e),"aria-selected":r?n.isSelectedQuarter(t,e,r):void 0,tabIndex:Number(n.getQuarterTabIndex(e)),"aria-current":n.isCurrentQuarter(t,e)?"date":void 0},n.getQuarterContent(e))})))},n.getClassNames=function(){var e=n.props,t=e.selectingDate,r=e.selectsStart,o=e.selectsEnd,a=e.showMonthYearPicker,i=e.showQuarterYearPicker,s=e.showWeekPicker;return g("react-datepicker__month",{"react-datepicker__month--selecting-range":t&&(r||o)},{"react-datepicker__monthPicker":a},{"react-datepicker__quarterPicker":i},{"react-datepicker__weekPicker":s})},n}return Ln(n,t),n.prototype.getSelection=function(){var e=this.props,t=e.selected,n=e.selectedDates;return e.selectsMultiple?n:t?[t]:void 0},n.prototype.render=function(){var e=this.props,t=e.showMonthYearPicker,n=e.showQuarterYearPicker,r=e.day,o=e.ariaLabelPrefix,a=void 0===o?"Month ":o,i=a?a.trim()+" ":"";return f.createElement("div",{className:this.getClassNames(),onMouseLeave:this.props.usePointerEvent?void 0:this.handleMouseLeave,onPointerLeave:this.props.usePointerEvent?this.handleMouseLeave:void 0,"aria-label":"".concat(i).concat($n(r,"MMMM, yyyy",this.props.locale)),role:"listbox"},t?this.renderMonths():n?this.renderQuarters():this.renderWeeks())},n}(e.Component),to=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.isSelectedMonth=function(e){return t.props.month===e},t.renderOptions=function(){return t.props.monthNames.map((function(e,n){return f.createElement("div",{className:t.isSelectedMonth(n)?"react-datepicker__month-option react-datepicker__month-option--selected_month":"react-datepicker__month-option",key:e,onClick:t.onChange.bind(t,n),"aria-selected":t.isSelectedMonth(n)?"true":void 0},t.isSelectedMonth(n)?f.createElement("span",{className:"react-datepicker__month-option--selected"},"✓"):"",e)}))},t.onChange=function(e){return t.props.onChange(e)},t.handleClickOutside=function(){return t.props.onCancel()},t}return Ln(t,e),t.prototype.render=function(){return f.createElement(zn,{className:"react-datepicker__month-dropdown",onClickOutside:this.handleClickOutside},this.renderOptions())},t}(e.Component),no=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={dropdownVisible:!1},t.renderSelectOptions=function(e){return e.map((function(e,t){return f.createElement("option",{key:e,value:t},e)}))},t.renderSelectMode=function(e){return f.createElement("select",{value:t.props.month,className:"react-datepicker__month-select",onChange:function(e){return t.onChange(parseInt(e.target.value))}},t.renderSelectOptions(e))},t.renderReadView=function(e,n){return f.createElement("div",{key:"read",style:{visibility:e?"visible":"hidden"},className:"react-datepicker__month-read-view",onClick:t.toggleDropdown},f.createElement("span",{className:"react-datepicker__month-read-view--down-arrow"}),f.createElement("span",{className:"react-datepicker__month-read-view--selected-month"},n[t.props.month]))},t.renderDropdown=function(e){return f.createElement(to,Yn({key:"dropdown"},t.props,{monthNames:e,onChange:t.onChange,onCancel:t.toggleDropdown}))},t.renderScrollMode=function(e){var n=t.state.dropdownVisible,r=[t.renderReadView(!n,e)];return n&&r.unshift(t.renderDropdown(e)),r},t.onChange=function(e){t.toggleDropdown(),e!==t.props.month&&t.props.onChange(e)},t.toggleDropdown=function(){return t.setState({dropdownVisible:!t.state.dropdownVisible})},t}return Ln(t,e),t.prototype.render=function(){var e,t=this,n=[0,1,2,3,4,5,6,7,8,9,10,11].map(this.props.useShortMonthInDropdown?function(e){return mr(e,t.props.locale)}:function(e){return hr(e,t.props.locale)});switch(this.props.dropdownMode){case"scroll":e=this.renderScrollMode(n);break;case"select":e=this.renderSelectMode(n)}return f.createElement("div",{className:"react-datepicker__month-dropdown-container react-datepicker__month-dropdown-container--".concat(this.props.dropdownMode)},e)},t}(e.Component);function ro(e,t){for(var n=[],r=tr(e),o=tr(t);!ee(r,o);)n.push(Qn(r)),r=Ee(r,1);return n}var oo=function(e){function t(t){var n=e.call(this,t)||this;return n.renderOptions=function(){return n.state.monthYearsList.map((function(e){var t=Ge(e),r=ir(n.props.date,e)&&sr(n.props.date,e);return f.createElement("div",{className:r?"react-datepicker__month-year-option--selected_month-year":"react-datepicker__month-year-option",key:t,onClick:n.onChange.bind(n,t),"aria-selected":r?"true":void 0},r?f.createElement("span",{className:"react-datepicker__month-year-option--selected"},"✓"):"",$n(e,n.props.dateFormat,n.props.locale))}))},n.onChange=function(e){return n.props.onChange(e)},n.handleClickOutside=function(){n.props.onCancel()},n.state={monthYearsList:ro(n.props.minDate,n.props.maxDate)},n}return Ln(t,e),t.prototype.render=function(){var e=g({"react-datepicker__month-year-dropdown":!0,"react-datepicker__month-year-dropdown--scrollable":this.props.scrollableMonthYearDropdown});return f.createElement(zn,{className:e,onClickOutside:this.handleClickOutside},this.renderOptions())},t}(e.Component),ao=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={dropdownVisible:!1},t.renderSelectOptions=function(){for(var e=tr(t.props.minDate),n=tr(t.props.maxDate),r=[];!ee(e,n);){var o=Ge(e);r.push(f.createElement("option",{key:o,value:o},$n(e,t.props.dateFormat,t.props.locale))),e=Ee(e,1)}return r},t.onSelectChange=function(e){t.onChange(parseInt(e.target.value))},t.renderSelectMode=function(){return f.createElement("select",{value:Ge(tr(t.props.date)),className:"react-datepicker__month-year-select",onChange:t.onSelectChange},t.renderSelectOptions())},t.renderReadView=function(e){var n=$n(t.props.date,t.props.dateFormat,t.props.locale);return f.createElement("div",{key:"read",style:{visibility:e?"visible":"hidden"},className:"react-datepicker__month-year-read-view",onClick:t.toggleDropdown},f.createElement("span",{className:"react-datepicker__month-year-read-view--down-arrow"}),f.createElement("span",{className:"react-datepicker__month-year-read-view--selected-month-year"},n))},t.renderDropdown=function(){return f.createElement(oo,Yn({key:"dropdown"},t.props,{onChange:t.onChange,onCancel:t.toggleDropdown}))},t.renderScrollMode=function(){var e=t.state.dropdownVisible,n=[t.renderReadView(!e)];return e&&n.unshift(t.renderDropdown()),n},t.onChange=function(e){t.toggleDropdown();var n=Qn(e);ir(t.props.date,n)&&sr(t.props.date,n)||t.props.onChange(n)},t.toggleDropdown=function(){return t.setState({dropdownVisible:!t.state.dropdownVisible})},t}return Ln(t,e),t.prototype.render=function(){var e;switch(this.props.dropdownMode){case"scroll":e=this.renderScrollMode();break;case"select":e=this.renderSelectMode()}return f.createElement("div",{className:"react-datepicker__month-year-dropdown-container react-datepicker__month-year-dropdown-container--".concat(this.props.dropdownMode)},e)},t}(e.Component),io=function(e){function t(){var n=null!==e&&e.apply(this,arguments)||this;return n.state={height:null},n.scrollToTheSelectedTime=function(){requestAnimationFrame((function(){var e,r,o;n.list&&(n.list.scrollTop=null!==(o=n.centerLi&&t.calcCenterPosition(n.props.monthRef?n.props.monthRef.clientHeight-(null!==(r=null===(e=n.header)||void 0===e?void 0:e.clientHeight)&&void 0!==r?r:0):n.list.clientHeight,n.centerLi))&&void 0!==o?o:0)}))},n.handleClick=function(e){var t,r;(n.props.minTime||n.props.maxTime)&&Er(e,n.props)||(n.props.excludeTimes||n.props.includeTimes||n.props.filterTime)&&Mr(e,n.props)||null===(r=(t=n.props).onChange)||void 0===r||r.call(t,e)},n.isSelectedTime=function(e){return n.props.selected&&(t=n.props.selected,r=e,Wr(t).getTime()===Wr(r).getTime());var t,r},n.isDisabledTime=function(e){return(n.props.minTime||n.props.maxTime)&&Er(e,n.props)||(n.props.excludeTimes||n.props.includeTimes||n.props.filterTime)&&Mr(e,n.props)},n.liClasses=function(e){var r,o=["react-datepicker__time-list-item",n.props.timeClassName?n.props.timeClassName(e):void 0];return n.isSelectedTime(e)&&o.push("react-datepicker__time-list-item--selected"),n.isDisabledTime(e)&&o.push("react-datepicker__time-list-item--disabled"),n.props.injectTimes&&(3600*ae(e)+60*oe(e)+re(e))%(60*(null!==(r=n.props.intervals)&&void 0!==r?r:t.defaultProps.intervals))!=0&&o.push("react-datepicker__time-list-item--injected"),o.join(" ")},n.handleOnKeyDown=function(e,t){var r,o;e.key===Hn.Space&&(e.preventDefault(),e.key=Hn.Enter),(e.key===Hn.ArrowUp||e.key===Hn.ArrowLeft)&&e.target instanceof HTMLElement&&e.target.previousSibling&&(e.preventDefault(),e.target.previousSibling instanceof HTMLElement&&e.target.previousSibling.focus()),(e.key===Hn.ArrowDown||e.key===Hn.ArrowRight)&&e.target instanceof HTMLElement&&e.target.nextSibling&&(e.preventDefault(),e.target.nextSibling instanceof HTMLElement&&e.target.nextSibling.focus()),e.key===Hn.Enter&&n.handleClick(t),null===(o=(r=n.props).handleOnKeyDown)||void 0===o||o.call(r,e)},n.renderTimes=function(){for(var e,r,o,a,i=[],s="string"==typeof n.props.format?n.props.format:"p",l=null!==(e=n.props.intervals)&&void 0!==e?e:t.defaultProps.intervals,c=n.props.selected||n.props.openToDate||Qn(),d=Jn(c),p=n.props.injectTimes&&n.props.injectTimes.sort((function(e,t){return e.getTime()-t.getTime()})),u=60*(r=c,o=new Date(r.getFullYear(),r.getMonth(),r.getDate()),a=new Date(r.getFullYear(),r.getMonth(),r.getDate(),24),Math.round((+a-+o)/36e5))/l,h=0;h<u;h++){var m=Xe(d,h*l);if(i.push(m),p){var g=Or(d,m,h,l,p);i=i.concat(g)}}var v=i.reduce((function(e,t){return t.getTime()<=c.getTime()?t:e}),i[0]);return i.map((function(e){return f.createElement("li",{key:e.valueOf(),onClick:n.handleClick.bind(n,e),className:n.liClasses(e),ref:function(t){e===v&&(n.centerLi=t)},onKeyDown:function(t){n.handleOnKeyDown(t,e)},tabIndex:e===v?0:-1,role:"option","aria-selected":n.isSelectedTime(e)?"true":void 0,"aria-disabled":n.isDisabledTime(e)?"true":void 0},$n(e,s,n.props.locale))}))},n.renderTimeCaption=function(){return!1===n.props.showTimeCaption?f.createElement(f.Fragment,null):f.createElement("div",{className:"react-datepicker__header react-datepicker__header--time ".concat(n.props.showTimeSelectOnly?"react-datepicker__header--time--only":""),ref:function(e){n.header=e}},f.createElement("div",{className:"react-datepicker-time__header"},n.props.timeCaption))},n}return Ln(t,e),Object.defineProperty(t,"defaultProps",{get:function(){return{intervals:30,todayButton:null,timeCaption:"Time",showTimeCaption:!0}},enumerable:!1,configurable:!0}),t.prototype.componentDidMount=function(){this.scrollToTheSelectedTime(),this.props.monthRef&&this.header&&this.setState({height:this.props.monthRef.clientHeight-this.header.clientHeight})},t.prototype.render=function(){var e,n=this,r=this.state.height;return f.createElement("div",{className:"react-datepicker__time-container ".concat((null!==(e=this.props.todayButton)&&void 0!==e?e:t.defaultProps.todayButton)?"react-datepicker__time-container--with-today-button":"")},this.renderTimeCaption(),f.createElement("div",{className:"react-datepicker__time"},f.createElement("div",{className:"react-datepicker__time-box"},f.createElement("ul",{className:"react-datepicker__time-list",ref:function(e){n.list=e},style:r?{height:r}:{},role:"listbox","aria-label":this.props.timeCaption},this.renderTimes()))))},t.calcCenterPosition=function(e,t){return t.offsetTop-(e/2-t.clientHeight/2)},t}(e.Component),so=function(t){function n(n){var r=t.call(this,n)||this;return r.YEAR_REFS=Wn([],Array(r.props.yearItemNumber),!0).map((function(){return e.createRef()})),r.isDisabled=function(e){return gr(e,{minDate:r.props.minDate,maxDate:r.props.maxDate,excludeDates:r.props.excludeDates,includeDates:r.props.includeDates,filterDate:r.props.filterDate})},r.isExcluded=function(e){return vr(e,{excludeDates:r.props.excludeDates})},r.selectingDate=function(){var e;return null!==(e=r.props.selectingDate)&&void 0!==e?e:r.props.preSelection},r.updateFocusOnPaginate=function(e){window.requestAnimationFrame((function(){var t,n;null===(n=null===(t=r.YEAR_REFS[e])||void 0===t?void 0:t.current)||void 0===n||n.focus()}))},r.handleYearClick=function(e,t){r.props.onDayClick&&r.props.onDayClick(e,t)},r.handleYearNavigation=function(e,t){var n,o,a,i,s=r.props,l=s.date,c=s.yearItemNumber;if(void 0!==l&&void 0!==c){var d=Yr(l,c).startPeriod;r.isDisabled(t)||r.isExcluded(t)||(null===(o=(n=r.props).setPreSelection)||void 0===o||o.call(n,t),e-d<0?r.updateFocusOnPaginate(c-(d-e)):e-d>=c?r.updateFocusOnPaginate(Math.abs(c-(e-d))):null===(i=null===(a=r.YEAR_REFS[e-d])||void 0===a?void 0:a.current)||void 0===i||i.focus())}},r.isSameDay=function(e,t){return cr(e,t)},r.isCurrentYear=function(e){return e===ie(Qn())},r.isRangeStart=function(e){return r.props.startDate&&r.props.endDate&&ir(Te(Qn(),e),r.props.startDate)},r.isRangeEnd=function(e){return r.props.startDate&&r.props.endDate&&ir(Te(Qn(),e),r.props.endDate)},r.isInRange=function(e){return Dr(e,r.props.startDate,r.props.endDate)},r.isInSelectingRange=function(e){var t=r.props,n=t.selectsStart,o=t.selectsEnd,a=t.selectsRange,i=t.startDate,s=t.endDate;return!(!(n||o||a)||!r.selectingDate())&&(n&&s?Dr(e,r.selectingDate(),s):(o&&i||!(!a||!i||s))&&Dr(e,i,r.selectingDate()))},r.isSelectingRangeStart=function(e){var t;if(!r.isInSelectingRange(e))return!1;var n=r.props,o=n.startDate,a=n.selectsStart,i=Te(Qn(),e);return ir(i,a?null!==(t=r.selectingDate())&&void 0!==t?t:null:null!=o?o:null)},r.isSelectingRangeEnd=function(e){var t;if(!r.isInSelectingRange(e))return!1;var n=r.props,o=n.endDate,a=n.selectsEnd,i=n.selectsRange,s=Te(Qn(),e);return ir(s,a||i?null!==(t=r.selectingDate())&&void 0!==t?t:null:null!=o?o:null)},r.isKeyboardSelected=function(e){if(void 0!==r.props.date&&null!=r.props.selected&&null!=r.props.preSelection){var t=r.props,n=t.minDate,o=t.maxDate,a=t.excludeDates,i=t.includeDates,s=t.filterDate,l=nr(Te(r.props.date,e)),c=(n||o||a||i||s)&&Cr(e,r.props);return!r.props.disabledKeyboardNavigation&&!r.props.inline&&!cr(l,nr(r.props.selected))&&cr(l,nr(r.props.preSelection))&&!c}},r.onYearClick=function(e,t){var n=r.props.date;void 0!==n&&r.handleYearClick(nr(Te(n,t)),e)},r.onYearKeyDown=function(e,t){var n,o,a=e.key,i=r.props,s=i.date,l=i.yearItemNumber,c=i.handleOnKeyDown;if(a!==Hn.Tab&&e.preventDefault(),!r.props.disabledKeyboardNavigation)switch(a){case Hn.Enter:if(null==r.props.selected)break;r.onYearClick(e,t),null===(o=(n=r.props).setPreSelection)||void 0===o||o.call(n,r.props.selected);break;case Hn.ArrowRight:if(null==r.props.preSelection)break;r.handleYearNavigation(t+1,Ne(r.props.preSelection,1));break;case Hn.ArrowLeft:if(null==r.props.preSelection)break;r.handleYearNavigation(t-1,Ie(r.props.preSelection,1));break;case Hn.ArrowUp:if(void 0===s||void 0===l||null==r.props.preSelection)break;var d=Yr(s,l).startPeriod;if((f=t-(u=3))<d){var p=l%u;t>=d&&t<d+p?u=p:u+=p,f=t-u}r.handleYearNavigation(f,Ie(r.props.preSelection,u));break;case Hn.ArrowDown:if(void 0===s||void 0===l||null==r.props.preSelection)break;var u,f,h=Yr(s,l).endPeriod;if((f=t+(u=3))>h){p=l%u;t<=h&&t>h-p?u=p:u+=p,f=t+u}r.handleYearNavigation(f,Ne(r.props.preSelection,u))}c&&c(e)},r.getYearClassNames=function(e){var t=r.props,n=t.date,o=t.minDate,a=t.maxDate,i=t.selected,s=t.excludeDates,l=t.includeDates,c=t.filterDate,d=t.yearClassName;return g("react-datepicker__year-text","react-datepicker__year-".concat(e),n?null==d?void 0:d(Te(n,e)):void 0,{"react-datepicker__year-text--selected":i?e===ie(i):void 0,"react-datepicker__year-text--disabled":(o||a||s||l||c)&&Cr(e,r.props),"react-datepicker__year-text--keyboard-selected":r.isKeyboardSelected(e),"react-datepicker__year-text--range-start":r.isRangeStart(e),"react-datepicker__year-text--range-end":r.isRangeEnd(e),"react-datepicker__year-text--in-range":r.isInRange(e),"react-datepicker__year-text--in-selecting-range":r.isInSelectingRange(e),"react-datepicker__year-text--selecting-range-start":r.isSelectingRangeStart(e),"react-datepicker__year-text--selecting-range-end":r.isSelectingRangeEnd(e),"react-datepicker__year-text--today":r.isCurrentYear(e)})},r.getYearTabIndex=function(e){if(r.props.disabledKeyboardNavigation||null==r.props.preSelection)return"-1";var t=ie(r.props.preSelection),n=Cr(e,r.props);return e!==t||n?"-1":"0"},r.getYearContent=function(e){return r.props.renderYearContent?r.props.renderYearContent(e):e},r}return Ln(n,t),n.prototype.render=function(){var e=this,t=[],n=this.props,r=n.date,o=n.yearItemNumber,a=n.onYearMouseEnter,i=n.onYearMouseLeave;if(void 0===r)return null;for(var s=Yr(r,o),l=s.startPeriod,c=s.endPeriod,d=function(n){t.push(f.createElement("div",{ref:p.YEAR_REFS[n-l],onClick:function(t){e.onYearClick(t,n)},onKeyDown:function(t){Vr(t)&&(t.preventDefault(),t.key=Hn.Enter),e.onYearKeyDown(t,n)},tabIndex:Number(p.getYearTabIndex(n)),className:p.getYearClassNames(n),onMouseEnter:p.props.usePointerEvent?void 0:function(e){return a(e,n)},onPointerEnter:p.props.usePointerEvent?function(e){return a(e,n)}:void 0,onMouseLeave:p.props.usePointerEvent?void 0:function(e){return i(e,n)},onPointerLeave:p.props.usePointerEvent?function(e){return i(e,n)}:void 0,key:n,"aria-current":p.isCurrentYear(n)?"date":void 0},p.getYearContent(n)))},p=this,u=l;u<=c;u++)d(u);return f.createElement("div",{className:"react-datepicker__year"},f.createElement("div",{className:"react-datepicker__year-wrapper",onMouseLeave:this.props.usePointerEvent?void 0:this.props.clearSelectingDate,onPointerLeave:this.props.usePointerEvent?this.props.clearSelectingDate:void 0},t))},n}(e.Component);function lo(e,t,n,r){for(var o=[],a=0;a<2*t+1;a++){var i=e+t-a,s=!0;n&&(s=ie(n)<=i),r&&s&&(s=ie(r)>=i),s&&o.push(i)}return o}var co=function(t){function n(n){var r=t.call(this,n)||this;r.renderOptions=function(){var e=r.props.year,t=r.state.yearsList.map((function(t){return f.createElement("div",{className:e===t?"react-datepicker__year-option react-datepicker__year-option--selected_year":"react-datepicker__year-option",key:t,onClick:r.onChange.bind(r,t),"aria-selected":e===t?"true":void 0},e===t?f.createElement("span",{className:"react-datepicker__year-option--selected"},"✓"):"",t)})),n=r.props.minDate?ie(r.props.minDate):null,o=r.props.maxDate?ie(r.props.maxDate):null;return o&&r.state.yearsList.find((function(e){return e===o}))||t.unshift(f.createElement("div",{className:"react-datepicker__year-option",key:"upcoming",onClick:r.incrementYears},f.createElement("a",{className:"react-datepicker__navigation react-datepicker__navigation--years react-datepicker__navigation--years-upcoming"}))),n&&r.state.yearsList.find((function(e){return e===n}))||t.push(f.createElement("div",{className:"react-datepicker__year-option",key:"previous",onClick:r.decrementYears},f.createElement("a",{className:"react-datepicker__navigation react-datepicker__navigation--years react-datepicker__navigation--years-previous"}))),t},r.onChange=function(e){r.props.onChange(e)},r.handleClickOutside=function(){r.props.onCancel()},r.shiftYears=function(e){var t=r.state.yearsList.map((function(t){return t+e}));r.setState({yearsList:t})},r.incrementYears=function(){return r.shiftYears(1)},r.decrementYears=function(){return r.shiftYears(-1)};var o=n.yearDropdownItemNumber,a=n.scrollableYearDropdown,i=o||(a?10:5);return r.state={yearsList:lo(r.props.year,i,r.props.minDate,r.props.maxDate)},r.dropdownRef=e.createRef(),r}return Ln(n,t),n.prototype.componentDidMount=function(){var e=this.dropdownRef.current;if(e){var t=e.children?Array.from(e.children):null,n=t?t.find((function(e){return e.ariaSelected})):null;e.scrollTop=n&&n instanceof HTMLElement?n.offsetTop+(n.clientHeight-e.clientHeight)/2:(e.scrollHeight-e.clientHeight)/2}},n.prototype.render=function(){var e=g({"react-datepicker__year-dropdown":!0,"react-datepicker__year-dropdown--scrollable":this.props.scrollableYearDropdown});return f.createElement(zn,{className:e,containerRef:this.dropdownRef,onClickOutside:this.handleClickOutside},this.renderOptions())},n}(e.Component),po=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={dropdownVisible:!1},t.renderSelectOptions=function(){for(var e=t.props.minDate?ie(t.props.minDate):1900,n=t.props.maxDate?ie(t.props.maxDate):2100,r=[],o=e;o<=n;o++)r.push(f.createElement("option",{key:o,value:o},o));return r},t.onSelectChange=function(e){t.onChange(parseInt(e.target.value))},t.renderSelectMode=function(){return f.createElement("select",{value:t.props.year,className:"react-datepicker__year-select",onChange:t.onSelectChange},t.renderSelectOptions())},t.renderReadView=function(e){return f.createElement("div",{key:"read",style:{visibility:e?"visible":"hidden"},className:"react-datepicker__year-read-view",onClick:function(e){return t.toggleDropdown(e)}},f.createElement("span",{className:"react-datepicker__year-read-view--down-arrow"}),f.createElement("span",{className:"react-datepicker__year-read-view--selected-year"},t.props.year))},t.renderDropdown=function(){return f.createElement(co,Yn({key:"dropdown"},t.props,{onChange:t.onChange,onCancel:t.toggleDropdown}))},t.renderScrollMode=function(){var e=t.state.dropdownVisible,n=[t.renderReadView(!e)];return e&&n.unshift(t.renderDropdown()),n},t.onChange=function(e){t.toggleDropdown(),e!==t.props.year&&t.props.onChange(e)},t.toggleDropdown=function(e){t.setState({dropdownVisible:!t.state.dropdownVisible},(function(){t.props.adjustDateOnChange&&t.handleYearChange(t.props.date,e)}))},t.handleYearChange=function(e,n){var r;null===(r=t.onSelect)||void 0===r||r.call(t,e,n),t.setOpen()},t.onSelect=function(e,n){var r,o;null===(o=(r=t.props).onSelect)||void 0===o||o.call(r,e,n)},t.setOpen=function(){var e,n;null===(n=(e=t.props).setOpen)||void 0===n||n.call(e,!0)},t}return Ln(t,e),t.prototype.render=function(){var e;switch(this.props.dropdownMode){case"scroll":e=this.renderScrollMode();break;case"select":e=this.renderSelectMode()}return f.createElement("div",{className:"react-datepicker__year-dropdown-container react-datepicker__year-dropdown-container--".concat(this.props.dropdownMode)},e)},t}(e.Component),uo=["react-datepicker__year-select","react-datepicker__month-select","react-datepicker__month-year-select"],fo=function(t){function n(r){var o=t.call(this,r)||this;return o.monthContainer=void 0,o.handleClickOutside=function(e){o.props.onClickOutside(e)},o.setClickOutsideRef=function(){return o.containerRef.current},o.handleDropdownFocus=function(e){var t,n,r,a;r=e.target,a=(r.className||"").split(/\s+/),uo.some((function(e){return a.indexOf(e)>=0}))&&(null===(n=(t=o.props).onDropdownFocus)||void 0===n||n.call(t,e))},o.getDateInView=function(){var e=o.props,t=e.preSelection,n=e.selected,r=e.openToDate,a=jr(o.props),i=Ir(o.props),s=Qn(),l=r||n||t;return l||(a&&J(s,a)?a:i&&ee(s,i)?i:s)},o.increaseMonth=function(){o.setState((function(e){var t=e.date;return{date:Ee(t,1)}}),(function(){return o.handleMonthChange(o.state.date)}))},o.decreaseMonth=function(){o.setState((function(e){var t=e.date;return{date:Fe(t,1)}}),(function(){return o.handleMonthChange(o.state.date)}))},o.handleDayClick=function(e,t,n){o.props.onSelect(e,t,n),o.props.setPreSelection&&o.props.setPreSelection(e)},o.handleDayMouseEnter=function(e){o.setState({selectingDate:e}),o.props.onDayMouseEnter&&o.props.onDayMouseEnter(e)},o.handleMonthMouseLeave=function(){o.setState({selectingDate:void 0}),o.props.onMonthMouseLeave&&o.props.onMonthMouseLeave()},o.handleYearMouseEnter=function(e,t){o.setState({selectingDate:Te(Qn(),t)}),o.props.onYearMouseEnter&&o.props.onYearMouseEnter(e,t)},o.handleYearMouseLeave=function(e,t){o.props.onYearMouseLeave&&o.props.onYearMouseLeave(e,t)},o.handleYearChange=function(e){var t,n,r,a;null===(n=(t=o.props).onYearChange)||void 0===n||n.call(t,e),o.setState({isRenderAriaLiveMessage:!0}),o.props.adjustDateOnChange&&(o.props.onSelect(e),null===(a=(r=o.props).setOpen)||void 0===a||a.call(r,!0)),o.props.setPreSelection&&o.props.setPreSelection(e)},o.getEnabledPreSelectionDateForMonth=function(e){if(!gr(e,o.props))return e;for(var t=tr(e),n=function(e){return Ce(e)}(e),r=Pe(n,t),a=null,i=0;i<=r;i++){var s=Re(t,i);if(!gr(s,o.props)){a=s;break}}return a},o.handleMonthChange=function(e){var t,n,r,a=null!==(t=o.getEnabledPreSelectionDateForMonth(e))&&void 0!==t?t:e;o.handleCustomMonthChange(a),o.props.adjustDateOnChange&&(o.props.onSelect(a),null===(r=(n=o.props).setOpen)||void 0===r||r.call(n,!0)),o.props.setPreSelection&&o.props.setPreSelection(a)},o.handleCustomMonthChange=function(e){var t,n;null===(n=(t=o.props).onMonthChange)||void 0===n||n.call(t,e),o.setState({isRenderAriaLiveMessage:!0})},o.handleMonthYearChange=function(e){o.handleYearChange(e),o.handleMonthChange(e)},o.changeYear=function(e){o.setState((function(t){var n=t.date;return{date:Te(n,Number(e))}}),(function(){return o.handleYearChange(o.state.date)}))},o.changeMonth=function(e){o.setState((function(t){var n=t.date;return{date:je(n,Number(e))}}),(function(){return o.handleMonthChange(o.state.date)}))},o.changeMonthYear=function(e){o.setState((function(t){var n=t.date;return{date:Te(je(n,se(e)),ie(e))}}),(function(){return o.handleMonthYearChange(o.state.date)}))},o.header=function(e){void 0===e&&(e=o.state.date);var t=er(e,o.props.locale,o.props.calendarStartDay),n=[];return o.props.showWeekNumbers&&n.push(f.createElement("div",{key:"W",className:"react-datepicker__day-name"},o.props.weekLabel||"#")),n.concat([0,1,2,3,4,5,6].map((function(e){var n=Re(t,e),r=o.formatWeekday(n,o.props.locale),a=o.props.weekDayClassName?o.props.weekDayClassName(n):void 0;return f.createElement("div",{key:e,"aria-label":$n(n,"EEEE",o.props.locale),className:g("react-datepicker__day-name",a)},r)})))},o.formatWeekday=function(e,t){return o.props.formatWeekDay?function(e,t,n){return t($n(e,"EEEE",n))}(e,o.props.formatWeekDay,t):o.props.useWeekdaysShort?function(e,t){return $n(e,"EEE",t)}(e,t):function(e,t){return $n(e,"EEEEEE",t)}(e,t)},o.decreaseYear=function(){o.setState((function(e){var t,r=e.date;return{date:Ie(r,o.props.showYearPicker?null!==(t=o.props.yearItemNumber)&&void 0!==t?t:n.defaultProps.yearItemNumber:1)}}),(function(){return o.handleYearChange(o.state.date)}))},o.clearSelectingDate=function(){o.setState({selectingDate:void 0})},o.renderPreviousButton=function(){var e,t,r;if(!o.props.renderCustomHeader){var a,i=null!==(e=o.props.monthsShown)&&void 0!==e?e:n.defaultProps.monthsShown,s=o.props.showPreviousMonths?i-1:0,l=null!==(t=o.props.monthSelectedIn)&&void 0!==t?t:s,c=Fe(o.state.date,l);switch(!0){case o.props.showMonthYearPicker:a=Pr(o.state.date,o.props);break;case o.props.showYearPicker:a=function(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.yearItemNumber,a=void 0===o?12:o,i=Yr(nr(Ie(e,a)),a).endPeriod,s=r&&ie(r);return s&&s>i||!1}(o.state.date,o.props);break;case o.props.showQuarterYearPicker:a=function(e,t){var n=void 0===t?{}:t,r=n.minDate,o=n.includeDates,a=De(e),i=ze(a);return r&&Ke(r,i)>0||o&&o.every((function(e){return Ke(e,i)>0}))||!1}(o.state.date,o.props);break;default:a=Fr(c,o.props)}if(((null!==(r=o.props.forceShowMonthNavigation)&&void 0!==r?r:n.defaultProps.forceShowMonthNavigation)||o.props.showDisabledMonthNavigation||!a)&&!o.props.showTimeSelectOnly){var d=["react-datepicker__navigation","react-datepicker__navigation--previous"],p=o.decreaseMonth;(o.props.showMonthYearPicker||o.props.showQuarterYearPicker||o.props.showYearPicker)&&(p=o.decreaseYear),a&&o.props.showDisabledMonthNavigation&&(d.push("react-datepicker__navigation--previous--disabled"),p=void 0);var u=o.props.showMonthYearPicker||o.props.showQuarterYearPicker||o.props.showYearPicker,h=o.props,m=h.previousMonthButtonLabel,g=void 0===m?n.defaultProps.previousMonthButtonLabel:m,v=h.previousYearButtonLabel,y=void 0===v?n.defaultProps.previousYearButtonLabel:v,x=o.props,w=x.previousMonthAriaLabel,b=void 0===w?"string"==typeof g?g:"Previous Month":w,D=x.previousYearAriaLabel,C=void 0===D?"string"==typeof y?y:"Previous Year":D;return f.createElement("button",{type:"button",className:d.join(" "),onClick:p,onKeyDown:o.props.handleOnKeyDown,"aria-label":u?C:b},f.createElement("span",{className:["react-datepicker__navigation-icon","react-datepicker__navigation-icon--previous"].join(" ")},u?y:g))}}},o.increaseYear=function(){o.setState((function(e){var t,r=e.date;return{date:Ne(r,o.props.showYearPicker?null!==(t=o.props.yearItemNumber)&&void 0!==t?t:n.defaultProps.yearItemNumber:1)}}),(function(){return o.handleYearChange(o.state.date)}))},o.renderNextButton=function(){var e;if(!o.props.renderCustomHeader){var t;switch(!0){case o.props.showMonthYearPicker:t=Rr(o.state.date,o.props);break;case o.props.showYearPicker:t=function(e,t){var n=void 0===t?{}:t,r=n.maxDate,o=n.yearItemNumber,a=void 0===o?12:o,i=Yr(Ne(e,a),a).startPeriod,s=r&&ie(r);return s&&s<i||!1}(o.state.date,o.props);break;case o.props.showQuarterYearPicker:t=function(e,t){var n=void 0===t?{}:t,r=n.maxDate,o=n.includeDates,a=be(e),i=Qe(a,1);return r&&Ke(i,r)>0||o&&o.every((function(e){return Ke(i,e)>0}))||!1}(o.state.date,o.props);break;default:t=Tr(o.state.date,o.props)}if(((null!==(e=o.props.forceShowMonthNavigation)&&void 0!==e?e:n.defaultProps.forceShowMonthNavigation)||o.props.showDisabledMonthNavigation||!t)&&!o.props.showTimeSelectOnly){var r=["react-datepicker__navigation","react-datepicker__navigation--next"];o.props.showTimeSelect&&r.push("react-datepicker__navigation--next--with-time"),o.props.todayButton&&r.push("react-datepicker__navigation--next--with-today-button");var a=o.increaseMonth;(o.props.showMonthYearPicker||o.props.showQuarterYearPicker||o.props.showYearPicker)&&(a=o.increaseYear),t&&o.props.showDisabledMonthNavigation&&(r.push("react-datepicker__navigation--next--disabled"),a=void 0);var i=o.props.showMonthYearPicker||o.props.showQuarterYearPicker||o.props.showYearPicker,s=o.props,l=s.nextMonthButtonLabel,c=void 0===l?n.defaultProps.nextMonthButtonLabel:l,d=s.nextYearButtonLabel,p=void 0===d?n.defaultProps.nextYearButtonLabel:d,u=o.props,h=u.nextMonthAriaLabel,m=void 0===h?"string"==typeof c?c:"Next Month":h,g=u.nextYearAriaLabel,v=void 0===g?"string"==typeof p?p:"Next Year":g;return f.createElement("button",{type:"button",className:r.join(" "),onClick:a,onKeyDown:o.props.handleOnKeyDown,"aria-label":i?v:m},f.createElement("span",{className:["react-datepicker__navigation-icon","react-datepicker__navigation-icon--next"].join(" ")},i?p:c))}}},o.renderCurrentMonth=function(e){void 0===e&&(e=o.state.date);var t=["react-datepicker__current-month"];return o.props.showYearDropdown&&t.push("react-datepicker__current-month--hasYearDropdown"),o.props.showMonthDropdown&&t.push("react-datepicker__current-month--hasMonthDropdown"),o.props.showMonthYearDropdown&&t.push("react-datepicker__current-month--hasMonthYearDropdown"),f.createElement("h2",{className:t.join(" ")},$n(e,o.props.dateFormat,o.props.locale))},o.renderYearDropdown=function(e){if(void 0===e&&(e=!1),o.props.showYearDropdown&&!e)return f.createElement(po,Yn({},n.defaultProps,o.props,{date:o.state.date,onChange:o.changeYear,year:ie(o.state.date)}))},o.renderMonthDropdown=function(e){if(void 0===e&&(e=!1),o.props.showMonthDropdown&&!e)return f.createElement(no,Yn({},n.defaultProps,o.props,{month:se(o.state.date),onChange:o.changeMonth}))},o.renderMonthYearDropdown=function(e){if(void 0===e&&(e=!1),o.props.showMonthYearDropdown&&!e)return f.createElement(ao,Yn({},n.defaultProps,o.props,{date:o.state.date,onChange:o.changeMonthYear}))},o.handleTodayButtonClick=function(e){o.props.onSelect(or(),e),o.props.setPreSelection&&o.props.setPreSelection(or())},o.renderTodayButton=function(){if(o.props.todayButton&&!o.props.showTimeSelectOnly)return f.createElement("div",{className:"react-datepicker__today-button",onClick:o.handleTodayButtonClick},o.props.todayButton)},o.renderDefaultHeader=function(e){var t=e.monthDate,n=e.i;return f.createElement("div",{className:"react-datepicker__header ".concat(o.props.showTimeSelect?"react-datepicker__header--has-time-select":"")},o.renderCurrentMonth(t),f.createElement("div",{className:"react-datepicker__header__dropdown react-datepicker__header__dropdown--".concat(o.props.dropdownMode),onFocus:o.handleDropdownFocus},o.renderMonthDropdown(0!==n),o.renderMonthYearDropdown(0!==n),o.renderYearDropdown(0!==n)),f.createElement("div",{className:"react-datepicker__day-names"},o.header(t)))},o.renderCustomHeader=function(e){var t,n,r=e.monthDate,a=e.i;if(o.props.showTimeSelect&&!o.state.monthContainer||o.props.showTimeSelectOnly)return null;var i=Fr(o.state.date,o.props),s=Tr(o.state.date,o.props),l=Pr(o.state.date,o.props),c=Rr(o.state.date,o.props),d=!o.props.showMonthYearPicker&&!o.props.showQuarterYearPicker&&!o.props.showYearPicker;return f.createElement("div",{className:"react-datepicker__header react-datepicker__header--custom",onFocus:o.props.onDropdownFocus},null===(n=(t=o.props).renderCustomHeader)||void 0===n?void 0:n.call(t,Yn(Yn({},o.state),{customHeaderCount:a,monthDate:r,changeMonth:o.changeMonth,changeYear:o.changeYear,decreaseMonth:o.decreaseMonth,increaseMonth:o.increaseMonth,decreaseYear:o.decreaseYear,increaseYear:o.increaseYear,prevMonthButtonDisabled:i,nextMonthButtonDisabled:s,prevYearButtonDisabled:l,nextYearButtonDisabled:c})),d&&f.createElement("div",{className:"react-datepicker__day-names"},o.header(r)))},o.renderYearHeader=function(e){var t=e.monthDate,r=o.props,a=r.showYearPicker,i=r.yearItemNumber,s=Yr(t,void 0===i?n.defaultProps.yearItemNumber:i),l=s.startPeriod,c=s.endPeriod;return f.createElement("div",{className:"react-datepicker__header react-datepicker-year-header"},a?"".concat(l," - ").concat(c):ie(t))},o.renderHeader=function(e){var t=e.monthDate,n=e.i,r={monthDate:t,i:void 0===n?0:n};switch(!0){case void 0!==o.props.renderCustomHeader:return o.renderCustomHeader(r);case o.props.showMonthYearPicker||o.props.showQuarterYearPicker||o.props.showYearPicker:return o.renderYearHeader(r);default:return o.renderDefaultHeader(r)}},o.renderMonths=function(){var e,t;if(!o.props.showTimeSelectOnly&&!o.props.showYearPicker){for(var r=[],a=null!==(e=o.props.monthsShown)&&void 0!==e?e:n.defaultProps.monthsShown,i=o.props.showPreviousMonths?a-1:0,s=o.props.showMonthYearPicker||o.props.showQuarterYearPicker?Ne(o.state.date,i):Fe(o.state.date,i),l=null!==(t=o.props.monthSelectedIn)&&void 0!==t?t:i,c=0;c<a;++c){var d=c-l+i,p=o.props.showMonthYearPicker||o.props.showQuarterYearPicker?Ne(s,d):Ee(s,d),u="month-".concat(c),h=c<a-1,m=c>0;r.push(f.createElement("div",{key:u,ref:function(e){o.monthContainer=null!=e?e:void 0},className:"react-datepicker__month-container"},o.renderHeader({monthDate:p,i:c}),f.createElement(eo,Yn({},n.defaultProps,o.props,{ariaLabelPrefix:o.props.monthAriaLabelPrefix,day:p,onDayClick:o.handleDayClick,handleOnKeyDown:o.props.handleOnDayKeyDown,handleOnMonthKeyDown:o.props.handleOnKeyDown,onDayMouseEnter:o.handleDayMouseEnter,onMouseLeave:o.handleMonthMouseLeave,orderInDisplay:c,selectingDate:o.state.selectingDate,monthShowsDuplicateDaysEnd:h,monthShowsDuplicateDaysStart:m}))))}return r}},o.renderYears=function(){if(!o.props.showTimeSelectOnly)return o.props.showYearPicker?f.createElement("div",{className:"react-datepicker__year--container"},o.renderHeader({monthDate:o.state.date}),f.createElement(so,Yn({},n.defaultProps,o.props,{selectingDate:o.state.selectingDate,date:o.state.date,onDayClick:o.handleDayClick,clearSelectingDate:o.clearSelectingDate,onYearMouseEnter:o.handleYearMouseEnter,onYearMouseLeave:o.handleYearMouseLeave}))):void 0},o.renderTimeSection=function(){if(o.props.showTimeSelect&&(o.state.monthContainer||o.props.showTimeSelectOnly))return f.createElement(io,Yn({},n.defaultProps,o.props,{onChange:o.props.onTimeChange,format:o.props.timeFormat,intervals:o.props.timeIntervals,monthRef:o.state.monthContainer}))},o.renderInputTimeSection=function(){var e=o.props.selected?new Date(o.props.selected):void 0,t=e&&qn(e)&&Boolean(o.props.selected)?"".concat(Lr(e.getHours()),":").concat(Lr(e.getMinutes())):"";if(o.props.showTimeInput)return f.createElement(Kr,Yn({},n.defaultProps,o.props,{date:e,timeString:t,onChange:o.props.onTimeChange}))},o.renderAriaLiveRegion=function(){var e,t,r=Yr(o.state.date,null!==(e=o.props.yearItemNumber)&&void 0!==e?e:n.defaultProps.yearItemNumber),a=r.startPeriod,i=r.endPeriod;return t=o.props.showYearPicker?"".concat(a," - ").concat(i):o.props.showMonthYearPicker||o.props.showQuarterYearPicker?ie(o.state.date):"".concat(hr(se(o.state.date),o.props.locale)," ").concat(ie(o.state.date)),f.createElement("span",{role:"alert","aria-live":"polite",className:"react-datepicker__aria-live"},o.state.isRenderAriaLiveMessage&&t)},o.renderChildren=function(){if(o.props.children)return f.createElement("div",{className:"react-datepicker__children-container"},o.props.children)},o.containerRef=e.createRef(),o.state={date:o.getDateInView(),selectingDate:void 0,monthContainer:void 0,isRenderAriaLiveMessage:!1},o}return Ln(n,t),Object.defineProperty(n,"defaultProps",{get:function(){return{monthsShown:1,forceShowMonthNavigation:!1,timeCaption:"Time",previousYearButtonLabel:"Previous Year",nextYearButtonLabel:"Next Year",previousMonthButtonLabel:"Previous Month",nextMonthButtonLabel:"Next Month",yearItemNumber:12}},enumerable:!1,configurable:!0}),n.prototype.componentDidMount=function(){var e=this;this.props.showTimeSelect&&(this.assignMonthContainer=void e.setState({monthContainer:e.monthContainer}))},n.prototype.componentDidUpdate=function(e){var t=this;if(!this.props.preSelection||cr(this.props.preSelection,e.preSelection)&&this.props.monthSelectedIn===e.monthSelectedIn)this.props.openToDate&&!cr(this.props.openToDate,e.openToDate)&&this.setState({date:this.props.openToDate});else{var n=!sr(this.state.date,this.props.preSelection);this.setState({date:this.props.preSelection},(function(){return n&&t.handleCustomMonthChange(t.state.date)}))}},n.prototype.render=function(){var e=this.props.container||Vn;return f.createElement(zn,{onClickOutside:this.handleClickOutside,style:{display:"contents"},containerRef:this.containerRef,ignoreClass:this.props.outsideClickIgnoreClass},f.createElement(e,{className:g("react-datepicker",this.props.className,{"react-datepicker--time-only":this.props.showTimeSelectOnly}),showTime:this.props.showTimeSelect||this.props.showTimeInput,showTimeSelectOnly:this.props.showTimeSelectOnly},this.renderAriaLiveRegion(),this.renderPreviousButton(),this.renderNextButton(),this.renderMonths(),this.renderYears(),this.renderTodayButton(),this.renderTimeSection(),this.renderInputTimeSection(),this.renderChildren()))},n}(e.Component),ho=function(e){var t=e.icon,n=e.className,r=void 0===n?"":n,o=e.onClick,a="react-datepicker__calendar-icon";return"string"==typeof t?f.createElement("i",{className:"".concat(a," ").concat(t," ").concat(r),"aria-hidden":"true",onClick:o}):f.isValidElement(t)?f.cloneElement(t,{className:"".concat(t.props.className||""," ").concat(a," ").concat(r),onClick:function(e){"function"==typeof t.props.onClick&&t.props.onClick(e),"function"==typeof o&&o(e)}}):f.createElement("svg",{className:"".concat(a," ").concat(r),xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 448 512",onClick:o},f.createElement("path",{d:"M96 32V64H48C21.5 64 0 85.5 0 112v48H448V112c0-26.5-21.5-48-48-48H352V32c0-17.7-14.3-32-32-32s-32 14.3-32 32V64H160V32c0-17.7-14.3-32-32-32S96 14.3 96 32zM448 192H0V464c0 26.5 21.5 48 48 48H400c26.5 0 48-21.5 48-48V192z"}))},mo=function(e){function t(t){var n=e.call(this,t)||this;return n.portalRoot=null,n.el=document.createElement("div"),n}return Ln(t,e),t.prototype.componentDidMount=function(){this.portalRoot=(this.props.portalHost||document).getElementById(this.props.portalId),this.portalRoot||(this.portalRoot=document.createElement("div"),this.portalRoot.setAttribute("id",this.props.portalId),(this.props.portalHost||document.body).appendChild(this.portalRoot)),this.portalRoot.appendChild(this.el)},t.prototype.componentWillUnmount=function(){this.portalRoot&&this.portalRoot.removeChild(this.el)},t.prototype.render=function(){return v.createPortal(this.props.children,this.el)},t}(e.Component),go=function(e){return(e instanceof HTMLAnchorElement||!e.disabled)&&-1!==e.tabIndex},vo=function(t){function n(n){var r=t.call(this,n)||this;return r.getTabChildren=function(){var e;return Array.prototype.slice.call(null===(e=r.tabLoopRef.current)||void 0===e?void 0:e.querySelectorAll("[tabindex], a, button, input, select, textarea"),1,-1).filter(go)},r.handleFocusStart=function(){var e=r.getTabChildren();e&&e.length>1&&e[e.length-1].focus()},r.handleFocusEnd=function(){var e=r.getTabChildren();e&&e.length>1&&e[0].focus()},r.tabLoopRef=e.createRef(),r}return Ln(n,t),n.prototype.render=function(){var e;return(null!==(e=this.props.enableTabLoop)&&void 0!==e?e:n.defaultProps.enableTabLoop)?f.createElement("div",{className:"react-datepicker__tab-loop",ref:this.tabLoopRef},f.createElement("div",{className:"react-datepicker__tab-loop__start",tabIndex:0,onFocus:this.handleFocusStart}),this.props.children,f.createElement("div",{className:"react-datepicker__tab-loop__end",tabIndex:0,onFocus:this.handleFocusEnd})):this.props.children},n.defaultProps={enableTabLoop:!0},n}(e.Component);var yo,xo=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return Ln(n,t),Object.defineProperty(n,"defaultProps",{get:function(){return{hidePopper:!0}},enumerable:!1,configurable:!0}),n.prototype.render=function(){var t=this.props,r=t.className,o=t.wrapperClassName,a=t.hidePopper,i=void 0===a?n.defaultProps.hidePopper:a,s=t.popperComponent,l=t.targetComponent,c=t.enableTabLoop,d=t.popperOnKeyDown,p=t.portalId,u=t.portalHost,h=t.popperProps,m=t.showArrow,v=void 0;if(!i){var y=g("react-datepicker-popper",r);v=f.createElement(vo,{enableTabLoop:c},f.createElement("div",{ref:h.refs.setFloating,style:h.floatingStyles,className:y,"data-placement":h.placement,onKeyDown:d},s,m&&f.createElement(Rn,{ref:h.arrowRef,context:h.context,fill:"currentColor",strokeWidth:1,height:8,width:16,style:{transform:"translateY(-1px)"},className:"react-datepicker__triangle"})))}this.props.popperContainer&&(v=e.createElement(this.props.popperContainer,{},v)),p&&!i&&(v=f.createElement(mo,{portalId:p,portalHost:u},v));var x=g("react-datepicker-wrapper",o);return f.createElement(f.Fragment,null,f.createElement("div",{ref:h.refs.setReference,className:x},l),v)},n}(e.Component),wo=(yo=xo,function(t){var n,r,o,a="boolean"!=typeof t.hidePopper||t.hidePopper,i=e.useRef(null),s=An(Yn({open:!a,whileElementsMounted:hn,placement:t.popperPlacement,middleware:Wn([(r={padding:15},{...gn(r),options:[r,o]}),kn(10),_n({element:i})],null!==(n=t.popperModifiers)&&void 0!==n?n:[],!0)},t.popperProps)),l=Yn(Yn({},t),{hidePopper:a,popperProps:Yn(Yn({},s),{arrowRef:i})});return f.createElement(yo,Yn({},l))}),bo="react-datepicker-ignore-onclickoutside";var Do="Date input not valid.",Co=function(t){function n(r){var o=t.call(this,r)||this;return o.calendar=null,o.input=null,o.getPreSelection=function(){return o.props.openToDate?o.props.openToDate:o.props.selectsEnd&&o.props.startDate?o.props.startDate:o.props.selectsStart&&o.props.endDate?o.props.endDate:Qn()},o.modifyHolidays=function(){var e;return null===(e=o.props.holidays)||void 0===e?void 0:e.reduce((function(e,t){var n=new Date(t.date);return qn(n)?Wn(Wn([],e,!0),[Yn(Yn({},t),{date:n})],!1):e}),[])},o.calcInitialState=function(){var e,t=o.getPreSelection(),n=jr(o.props),r=Ir(o.props),a=n&&J(t,Jn(n))?n:r&&ee(t,ar(r))?r:t;return{open:o.props.startOpen||!1,preventFocus:!1,inputValue:null,preSelection:null!==(e=o.props.selectsRange?o.props.startDate:o.props.selected)&&void 0!==e?e:a,highlightDates:Nr(o.props.highlightDates),focused:!1,shouldFocusDayInline:!1,isRenderAriaLiveMessage:!1,wasHidden:!1}},o.resetHiddenStatus=function(){o.setState(Yn(Yn({},o.state),{wasHidden:!1}))},o.setHiddenStatus=function(){o.setState(Yn(Yn({},o.state),{wasHidden:!0}))},o.setHiddenStateOnVisibilityHidden=function(){"hidden"===document.visibilityState&&o.setHiddenStatus()},o.clearPreventFocusTimeout=function(){o.preventFocusTimeout&&clearTimeout(o.preventFocusTimeout)},o.safeFocus=function(){setTimeout((function(){var e,t;null===(t=null===(e=o.input)||void 0===e?void 0:e.focus)||void 0===t||t.call(e,{preventScroll:!0})}),0)},o.safeBlur=function(){setTimeout((function(){var e,t;null===(t=null===(e=o.input)||void 0===e?void 0:e.blur)||void 0===t||t.call(e)}),0)},o.setFocus=function(){o.safeFocus()},o.setBlur=function(){o.safeBlur(),o.cancelFocusInput()},o.setOpen=function(e,t){void 0===t&&(t=!1),o.setState({open:e,preSelection:e&&o.state.open?o.state.preSelection:o.calcInitialState().preSelection,lastPreSelectChange:ko},(function(){e||o.setState((function(e){return{focused:!!t&&e.focused}}),(function(){!t&&o.setBlur(),o.setState({inputValue:null})}))}))},o.inputOk=function(){return te(o.state.preSelection)},o.isCalendarOpen=function(){return void 0===o.props.open?o.state.open&&!o.props.disabled&&!o.props.readOnly:o.props.open},o.handleFocus=function(e){var t,n,r=o.state.wasHidden,a=!r||o.state.open;r&&o.resetHiddenStatus(),!o.state.preventFocus&&a&&(null===(n=(t=o.props).onFocus)||void 0===n||n.call(t,e),o.props.preventOpenOnFocus||o.props.readOnly||o.setOpen(!0)),o.setState({focused:!0})},o.sendFocusBackToInput=function(){o.preventFocusTimeout&&o.clearPreventFocusTimeout(),o.setState({preventFocus:!0},(function(){o.preventFocusTimeout=setTimeout((function(){o.setFocus(),o.setState({preventFocus:!1})}))}))},o.cancelFocusInput=function(){clearTimeout(o.inputFocusTimeout),o.inputFocusTimeout=void 0},o.deferFocusInput=function(){o.cancelFocusInput(),o.inputFocusTimeout=setTimeout((function(){return o.setFocus()}),1)},o.handleDropdownFocus=function(){o.cancelFocusInput()},o.handleBlur=function(e){var t,n;(!o.state.open||o.props.withPortal||o.props.showTimeInput)&&(null===(n=(t=o.props).onBlur)||void 0===n||n.call(t,e)),o.setState({focused:!1})},o.handleCalendarClickOutside=function(e){var t,n;o.props.inline||o.setOpen(!1),null===(n=(t=o.props).onClickOutside)||void 0===n||n.call(t,e),o.props.withPortal&&e.preventDefault()},o.handleChange=function(){for(var e,t,r=[],a=0;a<arguments.length;a++)r[a]=arguments[a];var i=r[0];if(!o.props.onChangeRaw||(o.props.onChangeRaw.apply(o,r),i&&"function"==typeof i.isDefaultPrevented&&!i.isDefaultPrevented())){o.setState({inputValue:(null==i?void 0:i.target)instanceof HTMLInputElement?i.target.value:null,lastPreSelectChange:So});var s=o.props,l=s.dateFormat,c=void 0===l?n.defaultProps.dateFormat:l,d=s.strictParsing,p=void 0===d?n.defaultProps.strictParsing:d,u=s.selectsRange,f=s.startDate,h=s.endDate,m=(null==i?void 0:i.target)instanceof HTMLInputElement?i.target.value:"";if(u){var g=m.split("-",2).map((function(e){return e.trim()})),v=g[0],y=g[1],x=Gn(null!=v?v:"",c,o.props.locale,p),w=Gn(null!=y?y:"",c,o.props.locale,p),b=(null==f?void 0:f.getTime())!==(null==x?void 0:x.getTime()),D=(null==h?void 0:h.getTime())!==(null==w?void 0:w.getTime());if(!b&&!D)return;if(x&&gr(x,o.props))return;if(w&&gr(w,o.props))return;null===(t=(e=o.props).onChange)||void 0===t||t.call(e,[x,w],i)}else{var C=Gn(m,c,o.props.locale,p,o.props.minDate);o.props.showTimeSelectOnly&&o.props.selected&&C&&!cr(C,o.props.selected)&&(C=ne(o.props.selected,{hours:ae(C),minutes:oe(C),seconds:re(C)})),!C&&m||o.setSelected(C,i,!0)}}},o.handleSelect=function(e,t,n){if(o.props.shouldCloseOnSelect&&!o.props.showTimeSelect&&o.sendFocusBackToInput(),o.props.onChangeRaw&&o.props.onChangeRaw(t),o.setSelected(e,t,!1,n),o.props.showDateSelect&&o.setState({isRenderAriaLiveMessage:!0}),!o.props.shouldCloseOnSelect||o.props.showTimeSelect)o.setPreSelection(e);else if(!o.props.inline){o.props.selectsRange||o.setOpen(!1);var r=o.props,a=r.startDate,i=r.endDate;!a||i||!o.props.swapRange&&Br(e,a)||o.setOpen(!1)}},o.setSelected=function(e,t,n,r){var a,i,s=e;if(o.props.showYearPicker){if(null!==s&&Cr(ie(s),o.props))return}else if(o.props.showMonthYearPicker){if(null!==s&&yr(s,o.props))return}else if(null!==s&&gr(s,o.props))return;var l=o.props,c=l.onChange,d=l.selectsRange,p=l.startDate,u=l.endDate,f=l.selectsMultiple,h=l.selectedDates,m=l.minTime,g=l.swapRange;if(!dr(o.props.selected,s)||o.props.allowSameDay||d||f)if(null!==s&&(!o.props.selected||n&&(o.props.showTimeSelect||o.props.showTimeSelectOnly||o.props.showTimeInput)||(s=Zn(s,{hour:ae(o.props.selected),minute:oe(o.props.selected),second:re(o.props.selected)})),n||!o.props.showTimeSelect&&!o.props.showTimeSelectOnly||m&&(s=Zn(s,{hour:m.getHours(),minute:m.getMinutes(),second:m.getSeconds()})),o.props.inline||o.setState({preSelection:s}),o.props.focusSelectedMonth||o.setState({monthSelectedIn:r})),d){var v=p&&!u,y=p&&u;!p&&!u?null==c||c([s,null],t):v&&(null===s?null==c||c([null,null],t):Br(s,p)?g?null==c||c([s,p],t):null==c||c([s,null],t):null==c||c([p,s],t)),y&&(null==c||c([s,null],t))}else if(f){if(null!==s)if(null==h?void 0:h.length)if(h.some((function(e){return cr(e,s)}))){var x=h.filter((function(e){return!cr(e,s)}));null==c||c(x,t)}else null==c||c(Wn(Wn([],h,!0),[s],!1),t);else null==c||c([s],t)}else null==c||c(s,t);n||(null===(i=(a=o.props).onSelect)||void 0===i||i.call(a,s,t),o.setState({inputValue:null}))},o.setPreSelection=function(e){var t=te(o.props.minDate),n=te(o.props.maxDate),r=!0;if(e){var a=Jn(e);if(t&&n)r=pr(e,o.props.minDate,o.props.maxDate);else if(t){var i=Jn(o.props.minDate);r=ee(e,i)||dr(a,i)}else if(n){var s=ar(o.props.maxDate);r=J(e,s)||dr(a,s)}}r&&o.setState({preSelection:e})},o.toggleCalendar=function(){o.setOpen(!o.state.open)},o.handleTimeChange=function(e){var t,n;if(!o.props.selectsRange&&!o.props.selectsMultiple){var r=o.props.selected?o.props.selected:o.getPreSelection(),a=o.props.selected?e:Zn(r,{hour:ae(e),minute:oe(e)});o.setState({preSelection:a}),null===(n=(t=o.props).onChange)||void 0===n||n.call(t,a),o.props.shouldCloseOnSelect&&!o.props.showTimeInput&&(o.sendFocusBackToInput(),o.setOpen(!1)),o.props.showTimeInput&&o.setOpen(!0),(o.props.showTimeSelectOnly||o.props.showTimeSelect)&&o.setState({isRenderAriaLiveMessage:!0}),o.setState({inputValue:null})}},o.onInputClick=function(){var e,t;o.props.disabled||o.props.readOnly||o.setOpen(!0),null===(t=(e=o.props).onInputClick)||void 0===t||t.call(e)},o.onInputKeyDown=function(e){var t,n,r,a,i,s;null===(n=(t=o.props).onKeyDown)||void 0===n||n.call(t,e);var l=e.key;if(o.state.open||o.props.inline||o.props.preventOpenOnFocus){if(o.state.open){if(l===Hn.ArrowDown||l===Hn.ArrowUp){e.preventDefault();var c=o.props.showTimeSelectOnly?".react-datepicker__time-list-item[tabindex='0']":o.props.showWeekPicker&&o.props.showWeekNumbers?'.react-datepicker__week-number[tabindex="0"]':o.props.showFullMonthYearPicker||o.props.showMonthYearPicker?'.react-datepicker__month-text[tabindex="0"]':'.react-datepicker__day[tabindex="0"]',d=(null===(a=o.calendar)||void 0===a?void 0:a.containerRef.current)instanceof Element&&o.calendar.containerRef.current.querySelector(c);return void(d instanceof HTMLElement&&d.focus({preventScroll:!0}))}var p=Qn(o.state.preSelection);l===Hn.Enter?(e.preventDefault(),e.target.blur(),o.inputOk()&&o.state.lastPreSelectChange===ko?(o.handleSelect(p,e),!o.props.shouldCloseOnSelect&&o.setPreSelection(p)):o.setOpen(!1)):l===Hn.Escape?(e.preventDefault(),e.target.blur(),o.sendFocusBackToInput(),o.setOpen(!1)):l===Hn.Tab&&o.setOpen(!1),o.inputOk()||null===(s=(i=o.props).onInputError)||void 0===s||s.call(i,{code:1,msg:Do})}}else l!==Hn.ArrowDown&&l!==Hn.ArrowUp&&l!==Hn.Enter||null===(r=o.onInputClick)||void 0===r||r.call(o)},o.onPortalKeyDown=function(e){e.key===Hn.Escape&&(e.preventDefault(),o.setState({preventFocus:!0},(function(){o.setOpen(!1),setTimeout((function(){o.setFocus(),o.setState({preventFocus:!1})}))})))},o.onDayKeyDown=function(e){var t,n,r,a,i,s,l=o.props,c=l.minDate,d=l.maxDate,p=l.disabledKeyboardNavigation,u=l.showWeekPicker,f=l.shouldCloseOnSelect,h=l.locale,m=l.calendarStartDay,g=l.adjustDateOnChange,v=l.inline;if(null===(n=(t=o.props).onKeyDown)||void 0===n||n.call(t,e),!p){var y=e.key,x=e.shiftKey,w=Qn(o.state.preSelection),b=function(e,t){var n=t;switch(e){case Hn.ArrowRight:n=u?Ye(t,1):Re(t,1);break;case Hn.ArrowLeft:n=u?We(t):He(t);break;case Hn.ArrowUp:n=We(t);break;case Hn.ArrowDown:n=Ye(t,1);break;case Hn.PageUp:n=x?Ie(t,1):Fe(t,1);break;case Hn.PageDown:n=x?Ne(t,1):Ee(t,1);break;case Hn.Home:n=er(t,h,m);break;case Hn.End:n=function(e){return Ze(e)}(t)}return n};if(y===Hn.Enter)return e.preventDefault(),o.handleSelect(w,e),void(!f&&o.setPreSelection(w));if(y===Hn.Escape)return e.preventDefault(),o.setOpen(!1),void(o.inputOk()||null===(a=(r=o.props).onInputError)||void 0===a||a.call(r,{code:1,msg:Do}));var D=null;switch(y){case Hn.ArrowLeft:case Hn.ArrowRight:case Hn.ArrowUp:case Hn.ArrowDown:case Hn.PageUp:case Hn.PageDown:case Hn.Home:case Hn.End:D=function(e,t){for(var n=e,r=!1,a=0,i=b(e,t);!r;){if(a>=40){i=t;break}c&&i<c&&(n=Hn.ArrowRight,i=gr(c,o.props)?b(n,i):c),d&&i>d&&(n=Hn.ArrowLeft,i=gr(d,o.props)?b(n,i):d),gr(i,o.props)?(n!==Hn.PageUp&&n!==Hn.Home||(n=Hn.ArrowRight),n!==Hn.PageDown&&n!==Hn.End||(n=Hn.ArrowLeft),i=b(n,i)):r=!0,a++}return i}(y,w)}if(D){if(e.preventDefault(),o.setState({lastPreSelectChange:ko}),g&&o.setSelected(D),o.setPreSelection(D),v){var C=se(w),S=se(D),k=ie(w),_=ie(D);C!==S||k!==_?o.setState({shouldFocusDayInline:!0}):o.setState({shouldFocusDayInline:!1})}}else null===(s=(i=o.props).onInputError)||void 0===s||s.call(i,{code:1,msg:Do})}},o.onPopperKeyDown=function(e){e.key===Hn.Escape&&(e.preventDefault(),o.sendFocusBackToInput())},o.onClearClick=function(e){e&&e.preventDefault&&e.preventDefault(),o.sendFocusBackToInput();var t=o.props,n=t.selectsRange,r=t.onChange;n?null==r||r([null,null],e):null==r||r(null,e),o.setState({inputValue:null})},o.clear=function(){o.onClearClick()},o.onScroll=function(e){"boolean"==typeof o.props.closeOnScroll&&o.props.closeOnScroll?e.target!==document&&e.target!==document.documentElement&&e.target!==document.body||o.setOpen(!1):"function"==typeof o.props.closeOnScroll&&o.props.closeOnScroll(e)&&o.setOpen(!1)},o.renderCalendar=function(){var e,t;return o.props.inline||o.isCalendarOpen()?f.createElement(fo,Yn({showMonthYearDropdown:void 0,ref:function(e){o.calendar=e}},o.props,o.state,{setOpen:o.setOpen,dateFormat:null!==(e=o.props.dateFormatCalendar)&&void 0!==e?e:n.defaultProps.dateFormatCalendar,onSelect:o.handleSelect,onClickOutside:o.handleCalendarClickOutside,holidays:Ar(o.modifyHolidays()),outsideClickIgnoreClass:bo,onDropdownFocus:o.handleDropdownFocus,onTimeChange:o.handleTimeChange,className:o.props.calendarClassName,container:o.props.calendarContainer,handleOnKeyDown:o.props.onKeyDown,handleOnDayKeyDown:o.onDayKeyDown,setPreSelection:o.setPreSelection,dropdownMode:null!==(t=o.props.dropdownMode)&&void 0!==t?t:n.defaultProps.dropdownMode}),o.props.children):null},o.renderAriaLiveRegion=function(){var e,t=o.props,r=t.dateFormat,a=void 0===r?n.defaultProps.dateFormat:r,i=t.locale,s=o.props.showTimeInput||o.props.showTimeSelect?"PPPPp":"PPPP";return e=o.props.selectsRange?"Selected start date: ".concat(Xn(o.props.startDate,{dateFormat:s,locale:i}),". ").concat(o.props.endDate?"End date: "+Xn(o.props.endDate,{dateFormat:s,locale:i}):""):o.props.showTimeSelectOnly?"Selected time: ".concat(Xn(o.props.selected,{dateFormat:a,locale:i})):o.props.showYearPicker?"Selected year: ".concat(Xn(o.props.selected,{dateFormat:"yyyy",locale:i})):o.props.showMonthYearPicker?"Selected month: ".concat(Xn(o.props.selected,{dateFormat:"MMMM yyyy",locale:i})):o.props.showQuarterYearPicker?"Selected quarter: ".concat(Xn(o.props.selected,{dateFormat:"yyyy, QQQ",locale:i})):"Selected date: ".concat(Xn(o.props.selected,{dateFormat:s,locale:i})),f.createElement("span",{role:"alert","aria-live":"polite",className:"react-datepicker__aria-live"},e)},o.renderDateInput=function(){var t,r,a,i=g(o.props.className,((t={})[bo]=o.state.open,t)),s=o.props.customInput||f.createElement("input",{type:"text"}),l=o.props.customInputRef||"ref",c=o.props,d=c.dateFormat,p=void 0===d?n.defaultProps.dateFormat:d,u=c.locale,h="string"==typeof o.props.value?o.props.value:"string"==typeof o.state.inputValue?o.state.inputValue:o.props.selectsRange?function(e,t,n){if(!e)return"";var r=Xn(e,n),o=t?Xn(t,n):"";return"".concat(r," - ").concat(o)}(o.props.startDate,o.props.endDate,{dateFormat:p,locale:u}):o.props.selectsMultiple?function(e,t){if(!(null==e?void 0:e.length))return"";var n=e[0]?Xn(e[0],t):"";if(1===e.length)return n;if(2===e.length&&e[1]){var r=Xn(e[1],t);return"".concat(n,", ").concat(r)}var o=e.length-1;return"".concat(n," (+").concat(o,")")}(null!==(a=o.props.selectedDates)&&void 0!==a?a:[],{dateFormat:p,locale:u}):Xn(o.props.selected,{dateFormat:p,locale:u});return e.cloneElement(s,((r={})[l]=function(e){o.input=e},r.value=h,r.onBlur=o.handleBlur,r.onChange=o.handleChange,r.onClick=o.onInputClick,r.onFocus=o.handleFocus,r.onKeyDown=o.onInputKeyDown,r.id=o.props.id,r.name=o.props.name,r.form=o.props.form,r.autoFocus=o.props.autoFocus,r.placeholder=o.props.placeholderText,r.disabled=o.props.disabled,r.autoComplete=o.props.autoComplete,r.className=g(s.props.className,i),r.title=o.props.title,r.readOnly=o.props.readOnly,r.required=o.props.required,r.tabIndex=o.props.tabIndex,r["aria-describedby"]=o.props.ariaDescribedBy,r["aria-invalid"]=o.props.ariaInvalid,r["aria-labelledby"]=o.props.ariaLabelledBy,r["aria-required"]=o.props.ariaRequired,r))},o.renderClearButton=function(){var e=o.props,t=e.isClearable,n=e.disabled,r=e.selected,a=e.startDate,i=e.endDate,s=e.clearButtonTitle,l=e.clearButtonClassName,c=void 0===l?"":l,d=e.ariaLabelClose,p=void 0===d?"Close":d,u=e.selectedDates;return t&&(null!=r||null!=a||null!=i||(null==u?void 0:u.length))?f.createElement("button",{type:"button",className:g("react-datepicker__close-icon",c,{"react-datepicker__close-icon--disabled":n}),disabled:n,"aria-label":p,onClick:o.onClearClick,title:s,tabIndex:-1}):null},o.state=o.calcInitialState(),o.preventFocusTimeout=void 0,o}return Ln(n,t),Object.defineProperty(n,"defaultProps",{get:function(){return{allowSameDay:!1,dateFormat:"MM/dd/yyyy",dateFormatCalendar:"LLLL yyyy",disabled:!1,disabledKeyboardNavigation:!1,dropdownMode:"scroll",preventOpenOnFocus:!1,monthsShown:1,readOnly:!1,withPortal:!1,selectsDisabledDaysInRange:!1,shouldCloseOnSelect:!0,showTimeSelect:!1,showTimeInput:!1,showPreviousMonths:!1,showMonthYearPicker:!1,showFullMonthYearPicker:!1,showTwoColumnMonthYearPicker:!1,showFourColumnMonthYearPicker:!1,showYearPicker:!1,showQuarterYearPicker:!1,showWeekPicker:!1,strictParsing:!1,swapRange:!1,timeIntervals:30,timeCaption:"Time",previousMonthAriaLabel:"Previous Month",previousMonthButtonLabel:"Previous Month",nextMonthAriaLabel:"Next Month",nextMonthButtonLabel:"Next Month",previousYearAriaLabel:"Previous Year",previousYearButtonLabel:"Previous Year",nextYearAriaLabel:"Next Year",nextYearButtonLabel:"Next Year",timeInputLabel:"Time",enableTabLoop:!0,yearItemNumber:12,focusSelectedMonth:!1,showPopperArrow:!0,excludeScrollbar:!0,customTimeInput:null,calendarStartDay:void 0,toggleCalendarOnIconClick:!1,usePointerEvent:!1}},enumerable:!1,configurable:!0}),n.prototype.componentDidMount=function(){window.addEventListener("scroll",this.onScroll,!0),document.addEventListener("visibilitychange",this.setHiddenStateOnVisibilityHidden)},n.prototype.componentDidUpdate=function(e,t){var n,r,o,a,i,s;e.inline&&(i=e.selected,s=this.props.selected,i&&s?se(i)!==se(s)||ie(i)!==ie(s):i!==s)&&this.setPreSelection(this.props.selected),void 0!==this.state.monthSelectedIn&&e.monthsShown!==this.props.monthsShown&&this.setState({monthSelectedIn:0}),e.highlightDates!==this.props.highlightDates&&this.setState({highlightDates:Nr(this.props.highlightDates)}),t.focused||dr(e.selected,this.props.selected)||this.setState({inputValue:null}),t.open!==this.state.open&&(!1===t.open&&!0===this.state.open&&(null===(r=(n=this.props).onCalendarOpen)||void 0===r||r.call(n)),!0===t.open&&!1===this.state.open&&(null===(a=(o=this.props).onCalendarClose)||void 0===a||a.call(o)))},n.prototype.componentWillUnmount=function(){this.clearPreventFocusTimeout(),window.removeEventListener("scroll",this.onScroll,!0),document.removeEventListener("visibilitychange",this.setHiddenStateOnVisibilityHidden)},n.prototype.renderInputContainer=function(){var e=this.props,t=e.showIcon,n=e.icon,r=e.calendarIconClassname,o=e.calendarIconClassName,a=e.toggleCalendarOnIconClick,i=this.state.open;return f.createElement("div",{className:"react-datepicker__input-container".concat(t?" react-datepicker__view-calendar-icon":"")},t&&f.createElement(ho,Yn({icon:n,className:g(o,!o&&r,i&&"react-datepicker-ignore-onclickoutside")},a?{onClick:this.toggleCalendar}:null)),this.state.isRenderAriaLiveMessage&&this.renderAriaLiveRegion(),this.renderDateInput(),this.renderClearButton())},n.prototype.render=function(){var e=this.renderCalendar();if(this.props.inline)return e;if(this.props.withPortal){var t=this.state.open?f.createElement(vo,{enableTabLoop:this.props.enableTabLoop},f.createElement("div",{className:"react-datepicker__portal",tabIndex:-1,onKeyDown:this.onPortalKeyDown},e)):null;return this.state.open&&this.props.portalId&&(t=f.createElement(mo,Yn({portalId:this.props.portalId},this.props),t)),f.createElement("div",null,this.renderInputContainer(),t)}return f.createElement(wo,Yn({},this.props,{className:this.props.popperClassName,hidePopper:!this.isCalendarOpen(),targetComponent:this.renderInputContainer(),popperComponent:e,popperOnKeyDown:this.onPopperKeyDown,showArrow:this.props.showPopperArrow}))},n}(e.Component),So="input",ko="navigate";const _o=[{name:"black",hex:"#000000"},{name:"white",hex:"#FFFFFF"},{name:"red",hex:"#FF0000"},{name:"green",hex:"#00FF00"},{name:"blue",hex:"#0000FF"},{name:"yellow",hex:"#FFFF00"},{name:"cyan",hex:"#00FFFF"},{name:"magenta",hex:"#FF00FF"},{name:"silver",hex:"#C0C0C0"},{name:"gray",hex:"#808080"},{name:"maroon",hex:"#800000"},{name:"olive",hex:"#808000"},{name:"purple",hex:"#800080"},{name:"teal",hex:"#008080"},{name:"navy",hex:"#000080"},{name:"orange",hex:"#FFA500"},{name:"brown",hex:"#A52A2A"},{name:"lime",hex:"#00FF00"},{name:"indigo",hex:"#4B0082"},{name:"violet",hex:"#EE82EE"},{name:"pink",hex:"#FFC0CB"},{name:"gold",hex:"#FFD700"},{name:"beige",hex:"#F5F5DC"},{name:"coral",hex:"#FF7F50"},{name:"crimson",hex:"#DC143C"},{name:"khaki",hex:"#F0E68C"},{name:"lavender",hex:"#E6E6FA"},{name:"salmon",hex:"#FA8072"},{name:"tan",hex:"#D2B48C"},{name:"turquoise",hex:"#40E0D0"},{name:"aquamarine",hex:"#7FFFD4"},{name:"azure",hex:"#F0FFFF"},{name:"chartreuse",hex:"#7FFF00"},{name:"chocolate",hex:"#D2691E"},{name:"plum",hex:"#DDA0DD"},{name:"orchid",hex:"#DA70D6"},{name:"rose",hex:"#FF007F"},{name:"mint",hex:"#98FF98"},{name:"peach",hex:"#FFDAB9"},{name:"apricot",hex:"#FBCEB1"},{name:"amber",hex:"#FFBF00"},{name:"mustard",hex:"#FFDB58"},{name:"sky",hex:"#87CEEB"},{name:"sea",hex:"#2E8B57"},{name:"grass",hex:"#7CFC00"},{name:"sand",hex:"#C2B280"},{name:"stone",hex:"#888888"},{name:"smoke",hex:"#737373"},{name:"ice",hex:"#AFEEEE"}];const Mo=({showFilterModal:o,setShowFilterModal:a,events:i,setFilteredEvents:l,filterItems:c,setFilters:d,vessels:u,filters:h,selectedTime:m,setSelectedTime:g,selectedType:v,setSelectedType:S,selectedArea:k,setSelectedArea:_,selectedVessel:M,setSelectedVessel:E,selectedCategory:F,setSelectedCategory:T,timeStart:P,setTimeStart:R,timeEnd:j,setTimeEnd:I,selectedSize:A,setSelectedSize:O,selectedColor:L,setSelectedColor:H,selectedWeapon:V,setSelectedWeapon:z})=>{const{devMode:K}=N();e.useEffect((()=>{o&&(g(h?.start_time||h?.end_time?"custom":"all"),S(h?.type||"both"),_(h?.country_flags||[]),E(h?.vessel_ids?u.filter((e=>h.vessel_ids.includes(e.vessel_id))):[]),T(h?.categories||[]),O(h?.sizes||[]),H(h?.colors||[]),z(h?.weapons||[]),R(h?.start_time?new Date(h.start_time):new Date),I(h?.end_time?new Date(h.end_time):new Date))}),[o,h,u]);const[U,Q]=e.useState({changedTime:!1,oldTime:"",changedType:!1,oldType:"",changedArea:!1,oldArea:[],changedVessel:!1,oldVessel:[],changedCategory:!1,oldCategory:[]}),G=(e,t)=>{"backdropClick"!==t&&(a(!1),U.changedTime&&g(U.oldTime),U.changedType&&S(U.oldType),U.changedArea&&_(U.oldArea),U.changedVessel&&E(U.oldVessel),U.changedCategory&&T(U.oldCategory),Q({changedTime:!1,oldTime:"",changedType:!1,oldType:"",changedArea:!1,oldArea:[],changedVessel:!1,oldVessel:[],changedCategory:!1,oldCategory:[]}))},q=(e,n,o,a,i=!0,l)=>t.jsx(x,{sx:{width:"100%",maxHeight:"150px",overflowY:"auto"},size:"small",children:t.jsx(w,{multiple:i,value:n,onChange:(e,t)=>((e,t,n)=>{e(t),Q((e=>{const t={...e};return"Time"===n&&(t.changedTime=!0,t.oldTime=m),"Type"===n&&(t.changedType=!0,t.oldType=v),"Category"===n&&(t.changedCategory=!0,t.oldCategory=F),"Vessel"===n&&(t.changedVessel=!0,t.oldVessel=M),"Flag State"===n&&(t.changedArea=!0,t.oldArea=k),t}))})(o,t,a),options:e,disableCloseOnSelect:!0,getOptionLabel:e=>l?l(e):e.replace(/_/g," ").replace(/\b\w/g,(e=>e.toUpperCase())),renderInput:e=>t.jsx(C,{...e,label:"Select "+a,variant:"outlined"}),sx:{"& .MuiFormLabel-root":{color:p("#FFFFFF",.6),fontWeight:400,maxHeight:"250px",overflowY:"auto"}},renderOption:(e,n,{selected:o})=>{const{key:a,...i}=e;return t.jsxs(f.Fragment,{children:[t.jsxs("li",{...i,children:[t.jsx(b,{checked:o}),t.jsx(D,{primary:l?l(n):n.replace(/_/g," ").replace(/\b\w/g,(e=>e.toUpperCase()))})]}),"custom"===n&&o&&t.jsxs(r,{container:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-around"},children:[t.jsxs(r,{container:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-around"},size:{xs:12,md:5},children:[t.jsx(r,{size:{xs:12,md:12},children:t.jsx(s,{children:"From"})}),t.jsx(r,{size:{xs:12,md:12},children:t.jsx(Co,{showIcon:!0,icon:t.jsx("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M11.25 1.12236L8.24738 1.12237V0.375C8.24738 0.167812 8.07956 0 7.87238 0C7.66519 0 7.49738 0.167812 7.49738 0.375V1.12219H4.49738V0.375C4.49738 0.167812 4.32956 0 4.12238 0C3.91519 0 3.74738 0.167812 3.74738 0.375V1.12219H0.75C0.335812 1.12219 0 1.458 0 1.87219V11.2472C0 11.6614 0.335812 11.9972 0.75 11.9972H11.25C11.6642 11.9972 12 11.6614 12 11.2472V1.87219C12 1.45818 11.6642 1.12236 11.25 1.12236ZM11.25 11.2472H0.75V1.87219H3.74738V2.25C3.74738 2.45718 3.91519 2.625 4.12238 2.625C4.32956 2.625 4.49738 2.45718 4.49738 2.25V1.87237H7.49738V2.25019C7.49738 2.45737 7.66519 2.62519 7.87238 2.62519C8.07956 2.62519 8.24738 2.45737 8.24738 2.25019V1.87237H11.25V11.2472ZM8.625 5.99736H9.375C9.582 5.99736 9.75 5.82936 9.75 5.62236V4.87236C9.75 4.66536 9.582 4.49736 9.375 4.49736H8.625C8.418 4.49736 8.25 4.66536 8.25 4.87236V5.62236C8.25 5.82936 8.418 5.99736 8.625 5.99736ZM8.625 8.99718H9.375C9.582 8.99718 9.75 8.82936 9.75 8.62218V7.87218C9.75 7.66518 9.582 7.49718 9.375 7.49718H8.625C8.418 7.49718 8.25 7.66518 8.25 7.87218V8.62218C8.25 8.82955 8.418 8.99718 8.625 8.99718ZM6.375 7.49718H5.625C5.418 7.49718 5.25 7.66518 5.25 7.87218V8.62218C5.25 8.82936 5.418 8.99718 5.625 8.99718H6.375C6.582 8.99718 6.75 8.82936 6.75 8.62218V7.87218C6.75 7.66536 6.582 7.49718 6.375 7.49718ZM6.375 4.49736H5.625C5.418 4.49736 5.25 4.66536 5.25 4.87236V5.62236C5.25 5.82936 5.418 5.99736 5.625 5.99736H6.375C6.582 5.99736 6.75 5.82936 6.75 5.62236V4.87236C6.75 4.66518 6.582 4.49736 6.375 4.49736ZM3.375 4.49736H2.625C2.418 4.49736 2.25 4.66536 2.25 4.87236V5.62236C2.25 5.82936 2.418 5.99736 2.625 5.99736H3.375C3.582 5.99736 3.75 5.82936 3.75 5.62236V4.87236C3.75 4.66518 3.582 4.49736 3.375 4.49736ZM3.375 7.49718H2.625C2.418 7.49718 2.25 7.66518 2.25 7.87218V8.62218C2.25 8.82936 2.418 8.99718 2.625 8.99718H3.375C3.582 8.99718 3.75 8.82936 3.75 8.62218V7.87218C3.75 7.66536 3.582 7.49718 3.375 7.49718Z",fill:"white"})}),maxDate:W().valueOf(),selected:P,onChange:e=>{R(e)}})})]}),t.jsxs(r,{container:!0,sx:{display:"flex",alignItems:"center",justifyContent:"space-around"},size:{xs:12,md:5},children:[t.jsx(r,{size:{xs:12,md:12},children:t.jsx(s,{children:"To"})}),t.jsx(r,{size:{xs:12,md:12},children:t.jsx(Co,{showIcon:!0,icon:t.jsx("svg",{width:"12",height:"12",viewBox:"0 0 12 12",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:t.jsx("path",{d:"M11.25 1.12236L8.24738 1.12237V0.375C8.24738 0.167812 8.07956 0 7.87238 0C7.66519 0 7.49738 0.167812 7.49738 0.375V1.12219H4.49738V0.375C4.49738 0.167812 4.32956 0 4.12238 0C3.91519 0 3.74738 0.167812 3.74738 0.375V1.12219H0.75C0.335812 1.12219 0 1.458 0 1.87219V11.2472C0 11.6614 0.335812 11.9972 0.75 11.9972H11.25C11.6642 11.9972 12 11.6614 12 11.2472V1.87219C12 1.45818 11.6642 1.12236 11.25 1.12236ZM11.25 11.2472H0.75V1.87219H3.74738V2.25C3.74738 2.45718 3.91519 2.625 4.12238 2.625C4.32956 2.625 4.49738 2.45718 4.49738 2.25V1.87237H7.49738V2.25019C7.49738 2.45737 7.66519 2.62519 7.87238 2.62519C8.07956 2.62519 8.24738 2.45737 8.24738 2.25019V1.87237H11.25V11.2472ZM8.625 5.99736H9.375C9.582 5.99736 9.75 5.82936 9.75 5.62236V4.87236C9.75 4.66536 9.582 4.49736 9.375 4.49736H8.625C8.418 4.49736 8.25 4.66536 8.25 4.87236V5.62236C8.25 5.82936 8.418 5.99736 8.625 5.99736ZM8.625 8.99718H9.375C9.582 8.99718 9.75 8.82936 9.75 8.62218V7.87218C9.75 7.66518 9.582 7.49718 9.375 7.49718H8.625C8.418 7.49718 8.25 7.66518 8.25 7.87218V8.62218C8.25 8.82955 8.418 8.99718 8.625 8.99718ZM6.375 7.49718H5.625C5.418 7.49718 5.25 7.66518 5.25 7.87218V8.62218C5.25 8.82936 5.418 8.99718 5.625 8.99718H6.375C6.582 8.99718 6.75 8.82936 6.75 8.62218V7.87218C6.75 7.66536 6.582 7.49718 6.375 7.49718ZM6.375 4.49736H5.625C5.418 4.49736 5.25 4.66536 5.25 4.87236V5.62236C5.25 5.82936 5.418 5.99736 5.625 5.99736H6.375C6.582 5.99736 6.75 5.82936 6.75 5.62236V4.87236C6.75 4.66518 6.582 4.49736 6.375 4.49736ZM3.375 4.49736H2.625C2.418 4.49736 2.25 4.66536 2.25 4.87236V5.62236C2.25 5.82936 2.418 5.99736 2.625 5.99736H3.375C3.582 5.99736 3.75 5.82936 3.75 5.62236V4.87236C3.75 4.66518 3.582 4.49736 3.375 4.49736ZM3.375 7.49718H2.625C2.418 7.49718 2.25 7.66518 2.25 7.87218V8.62218C2.25 8.82936 2.418 8.99718 2.625 8.99718H3.375C3.582 8.99718 3.75 8.82936 3.75 8.62218V7.87218C3.75 7.66536 3.582 7.49718 3.375 7.49718Z",fill:"white"})}),maxDate:W().valueOf(),selected:j,onChange:e=>{I(e)}})})]})]})]},a)},isOptionEqualToValue:(e,t)=>e===t})});if(!u||!c||0===Object.keys(c).length)return null;const X=c.countryFlags.filter((e=>e.name)).map((e=>e.name)),Z=u.filter((e=>e)).filter((e=>!!K||e.is_active)),J=(c.superCategories||[]).filter((e=>e)),ee=c.sizes||[],te=function(e="name"){return"hex"===e?_o.map((e=>e.hex)):_o.map((e=>e.name))}("name"),ne=c.weapons||[];return t.jsx(n,{open:Boolean(o),onClose:G,children:t.jsxs($,{title:"Filter",onClose:G,showDivider:!0,children:[t.jsxs(r,{container:!0,direction:"row",gap:2,width:{xs:300,sm:500},sx:{maxHeight:"70vh",overflowY:"auto"},children:[q(["last_1_hour","last_24_hours","last_7_days","last_1_month","all","custom"],m,g,"Time",!1),q(["image","video","both"],v,S,"Type",!1),q(X,k,_,"Flag State"),q(Z,M,E,"Vessel",!0,(e=>K?e.unit_id:e.name)),q(J,F,T,"Category"),q(ee,A,O,"Size"),q(te,L,H,"Color"),q(ne,V,z,"Weapon")]}),t.jsxs(r,{container:!0,gap:2,justifyContent:"space-between",mt:2,children:[t.jsx(r,{children:t.jsx(y,{sx:{color:"#FFFFFF",textTransform:"none"},onClick:()=>{g("all"),S("both"),_([]),E([]),T([]),O([]),H([]),z([]),l(i),Q({changedTime:!1,oldTime:"",changedArea:!1,oldArea:[],changedVessel:!1,oldVessel:[],changedCategory:!1,oldCategory:[]}),d({})},children:"Clear filters"})}),t.jsx(r,{children:t.jsx(y,{sx:{color:"#FFFFFF",backgroundColor:Y.palette.custom.mainBlue,"&:hover":{backgroundColor:Y.palette.custom.mainBlue}},variant:"contained",onClick:()=>{const e={},t=W().valueOf();let n;switch(m){case"last_1_hour":n=t-36e5;break;case"last_24_hours":n=t-864e5;break;case"last_7_days":n=t-6048e5;break;case"last_1_month":n=t-2592e6;break;case"all":n=null;break;case"custom":n=W(P).valueOf()}v&&(e.type=v),k&&k.length>0&&(e.country_flags=k),M&&M.length>0&&(e.vessel_ids=M.map((e=>e.vessel_id))),F&&F.length>0&&(e.categories=F),A&&A.length>0&&(e.sizes=A),L&&L.length>0&&(e.colors=L),V&&V.length>0&&(e.weapons=V),n&&(e.start_time=W(n).valueOf(),e.end_time="custom"===m?W(j).valueOf():W(t).valueOf()),Q({changedTime:!1,oldTime:"",changedType:!1,oldType:"",changedArea:!1,oldArea:[],changedVessel:!1,oldVessel:[],changedCategory:!1,oldCategory:[]}),d(e),a(!1),B("EventFilterApplied",{filters:e})},children:"Apply"})})]})]})})},Eo=e.memo((({listRef:e,containerHeight:n,getItemSize:r,handleScroll:o,Row:a,items:i,columnCount:s,rowData:l})=>t.jsx(it,{ref:e,height:n,width:"100%",itemCount:Math.ceil(i.length/s),itemSize:r,overscanCount:3,onScroll:o,itemData:{items:i,columnCount:s,...l||{}},children:a})),((e,t)=>e.items.length===t.items.length&&e.containerHeight===t.containerHeight&&e.columnCount===t.columnCount));Eo.displayName="VirtualizedList";const Fo=e.memo((({card:n,setShowDetailModal:o,setSelectedCard:a,buttonsToShow:i,signedUrls:l})=>{const[c,d]=e.useState(null),{user:p}=A(),[u,f]=e.useState(null),{vesselInfo:h}=Z(),m=p?.hasPermissions([O.manageArtifacts]),g=e.useMemo((()=>L(n.location?.coordinates,!!p?.use_MGRS)),[n.location?.coordinates,p?.use_MGRS]),v=h.find((e=>e.vessel_id===n.onboard_vessel_id)).name,y=Boolean(n.video_path),x=()=>{o(!0),a({...n,vesselName:v})};return e.useEffect((()=>{if(l){const e=l.get(`${n._id}:thumbnail`),t=l.get(`${n._id}:image`),r=l.get(`${n._id}:video`);y?(f(e||t||null),d(r||null)):(f(e||t||null),d(t||null))}}),[n,y,l]),h?t.jsx(r,{container:!0,paddingTop:"0 !important",height:"100%",maxHeight:"350px",className:"events-step-2",onClick:x,sx:{cursor:"pointer"},children:t.jsxs(r,{container:!0,backgroundColor:"primary.main",borderRadius:2,padding:1,gap:1,children:[t.jsx(r,{size:12,height:"200px",children:t.jsx(X,{thumbnailLink:u,originalLink:c,cardId:n._id,isImage:!y,style:{borderRadius:8},showVideoThumbnail:y,onThumbnailClick:x,showArchiveButton:m,isArchived:n?.is_archived||!1,vesselId:n?.onboard_vessel_id,buttonsToShow:i})}),t.jsxs(r,{container:!0,size:12,children:[t.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[t.jsx(S,{title:v.length>12?v:"",children:t.jsx(s,{fontSize:"14px",fontWeight:500,children:v&&(v.length>12?v.slice(0,12)+"...":v)})}),t.jsx(s,{fontSize:"14px",fontWeight:500,children:W(n.timestamp).format(H.dateTimeFormat(p,{exclude_seconds:!0}))})]}),t.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[t.jsx(s,{fontSize:"14px",fontWeight:500,color:Y.palette.custom.mainBlue,children:"Location"}),t.jsx(s,{fontSize:"14px",fontWeight:500,color:Y.palette.custom.mainBlue,children:"Category"})]}),t.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[t.jsx(s,{fontSize:"14px",fontWeight:500,maxWidth:"50%",children:g}),t.jsx(s,{fontSize:"14px",fontWeight:500,maxWidth:"50%",textAlign:"right",children:n.super_category?n.super_category.length>12?n.super_category.slice(0,12)+"...":n.super_category:"Unspecified category"})]})]})]})}):t.jsx(s,{children:"No vessel info"})})),To=e.memo((({card:n,setShowDetailModal:f,setSelectedCard:h,buttonsToShow:m,signedUrls:g})=>{const{user:v}=A(),{vesselInfo:y}=Z(),[x,w]=e.useState(0),[b,D]=e.useState(!1),[C,k]=e.useState(!0),[_,M]=e.useState(!1),[E,F]=e.useState(null),[T,P]=e.useState(null),R=e.useMemo((()=>n.isGroup?n.groupArtifacts[x]:n),[n,x]),j=n.isGroup?n.groupArtifacts.length:1,I=Boolean(R.video_path),N=v?.hasPermissions([O.manageArtifacts]),B=e.useMemo((()=>y.find((e=>e.vessel_id===R.onboard_vessel_id))),[y,R.onboard_vessel_id]),V=B?.name,z=e.useMemo((()=>L(R.location?.coordinates,!!v?.use_MGRS)),[R.location?.coordinates,v?.use_MGRS]),K=e.useCallback((()=>{f(!0),h({...R,vesselName:V,isGroup:n.isGroup,groupArtifacts:n.groupArtifacts,currentGroupIndex:x})}),[f,h,R,V,n.isGroup,n.groupArtifacts,x]),U=e.useCallback((e=>{const t=x+e;t<0||t>=j||(D(!0),M(!1),w(t),setTimeout((()=>{D(!1)}),100))}),[x,j,b,C]),Q=e.useCallback((e=>{e.stopPropagation(),U(-1)}),[U]),G=e.useCallback((e=>{e.stopPropagation(),U(1)}),[U]);if(e.useEffect((()=>{const e=g.get(`${R._id}:thumbnail`);if(!e)return k(!1),void M(!1);k(!0),M(!1),F(null),P(null);const t=new Image;return t.onload=()=>{g.get(`${R._id}:thumbnail`)===e&&(P(e),F(e),k(!1))},t.onerror=()=>{g.get(`${R._id}:thumbnail`)===e&&(k(!1),M(!0))},t.src=e,()=>{t.onload=null,t.onerror=null}}),[R?._id,g]),!y?.length)return t.jsx(s,{children:"No vessel info"});const q=!C&&!b&&!_,$=n.isGroup&&j>1;return t.jsx(r,{container:!0,paddingTop:"0 !important",height:"100%",maxHeight:"350px",className:"events-step-2",onClick:K,sx:{cursor:"pointer"},children:t.jsxs(r,{container:!0,backgroundColor:"primary.main",borderRadius:2,padding:1,gap:1,children:[t.jsxs(r,{size:12,height:"200px",position:"relative",children:[(C||b)&&t.jsxs(t.Fragment,{children:[t.jsx(o,{sx:{display:"flex",justifyContent:"center",alignItems:"center",position:"absolute",top:0,left:0,width:"100%",height:"100%",zIndex:1},children:t.jsx(a,{})}),t.jsx(i,{variant:"rectangular",width:"100%",height:"100%",sx:{borderRadius:2,position:"absolute",top:0,left:0,right:0,bottom:0}})]}),_&&!C&&!b&&t.jsx(o,{position:"absolute",top:0,left:0,right:0,bottom:0,display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"rgba(0,0,0,0.2)",borderRadius:2,zIndex:2,children:t.jsx(s,{color:"text.secondary",variant:"body2",children:"Failed to load image"})}),q&&t.jsx(l,{in:!0,timeout:300,unmountOnExit:!0,children:t.jsx(o,{height:"100%",width:"100%",children:t.jsx(X,{thumbnailLink:T,originalLink:E,cardId:R._id,isImage:!I,style:{borderRadius:8},showVideoThumbnail:I,onThumbnailClick:K,showArchiveButton:N,isArchived:R?.is_archived||!1,vesselId:R?.onboard_vessel_id,buttonsToShow:m,isGrouped:n.isGroup,groupArtifacts:n.groupArtifacts})})}),$&&t.jsxs(o,{position:"absolute",bottom:8,left:"50%",sx:{transform:"translateX(-50%)",display:"flex",alignItems:"center",padding:"4px 8px",gap:1,zIndex:2},onClick:e=>e.stopPropagation(),children:[t.jsx(c,{size:"small",onClick:Q,disabled:0===x,sx:{color:"white",padding:"4px",background:p(Y.palette.custom.borderColor,.8)+" !important","&:disabled":{color:"rgba(255,255,255,0.3)"}},children:t.jsx(d,{fontSize:"small"})}),t.jsxs(s,{variant:"caption",sx:{color:"white",fontWeight:500,minWidth:"40px",textAlign:"center",padding:"5px 17px",borderRadius:"100px",background:p(Y.palette.primary.main,.8)},children:[String(x+1).padStart(2,"0"),"/",String(j).padStart(2,"0")]}),t.jsx(c,{size:"small",onClick:G,disabled:x===j-1,sx:{color:"white",padding:"4px",background:p(Y.palette.custom.borderColor,.8)+" !important","&:disabled":{color:"rgba(255,255,255,0.3)"}},children:t.jsx(u,{fontSize:"small"})})]})]}),t.jsxs(r,{container:!0,size:12,children:[t.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[t.jsx(S,{title:V?.length>12?V:"",children:t.jsx(s,{fontSize:"14px",fontWeight:500,children:V?.length>12?V.slice(0,12)+"...":V||"Unknown"})}),t.jsx(s,{fontSize:"14px",fontWeight:500,children:W(R.timestamp).format(H.dateTimeFormat(v,{exclude_seconds:!0}))})]}),t.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[t.jsx(s,{fontSize:"14px",fontWeight:500,color:Y.palette.custom.mainBlue,children:"Location"}),t.jsx(s,{fontSize:"14px",fontWeight:500,color:Y.palette.custom.mainBlue,children:"Category"})]}),t.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[t.jsx(s,{fontSize:"14px",fontWeight:500,maxWidth:"50%",children:z}),t.jsx(s,{fontSize:"14px",fontWeight:500,maxWidth:"50%",textAlign:"right",children:R.super_category?.length>12?R.super_category.slice(0,12)+"...":R.super_category||"Unspecified category"})]})]})]})})})),Po=({index:e,style:n,data:o})=>{const{items:a,columnCount:i,CustomCard:s,setShowDetailModal:l,setSelectedCard:c,onFlaggedByClick:d,buttonsToShow:p,signedUrls:u}=o,f=e*i;return t.jsx(r,{container:!0,style:n,spacing:2,paddingBottom:2,children:Array.from({length:i}).map(((e,n)=>{const o=f+n;if(o>=a.length)return null;const h=a[o],m=s||(h.isGroup?To:Fo),g=12/i;return t.jsx(r,{size:{xs:12,sm:g,md:g,lg:g,xl:g},children:t.jsx(m,{card:h,setShowDetailModal:l,setSelectedCard:c,onFlaggedByClick:d,buttonsToShow:p,signedUrls:u})},h._id||h.artifact?._id)}))})};Po.displayName="VirtualizedCardListRow";const Ro=e.memo(Po),jo=e.forwardRef((({events:n,setShowDetailModal:i,setSelectedCard:l,isLoading:c,onLoadMore:d,hasMore:p,containerRef:u,CustomCard:f,onFlaggedByClick:h,buttonsToShow:m,signedUrls:g},v)=>{const{screenSize:y}=N(),x=e.useRef(),[w,b]=e.useState(!1),[D,C]=e.useState(0),[S,_]=e.useState(0);e.useEffect((()=>{v&&(v.current={scrollToTop:()=>{x.current&&x.current.scrollTo(0)}})}),[v]),e.useEffect((()=>{const e=u?.current;if(!e)return;const t=()=>{const t=e.clientHeight,n=e.clientWidth;C(c?t-70:t),_(n)};t();const n=new ResizeObserver(t);return n.observe(e),()=>{n.unobserve(e)}}),[u,c]),e.useEffect((()=>{x.current&&x.current.resetAfterIndex(0)}),[n,y,S]),e.useEffect((()=>{if(!c&&d&&p&&x.current){const e=u?.current;if(e){if(0===e.getClientRects().length)return;const t=window.getComputedStyle(e);if(!("none"!==t.display&&"hidden"!==t.visibility))return}const{scrollHeight:t,clientHeight:n}=x.current._outerRef;t<=n&&p&&d()}}),[n,c,d,p,u]);const M=e.useCallback((()=>{const e=S||0;if(e<=0)return y.xs?1:y.sm?2:y.md?3:y.lg?4:5;const t=Math.max(1,Math.floor((e+16)/316));return Math.min(t,12)}),[S,y]),E=e.useCallback(((e,t,n)=>e+t>=.8*n),[]),F=e.useCallback((({scrollOffset:e,scrollUpdateWasRequested:t})=>{if(!c&&d&&!t&&x.current){const{scrollHeight:t,clientHeight:n}=x.current._outerRef;E(e,n,t)&&p&&!c&&!w&&(b(!0),d(),setTimeout((()=>b(!1)),1e3))}}),[p,c,d,w,E]);if(c&&0===n.length)return t.jsx(r,{display:"flex",justifyContent:"center",alignItems:"center",size:12,children:t.jsx(a,{})});if(0===n.length)return t.jsx(r,{display:"flex",justifyContent:"center",alignItems:"center",size:12,children:t.jsxs(r,{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",children:[t.jsx(k,{sx:{fontSize:"100px",color:Y.palette.custom.borderColor}}),t.jsx(s,{variant:"h6",component:"div",gutterBottom:!0,color:Y.palette.custom.borderColor,children:"No data available"})]})});const T=M();return t.jsxs(t.Fragment,{children:[t.jsx(Eo,{listRef:x,containerHeight:D,getItemSize:e=>{const t=M();Math.floor(e/t);return 350},handleScroll:F,Row:Ro,items:n,columnCount:T,rowData:{CustomCard:f,setShowDetailModal:i,setSelectedCard:l,onFlaggedByClick:h,buttonsToShow:m,signedUrls:g}}),c&&n.length>0&&t.jsx(o,{display:"flex",justifyContent:"center",width:"100%",padding:2,children:t.jsx(a,{})})]})}));jo.displayName="VirtualizedCardList";const Io=e.memo(jo),No=e.memo((({showFilterModal:n,setShowFilterModal:o,filters:i,setFilters:s,vessels:l,tab:c,onCompletion:d,onLoadingChange:p})=>{const{id:u}=_(),[f,h]=e.useState([]),[m,g]=e.useState([]),[v,y]=e.useState(!0),x=e.useRef(!0),[w,b]=e.useState(1),[D,C]=e.useState({}),[S,k]=e.useState(!1),[M,E]=e.useState(null),[F,T]=e.useState(new Map),P=e.useRef(),R=e.useRef(),j=e.useRef(),I=e.useRef([]),[N,A]=e.useState("all"),[O,L]=e.useState("both"),[W,H]=e.useState([]),[U,Q]=e.useState([]),[G,q]=e.useState([]),[$,X]=e.useState(new Date),[Z,J]=e.useState(new Date),[ee,te]=e.useState([]),[ne,re]=e.useState([]),[oe,ae]=e.useState([]),ie=async(e=!1,t=!1)=>{e||y(!0);try{const e=t?w+1:1,n={page:e,pageSize:100,filters:i,group:1};u&&(n.filters={...n.filters,id:u},n.pageSize=1);const r=await z.post("/artifacts",n),{artifacts:o,totalCount:a,groupedArtifacts:s}=r.data;I.current=[...I.current,...s];const c=K(o,s).sort(((e,t)=>new Date(t.timestamp)-new Date(e.timestamp)));if(t?(h((e=>[...e,...c])),b(e)):(h(c),b(1)),x.current=o.length<(u?1:a),await(async(e,t=!1)=>{try{const n=await st.fetchSignedUrlsBatch(e);T((e=>{if(t){const t=new Map(e);return n.forEach(((e,n)=>{t.set(n,e)})),t}return n}))}catch(n){}})(o,t),y(!1),u&&o.length>0){const e=o[0],t=l.find((t=>t.unit_id===e.unit_id));E({...e,vesselName:t?.name||t?.unit_id}),k(!0)}}catch(n){y(!1)}},se=e.useCallback((()=>{v||!x.current||u||(R.current&&clearTimeout(R.current),R.current=setTimeout((()=>{ie(!1,!0)}),500))}),[v,x.current,u,ie]);e.useEffect((()=>{(async()=>{try{const e=await z.get("/artifacts/filters").then((e=>e.data)).catch((e=>{}));0!==Object.keys(e).length&&C(e)}catch(e){}})(),sessionStorage.getItem("eventPath")&&sessionStorage.removeItem("eventPath")}),[]),e.useEffect((()=>{g(f)}),[f]),e.useEffect((()=>{ie()}),[i,u]),e.useEffect((()=>{"events"!==c&&(h((e=>e.slice(0,100))),b(1),y(!1),j.current&&j.current.scrollToTop())}),[c]),e.useEffect((()=>{const e=V(),t=e=>{const t=e?.artifact;t&&h((e=>{if(t.is_archived)return e.filter((e=>e._id!==t._id));{const n=I.current.find((e=>e.includes(t._id))),r=e=>e.sort(((e,t)=>new Date(t.timestamp)-new Date(e.timestamp)));if(!n)return r([...e,{...t,isGroup:!1}]);const o=e.findIndex((e=>e.isGroup&&e.groupArtifacts?.some((e=>n.includes(e._id)))));if(-1!==o){const n=[...e];return n[o]={...n[o],groupArtifacts:[...n[o].groupArtifacts,t]},r(n)}const a=e.filter((e=>!e.isGroup&&n.includes(e._id)));if(a.length>0){const o=[...a,t],i=e.filter((e=>e.isGroup||!n.includes(e._id)));return i.push({...o[0],isGroup:!0,groupArtifacts:o}),r(i)}return r([...e,{...t,isGroup:!1}])}}))};return e.on("artifact/changed",t),()=>{e.off("artifact/changed",t),R.current&&clearTimeout(R.current)}}),[]);const le=e.useRef();return e.useEffect((()=>{if(!u)return le.current&&clearInterval(le.current),le.current=setInterval((()=>{ie(!0)}),3e5),()=>{le.current&&clearInterval(le.current)}}),[i,ie,u]),e.useEffect((()=>{"function"!=typeof d.current&&(d.current=e=>{y(e),h([])})}),[d]),e.useEffect((()=>{p(v)}),[v,p]),e.useEffect((()=>{S&&M&&M.artifact_id&&B("EventViewed",{artifactId:M.artifact_id})}),[S,M]),e.useEffect((()=>{S&&M&&M.artifact_id&&B("EventViewed",{artifactId:M.artifact_id})}),[S,M]),t.jsx(t.Fragment,{children:v&&0===f.length?t.jsx(r,{container:!0,display:"flex",justifyContent:"center",alignItems:"center",height:{xs:"90%",sm:"90%"},overflow:"auto",marginBottom:2,size:"grow",children:t.jsx(a,{sx:{color:Y.palette.custom.mainBlue},size:60})}):t.jsxs(r,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",children:[t.jsx(r,{container:!0,overflow:"auto",display:"block",border:`1px solid ${Y.palette.custom.borderColor}`,borderRadius:"10px",padding:"10px 24px",size:"grow",children:t.jsx(r,{container:!0,height:"100%",overflow:"hidden",ref:P,children:t.jsx(Io,{ref:j,events:m,setShowDetailModal:k,setSelectedCard:E,isLoading:v,onLoadMore:se,hasMore:x.current&&!u,containerRef:P,signedUrls:F})})}),t.jsx(ct,{showDetailModal:S,setShowDetailModal:k,selectedCard:M,setSelectedCard:E,id:u,signedUrls:F}),t.jsx(Mo,{showFilterModal:n,vessels:l,setFilters:s,setShowFilterModal:o,events:f,setFilteredEvents:g,filterItems:D,filters:i,selectedTime:N,setSelectedTime:A,selectedType:O,setSelectedType:L,selectedArea:W,setSelectedArea:H,selectedVessel:U,setSelectedVessel:Q,selectedCategory:G,setSelectedCategory:q,timeStart:$,setTimeStart:X,timeEnd:Z,setTimeEnd:J,selectedSize:ee,setSelectedSize:te,selectedColor:ne,setSelectedColor:re,selectedWeapon:oe,setSelectedWeapon:ae})]})})}));function Ao({showDetailModal:o,setShowDetailModal:a,selectedCard:i,setSelectedCard:l,id:c,signedUrls:d}){const{screenSize:p}=N(),[u,h]=e.useState(null),{user:m}=A(),g=m?.hasPermissions([O.manageArtifacts]),v=()=>{l(null),a(!1)},y=[{label:"Location",value:i?.location?.coordinates&&L(i.location.coordinates,!!m?.use_MGRS)},{label:"Category",value:i?.super_category||"Unspecified category"},{label:"Sub Category",value:i?.category},{label:"Weapons",value:i?.weapons},{label:"Size",value:i?.size},{label:"Color",value:i?.color},{label:"Imo Number",value:i?.imo_number},{label:"Country Flag",value:i?.country_flag},{label:"Text Detected",value:Array.isArray(i?.text_extraction)&&i.text_extraction.length>0?i.text_extraction.map((e=>e.text)).slice(0,5).join(", "):null},{label:"Description",value:i?.others}],x=["Text Detected","Description"];return e.useEffect((()=>{if(i&&d){const e=d.get(`${i._id}:thumbnail`),t=d.get(`${i._id}:image`),n=d.get(`${i._id}:video`);i.video_path?h(n||t||null):h(t||e||null)}}),[i,d]),t.jsx(n,{open:Boolean(o),onClose:v,children:t.jsx($,{title:"Event Details",onClose:v,showDivider:!0,children:t.jsxs(r,{container:!0,gap:1,maxHeight:"70vh",overflow:"auto",minWidth:{xs:300,sm:500},maxWidth:800,children:[t.jsx(r,{size:12,children:i&&t.jsx(X,{thumbnailLink:u,originalLink:u,cardId:c||i._id,isImage:!i.video_path,style:{borderRadius:8,height:300,objectFit:"contain",backgroundColor:"#000"},skeletonStyle:{height:300,width:"100%"},showFullscreenIconForMap:!i.video_path,showArchiveButton:g})}),t.jsxs(r,{display:"flex",justifyContent:p.xs?"flex-start":"space-between",alignItems:p.xs?"flex-start":"center",paddingX:1,flexDirection:p.xs?"column":"row",size:12,children:[p.xs&&t.jsx(s,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,color:Y.palette.custom.mainBlue,children:"Name"}),t.jsx(s,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,children:i?.vesselName}),p.xs&&t.jsx(s,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,color:Y.palette.custom.mainBlue,children:"Timestamp"}),t.jsx(s,{fontSize:{xs:"16px",sm:"20px"},fontWeight:500,children:W(i?.timestamp).format(H.dateTimeFormat(m,{exclude_seconds:!0}))})]}),y.map((({label:e,value:n},o)=>t.jsx(f.Fragment,{children:t.jsxs(r,{display:"flex",alignItems:{xs:"flex-start",sm:o%2==0||x.includes(e)?"flex-start":"flex-end"},paddingX:1,flexDirection:"column",size:{xs:12,sm:x.includes(e)?12:5.9},children:[t.jsx(s,{fontSize:"16px",fontWeight:500,color:Y.palette.custom.mainBlue,children:e}),t.jsx(s,{fontSize:"16px",fontWeight:500,children:n??"--"})]})},o)))]})})})}const Oo=e.memo((({vessels:n})=>{const{id:o}=_(),[a,i]=e.useState([]),[s,l]=e.useState([]),[c,d]=e.useState(!0),[p,u]=e.useState(!1),[f,h]=e.useState(null),[m,g]=e.useState(new Map),v=e.useRef(),{artifactsFavourites:y}=(()=>{const t=e.useContext(U);if(void 0===t)throw new Error("StoreContext must be used within a StoreProvider");return t})();return e.useEffect((()=>{const e=n.map((e=>e.unit_id));l(a.filter((t=>e.includes(t.unit_id))))}),[a,n]),e.useEffect((()=>{(async(e=!1)=>{e||d(!0);try{i(y);const e=await st.fetchSignedUrlsBatch(y);g(e),d(!1)}catch(t){d(!1)}})()}),[o,y]),e.useEffect((()=>{const e=V(),t=e=>{const t=e?.artifact;t&&i((e=>{if(t.is_archived)return e.filter((e=>e._id!==t._id));return[...e,t].sort(((e,t)=>new Date(t.timestamp)-new Date(e.timestamp)))}))};return e.on("artifact/changed",t),()=>{e.off("artifact/changed",t)}}),[]),t.jsxs(r,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",children:[t.jsx(r,{container:!0,overflow:"auto",display:"block",border:`1px solid ${Y.palette.custom.borderColor}`,borderRadius:"10px",padding:"10px 24px",size:"grow",children:t.jsx(r,{container:!0,height:"100%",overflow:"auto",ref:v,children:t.jsx(Io,{events:s,setShowDetailModal:u,setSelectedCard:h,isLoading:c,containerRef:v,signedUrls:m})})}),t.jsx(Ao,{showDetailModal:p,setShowDetailModal:u,selectedCard:f,setSelectedCard:h,id:o,signedUrls:m})]})})),Lo=e.memo((({vessels:n})=>{const[o,a]=e.useState([]),[i,s]=e.useState([]),[l,c]=e.useState(!0),d=e.useRef(!0),[p,u]=e.useState(1),[f,h]=e.useState(!1),[m,g]=e.useState(null),[v,y]=e.useState(new Map),x=e.useRef(),w=e.useRef(),b=e.useRef(),D=async(e=!1,t=!1)=>{e||c(!0);try{const e=t?p+1:1,n={page:e,pageSize:100},r=await z.get("/artifacts/archived",{params:n}),{artifacts:o,totalCount:i}=r.data,s=await st.fetchSignedUrlsBatch(o);y((e=>new Map([...e,...s]))),t?(a((e=>[...e,...o])),u(e)):(a(o),u(1)),d.current=o.length<i,c(!1)}catch(n){c(!1)}},C=e.useCallback((()=>{!l&&d.current&&(w.current&&clearTimeout(w.current),w.current=setTimeout((()=>{D(!1,!0)}),500))}),[l,d.current,D]);return e.useEffect((()=>{const e=n.map((e=>e.unit_id));s(o.filter((t=>e.includes(t.unit_id))))}),[o,n]),e.useEffect((()=>{D()}),[]),e.useEffect((()=>{const e=V(),t=e=>{const t=e?.artifact;t&&a((e=>{if(!t.is_archived)return e.filter((e=>e._id!==t._id));if(!e.find((e=>e._id===t._id)))return[t,...e].sort(((e,t)=>new Date(t.timestamp)-new Date(e.timestamp)));return e}))};return e.on("artifact/changed",t),()=>{e.off("artifact/changed",t),w.current&&clearTimeout(w.current)}}),[]),t.jsxs(r,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",children:[t.jsx(r,{container:!0,overflow:"auto",display:"block",border:`1px solid ${Y.palette.custom.borderColor}`,borderRadius:"10px",padding:"10px 24px",size:"grow",children:t.jsx(r,{container:!0,height:"100%",overflow:"hidden",ref:x,children:t.jsx(Io,{ref:b,events:i,setShowDetailModal:h,setSelectedCard:g,isLoading:l,onLoadMore:C,hasMore:d.current,containerRef:x,buttonsToShow:[Q.ARCHIVE],signedUrls:v})})}),t.jsx(Ao,{showDetailModal:f,setShowDetailModal:h,selectedCard:m,setSelectedCard:g,signedUrls:v})]})})),Yo=({open:e,onClose:o,onConfirm:a})=>t.jsx(n,{open:e,onClose:o,children:t.jsx($,{title:"Remove from Flagged",onClose:o,headerPosition:"center",children:t.jsxs(r,{container:!0,flexDirection:"column",gap:2,width:{xs:"auto",sm:400},children:[t.jsx(r,{display:"flex",justifyContent:"center",children:t.jsx(s,{fontWeight:"100",textAlign:"center",children:"Do you really want to remove this artifact from flagged artifacts."})}),t.jsxs(r,{container:!0,gap:1,justifyContent:"center",children:[t.jsx(r,{justifyContent:"center",display:"flex",children:t.jsx(y,{variant:"contained",onClick:o,backgroundColor:"#FFFFFF",sx:{background:"#FFFFFF !important",color:Y.palette.primary.main},children:"Cancel"})}),t.jsx(r,{justifyContent:"center",display:"flex",children:t.jsx(y,{variant:"contained",onClick:a,children:"Confirm"})})]})]})})}),Wo=e.memo((({card:n,onFlaggedByClick:o,signedUrls:a})=>{const[i,l]=e.useState(null),{user:c}=A(),[d,p]=e.useState(null),{vesselInfo:u}=Z(),[f,h]=e.useState(!1),[m,g]=e.useState(!1),v=e.useMemo((()=>L(n.artifact?.location?.coordinates,!!c?.use_MGRS)),[n.artifact?.location?.coordinates,c?.use_MGRS]),y=u.find((e=>e.vessel_id===n.artifact?.onboard_vessel_id)),x=y?.name||"Unknown Vessel",w=Boolean(n.artifact?.video_path);e.useEffect((()=>{if(n.artifact&&a){const e=a.get(`${n.artifact._id}:thumbnail`),t=a.get(`${n.artifact._id}:image`),r=a.get(`${n.artifact._id}:video`);w?(p(e||t||null),l(r||null)):(p(e||t||null),l(t||null))}}),[n.artifact,w,a]);return n.artifact&&u?t.jsxs(r,{container:!0,paddingTop:"0 !important",height:"100%",maxHeight:"350px",sx:{cursor:"pointer"},onClick:()=>o(n.artifact),children:[t.jsxs(r,{container:!0,backgroundColor:"primary.main",borderRadius:2,padding:1,gap:1,children:[t.jsx(r,{size:12,maxHeight:"200px",children:t.jsx(X,{thumbnailLink:d,originalLink:i,cardId:n.artifact._id,isImage:!w,style:{borderRadius:8},showVideoThumbnail:w,showArchiveButton:!n.artifact.is_archived,isArchived:n.artifact.is_archived,vesselId:n.artifact.onboard_vessel_id,buttonsToShow:[Q.ARCHIVE],handleUnflagClick:e=>{e.stopPropagation(),h(!0)}})}),t.jsxs(r,{container:!0,size:12,children:[t.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[t.jsx(S,{title:x.length>12?x:"",children:t.jsx(s,{fontSize:"14px",fontWeight:500,children:x.length>12?x.slice(0,12)+"...":x})}),t.jsx(s,{fontSize:"14px",fontWeight:500,children:W(n.artifact.timestamp).format(H.dateTimeFormat(c,{exclude_seconds:!0}))})]}),t.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[t.jsx(s,{fontSize:"14px",fontWeight:500,color:Y.palette.custom.mainBlue,children:"Location"}),t.jsx(s,{fontSize:"14px",fontWeight:500,color:Y.palette.custom.mainBlue,children:"Category"})]}),t.jsxs(r,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:[t.jsx(s,{fontSize:"14px",fontWeight:500,maxWidth:"50%",children:v}),t.jsx(s,{fontSize:"14px",fontWeight:500,maxWidth:"50%",textAlign:"right",children:n.artifact.super_category?n.artifact.super_category.length>12?n.artifact.super_category.slice(0,12)+"...":n.artifact.super_category:"Unspecified category"})]}),t.jsx(r,{display:"flex",justifyContent:"space-between",alignItems:"center",paddingX:1,size:12,children:t.jsxs(s,{fontSize:"14px",fontWeight:500,maxWidth:"50%",color:"#FDBF2D",fontFamily:'"Nunito Sans", sans-serif',sx:{fontStyle:"italic",textDecoration:"underline"},children:["Flagged by ",n.flagCount||0," ",1===n.flagCount?"user":"users"]})})]})]}),t.jsx(Yo,{open:f,onClose:e=>{e.stopPropagation(),h(!1)},onConfirm:async e=>{e.stopPropagation(),g(!0);try{await lt.removeAllFlagsFromArtifact(n.artifact._id),h(!1)}catch(t){}finally{g(!1)}},isLoading:m})]}):null})),Ho=()=>{const[n,o]=e.useState([]),[a,i]=e.useState(!0),[s,l]=e.useState(null),[c,d]=e.useState(!1),[p,u]=e.useState(new Map),f=e.useRef(),h=e.useRef();e.useEffect((()=>{const e=async()=>{try{i(!0);const e=await lt.getFlaggedArtifacts();o(e);const t=e.map((e=>e.artifact)).filter(Boolean),n=await st.fetchSignedUrlsBatch(t);u(n)}catch(e){}finally{i(!1)}};e();const t=V();return t.on("artifacts_flagged/changed",e),t.on("artifact/changed",e),()=>{t.off("artifacts_flagged/changed",e),t.off("artifact/changed",e)}}),[]);const m=e.useCallback((e=>{l(e),d(!0)}));return t.jsxs(r,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",children:[t.jsx(r,{container:!0,overflow:"auto",display:"block",border:`1px solid ${Y.palette.custom.borderColor}`,borderRadius:"10px",padding:"10px 24px",size:"grow",children:t.jsx(r,{container:!0,height:"100%",overflow:"auto",ref:f,children:t.jsx(Io,{ref:h,events:n,isLoading:a,containerRef:f,setShowDetailModal:d,setSelectedCard:l,CustomCard:Wo,onFlaggedByClick:m,signedUrls:p})})}),t.jsx(Ao,{showDetailModal:c,setShowDetailModal:d,selectedCard:s,setSelectedCard:l,signedUrls:p})]})},Bo=({onSelect:n,isLoading:r})=>{const[o,i]=e.useState(""),[s,l]=e.useState([]),[c,d]=e.useState(!1),[p,u]=e.useState(!1),[f,h]=e.useState(-1),[m,g]=e.useState(!1),[v,y]=e.useState(""),x=e.useRef(),w=e.useRef(),b=e.useRef([]),D=e.useRef(),S=e.useRef(),k=e.useRef(!1);e.useEffect((()=>{if(y(o),d(!1),!k.current)return o?(d(!0),x.current&&clearTimeout(x.current),x.current=setTimeout((async()=>{try{S.current&&S.current.abort(),S.current=new AbortController;const e=await z.post("/suggestions",{query:o},{signal:S.current.signal,meta:{showSnackbar:!1}});S.current=null,l(e.data?.suggestions||[]),u(!0),h(-1)}catch(e){if("CanceledError"===e.name)return;l([]),u(!1),h(-1)}finally{d(!1)}}),600),()=>x.current&&clearTimeout(x.current)):(l([]),u(!1),void h(-1));k.current=!1}),[o]),e.useEffect((()=>{f>=0&&b.current[f]&&b.current[f].scrollIntoView({block:"nearest"})}),[f]);const _=(e,t=!1)=>{k.current=!0,i(e),y(e),u(!1),h(-1),n&&(t?n(e,v):n(e))};return t.jsx(M,{onClickAway:()=>u(!1),children:t.jsxs("div",{ref:D,style:{position:"relative",width:"100%"},children:[t.jsx(C,{fullWidth:!0,placeholder:"Search (Powered by AI)",value:o,onChange:e=>i(e.target.value),autoComplete:"off",inputRef:w,InputProps:{endAdornment:c?t.jsx(a,{size:20,sx:{color:"#fff"}}):null,startAdornment:t.jsx(E,{position:"start",children:t.jsx(F,{sx:{color:"#fff"}})})},sx:{borderRadius:2,input:{color:"#fff",textTransform:"none"},label:{color:"#878787"},"& input.Mui-disabled":{"-webkit-text-fill-color":"unset",color:"#878787"},"& .MuiOutlinedInput-root":{"& fieldset":{borderColor:"#23272F"},"&:hover fieldset":{borderColor:"#1976d2"},"&.Mui-focused fieldset":{borderColor:"#1976d2"}}},onFocus:()=>{s.length>0&&u(!0)},onKeyDown:e=>{if(!c&&!r)if("ArrowDown"===e.key)e.preventDefault(),g(!0),!p&&s.length>0&&u(!0),h((e=>Math.min(e+1,s.length-1)));else if("ArrowUp"===e.key)e.preventDefault(),g(!0),h((e=>Math.max(e-1,0)));else if("Enter"===e.key){if(e.preventDefault(),""===o.trim())return void(n&&n(""));f>=0&&s[f]?m?_(s[f]):n&&n(s[f],v):s.length>0&&o.trim().toLowerCase()!==s[0].trim().toLowerCase()?_(s[0],!0):_(v)}},disabled:r}),p&&!r&&s.length>0&&t.jsx(T,{elevation:4,style:{position:"absolute",width:"100%",zIndex:10,maxHeight:400,overflowY:"auto",background:Y.palette.primary.main,borderRadius:10,marginTop:4,padding:0},children:t.jsx("ul",{style:{listStyle:"none",margin:0,padding:0,paddingTop:6},children:s.map(((e,n)=>t.jsxs("li",{ref:e=>b.current[n]=e,tabIndex:0,onClick:()=>_(e),onMouseDown:e=>e.preventDefault(),onMouseEnter:()=>{g(!0),h(n)},onMouseLeave:()=>{g(!0),h(-1)},style:{color:"#fff",cursor:"pointer",background:f===n?"#0B1222":"inherit",marginBottom:6,padding:"10px 16px",display:"flex",alignItems:"center",gap:12,outline:"none",textTransform:"none"},onKeyDown:t=>{"Enter"!==t.key&&" "!==t.key||_(e)},children:[t.jsx(F,{sx:{color:"#fff",mr:1}}),t.jsx("span",{style:{textTransform:"none"},children:e})]},n)))})})]})})},Vo=e.memo((()=>{const{user:n}=A(),{vesselInfo:o,fetchVesselsInfo:a}=Z(),[i,l]=e.useState(""),[c,d]=e.useState({}),[p,u]=e.useState(!1),f=P(),[h,m]=e.useState(!1),[g,v]=e.useState([]),[x,w]=e.useState(null),[b,D]=e.useState(""),[C,S]=e.useState(!1),{isMobile:k,devMode:_}=N(),M=e.useRef(null),E=e.useRef();e.useEffect((()=>{("/dashboard/events"===f.pathname||f.pathname.startsWith("/dashboard/events"))&&u(!0)}),[f]);const F=e.useMemo((()=>[{value:"events",label:"Events",component:g.length>0&&t.jsx(No,{showFilterModal:h,setShowFilterModal:m,filters:c,setFilters:d,vessels:g,tab:i,onCompletion:M,onLoadingChange:S}),display:!0},{value:"favourites",label:"Favorites",component:g.length>0&&t.jsx(Oo,{vessels:g}),display:!0},{value:"archived",label:"Archived",component:t.jsx(Lo,{vessels:g}),display:n?.hasPermissions([O.manageArtifacts])},{value:"flagged",label:"Flagged",component:t.jsx(Ho,{vessels:g}),display:n?.hasPermissions([O.manageArtifacts])}]),[n,h,c,g,i]),T=async()=>{try{if(o){const t=(e=o)&&Array.isArray(e)?e.filter((e=>!(!1===e.is_active&&!_))):[];v(t)}else a()}catch(t){}var e};e.useEffect((()=>{T()}),[o]),e.useEffect((()=>{i||l(F.find((e=>e.display))?.value||"")}),[F]);if(!p)return null;const L=Object.entries(c).filter((([e,t])=>!(!t||Array.isArray(t)&&0===t.length||!Array.isArray(t)&&""===t)&&(("type"!==e||"both"!==t)&&("end_time"!==e||!t)))).length;return n&&F.some((e=>e.display))&&i&&t.jsxs(r,{container:!0,color:"#FFFFFF",flexDirection:"column",width:"100%",height:"100%",overflow:"auto",sx:{backgroundColor:Y.palette.custom.darkBlue},children:[t.jsxs(r,{container:!0,padding:2,display:"flex",columnGap:{xs:2,lg:0},rowGap:2,justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",children:[t.jsx(r,{size:{xs:"grow",lg:4.5},children:t.jsx(R,{value:i,onChange:(e,t)=>{l(t)},sx:{width:"100%",padding:"4px",border:`2px solid ${Y.palette.custom.borderColor}`,borderRadius:"8px",backgroundColor:"transparent","& .MuiTabs-flexContainer":{height:"100%"},"& .MuiButtonBase-root":{width:100/F.filter((e=>e.display)).length+"%",borderRadius:"8px"},"& .MuiButtonBase-root.Mui-selected":{backgroundColor:Y.palette.custom.mainBlue}},children:F.filter((e=>e.display)).map((e=>t.jsx(j,{label:e.label,value:e.value,sx:{maxWidth:"none"}},e.value)))})}),"events"===i&&t.jsxs(r,{container:!0,columnGap:2,justifyContent:"space-between",size:{xs:12,lg:7.4},children:[t.jsx(r,{size:{xs:"grow",lg:5.8},children:!G(q.stagingAndProduction)&&t.jsx(Bo,{onSelect:async(e,t)=>{if(""===e)return d({}),D(""),void w(null);t&&e&&e!==t?(D(t),w(e)):(D(""),w(null));try{M.current?.(!0),E.current&&E.current.abort(),E.current=new AbortController;const t=await z.post("/completions",{text:e},{signal:E.current.signal,meta:{showSnackbar:!1}});E.current=null,t.data&&"object"==typeof t.data&&d(t.data)}catch(n){M.current?.(!1)}},isLoading:C})}),t.jsx(r,{alignItems:"center",display:"flex",justifyContent:"flex-end",gap:2,size:"auto",children:t.jsx(I,{badgeContent:L,color:"primary",invisible:0===L,sx:{height:k?"100%":"auto","& .MuiBadge-badge":{backgroundColor:Y.palette.custom.mainBlue,color:"#FFFFFF",borderRadius:"50%",height:20,width:20,border:"2px solid #FFFFFF",top:3,right:3}},children:t.jsx(y,{className:"events-step-1",variant:"outlined",startIcon:t.jsx("img",{src:"/icons/filter_icon.svg",width:20,height:20,alt:"Filter"}),sx:{"&.MuiButtonBase-root":{borderColor:0===L?Y.palette.custom.borderColor:Y.palette.custom.mainBlue,height:{xs:G(q.stagingAndProduction)?"50px":"100%",lg:"auto"},color:"#FFFFFF",padding:{xs:"0",lg:"10px 20px"},fontWeight:"bold"},"& .MuiButton-icon":{marginRight:{xs:0,lg:"10px"}}},onClick:()=>m(!0),disabled:C,children:!k&&"Filter"})})})]})]}),"events"===i&&x&&b&&t.jsx(r,{item:!0,xs:12,padding:2,pt:0,children:t.jsxs(s,{sx:{fontWeight:"bold",fontSize:16,pl:1},children:[t.jsx("span",{style:{color:Y.palette.custom.mainBlue},children:"Showing results for"}),' "',x,'"'," ",t.jsx("span",{style:{textDecoration:"line-through",color:"#888",marginLeft:8},children:b})]})}),F.filter((e=>e.display)).map((e=>t.jsx(r,{display:i!==e.value&&"none",paddingX:2,paddingBottom:2,width:"100%",size:"grow",children:e.component},e.value)))]})}));export{Vo as default};

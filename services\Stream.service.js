const Region = require("../models/Region");
const vesselService = require("../services/Vessel.service");
const awsKinesis = require("../modules/awsKinesis");
const pLimit = require("p-limit");
const RegionGroup = require("../models/RegionGroup");

const limit = pLimit(1);

class StreamService {
// NOTE: streamsInfo is a bounded cache refreshed every 5 minutes. It overwrites previous data
// and should not grow unbounded. If many distinct regions/streams exist at once, size == that set.

    constructor() {
        this.streamsInfo = { data: [], lastCheck: new Date(null) };
    }

    async resetCache() {
        this.streamsInfo = { data: [], lastCheck: new Date(null) };
    }

    async fetchAll({ regions } = {}) {
        const lastCheck = this.streamsInfo.lastCheck;

        if (Date.now() - lastCheck.getTime() < 300000) {
            // return existing data if last check was less than 5 minutes ago
            console.log("returning existing");
            return this.streamsInfo.data.filter((stream) => (regions === undefined ? true : regions.includes(stream.region)));
        }

        console.log("[StreamService.fetchAll] Fetching updated list");

        const awsRegions = (await Region.find()).filter((r) => r.is_live);

        const allStreams = await Promise.all(
            awsRegions.map(async (region) => {
                try {
                    return await awsKinesis.listStreams({ region: region.value });
                } catch {
                    console.error(`[StreamService.fetchAll] [FATAL] Error fetching streams for region ${region.value}`);
                    return [];
                }
            }),
        );

        const flattenedStreams = allStreams.flat();

        const [regionGroups, vessels] = await Promise.all([
            RegionGroup.find(),
            vesselService.find({ unit_id: { $ne: null } }, { _id: 1, unit_id: 1, name: 1, thumbnail_compressed_s3_key: 1, region_group_id: 1 }),
        ]);

        const unitToVesselMap = new Map();
        vessels.forEach((vessel) => {
            unitToVesselMap.set(vessel.unit_id, vessel);
        });

        const streamsInfo = flattenedStreams.map((stream) => {
            const vessel = unitToVesselMap.get(stream.StreamName);
            // const regionGroup = vessel ? regionGroups.find((rg) => rg.vessel_ids.some((v) => v.toString() === vessel._id.toString())) : null;
            const regionGroup = vessel ? regionGroups.find((rg) => rg._id.toString() === vessel.region_group_id?.toString()) : null;

            return {
                unit_id: stream.StreamName,
                name: vessel?.name || null,
                thumbnail: vessel?.thumbnail_compressed_s3_key || null,
                region: stream.Region,
                is_live: stream.IsLive,
                timezone: regionGroup?.timezone || null,
                // region_group_id: regionGroup?._id || null,
                region_group_id: vessel?.region_group_id || null,
            };
        });

        this.streamsInfo = { data: streamsInfo, lastCheck: new Date() };

        return streamsInfo.filter((stream) => (regions === undefined ? true : regions.includes(stream.region)));
    }

    async fetchSingle({ unitId }) {
        let streamInfo = this.streamsInfo.data.find((s) => s.unit_id === unitId);

        if (!streamInfo) {
            const streams = await limit(() => this.fetchAll());
            streamInfo = streams.find((s) => s.unit_id === unitId);
            if (!streamInfo) {
                console.error("[StreamService.fetchSingle] couldn't find stream", unitId);
            }
        }

        if (!streamInfo) return null;

        return streamInfo;
    }
}

const streamService = new StreamService();

module.exports = streamService;

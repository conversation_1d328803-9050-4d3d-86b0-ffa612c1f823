import{ay as e,j as s,G as t,cu as n,cv as o,T as a}from"./vendor-B98I-pgv.js";function i(){const i=e(),x=new URLSearchParams(i.search),c=parseInt(x.get("status")),r=x.get("message"),d=x.get("title");return s.jsxs(t,{container:!0,sx:{backgroundColor:"black",width:"100%",height:"100vh",display:"flex",flexDirection:"column",alignItems:"center",textAlign:"center",padding:"20px"},children:[s.jsx(n,{"data-testid":"CheckCircleIcon",color:"success",sx:{fontSize:{xs:"60px",md:"80px"},display:200===c?"":"none"}}),s.jsx(o,{"data-testid":"CancelIcon",sx:{fontSize:{xs:"60px",md:"80px",color:"red"},display:200!=c?"":"none"}}),s.jsxs(t,{sx:{mt:2},children:[s.jsx(a,{sx:{fontSize:{xs:"24px",md:"36px"},color:"white",fontWeight:"bold"},children:d}),s.jsx(a,{sx:{color:"white",fontSize:{xs:"14px",md:"18px"},fontWeight:"500",mt:1},children:r})]})]})}export{i as default};

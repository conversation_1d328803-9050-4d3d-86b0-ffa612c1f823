import{r as e,j as t,bE as r,G as o,T as i,bb as n,bf as a,x as s,X as l,Y as c,cb as d,cc as u,K as h,aA as p,az as g,aE as x,cd as f,aB as m,am as v,a0 as b,ce as F,cf as j,cg as w,ch as C,ci as y,cj as k,ck as S,cl as A,cm as I,aC as E,L,R,ao as T,cn as P,co as z,cp as O}from"./vendor-B98I-pgv.js";import{M as B}from"./ModalContainer-CTYPbNwV.js";import{D,P as M,t as U,u as V,h as H,e as W,q as _,Q as N,i as G}from"./index-Cmi2ob6r.js";import{u as $}from"./AppHook-CvjturwY.js";import{a as X}from"./ArtifactFlag.controller-BiYkhhNT.js";const J=({state:h,onClose:p,shareLink:g,isImageLink:x,shareEventLink:f})=>{const[m,v]=e.useState(!1),b=D();return t.jsx(r,{open:h,onClose:p,children:t.jsx(B,{title:"Share Link",onClose:p,children:t.jsxs(o,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:500},children:[t.jsx(o,{container:!0,justifyContent:"center",height:"100%",size:"grow",children:x?t.jsx("img",{src:g,alt:"share",loading:"lazy",style:{width:"100%",height:"100%",borderRadius:8}}):t.jsx("video",{src:g,controls:!0,autoPlay:!0,muted:!0,preload:"auto",style:{width:"100%",height:"100%",borderRadius:8}})}),t.jsx(o,{children:t.jsx(i,{fontWeight:"100",children:"Copy the link below to share:"})}),t.jsx(o,{container:!0,alignItems:"center",gap:1,children:t.jsx(n,{sx:{m:1,width:"100%"},variant:"outlined",children:t.jsx(a,{value:f||g,sx:{color:s("#FFFFFF",.5),fontWeight:300},endAdornment:t.jsx(l,{position:"end",children:t.jsx(c,{edge:"end",onClick:e=>{e.stopPropagation(),navigator.clipboard.writeText(f||g),b("Link copied to clipboard!",{variant:"success"}),v(!0),setTimeout((()=>v(!1)),5e3)},disabled:m,children:m?t.jsx(d,{sx:{color:"#FFFFFF"}}):t.jsx(u,{sx:{color:"#FFFFFF"}})})})})})})]})})})},q=e.forwardRef((({src:r,onFullscreen:i,isFullscreen:n=!1,styles:a={}},s)=>{const l=e.useRef(null),{screenSize:d}=$(),[u,v]=e.useState(!1),[b,F]=e.useState(0),[j,w]=e.useState(0),C={width:"100%",height:n?"100%":"auto",maxHeight:n?"auto":d.sm||d.md?"110px":"100px",objectFit:n?"contain":"cover"};e.useEffect((()=>{const e=l.current;if(e){const t=()=>{F(e.currentTime),w(e.duration)};return e.addEventListener("timeupdate",t),()=>{e.removeEventListener("timeupdate",t)}}}),[]);const y=()=>{const e=l.current;e&&(u?e.pause():e.play(),v(!u))};return t.jsxs(h,{sx:{background:"#000000",borderRadius:n?0:"10px",overflow:"hidden",position:"relative",height:n?"100%":"auto"},onClick:()=>{y()},children:[t.jsx("video",{ref:e=>{l.current=e,"function"==typeof s?s(e):s&&"object"==typeof s&&(s.current=e)},src:r,preload:"auto",style:{...C,...a}}),t.jsxs(o,{container:!0,alignItems:"center",gap:1,sx:{position:n?"absolute":"static",bottom:0,width:"100%",background:"rgba(0, 0, 0, 0.5)",paddingX:1,paddingBottom:.5,paddingTop:n?.5:0,marginTop:-.5},children:[t.jsx(o,{display:"flex",justifyContent:"center",alignItems:"center",children:t.jsx(c,{onClick:y,sx:{color:"white",padding:0},children:u?t.jsx(p,{sx:{fontSize:n?25:15}}):t.jsx(g,{sx:{fontSize:n?25:15}})})}),t.jsx(o,{display:"flex",justifyContent:"center",alignItems:"center",size:"grow",children:t.jsx(x,{value:j>0?b/j*100:0,onChange:(e,t)=>{const r=l.current;r&&(r.currentTime=t/100*r.duration)},valueLabelDisplay:"auto",valueLabelFormat:e=>(e=>{if(null==e||e<0)return"00:00";const t=Math.floor(e/100*j),r=Math.floor(t/60),o=t%60;return`${String(r).padStart(2,"0")}:${String(o).padStart(2,"0")}`})(e),sx:{"&.MuiSlider-root":{height:2},"& .MuiSlider-thumb":{height:10,width:10},"& .MuiSlider-thumb::after":{content:"none"}}})}),t.jsx(o,{display:"flex",justifyContent:"center",alignItems:"center",children:t.jsx(c,{onClick:i,sx:{color:"white",padding:0},children:n?t.jsx(f,{sx:{fontSize:n?25:15}}):t.jsx(m,{sx:{fontSize:n?25:15}})})})]})]})}));q.displayName="VideoPlayer";const K=({buttonsToShow:e=[],buttonHandlers:r={},buttonStates:o={},containerStyle:i={},direction:n="column",containerRef:a})=>t.jsxs(h,{sx:{position:"absolute",top:8,right:8,display:"flex",flexDirection:n,flexWrap:"wrap",gap:1,zIndex:1e3,...i},children:[e.includes(M.FAVOURITE)&&t.jsx(v,{...a?{slotProps:{popper:{container:a.current}}}:{},title:o.isFavourite?"Remove from favorites":"Add to favorites",arrow:!0,placement:"left",children:t.jsx(c,{onClick:e=>{e.stopPropagation(),o.isFavourite?r.removeFavourite():r.addFavourite()},sx:{height:27,width:27,padding:0,color:o.isFavourite?"yellow":"white",backgroundColor:"rgba(0, 0, 0, 0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.7)"},"&.Mui-disabled":{backgroundColor:"rgba(0, 0, 0, 0.5)"}},disableRipple:!0,disabled:o.isFavouriteLoading,children:o.isFavouriteLoading?t.jsx(b,{sx:{color:"white"},size:18}):o.isFavourite?t.jsx(F,{sx:{height:18}}):t.jsx(j,{sx:{height:18}})})}),e.includes(M.SHARE)&&t.jsx(v,{...a?{slotProps:{popper:{container:a.current}}}:{},title:"Share this event",arrow:!0,placement:"left",children:t.jsx(c,{className:"icon-button",onClick:r.toggleShare,sx:{height:27,width:27,padding:0,color:"#fff",backgroundColor:"rgba(0, 0, 0, 0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.7)"}},children:t.jsx(w,{sx:{height:18}})})}),e.includes(M.DOWNLOAD)&&!o.isDownloading&&t.jsx(v,{...a?{slotProps:{popper:{container:a.current}}}:{},title:"Download",arrow:!0,placement:"left",children:t.jsx(c,{onClick:e=>{e.stopPropagation(),r.downloadArtifact()},sx:{height:27,width:27,padding:0,color:"#fff",backgroundColor:"rgba(0, 0, 0, 0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.7)"}},children:t.jsx(C,{sx:{height:18}})})}),e.includes(M.DOWNLOAD)&&o.isDownloading&&t.jsx(b,{sx:{fontSize:18,color:"white",filter:"drop-shadow(0px 2px 3px rgba(0,0,0,0.5))"}}),e.includes(M.FULLSCREEN)&&r.handleFullscreenOpen&&o.showFullscreenIconForMap&&t.jsx(v,{...a?{slotProps:{popper:{container:a.current}}}:{},title:"View Full Screen",arrow:!0,placement:"left",children:t.jsx(c,{onClick:r.handleFullscreenOpen,sx:{height:27,width:27,padding:0,color:"#fff",backgroundColor:"rgba(0, 0, 0, 0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.7)"}},children:t.jsx(m,{sx:{height:18}})})}),e.includes(M.ARCHIVE)&&r.handleArchiveClick&&t.jsx(v,{...a?{slotProps:{popper:{container:a.current}}}:{},title:o.isArchiving?null:o.archived?"Unarchive":"Archive",arrow:!0,placement:"left",children:t.jsx(c,{size:"small",sx:{height:27,width:27,padding:0,color:"#fff",backgroundColor:"rgba(0,0,0,0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0,0,0,0.7)"}},onClick:r.handleArchiveClick,disabled:o.isArchiving,children:o.isArchiving?t.jsx(b,{sx:{color:"white"},size:18}):o.archived?t.jsx(y,{sx:{height:18,color:"#F59E0B"}}):t.jsx(k,{sx:{height:18,color:"#F59E0B"}})})}),e.includes(M.GROUP_ARCHIVE)&&r.handleArchiveClick&&t.jsx(v,{...a?{slotProps:{popper:{container:a.current}}}:{},title:o.isArchiving?null:o.archived?"Unarchive Group":"Archive Group",arrow:!0,placement:"left",children:t.jsx(c,{size:"small",sx:{height:27,width:27,padding:0,color:"#fff",backgroundColor:"rgba(0,0,0,0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0,0,0,0.7)"}},onClick:r.handleArchiveClick,disabled:o.isArchiving,children:o.isArchiving?t.jsx(b,{sx:{color:"white"},size:18}):t.jsx(h,{component:"img",src:"/icons/group_artifact.archive_icon.svg",alt:o.archived?"Unarchive Group":"Archive Group",sx:{height:18,width:18}})})})]});function Q({src:r,style:o,onLoadedData:i,onCurrentTimeChange:n,currentTime:a=0,fullscreenOpen:l=!1,isInFullScreen:d=!1,showFullscreenIcon:u=!1,setFullscreenOpen:h}){const p=e.useRef(null),[g,f]=e.useState(!1),[v,F]=e.useState(0),[j,w]=e.useState(0),[C,y]=e.useState(!0);e.useEffect((()=>{const e=p.current;if(!e)return;a&&(e.currentTime=a),d&&(e.play(),f(!0));const t=()=>{F(e.currentTime/e.duration*100),w(e.duration),n(e.currentTime),e.currentTime===e.duration&&f(!1)};return e.addEventListener("timeupdate",t),()=>e.removeEventListener("timeupdate",t)}),[]);const k=()=>{const e=p.current;e&&(e.paused?(e.play(),f(!0)):(e.pause(),f(!1)))};e.useEffect((()=>{if(!d){const e=p.current;if(!e)return;!e.paused&&l?(e.pause(),f(!1)):a>0&&e.paused&&!l&&(e.play(),e.currentTime=a,f(!0))}}),[l]);e.useEffect((()=>{const e=p.current;if(!e)return;const t=()=>y(!0),r=e=>{y(!1),i&&i(e)},o=()=>y(!0),n=()=>y(!1);return e.addEventListener("loadstart",t),e.addEventListener("loadeddata",r),e.addEventListener("waiting",o),e.addEventListener("playing",n),()=>{e.removeEventListener("loadstart",t),e.removeEventListener("loadeddata",r),e.removeEventListener("waiting",o),e.removeEventListener("playing",n)}}),[r]);const E={playerWrapper:{position:"relative",backgroundColor:"#000000",overflow:"hidden",width:"100%",display:"flex",alignItems:"center",justifyContent:"center"},controlsWrapper:{overflow:"hidden",background:"#FFFFFF4D",display:"flex",alignItems:"center",justifyContent:"space-between",paddingX:1},playButton:{color:"white",borderRadius:0,"&:hover":{color:U.palette.custom.mainBlue,backgroundColor:"#FFFFFF"}},sliderStyles:{color:U.palette.custom.mainBlue,padding:"4px 0",height:24,flexGrow:1,"& .MuiSlider-thumb":{width:8,height:28,borderRadius:0,backgroundColor:"#FFFFFF",transform:"translate(-50%, -50%)",transition:"all 0.5s ease !important","&:hover, &.Mui-focusVisible":{boxShadow:"none"}},"& .MuiSlider-track":{height:24,borderRadius:0,transition:"all 0.5s ease !important",color:U.palette.custom.mainBlue},"& .MuiSlider-rail":{height:24,borderRadius:0,opacity:.28,backgroundColor:"#FFFFFF4D"},"& .MuiSlider-mark":{height:24,width:4}},fullscreenButton:{color:"white",borderRadius:0,"&:hover":{color:U.palette.custom.mainBlue,backgroundColor:"#FFFFFF"}},loadingIndicator:{position:"absolute",inset:0,display:"flex",alignItems:"center",justifyContent:"center",color:U.palette.custom.mainBlue},centerPlayButton:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",backgroundColor:s(U.palette.custom.mainBlue,.7),color:"white",width:80,height:80,zIndex:2,display:"flex",alignItems:"center",justifyContent:"center","&:hover":{backgroundColor:U.palette.custom.mainBlue},boxShadow:"0px 0px 15px rgba(0, 0, 0, 0.3)"}};return t.jsxs("div",{style:{...E.playerWrapper,width:"100%",...o},children:[t.jsx("video",{ref:p,src:r,preload:"auto",style:{width:"100%",height:"100%",objectFit:"contain"},onLoadedData:i,onClick:k}),C&&t.jsx("div",{style:E.loadingIndicator,children:t.jsx(b,{sx:{color:U.palette.custom.mainBlue},size:50})}),!g&&!C&&t.jsx(c,{sx:E.centerPlayButton,onClick:k,"aria-label":"play video",children:t.jsx(S,{sx:{fontSize:48}})}),t.jsxs("div",{style:{...E.controlsWrapper,position:"absolute",bottom:0,left:0,right:0,zIndex:1},onClick:e=>e.stopPropagation(),children:[t.jsx(c,{sx:E.playButton,onClick:k,children:g?t.jsx(A,{}):t.jsx(S,{})}),t.jsx(x,{value:v,onChange:e=>{const t=p.current,r=+e.target.value;t.currentTime=r/100*t.duration,F(r)},valueLabelFormat:e=>Math.ceil(e/100*j)+"s",sx:E.sliderStyles}),u&&(l?t.jsx(c,{sx:E.fullscreenButton,onClick:()=>h(!l),children:t.jsx(I,{sx:{height:18}})}):t.jsx(c,{sx:E.fullscreenButton,onClick:()=>h(!l),children:t.jsx(m,{sx:{height:18}})}))]})]})}const Y=({isLoading:e,isFavourite:o,removeFavourite:i,addFavourite:n,toggleShare:a,downloadArtifact:s,open:l,onClose:d,mediaUrl:u,isImage:p,handleCurrentTimeChange:g=()=>{},currentTime:x=0,setFullscreenOpen:f})=>t.jsx(r,{open:l,onClose:d,"aria-labelledby":"fullscreen-media","aria-describedby":"fullscreen-media-description",children:t.jsxs(h,{sx:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",width:"90%",height:"90%",bgcolor:"#000",boxShadow:24,p:0,display:"flex",alignItems:"center",justifyContent:"center"},children:[t.jsx(c,{onClick:d,sx:{position:"absolute",top:8,right:8,zIndex:1,color:"white",backgroundColor:"rgba(0, 0, 0, 0.5)",transition:"all 0.3s",border:"1px solid #FFFFFF","&:hover":{backgroundColor:"#FFFFFF",color:"#000",border:"1px solid #000"}},children:t.jsx(E,{})}),p?t.jsx("img",{src:u,alt:"Fullscreen",style:{maxWidth:"100%",maxHeight:"100%",objectFit:"contain"}}):t.jsxs(h,{sx:{position:"relative",width:"100%",height:"100%",backgroundColor:"#000"},children:[void 0!==e&&!e&&t.jsx(K,{buttonsToShow:[M.FAVOURITE,M.SHARE,M.DOWNLOAD],buttonHandlers:{addFavourite:n,removeFavourite:i,toggleShare:a,downloadArtifact:s},buttonStates:{isFavourite:o},containerStyle:{top:80,right:15}}),t.jsx(Q,{src:u,style:{maxWidth:"100%",maxHeight:"100%",objectFit:"contain",height:"100%",width:"100%"},onLoadedData:()=>()=>{},onCurrentTimeChange:g,currentTime:x,fullscreenOpen:l,isInFullScreen:!0,showFullscreenIcon:!0,setFullscreenOpen:f})]})]})}),Z=({initialState:e,onClose:n,onConfirm:a,isArchive:s=!0})=>t.jsx(r,{open:e,onClose:n,children:t.jsx(B,{title:s?"Move to Archive":"Unarchive",onClose:n,headerPosition:"center",children:t.jsxs(o,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:"auto"},children:[t.jsx(o,{children:t.jsx(i,{fontWeight:"100",children:s?"Are you sure you want to move to archive this event?":"Are you sure you want to unarchive this event?"})}),t.jsxs(o,{container:!0,gap:1,justifyContent:"center",children:[t.jsx(o,{justifyContent:"center",display:"flex",children:t.jsx(L,{variant:"contained",sx:{background:"#FFFFFF !important",color:U.palette.primary.main},onClick:n,children:"Cancel"})}),t.jsx(o,{justifyContent:"center",display:"flex",children:t.jsx(L,{variant:"contained",onClick:a,sx:{background:"#F59E0B !important"},children:s?"Archive":"Unarchive"})})]})]})})}),ee=({thumbnailLink:o,originalLink:i,isImage:n,showVideoModal:a=!1,style:s={},skeletonStyle:l={borderRadius:"8px",height:200},cardId:d,showFullscreenIcon:u=!1,showVideoThumbnail:p=!1,showArchiveButton:g=!1,showFlagButton:x=!0,showFullscreenIconForMap:f,onThumbnailClick:F,isArchived:j=!1,vesselId:w,direction:C,buttonsToShow:y,handleUnflagClick:k,flaggedArtifact:S,isGrouped:A=!1,groupArtifacts:I=[]})=>{const[E,L]=e.useState([]);e.useEffect((()=>{L(localStorage.getItem("favouritesArtifacts")?JSON.parse(localStorage.getItem("favouritesArtifacts")):[]);const e=e=>{"favouritesArtifacts"===e.detail.key&&L(e.detail.newValue?JSON.parse(e.detail.newValue):[])};return window.addEventListener("localStorageChange",e),()=>{window.removeEventListener("localStorageChange",e)}}),[]);const[B,D]=e.useState(!1),[U,$]=e.useState(!1),[ee,te]=e.useState(!0),[re,oe]=e.useState(!0),[ie,ne]=e.useState(!1),[ae,se]=e.useState(!1),[le,ce]=e.useState(!1),de=R.useRef(null),{user:ue}=V(),[he,pe]=e.useState(0),[ge,xe]=e.useState(!1),[fe,me]=e.useState(null),[ve,be]=e.useState(!1),[Fe,je]=e.useState(!1),we=R.useRef(null),[Ce,ye]=e.useState(!1),[ke,Se]=e.useState(!1);e.useEffect((()=>{const e=E?.some((e=>e===d));ce(e),oe(!1)}),[d,E]),e.useEffect((()=>{je(j)}),[j]),e.useEffect((()=>{const e=de.current;if(e)return()=>{n?e.src="":(e.pause(),e.removeAttribute("src"),e.load())}}),[d]);const Ae=e=>{e.stopPropagation(),D((e=>!e))},Ie=(e=0)=>{pe(e)},Ee=e=>{e.stopPropagation(),se(!0)},Le=e=>{e.stopPropagation(),F?F(e):Ee(e)},Re=async()=>{$(!0),await W.post(`artifacts/${d}/download`,{},{responseType:"blob",timeout:12e4}).then(_).catch((e=>{})).finally((()=>{$(!1)}))},Te=async()=>{if(!d&&!ue._id)return;oe(!0);const e={artifact_id:d};201===(await N.addFavouriteArtifact(e)).status&&ce(!0),oe(!1)},Pe=async()=>{if(!d&&!ue._id)return;oe(!0);const e={artifact_id:d};200===(await N.removeFavouriteArtifact(e)).status&&ce(!1),oe(!1)},ze=async(e,t=!1)=>{e.stopPropagation(),t||xe(!1),be(!0);try{const e="archive"===fe||t;if(A&&I?.length>0){const t=I.map((t=>{const r=e?"archive":"unarchive";return W.post(`/artifacts/${t._id}/${r}`)}));if(await Promise.all(t),w){const e=I.map((e=>G.deleteItem(w+"_artifact",e._id).catch((e=>{}))));await Promise.all(e)}}else e?await W.post(`/artifacts/${d}/archive`):await W.post(`/artifacts/${d}/unarchive`),w&&await G.deleteItem(w+"_artifact",d).catch((e=>{}))}catch(r){}finally{be(!1)}},Oe=async e=>{if(e.stopPropagation(),k)k(e);else{Se(!0);try{if(Ce)return await X.unflagArtifact(d),void ye(!1);await X.flagArtifact(d),ye(!0)}catch(t){}finally{Se(!1)}}};return e.useEffect((()=>{!n&&a&&te(!1)}),[a]),e.useEffect((()=>{(async()=>{if(d)try{const e=X.isArtifactFlaggedByUser(d);ye(e)}catch(e){}})()}),[d,ue,X.userFlaggedArtifactIds.size,S]),t.jsxs(h,{position:"relative",sx:{width:"100%",height:"100%"},ref:we,children:[ee&&(n||!a)&&t.jsxs(t.Fragment,{children:[t.jsx(h,{sx:{display:"flex",justifyContent:"center",alignItems:"center",position:"absolute",top:0,left:0,width:"100%",height:"100%",zIndex:1},children:t.jsx(b,{})}),t.jsx(T,{variant:"rectangular",width:"100%",sx:{...l}})]}),n?t.jsxs(t.Fragment,{children:[t.jsx("img",{src:o,ref:de,alt:"media",style:{width:"100%",height:"100%",objectFit:"cover",display:ee?"none":"block",...s},onLoad:()=>te(!1),onError:()=>te(!1)}),!ee&&u&&!f&&t.jsx(v,{...we?{slotProps:{popper:{container:we.current}}}:{},title:"View Full Screen",arrow:!0,placement:"right",children:t.jsx(c,{onClick:Ee,sx:{position:"absolute",bottom:8,right:8,height:27,width:27,padding:0,color:"#fff",backgroundColor:"rgba(0, 0, 0, 0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0, 0, 0, 0.7)"}},children:t.jsx(m,{sx:{height:18}})})})]}):a?t.jsxs(t.Fragment,{children:[t.jsx(q,{ref:de,src:i,onFullscreen:()=>ne(!0)}),t.jsx(r,{open:ie,onClose:()=>ne(!1),children:t.jsx(h,{sx:{width:"100%",height:"100%",backgroundColor:"#000"},children:t.jsx(q,{src:i,isFullscreen:!0,onFullscreen:()=>ne(!1)})})})]}):p?t.jsxs(t.Fragment,{children:[t.jsx("img",{src:o,alt:"media",style:{width:"100%",height:"100%",objectFit:"cover",display:ee?"none":"block",...s},onLoad:()=>te(!1),onClick:Le}),!ee&&t.jsx(h,{sx:{position:"absolute",top:0,left:0,width:"100%",height:"100%",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"rgba(0, 0, 0, 0.3)",borderRadius:"8px",cursor:"pointer"},onClick:Le,children:t.jsx(P,{sx:{fontSize:50,color:"white",filter:"drop-shadow(0px 2px 3px rgba(0,0,0,0.5))"}})})]}):t.jsx(t.Fragment,{children:t.jsx(Q,{src:i,style:{width:"100%",height:"100%",display:ee?"none":"block",objectFit:"cover",...s},onLoadedData:()=>te(!1),onCurrentTimeChange:Ie,currentTime:he,fullscreenOpen:ae,isInFullScreen:!1,showFullscreenIcon:!0,setFullscreenOpen:se})}),!ee&&!j&&x&&Oe&&t.jsx(v,{...we?{slotProps:{popper:{container:we.current}}}:{},title:ke?null:Ce||k?"Remove flag":"Flag for cleanup and/or review",arrow:!0,placement:"right",children:t.jsx(c,{size:"small",sx:{position:"absolute",top:8,left:8,height:27,width:27,padding:0,color:"#fff",backgroundColor:Ce||k?"#E60000CC":"rgba(0,0,0,0.5)",borderRadius:"50%","&:hover":{backgroundColor:"rgba(0,0,0,0.7)"}},onClick:Oe,disabled:ke,children:ke?t.jsx(b,{sx:{color:"white"},size:18}):Ce||k?t.jsx(z,{sx:{height:18}}):t.jsx(O,{sx:{height:18}})})}),!ee&&t.jsx(K,{buttonsToShow:y||[M.FAVOURITE,M.SHARE,M.DOWNLOAD,...g?[A?M.GROUP_ARCHIVE:M.ARCHIVE]:[],...f?[M.FULLSCREEN]:[]],buttonHandlers:{addFavourite:Te,removeFavourite:Pe,toggleShare:Ae,downloadArtifact:Re,handleFullscreenOpen:Ee,handleArchiveClick:e=>{e.stopPropagation(),Fe?(me("unarchive"),xe(!0)):ze(e,!0)}},buttonStates:{isFavourite:le,isFavouriteLoading:re,isDownloading:U,showFullscreenIconForMap:f,archived:Fe,isArchiving:ve},direction:C,containerRef:we}),t.jsx(J,{state:B,onClose:Ae,shareLink:i||o,isImageLink:n,shareEventLink:d?`${H.VITE_API_URL}/dashboard/events/${d}`:""}),t.jsx(Z,{initialState:ge,onClose:e=>{e.stopPropagation(),xe(!1)},onConfirm:e=>ze(e),isArchive:"archive"===fe}),ae&&t.jsx(Y,{isLoading:ee,isFavourite:le,removeFavourite:Pe,addFavourite:Te,toggleShare:Ae,downloadArtifact:Re,open:ae,onClose:()=>{se(!1)},mediaUrl:i,isImage:n,handleCurrentTimeChange:Ie,currentTime:he,showFullscreenIcon:!0,setFullscreenOpen:se})]})};export{ee as P};

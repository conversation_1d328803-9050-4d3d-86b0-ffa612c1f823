import{r as e,R as t,a as n}from"./vendor-B98I-pgv.js";const r="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function o(e){const t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function i(e){return"nodeType"in e}function a(e){var t,n;return e?o(e)?e:i(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function l(e){const{Document:t}=a(e);return e instanceof t}function s(e){return!o(e)&&e instanceof a(e).HTMLElement}function c(e){return e instanceof a(e).SVGElement}function u(e){return e?o(e)?e.document:i(e)?l(e)?e:s(e)||c(e)?e.ownerDocument:document:document:document}const d=r?e.useLayoutEffect:e.useEffect;function f(t){const n=e.useRef(t);return d((()=>{n.current=t})),e.useCallback((function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return null==n.current?void 0:n.current(...t)}),[])}function v(t,n){void 0===n&&(n=[t]);const r=e.useRef(t);return d((()=>{r.current!==t&&(r.current=t)}),n),r}function h(t,n){const r=e.useRef();return e.useMemo((()=>{const e=t(r.current);return r.current=e,e}),[...n])}function g(t){const n=f(t),r=e.useRef(null),o=e.useCallback((e=>{e!==r.current&&(null==n||n(e,r.current)),r.current=e}),[]);return[r,o]}function p(t){const n=e.useRef();return e.useEffect((()=>{n.current=t}),[t]),n.current}let b={};function m(t,n){return e.useMemo((()=>{if(n)return n;const e=null==b[t]?0:b[t]+1;return b[t]=e,t+"-"+e}),[t,n])}function y(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>{const r=Object.entries(n);for(const[o,i]of r){const n=t[o];null!=n&&(t[o]=n+e*i)}return t}),{...t})}}const w=y(1),x=y(-1);function D(e){if(!e)return!1;const{KeyboardEvent:t}=a(e.target);return t&&e instanceof t}function C(e){if(function(e){if(!e)return!1;const{TouchEvent:t}=a(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){const{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){const{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return function(e){return"clientX"in e&&"clientY"in e}(e)?{x:e.clientX,y:e.clientY}:null}const R=Object.freeze({Translate:{toString(e){if(!e)return;const{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;const{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[R.Translate.toString(e),R.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),E="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function S(e){return e.matches(E)?e:e.querySelector(E)}const M={display:"none"};function I(e){let{id:n,value:r}=e;return t.createElement("div",{id:n,style:M},r)}function N(e){let{id:n,announcement:r,ariaLiveType:o="assertive"}=e;return t.createElement("div",{id:n,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":o,"aria-atomic":!0},r)}const T=e.createContext(null);const k={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},O={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function A(r){let{announcements:o=O,container:i,hiddenTextDescribedById:a,screenReaderInstructions:l=k}=r;const{announce:s,announcement:c}=function(){const[t,n]=e.useState("");return{announce:e.useCallback((e=>{null!=e&&n(e)}),[]),announcement:t}}(),u=m("DndLiveRegion"),[d,f]=e.useState(!1);if(e.useEffect((()=>{f(!0)}),[]),function(t){const n=e.useContext(T);e.useEffect((()=>{if(!n)throw new Error("useDndMonitor must be used within a children of <DndContext>");return n(t)}),[t,n])}(e.useMemo((()=>({onDragStart(e){let{active:t}=e;s(o.onDragStart({active:t}))},onDragMove(e){let{active:t,over:n}=e;o.onDragMove&&s(o.onDragMove({active:t,over:n}))},onDragOver(e){let{active:t,over:n}=e;s(o.onDragOver({active:t,over:n}))},onDragEnd(e){let{active:t,over:n}=e;s(o.onDragEnd({active:t,over:n}))},onDragCancel(e){let{active:t,over:n}=e;s(o.onDragCancel({active:t,over:n}))}})),[s,o])),!d)return null;const v=t.createElement(t.Fragment,null,t.createElement(I,{id:a,value:l.draggable}),t.createElement(N,{id:u,announcement:c}));return i?n.createPortal(v,i):v}var L;function B(){}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(L||(L={}));const P=Object.freeze({x:0,y:0});function z(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function F(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function j(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}const U=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=j(t,t.left,t.top),i=[];for(const s of r){const{id:e}=s,t=n.get(e);if(t){const n=(a=j(t),l=o,Math.sqrt(Math.pow(a.x-l.x,2)+Math.pow(a.y-l.y,2)));i.push({id:e,data:{droppableContainer:s,value:n}})}}var a,l;return i.sort(z)};function X(e,t){const n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),o=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height),a=o-r,l=i-n;if(r<o&&n<i){const n=t.width*t.height,r=e.width*e.height,o=a*l;return Number((o/(n+r-o)).toFixed(4))}return 0}const Y=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e;const o=[];for(const i of r){const{id:e}=i,r=n.get(e);if(r){const n=X(r,t);n>0&&o.push({id:e,data:{droppableContainer:i,value:n}})}}return o.sort(F)};function K(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:P}function W(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.reduce(((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x})),{...t})}}const H=W(1);const q={ignoreTransform:!1};function J(e,t){void 0===t&&(t=q);let n=e.getBoundingClientRect();if(t.ignoreTransform){const{transform:t,transformOrigin:r}=a(e).getComputedStyle(e);t&&(n=function(e,t,n){const r=function(e){if(e.startsWith("matrix3d(")){const t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){const t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;const{scaleX:o,scaleY:i,x:a,y:l}=r,s=e.left-a-(1-o)*parseFloat(n),c=e.top-l-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),u=o?e.width/o:e.width,d=i?e.height/i:e.height;return{width:u,height:d,top:c,right:s+u,bottom:c+d,left:s}}(n,t,r))}const{top:r,left:o,width:i,height:l,bottom:s,right:c}=n;return{top:r,left:o,width:i,height:l,bottom:s,right:c}}function V(e){return J(e,{ignoreTransform:!0})}function _(e,t){const n=[];return e?function r(o){if(null!=t&&n.length>=t)return n;if(!o)return n;if(l(o)&&null!=o.scrollingElement&&!n.includes(o.scrollingElement))return n.push(o.scrollingElement),n;if(!s(o)||c(o))return n;if(n.includes(o))return n;const i=a(e).getComputedStyle(o);return o!==e&&function(e,t){void 0===t&&(t=a(e).getComputedStyle(e));const n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some((e=>{const r=t[e];return"string"==typeof r&&n.test(r)}))}(o,i)&&n.push(o),function(e,t){return void 0===t&&(t=a(e).getComputedStyle(e)),"fixed"===t.position}(o,i)?n:r(o.parentNode)}(e):n}function G(e){const[t]=_(e,1);return null!=t?t:null}function Q(e){return r&&e?o(e)?e:i(e)?l(e)||e===u(e).scrollingElement?window:s(e)?e:null:null:null}function Z(e){return o(e)?e.scrollX:e.scrollLeft}function $(e){return o(e)?e.scrollY:e.scrollTop}function ee(e){return{x:Z(e),y:$(e)}}var te;function ne(e){return!(!r||!e)&&e===document.scrollingElement}function re(e){const t={x:0,y:0},n=ne(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height};return{isTop:e.scrollTop<=t.y,isLeft:e.scrollLeft<=t.x,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(te||(te={}));const oe={x:.2,y:.2};function ie(e,t,n,r,o){let{top:i,left:a,right:l,bottom:s}=n;void 0===r&&(r=10),void 0===o&&(o=oe);const{isTop:c,isBottom:u,isLeft:d,isRight:f}=re(e),v={x:0,y:0},h={x:0,y:0},g=t.height*o.y,p=t.width*o.x;return!c&&i<=t.top+g?(v.y=te.Backward,h.y=r*Math.abs((t.top+g-i)/g)):!u&&s>=t.bottom-g&&(v.y=te.Forward,h.y=r*Math.abs((t.bottom-g-s)/g)),!f&&l>=t.right-p?(v.x=te.Forward,h.x=r*Math.abs((t.right-p-l)/p)):!d&&a<=t.left+p&&(v.x=te.Backward,h.x=r*Math.abs((t.left+p-a)/p)),{direction:v,speed:h}}function ae(e){if(e===document.scrollingElement){const{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}const{top:t,left:n,right:r,bottom:o}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:o,width:e.clientWidth,height:e.clientHeight}}function le(e){return e.reduce(((e,t)=>w(e,ee(t))),P)}const se=[["x",["left","right"],function(e){return e.reduce(((e,t)=>e+Z(t)),0)}],["y",["top","bottom"],function(e){return e.reduce(((e,t)=>e+$(t)),0)}]];class ce{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const n=_(t),r=le(n);this.rect={...e},this.width=e.width,this.height=e.height;for(const[o,i,a]of se)for(const e of i)Object.defineProperty(this,e,{get:()=>{const t=a(n),i=r[o]-t;return this.rect[e]+i},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class ue{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach((e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)}))},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function de(e,t){const n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}var fe,ve;function he(e){e.preventDefault()}function ge(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(fe||(fe={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(ve||(ve={}));const pe={start:[ve.Space,ve.Enter],cancel:[ve.Esc],end:[ve.Space,ve.Enter,ve.Tab]},be=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case ve.Right:return{...n,x:n.x+25};case ve.Left:return{...n,x:n.x-25};case ve.Down:return{...n,y:n.y+25};case ve.Up:return{...n,y:n.y-25}}};class me{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;const{event:{target:t}}=e;this.props=e,this.listeners=new ue(u(t)),this.windowListeners=new ue(a(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(fe.Resize,this.handleCancel),this.windowListeners.add(fe.VisibilityChange,this.handleCancel),setTimeout((()=>this.listeners.add(fe.Keydown,this.handleKeyDown)))}handleStart(){const{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=J),!e)return;const{top:n,left:r,bottom:o,right:i}=t(e);G(e)&&(o<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(P)}handleKeyDown(e){if(D(e)){const{active:t,context:n,options:r}=this.props,{keyboardCodes:o=pe,coordinateGetter:i=be,scrollBehavior:a="smooth"}=r,{code:l}=e;if(o.end.includes(l))return void this.handleEnd(e);if(o.cancel.includes(l))return void this.handleCancel(e);const{collisionRect:s}=n.current,c=s?{x:s.left,y:s.top}:P;this.referenceCoordinates||(this.referenceCoordinates=c);const u=i(e,{active:t,context:n.current,currentCoordinates:c});if(u){const t=x(u,c),r={x:0,y:0},{scrollableAncestors:o}=n.current;for(const n of o){const o=e.code,{isTop:i,isRight:l,isLeft:s,isBottom:c,maxScroll:d,minScroll:f}=re(n),v=ae(n),h={x:Math.min(o===ve.Right?v.right-v.width/2:v.right,Math.max(o===ve.Right?v.left:v.left+v.width/2,u.x)),y:Math.min(o===ve.Down?v.bottom-v.height/2:v.bottom,Math.max(o===ve.Down?v.top:v.top+v.height/2,u.y))},g=o===ve.Right&&!l||o===ve.Left&&!s,p=o===ve.Down&&!c||o===ve.Up&&!i;if(g&&h.x!==u.x){const e=n.scrollLeft+t.x,i=o===ve.Right&&e<=d.x||o===ve.Left&&e>=f.x;if(i&&!t.y)return void n.scrollTo({left:e,behavior:a});r.x=i?n.scrollLeft-e:o===ve.Right?n.scrollLeft-d.x:n.scrollLeft-f.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(p&&h.y!==u.y){const e=n.scrollTop+t.y,i=o===ve.Down&&e<=d.y||o===ve.Up&&e>=f.y;if(i&&!t.x)return void n.scrollTo({top:e,behavior:a});r.y=i?n.scrollTop-e:o===ve.Down?n.scrollTop-d.y:n.scrollTop-f.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,w(x(u,this.referenceCoordinates),r))}}}handleMove(e,t){const{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){const{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){const{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function ye(e){return Boolean(e&&"distance"in e)}function we(e){return Boolean(e&&"delay"in e)}me.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=pe,onActivation:o}=t,{active:i}=n;const{code:a}=e.nativeEvent;if(r.start.includes(a)){const t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==o||o({event:e.nativeEvent}),!0)}return!1}}];class xe{constructor(e,t,n){var r;void 0===n&&(n=function(e){const{EventTarget:t}=a(e);return e instanceof t?e:u(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;const{event:o}=e,{target:i}=o;this.props=e,this.events=t,this.document=u(i),this.documentListeners=new ue(this.document),this.listeners=new ue(n),this.windowListeners=new ue(a(i)),this.initialCoordinates=null!=(r=C(o))?r:P,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(fe.Resize,this.handleCancel),this.windowListeners.add(fe.DragStart,he),this.windowListeners.add(fe.VisibilityChange,this.handleCancel),this.windowListeners.add(fe.ContextMenu,he),this.documentListeners.add(fe.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(we(t))return this.timeoutId=setTimeout(this.handleStart,t.delay),void this.handlePending(t);if(ye(t))return void this.handlePending(t)}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){const{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){const{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(fe.Click,ge,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(fe.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;const{activated:n,initialCoordinates:r,props:o}=this,{onMove:i,options:{activationConstraint:a}}=o;if(!r)return;const l=null!=(t=C(e))?t:P,s=x(r,l);if(!n&&a){if(ye(a)){if(null!=a.tolerance&&de(s,a.tolerance))return this.handleCancel();if(de(s,a.distance))return this.handleStart()}return we(a)&&de(s,a.tolerance)?this.handleCancel():void this.handlePending(a,s)}e.cancelable&&e.preventDefault(),i(l)}handleEnd(){const{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){const{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===ve.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}const De={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class Ce extends xe{constructor(e){const{event:t}=e,n=u(t.target);super(e,De,n)}}Ce.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!(!n.isPrimary||0!==n.button)&&(null==r||r({event:n}),!0)}}];const Re={move:{name:"mousemove"},end:{name:"mouseup"}};var Ee;!function(e){e[e.RightClick=2]="RightClick"}(Ee||(Ee={}));(class extends xe{constructor(e){super(e,Re,u(e.event.target))}}).activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==Ee.RightClick&&(null==r||r({event:n}),!0)}}];const Se={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};var Me,Ie;function Ne(t){let{acceleration:n,activator:r=Me.Pointer,canScroll:o,draggingRect:i,enabled:a,interval:l=5,order:s=Ie.TreeOrder,pointerCoordinates:c,scrollableAncestors:u,scrollableAncestorRects:d,delta:f,threshold:v}=t;const g=function(e){let{delta:t,disabled:n}=e;const r=p(t);return h((e=>{if(n||!r||!e)return Te;const o={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[te.Backward]:e.x[te.Backward]||-1===o.x,[te.Forward]:e.x[te.Forward]||1===o.x},y:{[te.Backward]:e.y[te.Backward]||-1===o.y,[te.Forward]:e.y[te.Forward]||1===o.y}}}),[n,t,r])}({delta:f,disabled:!a}),[b,m]=function(){const t=e.useRef(null);return[e.useCallback(((e,n)=>{t.current=setInterval(e,n)}),[]),e.useCallback((()=>{null!==t.current&&(clearInterval(t.current),t.current=null)}),[])]}(),y=e.useRef({x:0,y:0}),w=e.useRef({x:0,y:0}),x=e.useMemo((()=>{switch(r){case Me.Pointer:return c?{top:c.y,bottom:c.y,left:c.x,right:c.x}:null;case Me.DraggableRect:return i}}),[r,i,c]),D=e.useRef(null),C=e.useCallback((()=>{const e=D.current;if(!e)return;const t=y.current.x*w.current.x,n=y.current.y*w.current.y;e.scrollBy(t,n)}),[]),R=e.useMemo((()=>s===Ie.TreeOrder?[...u].reverse():u),[s,u]);e.useEffect((()=>{if(a&&u.length&&x){for(const e of R){if(!1===(null==o?void 0:o(e)))continue;const t=u.indexOf(e),r=d[t];if(!r)continue;const{direction:i,speed:a}=ie(e,r,x,n,v);for(const e of["x","y"])g[e][i[e]]||(a[e]=0,i[e]=0);if(a.x>0||a.y>0)return m(),D.current=e,b(C,l),y.current=a,void(w.current=i)}y.current={x:0,y:0},w.current={x:0,y:0},m()}else m()}),[n,C,o,m,a,l,JSON.stringify(x),JSON.stringify(g),b,u,R,d,JSON.stringify(v)])}(class extends xe{constructor(e){super(e,Se)}static setup(){return window.addEventListener(Se.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(Se.move.name,e)};function e(){}}}).activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;const{touches:o}=n;return!(o.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(Me||(Me={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(Ie||(Ie={}));const Te={x:{[te.Backward]:!1,[te.Forward]:!1},y:{[te.Backward]:!1,[te.Forward]:!1}};var ke,Oe;!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(ke||(ke={})),function(e){e.Optimized="optimized"}(Oe||(Oe={}));const Ae=new Map;function Le(e,t){return h((n=>e?n||("function"==typeof t?t(e):e):null),[t,e])}function Be(t){let{callback:n,disabled:r}=t;const o=f(n),i=e.useMemo((()=>{if(r||"undefined"==typeof window||void 0===window.ResizeObserver)return;const{ResizeObserver:e}=window;return new e(o)}),[r]);return e.useEffect((()=>()=>null==i?void 0:i.disconnect()),[i]),i}function Pe(e){return new ce(J(e),e)}function ze(t,n,r){void 0===n&&(n=Pe);const[o,i]=e.useState(null);function a(){i((e=>{if(!t)return null;var o;if(!1===t.isConnected)return null!=(o=null!=e?e:r)?o:null;const i=n(t);return JSON.stringify(e)===JSON.stringify(i)?e:i}))}const l=function(t){let{callback:n,disabled:r}=t;const o=f(n),i=e.useMemo((()=>{if(r||"undefined"==typeof window||void 0===window.MutationObserver)return;const{MutationObserver:e}=window;return new e(o)}),[o,r]);return e.useEffect((()=>()=>null==i?void 0:i.disconnect()),[i]),i}({callback(e){if(t)for(const n of e){const{type:e,target:r}=n;if("childList"===e&&r instanceof HTMLElement&&r.contains(t)){a();break}}}}),s=Be({callback:a});return d((()=>{a(),t?(null==s||s.observe(t),null==l||l.observe(document.body,{childList:!0,subtree:!0})):(null==s||s.disconnect(),null==l||l.disconnect())}),[t]),o}const Fe=[];function je(t,n){void 0===n&&(n=[]);const r=e.useRef(null);return e.useEffect((()=>{r.current=null}),n),e.useEffect((()=>{const e=t!==P;e&&!r.current&&(r.current=t),!e&&r.current&&(r.current=null)}),[t]),r.current?x(t,r.current):P}function Ue(t){return e.useMemo((()=>t?function(e){const t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(t):null),[t])}const Xe=[];function Ye(t){let{measure:n}=t;const[r,o]=e.useState(null),i=e.useCallback((e=>{for(const{target:t}of e)if(s(t)){o((e=>{const r=n(t);return e?{...e,width:r.width,height:r.height}:r}));break}}),[n]),a=Be({callback:i}),l=e.useCallback((e=>{const t=function(e){if(!e)return null;if(e.children.length>1)return e;const t=e.children[0];return s(t)?t:e}(e);null==a||a.disconnect(),t&&(null==a||a.observe(t)),o(t?n(t):null)}),[n,a]),[c,u]=g(l);return e.useMemo((()=>({nodeRef:c,rect:r,setRef:u})),[r,c,u])}const Ke=[{sensor:Ce,options:{}},{sensor:me,options:{}}],We={current:{}},He={draggable:{measure:V},droppable:{measure:V,strategy:ke.WhileDragging,frequency:Oe.Optimized},dragOverlay:{measure:J}};class qe extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter((e=>{let{disabled:t}=e;return!t}))}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}const Je={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new qe,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:B},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:He,measureDroppableContainers:B,windowRect:null,measuringScheduled:!1},Ve={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:B,draggableNodes:new Map,over:null,measureDroppableContainers:B},_e=e.createContext(Ve),Ge=e.createContext(Je);function Qe(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new qe}}}function Ze(e,t){switch(t.type){case L.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case L.DragMove:return null==e.draggable.active?e:{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case L.DragEnd:case L.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case L.RegisterDroppable:{const{element:n}=t,{id:r}=n,o=new qe(e.droppable.containers);return o.set(r,n),{...e,droppable:{...e.droppable,containers:o}}}case L.SetDroppableDisabled:{const{id:n,key:r,disabled:o}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;const a=new qe(e.droppable.containers);return a.set(n,{...i,disabled:o}),{...e,droppable:{...e.droppable,containers:a}}}case L.UnregisterDroppable:{const{id:n,key:r}=t,o=e.droppable.containers.get(n);if(!o||r!==o.key)return e;const i=new qe(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function $e(t){let{disabled:n}=t;const{active:r,activatorEvent:o,draggableNodes:i}=e.useContext(_e),a=p(o),l=p(null==r?void 0:r.id);return e.useEffect((()=>{if(!n&&!o&&a&&null!=l){if(!D(a))return;if(document.activeElement===a.target)return;const e=i.get(l);if(!e)return;const{activatorNode:t,node:n}=e;if(!t.current&&!n.current)return;requestAnimationFrame((()=>{for(const e of[t.current,n.current]){if(!e)continue;const t=S(e);if(t){t.focus();break}}}))}}),[o,n,i,l,a]),null}const et=e.createContext({...P,scaleX:1,scaleY:1});var tt;!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(tt||(tt={}));const nt=e.memo((function(o){var i,l,s,c;let{id:u,accessibility:f,autoScroll:g=!0,children:p,sensors:b=Ke,collisionDetection:y=Y,measuring:x,modifiers:D,...R}=o;const E=e.useReducer(Ze,void 0,Qe),[S,M]=E,[I,N]=function(){const[t]=e.useState((()=>new Set)),n=e.useCallback((e=>(t.add(e),()=>t.delete(e))),[t]);return[e.useCallback((e=>{let{type:n,event:r}=e;t.forEach((e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)}))}),[t]),n]}(),[k,O]=e.useState(tt.Uninitialized),B=k===tt.Initialized,{draggable:{active:z,nodes:F,translate:j},droppable:{containers:U}}=S,X=null!=z?F.get(z):null,W=e.useRef({initial:null,translated:null}),q=e.useMemo((()=>{var e;return null!=z?{id:z,data:null!=(e=null==X?void 0:X.data)?e:We,rect:W}:null}),[z,X]),V=e.useRef(null),[Z,$]=e.useState(null),[te,re]=e.useState(null),oe=v(R,Object.values(R)),ie=m("DndDescribedBy",u),ae=e.useMemo((()=>U.getEnabled()),[U]),se=(ue=x,e.useMemo((()=>({draggable:{...He.draggable,...null==ue?void 0:ue.draggable},droppable:{...He.droppable,...null==ue?void 0:ue.droppable},dragOverlay:{...He.dragOverlay,...null==ue?void 0:ue.dragOverlay}})),[null==ue?void 0:ue.draggable,null==ue?void 0:ue.droppable,null==ue?void 0:ue.dragOverlay]));var ue;const{droppableRects:de,measureDroppableContainers:fe,measuringScheduled:ve}=function(t,n){let{dragging:r,dependencies:o,config:i}=n;const[a,l]=e.useState(null),{frequency:s,measure:c,strategy:u}=i,d=e.useRef(t),f=function(){switch(u){case ke.Always:return!1;case ke.BeforeDragging:return r;default:return!r}}(),g=v(f),p=e.useCallback((function(e){void 0===e&&(e=[]),g.current||l((t=>null===t?e:t.concat(e.filter((e=>!t.includes(e))))))}),[g]),b=e.useRef(null),m=h((e=>{if(f&&!r)return Ae;if(!e||e===Ae||d.current!==t||null!=a){const e=new Map;for(let n of t){if(!n)continue;if(a&&a.length>0&&!a.includes(n.id)&&n.rect.current){e.set(n.id,n.rect.current);continue}const t=n.node.current,r=t?new ce(c(t),t):null;n.rect.current=r,r&&e.set(n.id,r)}return e}return e}),[t,a,r,f,c]);return e.useEffect((()=>{d.current=t}),[t]),e.useEffect((()=>{f||p()}),[r,f]),e.useEffect((()=>{a&&a.length>0&&l(null)}),[JSON.stringify(a)]),e.useEffect((()=>{f||"number"!=typeof s||null!==b.current||(b.current=setTimeout((()=>{p(),b.current=null}),s))}),[s,f,p,...o]),{droppableRects:m,measureDroppableContainers:p,measuringScheduled:null!=a}}(ae,{dragging:B,dependencies:[j.x,j.y],config:se.droppable}),he=function(e,t){const n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return h((e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null}),[r,t])}(F,z),ge=e.useMemo((()=>te?C(te):null),[te]),pe=function(){const e=!1===(null==Z?void 0:Z.autoScrollEnabled),t="object"==typeof g?!1===g.enabled:!1===g,n=B&&!e&&!t;if("object"==typeof g)return{...g,enabled:n};return{enabled:n}}(),be=function(e,t){return Le(e,t)}(he,se.draggable.measure);!function(t){let{activeNode:n,measure:r,initialRect:o,config:i=!0}=t;const a=e.useRef(!1),{x:l,y:s}="boolean"==typeof i?{x:i,y:i}:i;d((()=>{if(!l&&!s||!n)return void(a.current=!1);if(a.current||!o)return;const e=null==n?void 0:n.node.current;if(!e||!1===e.isConnected)return;const t=K(r(e),o);if(l||(t.x=0),s||(t.y=0),a.current=!0,Math.abs(t.x)>0||Math.abs(t.y)>0){const n=G(e);n&&n.scrollBy({top:t.y,left:t.x})}}),[n,l,s,o,r])}({activeNode:null!=z?F.get(z):null,config:pe.layoutShiftCompensation,initialRect:be,measure:se.draggable.measure});const me=ze(he,se.draggable.measure,be),ye=ze(he?he.parentElement:null),we=e.useRef({activatorEvent:null,active:null,activeNode:he,collisionRect:null,collisions:null,droppableRects:de,draggableNodes:F,draggingNode:null,draggingNodeRect:null,droppableContainers:U,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),xe=U.getNodeFor(null==(i=we.current.over)?void 0:i.id),De=Ye({measure:se.dragOverlay.measure}),Ce=null!=(l=De.nodeRef.current)?l:he,Re=B?null!=(s=De.rect)?s:me:null,Ee=Boolean(De.nodeRef.current&&De.rect),Se=K(Me=Ee?null:me,Le(Me));var Me;const Ie=Ue(Ce?a(Ce):null),Te=function(t){const n=e.useRef(t),r=h((e=>t?e&&e!==Fe&&t&&n.current&&t.parentNode===n.current.parentNode?e:_(t):Fe),[t]);return e.useEffect((()=>{n.current=t}),[t]),r}(B?null!=xe?xe:he:null),Oe=function(t,n){void 0===n&&(n=J);const[r]=t,o=Ue(r?a(r):null),[i,l]=e.useState(Xe);function s(){l((()=>t.length?t.map((e=>ne(e)?o:new ce(n(e),e))):Xe))}const c=Be({callback:s});return d((()=>{null==c||c.disconnect(),s(),t.forEach((e=>null==c?void 0:c.observe(e)))}),[t]),i}(Te),Pe=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce(((e,t)=>t({transform:e,...r})),n):n}(D,{transform:{x:j.x-Se.x,y:j.y-Se.y,scaleX:1,scaleY:1},activatorEvent:te,active:q,activeNodeRect:me,containerNodeRect:ye,draggingNodeRect:Re,over:we.current.over,overlayNodeRect:De.rect,scrollableAncestors:Te,scrollableAncestorRects:Oe,windowRect:Ie}),qe=ge?w(ge,j):null,Je=function(t){const[n,r]=e.useState(null),o=e.useRef(t),i=e.useCallback((e=>{const t=Q(e.target);t&&r((e=>e?(e.set(t,ee(t)),new Map(e)):null))}),[]);return e.useEffect((()=>{const e=o.current;if(t!==e){n(e);const a=t.map((e=>{const t=Q(e);return t?(t.addEventListener("scroll",i,{passive:!0}),[t,ee(t)]):null})).filter((e=>null!=e));r(a.length?new Map(a):null),o.current=t}return()=>{n(t),n(e)};function n(e){e.forEach((e=>{const t=Q(e);null==t||t.removeEventListener("scroll",i)}))}}),[i,t]),e.useMemo((()=>t.length?n?Array.from(n.values()).reduce(((e,t)=>w(e,t)),P):le(t):P),[t,n])}(Te),Ve=je(Je),nt=je(Je,[me]),rt=w(Pe,Ve),ot=Re?H(Re,Pe):null,it=q&&ot?y({active:q,collisionRect:ot,droppableRects:de,droppableContainers:ae,pointerCoordinates:qe}):null,at=function(e,t){if(!e||0===e.length)return null;const[n]=e;return n[t]}(it,"id"),[lt,st]=e.useState(null),ct=function(e,t,n){return{...e,scaleX:t&&n?t.width/n.width:1,scaleY:t&&n?t.height/n.height:1}}(Ee?Pe:w(Pe,nt),null!=(c=null==lt?void 0:lt.rect)?c:null,me),ut=e.useRef(null),dt=e.useCallback(((e,t)=>{let{sensor:r,options:o}=t;if(null==V.current)return;const i=F.get(V.current);if(!i)return;const a=e.nativeEvent,l=new r({active:V.current,activeNode:i,event:a,options:o,context:we,onAbort(e){if(!F.get(e))return;const{onDragAbort:t}=oe.current,n={id:e};null==t||t(n),I({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!F.get(e))return;const{onDragPending:o}=oe.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==o||o(i),I({type:"onDragPending",event:i})},onStart(e){const t=V.current;if(null==t)return;const r=F.get(t);if(!r)return;const{onDragStart:o}=oe.current,i={activatorEvent:a,active:{id:t,data:r.data,rect:W}};n.unstable_batchedUpdates((()=>{null==o||o(i),O(tt.Initializing),M({type:L.DragStart,initialCoordinates:e,active:t}),I({type:"onDragStart",event:i}),$(ut.current),re(a)}))},onMove(e){M({type:L.DragMove,coordinates:e})},onEnd:s(L.DragEnd),onCancel:s(L.DragCancel)});function s(e){return async function(){const{active:t,collisions:r,over:o,scrollAdjustedTranslate:i}=we.current;let l=null;if(t&&i){const{cancelDrop:n}=oe.current;if(l={activatorEvent:a,active:t,collisions:r,delta:i,over:o},e===L.DragEnd&&"function"==typeof n){await Promise.resolve(n(l))&&(e=L.DragCancel)}}V.current=null,n.unstable_batchedUpdates((()=>{M({type:e}),O(tt.Uninitialized),st(null),$(null),re(null),ut.current=null;const t=e===L.DragEnd?"onDragEnd":"onDragCancel";if(l){const e=oe.current[t];null==e||e(l),I({type:t,event:l})}}))}}ut.current=l}),[F]),ft=e.useCallback(((e,t)=>(n,r)=>{const o=n.nativeEvent,i=F.get(r);if(null!==V.current||!i||o.dndKit||o.defaultPrevented)return;const a={active:i};!0===e(n,t.options,a)&&(o.dndKit={capturedBy:t.sensor},V.current=r,dt(n,t))}),[F,dt]),vt=function(t,n){return e.useMemo((()=>t.reduce(((e,t)=>{const{sensor:r}=t;return[...e,...r.activators.map((e=>({eventName:e.eventName,handler:n(e.handler,t)})))]}),[])),[t,n])}(b,ft);!function(t){e.useEffect((()=>{if(!r)return;const e=t.map((e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()}));return()=>{for(const t of e)null==t||t()}}),t.map((e=>{let{sensor:t}=e;return t})))}(b),d((()=>{me&&k===tt.Initializing&&O(tt.Initialized)}),[me,k]),e.useEffect((()=>{const{onDragMove:e}=oe.current,{active:t,activatorEvent:r,collisions:o,over:i}=we.current;if(!t||!r)return;const a={active:t,activatorEvent:r,collisions:o,delta:{x:rt.x,y:rt.y},over:i};n.unstable_batchedUpdates((()=>{null==e||e(a),I({type:"onDragMove",event:a})}))}),[rt.x,rt.y]),e.useEffect((()=>{const{active:e,activatorEvent:t,collisions:r,droppableContainers:o,scrollAdjustedTranslate:i}=we.current;if(!e||null==V.current||!t||!i)return;const{onDragOver:a}=oe.current,l=o.get(at),s=l&&l.rect.current?{id:l.id,rect:l.rect.current,data:l.data,disabled:l.disabled}:null,c={active:e,activatorEvent:t,collisions:r,delta:{x:i.x,y:i.y},over:s};n.unstable_batchedUpdates((()=>{st(s),null==a||a(c),I({type:"onDragOver",event:c})}))}),[at]),d((()=>{we.current={activatorEvent:te,active:q,activeNode:he,collisionRect:ot,collisions:it,droppableRects:de,draggableNodes:F,draggingNode:Ce,draggingNodeRect:Re,droppableContainers:U,over:lt,scrollableAncestors:Te,scrollAdjustedTranslate:rt},W.current={initial:Re,translated:ot}}),[q,he,it,ot,F,Ce,Re,de,U,lt,Te,rt]),Ne({...pe,delta:j,draggingRect:ot,pointerCoordinates:qe,scrollableAncestors:Te,scrollableAncestorRects:Oe});const ht=e.useMemo((()=>({active:q,activeNode:he,activeNodeRect:me,activatorEvent:te,collisions:it,containerNodeRect:ye,dragOverlay:De,draggableNodes:F,droppableContainers:U,droppableRects:de,over:lt,measureDroppableContainers:fe,scrollableAncestors:Te,scrollableAncestorRects:Oe,measuringConfiguration:se,measuringScheduled:ve,windowRect:Ie})),[q,he,me,te,it,ye,De,F,U,de,lt,fe,Te,Oe,se,ve,Ie]),gt=e.useMemo((()=>({activatorEvent:te,activators:vt,active:q,activeNodeRect:me,ariaDescribedById:{draggable:ie},dispatch:M,draggableNodes:F,over:lt,measureDroppableContainers:fe})),[te,vt,q,me,M,ie,F,lt,fe]);return t.createElement(T.Provider,{value:N},t.createElement(_e.Provider,{value:gt},t.createElement(Ge.Provider,{value:ht},t.createElement(et.Provider,{value:ct},p)),t.createElement($e,{disabled:!1===(null==f?void 0:f.restoreFocus)})),t.createElement(A,{...f,hiddenTextDescribedById:ie}))})),rt=e.createContext(null),ot="button";function it(t){let{id:n,data:r,disabled:o=!1,attributes:i}=t;const a=m("Draggable"),{activators:l,activatorEvent:s,active:c,activeNodeRect:u,ariaDescribedById:f,draggableNodes:h,over:p}=e.useContext(_e),{role:b=ot,roleDescription:y="draggable",tabIndex:w=0}=null!=i?i:{},x=(null==c?void 0:c.id)===n,D=e.useContext(x?et:rt),[C,R]=g(),[E,S]=g(),M=function(t,n){return e.useMemo((()=>t.reduce(((e,t)=>{let{eventName:r,handler:o}=t;return e[r]=e=>{o(e,n)},e}),{})),[t,n])}(l,n),I=v(r);d((()=>(h.set(n,{id:n,key:a,node:C,activatorNode:E,data:I}),()=>{const e=h.get(n);e&&e.key===a&&h.delete(n)})),[h,n]);return{active:c,activatorEvent:s,activeNodeRect:u,attributes:e.useMemo((()=>({role:b,tabIndex:w,"aria-disabled":o,"aria-pressed":!(!x||b!==ot)||void 0,"aria-roledescription":y,"aria-describedby":f.draggable})),[o,b,w,x,y,f.draggable]),isDragging:x,listeners:o?void 0:M,node:C,over:p,setNodeRef:R,setActivatorNodeRef:S,transform:D}}const at={timeout:25};function lt(e,t,n){const r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function st(e,t){return e.reduce(((e,n,r)=>{const o=t.get(n);return o&&(e[r]=o),e}),Array(e.length))}function ct(e){return null!==e&&e>=0}const ut=e=>{let{rects:t,activeIndex:n,overIndex:r,index:o}=e;const i=lt(t,r,n),a=t[o],l=i[o];return l&&a?{x:l.left-a.left,y:l.top-a.top,scaleX:l.width/a.width,scaleY:l.height/a.height}:null},dt="Sortable",ft=t.createContext({activeIndex:-1,containerId:dt,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:ut,disabled:{draggable:!1,droppable:!1}});function vt(n){let{children:r,id:o,items:i,strategy:a=ut,disabled:l=!1}=n;const{active:s,dragOverlay:c,droppableRects:u,over:f,measureDroppableContainers:v}=e.useContext(Ge),h=m(dt,o),g=Boolean(null!==c.rect),p=e.useMemo((()=>i.map((e=>"object"==typeof e&&"id"in e?e.id:e))),[i]),b=null!=s,y=s?p.indexOf(s.id):-1,w=f?p.indexOf(f.id):-1,x=e.useRef(p),D=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(p,x.current),C=-1!==w&&-1===y||D,R=function(e){return"boolean"==typeof e?{draggable:e,droppable:e}:e}(l);d((()=>{D&&b&&v(p)}),[D,p,b,v]),e.useEffect((()=>{x.current=p}),[p]);const E=e.useMemo((()=>({activeIndex:y,containerId:h,disabled:R,disableTransforms:C,items:p,overIndex:w,useDragOverlay:g,sortedRects:st(p,u),strategy:a})),[y,h,R.draggable,R.droppable,C,p,w,u,g,a]);return t.createElement(ft.Provider,{value:E},r)}const ht=e=>{let{id:t,items:n,activeIndex:r,overIndex:o}=e;return lt(n,r,o).indexOf(t)},gt=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:o,items:i,newIndex:a,previousItems:l,previousContainerId:s,transition:c}=e;return!(!c||!r)&&((l===i||o!==a)&&(!!n||a!==o&&t===s))},pt={duration:200,easing:"ease"},bt="transform",mt=R.Transition.toString({property:bt,duration:0,easing:"linear"}),yt={roleDescription:"sortable"};function wt(t){let{animateLayoutChanges:n=gt,attributes:r,disabled:o,data:i,getNewIndex:a=ht,id:l,strategy:s,resizeObserverConfig:c,transition:u=pt}=t;const{items:f,containerId:h,activeIndex:p,disabled:b,disableTransforms:y,sortedRects:w,overIndex:x,useDragOverlay:C,strategy:E}=e.useContext(ft),S=function(e,t){var n,r;if("boolean"==typeof e)return{draggable:e,droppable:!1};return{draggable:null!=(n=null==e?void 0:e.draggable)?n:t.draggable,droppable:null!=(r=null==e?void 0:e.droppable)?r:t.droppable}}(o,b),M=f.indexOf(l),I=e.useMemo((()=>({sortable:{containerId:h,index:M,items:f},...i})),[h,i,M,f]),N=e.useMemo((()=>f.slice(f.indexOf(l))),[f,l]),{rect:T,node:k,isOver:O,setNodeRef:A}=function(t){let{data:n,disabled:r=!1,id:o,resizeObserverConfig:i}=t;const a=m("Droppable"),{active:l,dispatch:s,over:c,measureDroppableContainers:u}=e.useContext(_e),d=e.useRef({disabled:r}),f=e.useRef(!1),h=e.useRef(null),p=e.useRef(null),{disabled:b,updateMeasurementsFor:y,timeout:w}={...at,...i},x=v(null!=y?y:o),D=Be({callback:e.useCallback((()=>{f.current?(null!=p.current&&clearTimeout(p.current),p.current=setTimeout((()=>{u(Array.isArray(x.current)?x.current:[x.current]),p.current=null}),w)):f.current=!0}),[w]),disabled:b||!l}),C=e.useCallback(((e,t)=>{D&&(t&&(D.unobserve(t),f.current=!1),e&&D.observe(e))}),[D]),[R,E]=g(C),S=v(n);return e.useEffect((()=>{D&&R.current&&(D.disconnect(),f.current=!1,D.observe(R.current))}),[R,D]),e.useEffect((()=>(s({type:L.RegisterDroppable,element:{id:o,key:a,disabled:r,node:R,rect:h,data:S}}),()=>s({type:L.UnregisterDroppable,key:a,id:o}))),[o]),e.useEffect((()=>{r!==d.current.disabled&&(s({type:L.SetDroppableDisabled,id:o,key:a,disabled:r}),d.current.disabled=r)}),[o,a,r,s]),{active:l,rect:h,isOver:(null==c?void 0:c.id)===o,node:R,over:c,setNodeRef:E}}({id:l,data:I,disabled:S.droppable,resizeObserverConfig:{updateMeasurementsFor:N,...c}}),{active:B,activatorEvent:P,activeNodeRect:z,attributes:F,setNodeRef:j,listeners:U,isDragging:X,over:Y,setActivatorNodeRef:K,transform:W}=it({id:l,data:I,attributes:{...yt,...r},disabled:S.draggable}),H=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return e.useMemo((()=>e=>{n.forEach((t=>t(e)))}),n)}(A,j),q=Boolean(B),V=q&&!y&&ct(p)&&ct(x),_=!C&&X,G=_&&V?W:null,Q=V?null!=G?G:(null!=s?s:E)({rects:w,activeNodeRect:z,activeIndex:p,overIndex:x,index:M}):null,Z=ct(p)&&ct(x)?a({id:l,items:f,activeIndex:p,overIndex:x}):M,$=null==B?void 0:B.id,ee=e.useRef({activeId:$,items:f,newIndex:Z,containerId:h}),te=f!==ee.current.items,ne=n({active:B,containerId:h,isDragging:X,isSorting:q,id:l,index:M,items:f,newIndex:ee.current.newIndex,previousItems:ee.current.items,previousContainerId:ee.current.containerId,transition:u,wasDragging:null!=ee.current.activeId}),re=function(t){let{disabled:n,index:r,node:o,rect:i}=t;const[a,l]=e.useState(null),s=e.useRef(r);return d((()=>{if(!n&&r!==s.current&&o.current){const e=i.current;if(e){const t=J(o.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&l(n)}}r!==s.current&&(s.current=r)}),[n,r,o,i]),e.useEffect((()=>{a&&l(null)}),[a]),a}({disabled:!ne,index:M,node:k,rect:T});return e.useEffect((()=>{q&&ee.current.newIndex!==Z&&(ee.current.newIndex=Z),h!==ee.current.containerId&&(ee.current.containerId=h),f!==ee.current.items&&(ee.current.items=f)}),[q,Z,h,f]),e.useEffect((()=>{if($===ee.current.activeId)return;if($&&!ee.current.activeId)return void(ee.current.activeId=$);const e=setTimeout((()=>{ee.current.activeId=$}),50);return()=>clearTimeout(e)}),[$]),{active:B,activeIndex:p,attributes:F,data:I,rect:T,index:M,newIndex:Z,items:f,isOver:O,isSorting:q,isDragging:X,listeners:U,node:k,overIndex:x,over:Y,setNodeRef:H,setActivatorNodeRef:K,setDroppableNodeRef:A,setDraggableNodeRef:j,transform:null!=re?re:Q,transition:function(){if(re||te&&ee.current.newIndex===M)return mt;if(_&&!D(P)||!u)return;if(q||ne)return R.Transition.toString({...u,property:bt});return}()}}ve.Down,ve.Right,ve.Up,ve.Left;export{R as C,nt as D,vt as S,lt as a,U as c,wt as u};

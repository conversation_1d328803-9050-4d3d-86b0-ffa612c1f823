import{cq as t,r as e,D as r,cr as n}from"./vendor-B98I-pgv.js";var o=Number.isNaN||function(t){return"number"==typeof t&&t!=t};function i(t,e){if(t.length!==e.length)return!1;for(var r=0;r<t.length;r++)if(n=t[r],i=e[r],!(n===i||o(n)&&o(i)))return!1;var n,i;return!0}function a(t,e){var r;void 0===e&&(e=i);var n,o=[],a=!1;return function(){for(var i=[],l=0;l<arguments.length;l++)i[l]=arguments[l];return a&&r===this&&e(i,o)||(n=t.apply(this,i),a=!0,r=this,o=i),n}}var l="object"==typeof performance&&"function"==typeof performance.now?function(){return performance.now()}:function(){return Date.now()};function s(t){cancelAnimationFrame(t.id)}var c=-1;function u(t){if(void 0===t&&(t=!1),-1===c||t){var e=document.createElement("div"),r=e.style;r.width="50px",r.height="50px",r.overflow="scroll",document.body.appendChild(e),c=e.offsetWidth-e.clientWidth,document.body.removeChild(e)}return c}var f=null;function d(t){if(void 0===t&&(t=!1),null===f||t){var e=document.createElement("div"),r=e.style;r.width="50px",r.height="50px",r.overflow="scroll",r.direction="rtl";var n=document.createElement("div"),o=n.style;return o.width="100px",o.height="100px",e.appendChild(n),document.body.appendChild(e),e.scrollLeft>0?f="positive-descending":(e.scrollLeft=1,f=0===e.scrollLeft?"negative":"positive-ascending"),document.body.removeChild(e),f}return f}var h=function(t,e){return t};function m(o){var i,c=o.getItemOffset,f=o.getEstimatedTotalSize,m=o.getItemSize,v=o.getOffsetForIndexAndAlignment,g=o.getStartIndexForOffset,S=o.getStopIndexForStartIndex,I=o.initInstanceProps,_=o.shouldResetStyleCacheOnItemSizeChange,M=o.validateProps;return(i=function(o){function i(t){var e;return(e=o.call(this,t)||this)._instanceProps=I(e.props,n(e)),e._outerRef=void 0,e._resetIsScrollingTimeoutId=null,e.state={instance:n(e),isScrolling:!1,scrollDirection:"forward",scrollOffset:"number"==typeof e.props.initialScrollOffset?e.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},e._callOnItemsRendered=void 0,e._callOnItemsRendered=a((function(t,r,n,o){return e.props.onItemsRendered({overscanStartIndex:t,overscanStopIndex:r,visibleStartIndex:n,visibleStopIndex:o})})),e._callOnScroll=void 0,e._callOnScroll=a((function(t,r,n){return e.props.onScroll({scrollDirection:t,scrollOffset:r,scrollUpdateWasRequested:n})})),e._getItemStyle=void 0,e._getItemStyle=function(t){var r,n=e.props,o=n.direction,i=n.itemSize,a=n.layout,l=e._getItemStyleCache(_&&i,_&&a,_&&o);if(l.hasOwnProperty(t))r=l[t];else{var s=c(e.props,t,e._instanceProps),u=m(e.props,t,e._instanceProps),f="horizontal"===o||"horizontal"===a,d="rtl"===o,h=f?s:0;l[t]=r={position:"absolute",left:d?void 0:h,right:d?h:void 0,top:f?0:s,height:f?"100%":u,width:f?u:"100%"}}return r},e._getItemStyleCache=void 0,e._getItemStyleCache=a((function(t,e,r){return{}})),e._onScrollHorizontal=function(t){var r=t.currentTarget,n=r.clientWidth,o=r.scrollLeft,i=r.scrollWidth;e.setState((function(t){if(t.scrollOffset===o)return null;var r=e.props.direction,a=o;if("rtl"===r)switch(d()){case"negative":a=-o;break;case"positive-descending":a=i-n-o}return a=Math.max(0,Math.min(a,i-n)),{isScrolling:!0,scrollDirection:t.scrollOffset<a?"forward":"backward",scrollOffset:a,scrollUpdateWasRequested:!1}}),e._resetIsScrollingDebounced)},e._onScrollVertical=function(t){var r=t.currentTarget,n=r.clientHeight,o=r.scrollHeight,i=r.scrollTop;e.setState((function(t){if(t.scrollOffset===i)return null;var e=Math.max(0,Math.min(i,o-n));return{isScrolling:!0,scrollDirection:t.scrollOffset<e?"forward":"backward",scrollOffset:e,scrollUpdateWasRequested:!1}}),e._resetIsScrollingDebounced)},e._outerRefSetter=function(t){var r=e.props.outerRef;e._outerRef=t,"function"==typeof r?r(t):null!=r&&"object"==typeof r&&r.hasOwnProperty("current")&&(r.current=t)},e._resetIsScrollingDebounced=function(){var t,r,n,o;null!==e._resetIsScrollingTimeoutId&&s(e._resetIsScrollingTimeoutId),e._resetIsScrollingTimeoutId=(t=e._resetIsScrolling,r=150,n=l(),o={id:requestAnimationFrame((function e(){l()-n>=r?t.call(null):o.id=requestAnimationFrame(e)}))})},e._resetIsScrolling=function(){e._resetIsScrollingTimeoutId=null,e.setState({isScrolling:!1},(function(){e._getItemStyleCache(-1,null)}))},e}t(i,o),i.getDerivedStateFromProps=function(t,e){return p(t,e),M(t),null};var y=i.prototype;return y.scrollTo=function(t){t=Math.max(0,t),this.setState((function(e){return e.scrollOffset===t?null:{scrollDirection:e.scrollOffset<t?"forward":"backward",scrollOffset:t,scrollUpdateWasRequested:!0}}),this._resetIsScrollingDebounced)},y.scrollToItem=function(t,e){void 0===e&&(e="auto");var r=this.props,n=r.itemCount,o=r.layout,i=this.state.scrollOffset;t=Math.max(0,Math.min(t,n-1));var a=0;if(this._outerRef){var l=this._outerRef;a="vertical"===o?l.scrollWidth>l.clientWidth?u():0:l.scrollHeight>l.clientHeight?u():0}this.scrollTo(v(this.props,t,e,i,this._instanceProps,a))},y.componentDidMount=function(){var t=this.props,e=t.direction,r=t.initialScrollOffset,n=t.layout;if("number"==typeof r&&null!=this._outerRef){var o=this._outerRef;"horizontal"===e||"horizontal"===n?o.scrollLeft=r:o.scrollTop=r}this._callPropsCallbacks()},y.componentDidUpdate=function(){var t=this.props,e=t.direction,r=t.layout,n=this.state,o=n.scrollOffset;if(n.scrollUpdateWasRequested&&null!=this._outerRef){var i=this._outerRef;if("horizontal"===e||"horizontal"===r)if("rtl"===e)switch(d()){case"negative":i.scrollLeft=-o;break;case"positive-ascending":i.scrollLeft=o;break;default:var a=i.clientWidth,l=i.scrollWidth;i.scrollLeft=l-a-o}else i.scrollLeft=o;else i.scrollTop=o}this._callPropsCallbacks()},y.componentWillUnmount=function(){null!==this._resetIsScrollingTimeoutId&&s(this._resetIsScrollingTimeoutId)},y.render=function(){var t=this.props,n=t.children,o=t.className,i=t.direction,a=t.height,l=t.innerRef,s=t.innerElementType,c=t.innerTagName,u=t.itemCount,d=t.itemData,m=t.itemKey,p=void 0===m?h:m,v=t.layout,g=t.outerElementType,S=t.outerTagName,I=t.style,_=t.useIsScrolling,M=t.width,y=this.state.isScrolling,x="horizontal"===i||"horizontal"===v,z=x?this._onScrollHorizontal:this._onScrollVertical,O=this._getRangeToRender(),w=O[0],C=O[1],R=[];if(u>0)for(var b=w;b<=C;b++)R.push(e.createElement(n,{data:d,key:p(b,d),index:b,isScrolling:_?y:void 0,style:this._getItemStyle(b)}));var T=f(this.props,this._instanceProps);return e.createElement(g||S||"div",{className:o,onScroll:z,ref:this._outerRefSetter,style:r({position:"relative",height:a,width:M,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:i},I)},e.createElement(s||c||"div",{children:R,ref:l,style:{height:x?"100%":T,pointerEvents:y?"none":void 0,width:x?T:"100%"}}))},y._callPropsCallbacks=function(){if("function"==typeof this.props.onItemsRendered&&this.props.itemCount>0){var t=this._getRangeToRender(),e=t[0],r=t[1],n=t[2],o=t[3];this._callOnItemsRendered(e,r,n,o)}if("function"==typeof this.props.onScroll){var i=this.state,a=i.scrollDirection,l=i.scrollOffset,s=i.scrollUpdateWasRequested;this._callOnScroll(a,l,s)}},y._getRangeToRender=function(){var t=this.props,e=t.itemCount,r=t.overscanCount,n=this.state,o=n.isScrolling,i=n.scrollDirection,a=n.scrollOffset;if(0===e)return[0,0,0,0];var l=g(this.props,a,this._instanceProps),s=S(this.props,l,a,this._instanceProps),c=o&&"backward"!==i?1:Math.max(1,r),u=o&&"forward"!==i?1:Math.max(1,r);return[Math.max(0,l-c),Math.max(0,Math.min(e-1,s+u)),l,s]},i}(e.PureComponent)).defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},i}var p=function(t,e){t.children,t.direction,t.height,t.layout,t.innerTagName,t.outerTagName,t.width,e.instance},v=function(t,e,r){var n=t.itemSize,o=r.itemMetadataMap,i=r.lastMeasuredIndex;if(e>i){var a=0;if(i>=0){var l=o[i];a=l.offset+l.size}for(var s=i+1;s<=e;s++){var c=n(s);o[s]={offset:a,size:c},a+=c}r.lastMeasuredIndex=e}return o[e]},g=function(t,e,r,n,o){for(;n<=r;){var i=n+Math.floor((r-n)/2),a=v(t,i,e).offset;if(a===o)return i;a<o?n=i+1:a>o&&(r=i-1)}return n>0?n-1:0},S=function(t,e,r,n){for(var o=t.itemCount,i=1;r<o&&v(t,r,e).offset<n;)r+=i,i*=2;return g(t,e,Math.min(r,o-1),Math.floor(r/2),n)},I=function(t,e){var r=t.itemCount,n=e.itemMetadataMap,o=e.estimatedItemSize,i=e.lastMeasuredIndex,a=0;if(i>=r&&(i=r-1),i>=0){var l=n[i];a=l.offset+l.size}return a+(r-i-1)*o},_=m({getItemOffset:function(t,e,r){return v(t,e,r).offset},getItemSize:function(t,e,r){return r.itemMetadataMap[e].size},getEstimatedTotalSize:I,getOffsetForIndexAndAlignment:function(t,e,r,n,o,i){var a=t.direction,l=t.height,s=t.layout,c=t.width,u="horizontal"===a||"horizontal"===s?c:l,f=v(t,e,o),d=I(t,o),h=Math.max(0,Math.min(d-u,f.offset)),m=Math.max(0,f.offset-u+f.size+i);switch("smart"===r&&(r=n>=m-u&&n<=h+u?"auto":"center"),r){case"start":return h;case"end":return m;case"center":return Math.round(m+(h-m)/2);default:return n>=m&&n<=h?n:n<m?m:h}},getStartIndexForOffset:function(t,e,r){return function(t,e,r){var n=e.itemMetadataMap,o=e.lastMeasuredIndex;return(o>0?n[o].offset:0)>=r?g(t,e,o,0,r):S(t,e,Math.max(0,o),r)}(t,r,e)},getStopIndexForStartIndex:function(t,e,r,n){for(var o=t.direction,i=t.height,a=t.itemCount,l=t.layout,s=t.width,c="horizontal"===o||"horizontal"===l?s:i,u=v(t,e,n),f=r+c,d=u.offset+u.size,h=e;h<a-1&&d<f;)h++,d+=v(t,h,n).size;return h},initInstanceProps:function(t,e){var r={itemMetadataMap:{},estimatedItemSize:t.estimatedItemSize||50,lastMeasuredIndex:-1};return e.resetAfterIndex=function(t,n){void 0===n&&(n=!0),r.lastMeasuredIndex=Math.min(r.lastMeasuredIndex,t-1),e._getItemStyleCache(-1),n&&e.forceUpdate()},r},shouldResetStyleCacheOnItemSizeChange:!1,validateProps:function(t){t.itemSize}}),M=m({getItemOffset:function(t,e){return e*t.itemSize},getItemSize:function(t,e){return t.itemSize},getEstimatedTotalSize:function(t){var e=t.itemCount;return t.itemSize*e},getOffsetForIndexAndAlignment:function(t,e,r,n,o,i){var a=t.direction,l=t.height,s=t.itemCount,c=t.itemSize,u=t.layout,f=t.width,d="horizontal"===a||"horizontal"===u?f:l,h=Math.max(0,s*c-d),m=Math.min(h,e*c),p=Math.max(0,e*c-d+c+i);switch("smart"===r&&(r=n>=p-d&&n<=m+d?"auto":"center"),r){case"start":return m;case"end":return p;case"center":var v=Math.round(p+(m-p)/2);return v<Math.ceil(d/2)?0:v>h+Math.floor(d/2)?h:v;default:return n>=p&&n<=m?n:n<p?p:m}},getStartIndexForOffset:function(t,e){var r=t.itemCount,n=t.itemSize;return Math.max(0,Math.min(r-1,Math.floor(e/n)))},getStopIndexForStartIndex:function(t,e,r){var n=t.direction,o=t.height,i=t.itemCount,a=t.itemSize,l=t.layout,s=t.width,c=e*a,u="horizontal"===n||"horizontal"===l?s:o,f=Math.ceil((u+r-c)/a);return Math.max(0,Math.min(i-1,e+f-1))},initInstanceProps:function(t){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(t){t.itemSize}});export{M as F,_ as V};

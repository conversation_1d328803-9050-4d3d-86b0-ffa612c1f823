import{r as e,j as t,bE as s,G as r,T as o,L as a,bb as n,bF as i,ba as l,x as d,bG as c,bP as u,bQ as x,bR as m,bS as p,aU as h,ay as g,Y as f,bT as F,bU as j,bV as b,a0 as _,bW as w,R as v,an as C,I as y,bX as S,bH as B,ag as z,af as k,bf as M,X as R,bN as D}from"./vendor-B98I-pgv.js";import{t as T,a as E,b as I,u as W,D as $,e as N,p as O}from"./index-Cmi2ob6r.js";import{u as A}from"./AppHook-CvjturwY.js";import{M as P}from"./ModalContainer-CTYPbNwV.js";import{F as L}from"./index.esm-BO-krc_n.js";import{C as U}from"./CustomFooter-BBYHktSX.js";import{u as G}from"./useDebounce-BI6pGekf.js";import"./utils-D3r61PVZ.js";import"./maps--fsV2DPB.js";import"./charts-gTQAinvd.js";const X=({showFilterModal:u,setShowFilterModal:x,setFilters:m})=>{const[p,h]=e.useState("all_users"),[g,f]=e.useState("all_time"),[F,j]=e.useState({statusChanged:!1,timeChanged:!1,oldTime:"",oldStatus:""}),b=()=>{F.statusChanged&&(h(F.oldStatus),j({...F,statusChanged:!1,oldStatus:""})),F.timeChanged&&(f(F.oldTime),j({...F,timeChanged:!1,oldTime:""})),x(!1)};return t.jsx(s,{open:Boolean(u),onClose:b,children:t.jsxs(P,{title:"Filter",onClose:b,showDivider:!0,children:[t.jsxs(r,{container:!0,flexDirection:"column",gap:2,width:{xs:300,sm:500},maxHeight:"70vh",overflow:"auto",flexWrap:"nowrap",children:[t.jsxs(r,{children:[t.jsx(o,{variant:"h6",fontSize:"16px !important",marginBottom:2,fontWeight:500,children:"Status"}),t.jsx(r,{container:!0,border:`1px solid ${T.palette.custom.borderColor}`,borderRadius:"8px",padding:"3px",children:["online","offline","all_users"].map((e=>t.jsx(r,{size:4,children:t.jsx(a,{onClick:()=>{j({...F,statusChanged:!0,oldStatus:p}),h(e)},sx:{width:"100%",borderRadius:"8px",color:p===e?"#FFFFFF":"#737791",outline:"none !important",textTransform:"capitalize",backgroundColor:"transparent",border:p===e?`1px solid ${T.palette.custom.borderColor}`:"none","&:hover":{backgroundColor:"transparent"}},children:e.charAt(0).toUpperCase()+e.slice(1).replace(/_/g," ")})},e)))})]}),t.jsxs(r,{children:[t.jsx(o,{variant:"h6",fontSize:"16px !important",marginBottom:2,fontWeight:500,children:"Time"}),t.jsx(n,{component:"fieldset",children:t.jsx(i,{value:g,onChange:e=>{j({...F,timeChanged:!0,oldTime:g}),f(e.target.value)},children:["today","last_3_days","last_week","last_month","last_year","all_time"].map((e=>t.jsx(l,{value:e,control:t.jsx(c,{sx:{color:"#737791",opacity:.3,"&.Mui-checked":{color:"#FFFFFF",opacity:.5}}}),label:e.replace(/_/g," ").replace(/\b\w/g,(e=>e.toUpperCase())),sx:{margin:0,marginBottom:1,width:"fit-content",padding:"0 15px 0 0",border:`1px solid ${g===e?d("#FFFFFF",.5):T.palette.custom.borderColor}`,borderRadius:"8px","& .MuiTypography-root":{color:g===e?"#FFFFFF":"#737791",fontWeight:300}}},e)))})})]})]}),t.jsxs(r,{container:!0,gap:2,justifyContent:"space-between",children:[t.jsx(r,{children:t.jsx(a,{sx:{color:"#FFFFFF",textTransform:"none"},onClick:()=>{h("all_users"),f("all_time"),j({statusChanged:!1,timeChanged:!1,oldTime:"",oldStatus:""}),m((e=>({...e,status:null,created_after:null})))},children:"Clear filters"})}),t.jsx(r,{children:t.jsx(a,{sx:{color:"#FFFFFF",backgroundColor:T.palette.custom.mainBlue,"&:hover":{backgroundColor:T.palette.custom.mainBlue}},variant:"contained",onClick:()=>{(()=>{let e;switch(g){case"today":e=E().startOf("day").valueOf();break;case"last_3_days":e=E().subtract(3,"days").valueOf();break;case"last_week":e=E().subtract(7,"days").valueOf();break;case"last_month":e=E().subtract(30,"days").valueOf();break;case"last_year":e=E().subtract(365,"days").valueOf();break;default:e=null}m((t=>({...t,status:p,created_after:e})))})(),x(!1)},children:"Apply"})})]})]})})},Y=({user:e,index:s,style:r,data:a})=>{const{logs:n,username:i}=a,l=n[s];return l&&t.jsx(u,{children:t.jsx(x,{style:r,children:t.jsx(m,{sx:{display:"flex",width:"100%",paddingY:2,borderBottom:n.length-1===s?"none":`1px solid ${T.palette.custom.borderColor}`},children:["name","connect_timestamp","disconnect_timestamp","device","browser"].map(((s,r)=>t.jsx(p,{sx:{color:"#FFFFFF",flex:1,padding:0,border:"none",...0===r&&{display:"flex",alignItems:"center",gap:2}},children:"name"===s?t.jsxs(t.Fragment,{children:[t.jsx(h,{sx:{width:"35px",height:"10px",visibility:"hidden"}}),t.jsx(o,{variant:"h6",fontSize:"16px !important",color:"#fff",fontWeight:500,children:l.user?.name??i})]}):t.jsx(o,{variant:"h6",fontSize:"16px !important",color:"#fff",children:s.endsWith("connect_timestamp")?l[s]?E(l[s]).format(I.dateTimeFormat(e,{exclude_seconds:!0})):"--":l[s]??"--"})},r)))},l._id)})})},H=({index:e,style:s,data:a})=>{const{logs:n}=a,i=n[e+1],{user:l}=W();return i?t.jsx("div",{style:s,children:t.jsx(r,{container:!0,sx:{display:"flex",width:"100%",paddingY:2,borderBottom:n.length-1===e+1?"none":`1px solid ${T.palette.custom.borderColor}`,rowGap:"10px",flexWrap:"wrap"},children:[{label:"Last Connected",value:E(i.connect_timestamp).format(I.dateTimeFormat(l,{exclude_seconds:!0}))},{label:"Disconnected",value:i.disconnect_timestamp&&E(i.disconnect_timestamp).format(I.dateTimeFormat(l,{exclude_seconds:!0}))},{label:"Device",value:i.device},{label:"Browser",value:i.browser}].map(((e,s)=>t.jsxs(r,{sx:{color:"#FFFFFF",padding:0,border:"none"},size:{xs:12,sm:4},children:[t.jsx(o,{fontSize:"14px",fontWeight:"400",sx:{color:T.palette.custom.mainBlue,flex:1,padding:0,border:"none"},children:e.label}),t.jsx(o,{variant:"h6",fontSize:"16px !important",color:"#fff",children:e.value?e.value:"--"})]},s)))},i._id)}):null},Q=[{field:"user.name",headerName:"Name"},{field:"connect_timestamp",headerName:"Last Connected"},{field:"disconnect_timestamp",headerName:"Disconnected"},{field:"device",headerName:"Device"},{field:"browser",headerName:"Browser"}];function q({showFilterModal:s,setShowFilterModal:a,searchQuery:n}){const{isMobile:i,screenSize:l}=A(),{user:c}=W(),z=$(),{pathname:k}=g(),[M,R]=e.useState({status:null,full_name_or_browser_or_device:null,created_after:null}),[D,O]=e.useState([]),[P,G]=e.useState(!0),[q,V]=e.useState(1),[J,K]=e.useState(10),[Z,ee]=e.useState(1),[te,se]=e.useState(0),[re,oe]=e.useState({}),[ae,ne]=e.useState({}),ie=e.useRef(),[le,de]=e.useState({}),[ce,ue]=e.useState({}),[xe,me]=e.useState(!1),pe=e.useRef(!1);e.useEffect((()=>{("/dashboard/logs"===k||k.startsWith("/dashboard/logs"))&&me(!0)}),[k]),e.useEffect((()=>{xe&&!pe.current&&(he(),pe.current=!0)}),[xe,c]),e.useEffect((()=>{xe&&pe.current&&he()}),[ae]);const he=async e=>{try{G(!0),oe({}),de({}),e&&V(1);const t=Object.entries(ae).map((([e,t])=>`sorting[${e}]=${t}`)).join("&"),s=new URLSearchParams({...Object.fromEntries(Object.entries({...M,...n}).filter((([,e])=>null!==e&&""!==e))),page:e?1:q,rowsPerPage:J}),{data:r}=await N.get(`/logs/sessions?${s.toString()}${t.length>0?`&${t}`:""}`);O(Array.isArray(r.logs)&&r.logs.length>0?r.logs:[]),ee(r.totalPages),se(r.totalCount),Array.isArray(r.logs)&&0!==r.logs.length||z("No data found for logs",{variant:"warning"}),de({})}catch(t){O([]),z("Something went wrong",{variant:"error"})}finally{G(!1)}},ge=(e,t,s)=>{oe((r=>{const o=!r[e];return o&&!le[t]&&(async(e,t)=>{try{ue((t=>({...t,[e]:!0})));const s=new URLSearchParams({...Object.fromEntries(Object.entries({...M,...n,sorting:ae}).filter((([,e])=>null!==e&&""!==e))),userId:e}),{data:r}=await N.get(`/logs/sessions/user?${s.toString()}`);Array.isArray(r)&&r.length>0&&de((s=>({...s,[e]:r.filter((e=>e._id!==t))})))}catch(s){z("Something went wrong while fetching user logs",{variant:"error"})}finally{ue((t=>({...t,[e]:!1})))}})(t,s),{...r,[e]:o}}))},fe=(e,s,r,o,a)=>{const n=o?l.xs?250:130:60,i=le[r]?.sort(((e,t)=>new Date(t.connect_timestamp).getTime()-new Date(e.connect_timestamp).getTime()))||[];if(!i||0===i.length||!re[s]||!le[r])return null;const d=i.length,c=Math.min(400,d*(n+10));return re[s]?t.jsx(L,{ref:ie,height:c,itemCount:d,itemSize:n,width:"100%",itemData:{user:e,username:s,logs:i},children:a}):null};return e.useEffect((()=>{const e=Z||Math.ceil(te/J);q>e&&e>0&&V(1)}),[q,J,D]),e.useEffect((()=>{xe&&pe.current&&he(!0)}),[J,M,n]),e.useEffect((()=>{xe&&pe.current&&he(!1)}),[q]),xe?t.jsxs(r,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",children:[t.jsxs(r,{container:!0,direction:"column",overflow:"auto",border:`1px solid ${T.palette.custom.borderColor}`,borderRadius:"10px",padding:"10px 15px",size:"grow",children:[t.jsxs(r,{container:!0,paddingY:1,children:[!i&&Q.map(((e,s)=>t.jsxs(r,{sx:{display:"flex",alignItems:"center",flex:1,padding:0,border:"none",paddingRight:2},children:[t.jsx(f,{width:"auto",sx:{padding:0},onClick:()=>{return t=e.field,void ne((e=>{const s=e[t]||"NONE",r="ASC"===s?"DESC":"DESC"===s?null:"ASC";return null===r?{}:{[t]:r}}));var t},children:"ASC"===ae[e.field]?t.jsx(F,{sx:{fontSize:"18px",color:"#FFFFFF",transition:"all 0.3s","&:hover":{color:T.palette.custom.mainBlue}}}):"DESC"===ae[e.field]?t.jsx(j,{sx:{fontSize:"18px",color:"#FFFFFF",transition:"all 0.3s","&:hover":{color:T.palette.custom.mainBlue}}}):t.jsx(b,{sx:{fontSize:"18px",color:d("#FFFFFF",.5)}})}),t.jsx(o,{sx:{color:T.palette.custom.mainBlue},children:e.headerName})]},s))),i&&t.jsx(t.Fragment,{children:["Name","Details"].map(((e,s)=>t.jsx(r,{sx:{color:T.palette.custom.mainBlue,flex:"Name"===e?1:0,minWidth:"Details"===e?"auto":0,padding:0,border:"none"},children:e},s)))})]}),t.jsxs(r,{container:!0,overflow:"auto",marginBottom:2,size:"grow",children:[P&&t.jsx(r,{height:"100%",display:"flex",justifyContent:"center",alignItems:"center",size:"grow",children:t.jsx(_,{size:40})}),!P&&0===D.length&&t.jsx(r,{height:"100%",width:"100%",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",size:"grow",children:t.jsxs(t.Fragment,{children:[t.jsx(B,{sx:{fontSize:"100px",color:T.palette.custom.borderColor}}),t.jsx(o,{variant:"h6",component:"div",gutterBottom:!0,color:T.palette.custom.borderColor,children:"No data available"})]})}),!P&&D.length>0&&t.jsx(w,{children:t.jsx(u,{sx:{minWidth:i?0:650},"aria-labelledby":"tableTitle",children:t.jsxs(x,{children:[!i&&D.map(((e,s)=>t.jsxs(v.Fragment,{children:[t.jsx(m,{hover:!0,children:t.jsx(p,{colSpan:5,sx:{paddingX:"0 !important",borderBottom:`1px solid ${T.palette.custom.borderColor}`,opacity:ce[e.user_id]?.5:1,pointerEvents:ce[e.user_id]?"none":"auto"},children:t.jsxs(r,{container:!0,display:"flex",children:[t.jsxs(r,{container:!0,display:"flex",flex:1,alignItems:"center",justifyContent:"space-between",onClick:()=>!ce[e.user_id]&&ge(e.user?.username,e.user_id,e._id),sx:{cursor:ce[e.user_id]?"not-allowed":"pointer"},children:[t.jsxs(r,{gap:2,display:"flex",alignItems:"center",children:[t.jsx(h,{sx:{width:"35px",height:"35px"}}),t.jsx(o,{variant:"h6",fontSize:"16px !important",color:"#fff",children:e.user?.name??e.user?.username})]}),t.jsx(r,{children:t.jsx(f,{sx:{padding:0},children:ce[e.user_id]?t.jsx(_,{size:20,sx:{color:d("#FFFFFF",.6),marginRight:2}}):re[e.user?.username]?t.jsx(C,{sx:{color:d(T.palette.common.white,.6),padding:0,marginRight:2}}):t.jsx(y,{sx:{color:d(T.palette.common.white,.6),padding:0,marginRight:2}})})})]}),["connect_timestamp","disconnect_timestamp","device","browser"].map(((s,a)=>t.jsx(r,{flex:1,display:"flex",alignItems:"center",children:t.jsx(o,{variant:"h6",fontSize:"16px !important",color:"#fff",children:s.includes("connect_timestamp")?e[s]?E(e[s]).format(I.dateTimeFormat(c,{exclude_seconds:!0})):"--":e[s]??"--"})},a)))]})})}),t.jsx(m,{children:t.jsx(p,{colSpan:5,sx:{padding:0,borderBottom:`1px solid ${T.palette.custom.borderColor}`},children:t.jsx(S,{in:re[e.user?.username],sx:{width:"100%",backgroundColor:d(T.palette.custom.offline,.08),"& .MuiCollapse-wrapperInner":{display:"flex",flexDirection:"column"}},children:fe(c,e.user?.username,e.user_id,!1,Y)})})})]},s))),i&&D.map(((e,s)=>t.jsxs(v.Fragment,{children:[t.jsx(m,{hover:!0,children:t.jsx(p,{colSpan:5,sx:{paddingX:"0 !important",borderBottom:0},children:t.jsx(r,{container:!0,display:"flex",children:t.jsxs(r,{container:!0,display:"flex",flex:1,alignItems:"center",justifyContent:"space-between",onClick:()=>!ce[e.user_id]&&ge(e.user?.username,e.user_id),style:{cursor:ce[e.user_id]?"not-allowed":"pointer",opacity:ce[e.user_id]?.7:1,pointerEvents:ce[e.user_id]?"none":"auto"},children:[t.jsxs(r,{gap:2,display:"flex",alignItems:"center",children:[t.jsx(h,{sx:{width:"35px",height:"35px"}}),t.jsx(o,{variant:"h6",fontSize:"16px !important",color:"#fff",children:e.user?.name??e.user?.username})]}),t.jsx(r,{children:t.jsx(f,{sx:{padding:0},children:ce[e.user_id]?t.jsx(_,{size:20,sx:{color:d("#FFFFFF",.6),marginRight:2}}):re[e.user?.username]?t.jsx(C,{sx:{color:d(T.palette.common.white,.6),padding:0,marginRight:2}}):t.jsx(y,{sx:{color:d(T.palette.common.white,.6),padding:0,marginRight:2}})})})]})})})}),t.jsx(m,{children:t.jsx(p,{colSpan:5,sx:{padding:0,borderBottom:0},children:t.jsx(S,{in:re[e.user?.username],sx:{width:"100%",backgroundColor:d(T.palette.custom.offline,.08),borderRadius:"10px",padding:"0 20px","& .MuiCollapse-wrapperInner":{display:"flex",flexDirection:"column"}},children:fe(c,e.user?.username,e.user_id,!0,H)})})})]},s)))]})})})]}),t.jsx(U,{page:q,rowsPerPage:J,totalRows:te||D.length,onPageChange:(e,t)=>{V(t)},onRowsPerPageChange:e=>{K(e.target.value),V(1)}})]}),t.jsx(X,{showFilterModal:s,setShowFilterModal:a,setFilters:R})]}):null}function V(){const{user:s}=W(),{isMobile:o}=A(),[n,i]=e.useState(!1),[l,d]=e.useState({}),[c,u]=v.useState(l.full_name_or_browser_or_device||""),x=G(c,600);e.useEffect((()=>{void 0!==x&&x!==l.full_name_or_browser_or_device&&m({target:{value:x}})}),[x]),e.useEffect((()=>{l.full_name_or_browser_or_device!==c&&u(l.full_name_or_browser_or_device||"")}),[l.full_name_or_browser_or_device]);const m=e=>{d({full_name_or_browser_or_device:e.target.value})},p=e.useMemo((()=>[{value:"sessions",label:"Sessions",component:t.jsx(q,{showFilterModal:n,setShowFilterModal:i,searchQuery:l}),display:s?.hasPermissions([O.viewSessionLogs])}]),[s,n,l]),[h,g]=e.useState(p[0].value);return s&&p.some((e=>e.display))&&h&&t.jsxs(r,{container:!0,color:"#FFFFFF",flexDirection:"column",height:"100%",overflow:"auto",sx:{backgroundColor:T.palette.custom.darkBlue},children:[t.jsxs(r,{container:!0,padding:2,display:"flex",rowGap:2,justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",children:[t.jsx(r,{size:{xs:12,lg:2.5},children:t.jsx(z,{value:h,onChange:(e,t)=>g(t),sx:{width:"100%",padding:"4px",border:`2px solid ${T.palette.custom.borderColor}`,borderRadius:"8px",backgroundColor:"transparent","& .MuiTabs-flexContainer":{height:"100%"},"& .MuiButtonBase-root":{width:"100%",borderRadius:"8px"},"& .MuiButtonBase-root.Mui-selected":{backgroundColor:T.palette.custom.mainBlue}},children:p.filter((e=>e.display)).map((e=>t.jsx(k,{label:e.label,value:e.value,sx:{maxWidth:"none"}},e.value)))})}),t.jsxs(r,{container:!0,columnGap:2,justifyContent:"space-between",size:{xs:12,lg:9.4},children:[t.jsx(r,{size:{xs:"grow",lg:5.8},children:t.jsx(M,{type:"text",value:c,onChange:e=>{u(e.target.value)},startAdornment:t.jsx(R,{position:"start",children:t.jsx(D,{sx:{color:"#FFFFFF"}})}),placeholder:"Search by name, browser or device",sx:{color:"#FFFFFF",width:"100%","& .MuiOutlinedInput-notchedOutline":{border:"2px solid",borderColor:T.palette.custom.borderColor+" !important",borderRadius:"8px"}}})}),t.jsx(r,{alignItems:"center",display:"flex",justifyContent:"flex-end",gap:2,size:"auto",children:t.jsx(a,{variant:"outlined",startIcon:t.jsx("img",{src:"/icons/filter_icon.svg",width:20,height:20}),sx:{"&.MuiButtonBase-root":{borderColor:T.palette.custom.borderColor,color:"#FFFFFF",height:{xs:"100%",lg:"auto"},padding:{xs:"0",lg:"10px 20px"},fontWeight:"bold"},"& .MuiButton-icon":{marginRight:{xs:0,lg:"10px"}}},onClick:()=>i(!0),children:!o&&"Filter"})})]})]}),p.filter((e=>e.display)).map((e=>t.jsx(r,{display:h!==e.value&&"none",paddingX:2,paddingBottom:2,width:"100%",size:"grow",children:e.component},e.value)))]})}export{V as default};

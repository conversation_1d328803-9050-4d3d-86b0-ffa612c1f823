import React, { useEffect, useState } from "react";
import { Grid, Typography, IconButton, Skeleton } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import s3Controller from "../../../controllers/S3.controller";
import dayjs from "dayjs";
import { defaultValues, displayCoordinates } from "../../../utils";
import { UserProvider } from "../../../providers/UserProvider";
import AudioPlayer from "../../../components/AudioPlayer";

const AudioInfoWindow = ({ audioPoint, audioInfoWindow, user }) => {
    const [src, setSrc] = useState(null);

    const fetchSignedURL = async ({ audioPoint }) => {
        try {
            const url = await s3Controller.getSignedUrl({
                bucket_name: audioPoint.bucket_name,
                key: audioPoint.audio_path,
                region: audioPoint.aws_region,
            });
            console.log("");
            setSrc(url);
        } catch (err) {
            console.log("Error in fetch SignedUrl", err);
        }
    };

    useEffect(() => {
        if (audioPoint) {
            fetchSignedURL({ audioPoint });
        }
    }, [audioPoint]);

    const handleClose = () => {
        audioInfoWindow.close();
    };
    const hasLocation =
        audioPoint &&
        audioPoint.host_location &&
        Array.isArray(audioPoint.host_location.coordinates) &&
        audioPoint.host_location.coordinates.length > 0;
    return (
        <Grid
            container
            direction="column"
            sx={{
                padding: 2,
                backgroundColor: "#343B44",
                color: "white",
                borderRadius: 2,
                maxWidth: 330,
                gap: 2,
            }}
        >
            {/* Add custom styles for InfoWindow */}
            <style>
                {`
                    .gm-style-iw-chr, .gm-style-iw-tc {
                        display: none !important;
                    }
                    .gm-style .gm-style-iw-c {
                        background-color: #343B44 !important;
                        outline: none;
                        padding: 0;
                    }
                    .gm-style .gm-style-iw-d {
                        overflow: auto !important;
                    }
                    .gm-style .gm-style-iw-c .gm-style-iw-d::-webkit-scrollbar-thumb {
                        background-color: #fff !important;
                    }
                    .gm-style .gm-style-iw-d::-webkit-scrollbar-track, .gm-style .gm-style-iw-d::-webkit-scrollbar-track-piece {
                        background: #343B44 !important;
                    }
                    
                    audio {
                     background-color: #343B44 !important;
                    }
                `}
            </style>
            {/* Header */}
            <Grid container justifyContent="space-between" alignItems="center">
                <Typography variant="h6">{"Artifact Audio"}</Typography>
                <IconButton
                    onClick={handleClose}
                    sx={{
                        color: "white",
                        border: "1px solid white",
                        "&:hover": {
                            backgroundColor: "white",
                            color: "#4F5968",
                        },
                    }}
                >
                    <CloseIcon sx={{ fontSize: "14px" }} />
                </IconButton>
            </Grid>
            <Grid
                sx={{
                    position: "relative",
                    backgroundColor: "#343B44",
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    // height: 100,
                    borderRadius: 1,
                    marginBottom: "10px",
                    marginTop: "10px",
                }}
            >
                {src ? (
                    <AudioPlayer src={src} />
                ) : (
                    <Skeleton
                        sx={{
                            width: "100%",
                            height: "50px",
                        }}
                    />
                )}
            </Grid>
            {/* {src} */}
            <Grid>
                <Typography>
                    <strong>Frequency:</strong> {audioPoint.frequency || "Not available"}
                </Typography>
                {hasLocation && (
                    <Typography>
                        <strong>Location:</strong> {displayCoordinates(audioPoint.host_location.coordinates, !!(user && user.use_MGRS))}
                    </Typography>
                )}
                <Typography>
                    <strong>Timestamp</strong>:{" "}
                    {audioPoint.timestamp ? dayjs(audioPoint.timestamp).format(defaultValues.dateTimeFormat()) : "Not available"}{" "}
                </Typography>
            </Grid>
        </Grid>
    );
};

const WrappedAudioInfoWindow = (props) => (
    <UserProvider>
        <AudioInfoWindow {...props} />
    </UserProvider>
);

export default WrappedAudioInfoWindow;

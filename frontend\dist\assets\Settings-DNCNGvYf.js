import{r as e,j as t,G as o,a0 as i,T as r,am as a,ba as n,aI as s,bd as l,ac as c,bg as d,x as p,L as h}from"./vendor-B98I-pgv.js";import{e as m,u as x,t as u,K as F,c as f,E as g}from"./index-Cmi2ob6r.js";import{C as b}from"./ConfirmModal-C7H-ur5S.js";import{u as j}from"./AppHook-CvjturwY.js";import"./utils-D3r61PVZ.js";import"./maps--fsV2DPB.js";import"./charts-gTQAinvd.js";import"./ModalContainer-CTYPbNwV.js";const w=new class{async getLatestUserNotificationAlerts(){try{return await m.get("/notificationsAlerts/testing",{meta:{showSnackbar:!0}})}catch(e){return e.response.data}}};function S(){const{devMode:S,setDevMode:_,showIDs:y,setShowIDs:M}=j(),{user:k,fetchUser:C}=x(),[v,z]=e.useState(!1),[R,B]=e.useState(k?.date_time_format),[I,T]=e.useState(k?.home_port_filter_mode||"ONLY_NON_HOME_PORTS"),[D,W]=e.useState(!!k?.use_MGRS),[L,O]=e.useState(!1),[A,G]=e.useState({title:"",message:""}),[E,N]=e.useState(!1);e.useEffect((()=>{void 0!==k?.email_verification_enabled&&z(k.email_verification_enabled),B(k?.date_time_format),T(k?.home_port_filter_mode||"ONLY_NON_HOME_PORTS")}),[k]);const P=()=>{z(k.email_verification_enabled)},U=e.useMemo((()=>v!==k?.email_verification_enabled||R!==k?.date_time_format||D!==!!k?.use_MGRS||I!==k?.home_port_filter_mode||y!==("true"===localStorage.getItem("showIDs"))),[v,k?.email_verification_enabled,k?.date_time_format,R,D,k?.use_MGRS,I,k?.home_port_filter_mode,y]);return k?t.jsxs(o,{container:!0,overflow:"auto",height:"100%",width:"100%",flexDirection:{xs:"row",lg:"column"},paddingX:{xs:3,md:10},paddingY:{xs:5,md:6},sx:{backgroundColor:u.palette.custom.darkBlue},children:[t.jsxs(o,{container:!0,direction:"column",gap:"30px",children:[t.jsxs(o,{children:[t.jsx(r,{variant:"h4",component:"h1",color:"#FFFFFF",children:"Profile Settings"}),t.jsx(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"400",children:"Update your profile."})]}),t.jsxs(o,{container:!0,direction:"column",gap:"30px",children:[t.jsxs(o,{container:!0,paddingBottom:"20px",borderBottom:u.palette.custom.borderColor,borderRadius:"10px",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsx(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:"Two Factor Authentication"}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"You will have to enter OTP while logging into the account."})]}),t.jsx(o,{size:{md:"grow",sm:12},children:t.jsx(a,{enterDelay:300,title:k?.email?"":"Your Account is not linked with an email",placement:"bottom",children:t.jsx(n,{control:t.jsx(s,{checked:v,onChange:e=>{z(e.target.checked)},disabled:!k?.email,color:"primary",disableRipple:!0,sx:{height:"50px",width:"80px",borderRadius:"50px","& .MuiSwitch-switchBase":{padding:"15px 4px",transform:"translate(9px, -2px)"},"& .MuiSwitch-track":{backgroundColor:"#FFFFFF",height:"30px",borderRadius:"50px"},"& .Mui-checked+.MuiSwitch-track":{backgroundColor:u.palette.custom.mainBlue+" !important",opacity:"1 !important"},"& .Mui-checked.MuiSwitch-switchBase":{transform:"translate(36px, -2px)"},"& .MuiSwitch-thumb":{backgroundColor:"#FFFFFF",height:"28px",width:"28px"},"& .Mui-disabled":{opacity:.4},"& .Mui-disabled+.MuiSwitch-track":{opacity:"0.3 !important"}}}),label:"Enable Two Factor Authentication",sx:{"& .MuiFormControlLabel-label":{color:"#FFFFFF !important",fontSize:"18px",fontWeight:"400"}}})})})]}),t.jsxs(o,{container:!0,paddingBottom:"20px",borderBottom:u.palette.custom.borderColor,borderRadius:"10px",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsx(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:"Location Format"}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"Choose your preferred location format."})]}),t.jsx(o,{size:{md:"grow",sm:12},children:t.jsx(n,{control:t.jsx(s,{checked:D,onChange:e=>{W(e.target.checked)},color:"primary",disableRipple:!0,sx:{height:"50px",width:"80px",borderRadius:"50px","& .MuiSwitch-switchBase":{padding:"15px 4px",transform:"translate(9px, -2px)"},"& .MuiSwitch-track":{backgroundColor:u.palette.custom.mainBlue+" !important",height:"30px",borderRadius:"50px",opacity:"1 !important"},"& .Mui-checked+.MuiSwitch-track":{backgroundColor:u.palette.custom.mainBlue+" !important",opacity:"1 !important"},"& .Mui-checked.MuiSwitch-switchBase":{transform:"translate(36px, -2px)"},"& .MuiSwitch-thumb":{backgroundColor:"#FFFFFF",height:"28px",width:"28px"},"& .Mui-disabled":{opacity:.4},"& .Mui-disabled+.MuiSwitch-track":{opacity:"0.3 !important"}}}),label:D?"MGRS":"Lat/Lng",sx:{"& .MuiFormControlLabel-label":{color:"#FFFFFF !important",fontSize:"18px",fontWeight:"400"}}})})]}),t.jsxs(o,{container:!0,paddingBottom:"20px",borderBottom:u.palette.custom.borderColor,borderRadius:"10px",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsx(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:"Date/ Time Format"}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"Choose your preferred date/time format."})]}),t.jsx(o,{size:{md:"grow",sm:12},children:t.jsx(n,{control:t.jsx(l,{labelId:"duration-select-label",id:"duration-select",value:R??Object.keys(F)[0],onChange:e=>{B(e.target.value)},variant:"filled",children:Object.entries(F).map((([e,o])=>t.jsx(c,{value:e,children:o},e)))})})})]}),t.jsxs(o,{container:!0,paddingBottom:"20px",borderBottom:u.palette.custom.borderColor,borderRadius:"10px",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsx(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:"Home Port filtering"}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"View Artifacts that are near or away from home port."})]}),t.jsx(o,{size:{md:"grow",sm:12},children:t.jsx(n,{control:t.jsx(l,{labelId:"duration-select-label",id:"duration-select",value:I??Object.keys(f.homePortsFilterModes)[0],onChange:e=>{T(e.target.value)},variant:"filled",children:Object.entries(f.homePortsFilterModes).map((([e,o])=>t.jsx(c,{value:e,children:e},e)))})})})]})]}),t.jsx(o,{item:!0,container:!0,flexDirection:"column",gap:"30px",display:k&&k.role_id===g.super_admin?"flex":"none",children:t.jsxs(o,{children:[t.jsx(r,{variant:"h4",component:"h1",color:"#FFFFFF",children:"Dashboard Settings"}),t.jsx(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"400",children:"Customize the behavior of the dashboard."})]})}),t.jsxs(o,{container:!0,display:!k||k.role_id!==g.super_admin&&k.role_id!==g.internal_admin?"none":"flex",paddingBottom:"20px",borderBottom:e=>e.palette.custom.borderColor,borderRadius:"10px",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsxs(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:["Developer Mode",t.jsx(a,{enterDelay:300,title:"Shows technical details throughout the app, such as stream names and unit IDs on the map, stream pages and other developer related information as well. This mode also unlocks (shows) non-production units on certain screens (i.e. stream sensors list)\r\n                                        In certain cases it might show some new buggy features that are hidden only for devMode (currently none have been hidden).\r\n                                        This option is only available for super admins and internal admins.",children:t.jsx(d,{sx:{color:u.palette.custom.mediumGrey,backgroundColor:p(u.palette.custom.offline,0),fontSize:"18px",cursor:"pointer",marginLeft:"10px"}})})]}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"View technical details on the dashboard."})]}),t.jsx(o,{size:{md:"grow",sm:12},children:t.jsx(n,{control:t.jsx(s,{checked:S,onChange:e=>{return t=e.target.checked,_(t),void window.location.reload();var t},color:"primary",disableRipple:!0,sx:{height:"50px",width:"80px",borderRadius:"50px","& .MuiSwitch-switchBase":{padding:"15px 4px",transform:"translate(9px, -2px)"},"& .MuiSwitch-track":{backgroundColor:"#FFFFFF",height:"30px",borderRadius:"50px"},"& .Mui-checked+.MuiSwitch-track":{backgroundColor:e=>e.palette.custom.mainBlue+" !important",opacity:"1 !important"},"& .Mui-checked.MuiSwitch-switchBase":{transform:"translate(36px, -2px)"},"& .MuiSwitch-thumb":{backgroundColor:"#FFFFFF",height:"28px",width:"28px"},"& .Mui-disabled":{opacity:.4},"& .Mui-disabled+.MuiSwitch-track":{opacity:"0.3 !important"}}}),label:"Enable Developer Mode",sx:{"& .MuiFormControlLabel-label":{color:"#FFFFFF !important",fontSize:"18px",fontWeight:"400"}}})})]}),t.jsxs(o,{container:!0,paddingBottom:"20px",borderBottom:u.palette.custom.borderColor,borderRadius:"10px",display:!k||k.role_id!==g.super_admin&&k.role_id!==g.internal_admin?"none":"flex",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsxs(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:["Show IDs",t.jsx(a,{enterDelay:300,title:"Displays the vessel unit ID alongside the vessel name, but only in the stream vessel list.\r\n                                    This option is only available for super admins and internal admins.",children:t.jsx(d,{sx:{color:u.palette.custom.mediumGrey,backgroundColor:p(u.palette.custom.offline,.08),fontSize:"18px",cursor:"pointer",marginLeft:"10px"}})})]}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"Toggle to display Unit IDs in the vessel list."})]}),t.jsx(o,{size:{md:5,sm:12},children:t.jsx(n,{control:t.jsx(s,{checked:y,onChange:e=>{return t=e.target.checked,M(t),void localStorage.setItem("showIDs",t);var t},color:"primary",disableRipple:!0,sx:{height:"50px",width:"80px",borderRadius:"50px","& .MuiSwitch-switchBase":{padding:"15px 4px",transform:"translate(9px, -2px)"},"& .MuiSwitch-track":{backgroundColor:"#FFFFFF",height:"30px",borderRadius:"50px"},"& .Mui-checked+.MuiSwitch-track":{backgroundColor:u.palette.custom.mainBlue+" !important",opacity:"1 !important"},"& .Mui-checked.MuiSwitch-switchBase":{transform:"translate(36px, -2px)"},"& .MuiSwitch-thumb":{backgroundColor:"#FFFFFF",height:"28px",width:"28px"},"& .Mui-disabled":{opacity:.4},"& .Mui-disabled+.MuiSwitch-track":{opacity:"0.3 !important"}}}),label:"Enable Show IDs",sx:{"& .MuiFormControlLabel-label":{color:"#FFFFFF !important",fontSize:"18px",fontWeight:"400"}}})})]}),t.jsxs(o,{item:!0,container:!0,display:k&&k.permissions.some((e=>"TEST_NOTIFICATION_ALERTS"===e.permission_name))?"flex":"none",paddingBottom:"20px",borderBottom:e=>e.palette.custom.borderColor,borderRadius:"10px",children:[t.jsxs(o,{size:{md:5,sm:12},children:[t.jsxs(r,{component:"h6",color:"#FFFFFF",fontSize:"18px",fontWeight:"600",children:["Get Test Notification Alerts",t.jsx(a,{enterDelay:300,title:"This is used to verify if the notification system is working correctly. It's mainly for testing the flow from the microservice server.\r\n                                    This will deliver some testing notifications to the user's email.\r\n                                    This option is only available for users with TEST_NOTIFICATION_ALERTS permission.",children:t.jsx(d,{sx:{color:u.palette.custom.mediumGrey,backgroundColor:p(u.palette.custom.offline,.08),fontSize:"18px",cursor:"pointer",marginLeft:"10px"}})})]}),t.jsx(r,{component:"p",color:"#FFFFFF",fontSize:"14px",fontWeight:"400",children:"Get the test notification alerts manually by clicking this button."})]}),t.jsx(o,{size:{md:"grow",sm:12},children:t.jsx(h,{variant:"contained",onClick:async()=>{N(!0);try{const e=await w.getLatestUserNotificationAlerts();return e.status,N(!1),e}catch(e){return N(!1),e.response.data}},disabled:E,sx:{color:"#FFFFFF",fontSize:"16px",padding:"5px 20px",borderRadius:"10px",backgroundColor:E?u.palette.grey[500]:u.palette.primary.blue,"&:hover":{backgroundColor:E?u.palette.grey[500]:u.palette.primary.dark}},children:E?"Loading...":"Get Alerts"})})]}),U&&t.jsxs(o,{container:!0,justifyContent:"flex-end",gap:"20px",children:[t.jsx(h,{variant:"text",sx:{color:"#FFFFFF",fontSize:"16px",padding:"10px 30px"},onClick:P,children:"Cancel"}),t.jsx(h,{onClick:async()=>{if(v!==k?.email_verification_enabled){if(!(await(({title:e,message:t})=>new Promise((o=>{G({title:e,message:t,onConfirm:()=>{o(!0),O(!1)},onCancel:()=>{o(!1),O(!1)}}),O(!0)})))({title:"Confirm",message:t.jsxs(t.Fragment,{children:["Are you sure you want to ",t.jsx("b",{children:v?"Enable":"Disable"})," the Two Factor Authentication."]})})))return void P();m.patch("/users/userEmailVerification",null,{params:{email_verification_enabled:v}}).then((()=>C().catch(console.error))).catch(console.error)}if(R!==k?.date_time_format||D!==!!k?.use_MGRS||I!==k?.home_port_filter_mode){const e={};D!==k?.use_MGRS&&(e.use_MGRS=D),R!==k?.date_time_format&&(e.date_time_format=R);const t=I!==k?.home_port_filter_mode;t&&(e.home_port_filter_mode=I),m.patch("/users/updateSettings",null,{params:e}).then((()=>{C().catch(console.error),t&&window.location.reload()})).catch(console.error)}y!==("true"===localStorage.getItem("showIDs"))&&localStorage.setItem("showIDs",y)},variant:"contained",sx:{color:"primary",fontSize:"16px",padding:"10px 30px",borderRadius:"5px"},children:"Save"})]})]}),t.jsx(b,{title:A.title,message:A.message,initialState:L,onClose:A.onCancel,onConfirm:A.onConfirm})]}):t.jsx(o,{container:!0,justifyContent:"center",alignItems:"center",height:"100%",sx:{backgroundColor:u.palette.custom.darkBlue},children:t.jsx(i,{size:30,sx:{color:"white"}})})}export{S as default};

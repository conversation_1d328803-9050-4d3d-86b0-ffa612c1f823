import { useEffect, useState, useMemo } from "react";
import { Grid } from "@mui/material";
import axiosInstance from "../../../axios";
import { useApp } from "../../../hooks/AppHook";
import CreateVesselModal from "./CreateVesselModal";
import EditVesselModal from "./EditVesselModal";
import DesktopVesselView from "./components/DesktopVesselView";
import MobileVesselView from "./components/MobileVesselView";
import { getSocket } from "../../../socket";
import useGroupRegions from "../../../hooks/GroupRegionHook";

export default function Vessels({ searchQuery, showAddVessel, setShowAddVessel, isVesselEditDisabled }) {
    const { isMobile, timezone } = useApp();
    const { regions } = useGroupRegions();
    const [isLoading, setIsLoading] = useState(true);
    const [vessels, setVessels] = useState([]);
    const [pagination, setPagination] = useState({
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
    });

    const [showEditModal, setShowEditModal] = useState(false);
    const [selectedVessel, setSelectedVessel] = useState(null);
    const [expandedRow, setExpandedRow] = useState({});
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState("");

    const [units, setUnits] = useState([]);
    const [unitsLoading, setUnitsLoading] = useState(false);
    const [assignedUnitIds, setAssignedUnitIds] = useState([]);
    const [assignedUnitIdsLoading, setAssignedUnitIdsLoading] = useState(false);

    useEffect(() => {
        const socket = getSocket();
        // fetchVessels();
        fetchUnits();
        fetchAssignedUnitIds();

        const handleVesselChange = () => {
            fetchVessels();
            fetchAssignedUnitIds();
        };

        socket.on("vessel/changed", handleVesselChange);

        return () => {
            socket.off("vessel/changed", handleVesselChange);
        };
    }, []);

    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearchQuery(searchQuery);
            setPagination((prev) => ({ ...prev, page: 1 }));
        }, 500);

        return () => clearTimeout(timer);
    }, [searchQuery]);

    const fetchVessels = async () => {
        try {
            setIsLoading(true);
            const { data } = await axiosInstance.get("/vesselManagement", {
                params: {
                    page: pagination.page,
                    limit: pagination.limit,
                    search: debouncedSearchQuery,
                },
            });

            if (Object.keys(data).length > 0) {
                const { vessels: vesselsData, pagination: paginationData } = data;
                setVessels(vesselsData);
                setPagination((prev) => {
                    if (
                        prev.page !== paginationData.page ||
                        prev.limit !== paginationData.limit ||
                        prev.total !== paginationData.total ||
                        prev.totalPages !== paginationData.totalPages
                    ) {
                        return paginationData;
                    }
                    return prev;
                });
            } else {
                console.error("Failed to fetch vessels");
            }
        } catch (error) {
            console.error("Error fetching vessels:", error);
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        fetchVessels();
    }, [pagination.page, pagination.limit, debouncedSearchQuery]);

    const fetchUnits = async () => {
        try {
            setUnitsLoading(true);
            const { data } = await axiosInstance.get("/vesselManagement/unitIds");
            setUnits(data || []);
        } catch (error) {
            console.error("Error fetching units:", error);
            setUnits([]);
        } finally {
            setUnitsLoading(false);
        }
    };

    const fetchAssignedUnitIds = async () => {
        try {
            setAssignedUnitIdsLoading(true);
            const { data } = await axiosInstance.get("/vesselManagement/assignedUnitIds");
            setAssignedUnitIds(data || []);
        } catch (error) {
            console.error("Error fetching assigned unit IDs:", error);
            setAssignedUnitIds([]);
        } finally {
            setAssignedUnitIdsLoading(false);
        }
    };

    const handlePageChange = (_, newPage) => {
        setPagination((prev) => ({ ...prev, page: newPage }));
    };

    const handlePageSizeChange = (event) => {
        setPagination((prev) => ({
            ...prev,
            limit: event.target.value,
            page: 1,
        }));
    };

    const handleExpandClick = (vessel) => {
        setExpandedRow(expandedRow._id === vessel._id ? {} : vessel);
    };

    const handleCreateVessel = async (vesselData) => {
        try {
            const { data } = await axiosInstance.post("/vesselManagement", vesselData);

            if (Object.keys(data).length > 0) {
                fetchAssignedUnitIds();
                return { success: true };
            } else {
                return { success: false, error: "Failed to create vessel" };
            }
        } catch (error) {
            console.error("Error creating vessel:", error);
            return { success: false, error: "An unexpected error occurred" };
        }
    };

    const handleEditVessel = async (vesselData) => {
        try {
            const { data } = await axiosInstance.put(`/vesselManagement/${selectedVessel._id}`, vesselData);

            if (Object.keys(data).length > 0) {
                setShowEditModal(false);
                setSelectedVessel(null);
                fetchAssignedUnitIds();
                return { success: true };
            } else {
                return { success: false, error: "Failed to update vessel" };
            }
        } catch (error) {
            console.error("Error updating vessel:", error);
            return { success: false, error: "An unexpected error occurred" };
        }
    };

    const handleEditClick = (vessel) => {
        setSelectedVessel(vessel);
        setShowEditModal(true);
    };

    const filteredVessels = useMemo(() => {
        return vessels.map((vessel, index) => ({
            ...vessel,
            serial: (pagination.page - 1) * pagination.limit + index + 1,
        }));
    }, [vessels, pagination.page, pagination.limit]);

    return (
        <Grid container direction="column" sx={{ color: "#FFFFFF", height: "100%" }}>
            {!isMobile && (
                <DesktopVesselView
                    isLoading={isLoading}
                    filteredVessels={filteredVessels}
                    pagination={pagination}
                    handlePageChange={handlePageChange}
                    handlePageSizeChange={handlePageSizeChange}
                    handleEditClick={handleEditClick}
                    timezone={timezone}
                    isVesselEditDisabled={isVesselEditDisabled}
                />
            )}

            {isMobile && (
                <MobileVesselView
                    isLoading={isLoading}
                    filteredVessels={filteredVessels}
                    pagination={pagination}
                    handlePageChange={handlePageChange}
                    handlePageSizeChange={handlePageSizeChange}
                    handleExpandClick={handleExpandClick}
                    handleEditClick={handleEditClick}
                    expandedRow={expandedRow}
                    timezone={timezone}
                    isVesselEditDisabled={isVesselEditDisabled}
                />
            )}

            {!isVesselEditDisabled && (
                <CreateVesselModal
                    open={showAddVessel}
                    onClose={() => setShowAddVessel(false)}
                    onSubmit={handleCreateVessel}
                    units={units}
                    unitsLoading={unitsLoading}
                    assignedUnitIds={assignedUnitIds}
                    assignedUnitIdsLoading={assignedUnitIdsLoading}
                    regions={regions}
                />
            )}

            {!isVesselEditDisabled && (
                <EditVesselModal
                    open={showEditModal}
                    onClose={() => {
                        setShowEditModal(false);
                        setSelectedVessel(null);
                    }}
                    vessel={selectedVessel}
                    onSubmit={handleEditVessel}
                    units={units}
                    unitsLoading={unitsLoading}
                    assignedUnitIds={assignedUnitIds}
                    assignedUnitIdsLoading={assignedUnitIdsLoading}
                    regions={regions}
                />
            )}
        </Grid>
    );
}

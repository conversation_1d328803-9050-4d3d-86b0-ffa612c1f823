import{r as e,ay as t,V as n,j as o,G as r,T as a,W as s,L as i,a0 as c}from"./vendor-B98I-pgv.js";import{u as l,t as d,e as p}from"./index-Cmi2ob6r.js";import"./utils-D3r61PVZ.js";import"./maps--fsV2DPB.js";import"./charts-gTQAinvd.js";const u=()=>{const[u,m]=e.useState(["","","","","",""]),[x,f]=e.useState(""),[h,g]=e.useState(!1),[j,y]=e.useState(0),[F,v]=e.useState(!1),S=t(),{login:b}=l(),W=n(),{username:C,password:T}=S.state||{},D=e.useRef();e.useEffect((()=>{x&&(clearTimeout(D.current),D.current=setTimeout((()=>f("")),3e3))}),[x]);const E=async()=>{try{await p.post("/users/sendEmailOTP",{username:C}),y(60),v(!0)}catch(e){f(e.response?.data?.message||"An error occurred")}};return e.useEffect((()=>{C?E():W("/login")}),[C]),e.useEffect((()=>{if(F){const e=setInterval((()=>{y((t=>t<=1?(clearInterval(e),v(!1),0):t-1))}),1e3);return()=>clearInterval(e)}}),[F]),o.jsxs(r,{container:!0,flexDirection:"column",gap:"32px",children:[o.jsxs(r,{container:!0,flexDirection:"column",color:"#FFFFFF",children:[o.jsx(a,{component:"h3",fontWeight:"600",fontSize:"46px",textAlign:"center",children:"Enter OTP Code"}),o.jsx(a,{component:"p",fontWeight:"500",fontSize:"24px",children:"Check your email"}),o.jsxs(a,{component:"p",fontWeight:"400",fontSize:"16px",children:["We've sent a 6-digit confirmation OTP code to ",o.jsx("strong",{children:C}),". Make sure you enter the correct code."]})]}),o.jsxs(r,{container:!0,flexDirection:"column",component:"form",onSubmit:async e=>{e.preventDefault(),g(!0);const t=u.join("");try{if(!C&&!T)return f("Username and password required. Please navigate to login page");await p.post("/users/emailOTPVerification",{otp:Number(t),username:C}),await b({username:C,password:T}),f("")}catch(n){f(n.response?.data?.message||"An error occurred")}finally{g(!1)}},gap:4,children:[o.jsx(r,{container:!0,justifyContent:"center",onPaste:e=>{const t=e.clipboardData.getData("text");if(/^\d{6}$/.test(t)){const e=t.split("");m(e),e.forEach(((t,n)=>{document.getElementById(`otp-input-${n}`).value=e[n]}))}e.preventDefault()},gap:"10px",children:u.map(((e,t)=>o.jsx(s,{id:`otp-input-${t}`,type:"text",value:e,onChange:e=>((e,t)=>{const n=e.target.value;if(/^[0-9]?$/.test(n)){const e=[...u];e[t]=n,m(e),n&&t<u.length-1&&document.getElementById(`otp-input-${t+1}`).focus()}})(e,t),maxLength:"1",variant:"outlined",sx:{width:"13%",backgroundColor:"transparent",border:"1px solid #818994",borderRadius:"5px",maxWidth:"80px",minWidth:"30px"},inputProps:{autoComplete:"off",inputMode:"numeric",pattern:"[0-9]*","aria-label":`OTP digit ${t+1}`,style:{color:"#FFFFFF",textAlign:"center",padding:0,height:"60px"},onKeyDown:e=>((e,t)=>{"Backspace"===e.key&&!u[t]&&t>0&&document.getElementById("otp-input-"+(t-1)).focus()})(e,t)}},t)))}),x&&o.jsx(r,{children:o.jsx(a,{color:"error",children:x})}),o.jsx(r,{children:o.jsx(i,{type:"submit",variant:"contained",className:"btn-login",fullWidth:!0,sx:{color:d.palette.primary.main,padding:"15px 10px",fontSize:"24px",fontWeight:"600"},disabled:h,endIcon:h&&o.jsx(c,{size:20}),children:"Confirm OTP"})}),o.jsx(r,{children:o.jsx(i,{variant:"text",fullWidth:!0,sx:{textDecoration:F?"none !important":"underline",color:F?"#717171 !important":"#FFFFFF",padding:0,fontSize:"20px",fontWeight:"500",textTransform:"none"},onClick:E,disabled:F,children:F?`Resend Code (${Math.floor(j/60)}:${String(j%60).padStart(2,"0")})`:"Resend Code"})})]})]})};export{u as default};

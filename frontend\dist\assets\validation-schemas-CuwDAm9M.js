import{ac as e,ad as r,ae as s,af as i,ag as a}from"./utils-D3r61PVZ.js";const d=e({email:s().required("Email is required").email("Enter a valid email"),role_id:i().strict(!0).required("Role is required"),allowed_vessels:r().of(s()).required("Vessel Id is required")}),u=e({firstName:s().required("Frist Name is required"),lastName:s().required("Last Name is required"),username:s().required("Username is required"),password:s().min(8,"Password should be of minimum 8 characters length").required("Password is required"),confirmPassword:s().oneOf([a("password"),null],"Passwords mismatch").required("Confirm password is required")}),m=e({name:s().required("Region name is required"),timezone:s().required("Timezone is required")});export{m as c,d as i,u as s};

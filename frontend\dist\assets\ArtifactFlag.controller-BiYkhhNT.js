import{e as t}from"./index-Cmi2ob6r.js";const a=new class{constructor(){this.userFlaggedArtifactIds=new Set}async flagArtifact(a){try{return(await t.post(`/artifacts/${a}/flag`)).data}catch(r){throw r}}async getFlaggedArtifacts(){try{const a=await t.get("/artifacts/flagged");return a.data||[]}catch(a){throw a}}async unflagArtifact(a){try{return(await t.post(`/artifacts/${a}/unflag`)).data}catch(r){throw r}}isArtifactFlaggedByUser(t){return this.userFlaggedArtifactIds.has(t)}async getUserFlaggedArtifactIds(){try{const a=(await t.get("/artifacts/flagged/user")).data.flaggedArtifactIds||[];return this.userFlaggedArtifactIds=new Set(a),a}catch(a){throw a}}async removeAllFlagsFromArtifact(a){try{return(await t.delete(`/artifacts/${a}/flags`)).data}catch(r){throw r}}};export{a};

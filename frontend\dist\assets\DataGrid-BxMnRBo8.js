import{r as e,z as t,cE as r,a3 as n,a2 as o,j as l,D as i,_ as a,a7 as s,a4 as u,a6 as c,cF as d,cG as p,a8 as f,bK as g,a5 as m,ca as h,bs as b,bv as w,bw as C,ae as v,ad as x,a9 as y,cH as S,cI as R,ab as I,cJ as M,g as k,a as P,cK as E,bu as F,bl as H,ao as D,x as T,cL as O,cM as L,cN as $,cO as z,bh as j,ac as V,cP as A,be as N,bt as G,ba as B,W,cQ as _,cR as U,a0 as K,cS as q,y as X,cT as Y,bA as Q,bc as Z,am as J,X as ee,Y as te,L as re,bd as ne,bb as oe,ai as le,b5 as ie,aR as ae,C as se,ak as ue}from"./vendor-B98I-pgv.js";function ce(e){return ce="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ce(e)}function de(e){var t=function(e,t){if("object"!=ce(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t);if("object"!=ce(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e,"string");return"symbol"==ce(t)?t:t+""}const pe=e.createContext(void 0);function fe(){const t=e.useContext(pe);if(void 0===t)throw new Error(["MUI X: Could not find the Data Grid context.","It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.","This can also happen if you are bundling multiple versions of the Data Grid."].join("\n"));return t}const ge=e.createContext(void 0),me=()=>{const t=e.useContext(ge);if(!t)throw new Error("MUI X: useGridRootProps should only be used inside the DataGrid, DataGridPro or DataGridPremium component.");return t},he=parseInt(e.version,10),be=t=>{if(he>=19){const e=e=>t(e,e.ref??null);return e.displayName=t.displayName??t.name,e}return e.forwardRef(t)};var we=Symbol("NOT_FOUND");var Ce=e=>Array.isArray(e)?e:[e];function ve(e){const t=Array.isArray(e[0])?e[0]:e;return function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every((e=>"function"==typeof e))){const r=e.map((e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e)).join(", ");throw new TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}var xe=(e,t)=>e===t;function ye(e,t){const r="object"==typeof t?t:{equalityCheck:t},{equalityCheck:n=xe,maxSize:o=1,resultEqualityCheck:l}=r,i=function(e){return function(t,r){if(null===t||null===r||t.length!==r.length)return!1;const{length:n}=t;for(let o=0;o<n;o++)if(!e(t[o],r[o]))return!1;return!0}}(n);let a=0;const s=o<=1?function(e){let t;return{get:r=>t&&e(t.key,r)?t.value:we,put(e,r){t={key:e,value:r}},getEntries:()=>t?[t]:[],clear(){t=void 0}}}(i):function(e,t){let r=[];function n(e){const n=r.findIndex((r=>t(e,r.key)));if(n>-1){const e=r[n];return n>0&&(r.splice(n,1),r.unshift(e)),e.value}return we}return{get:n,put:function(t,o){n(t)===we&&(r.unshift({key:t,value:o}),r.length>e&&r.pop())},getEntries:function(){return r},clear:function(){r=[]}}}(o,i);function u(){let t=s.get(arguments);if(t===we){if(t=e.apply(null,arguments),a++,l){const e=s.getEntries().find((e=>l(e.value,t)));e&&(t=e.value,0!==a&&a--)}s.put(arguments,t)}return t}return u.clearCache=()=>{s.clear(),u.resetResultsCount()},u.resultsCount=()=>a,u.resetResultsCount=()=>{a=0},u}var Se="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function Re(e,t={}){let r={s:0,v:void 0,o:null,p:null};const{resultEqualityCheck:n}=t;let o,l=0;function i(){let t=r;const{length:i}=arguments;for(let e=0,r=i;e<r;e++){const r=arguments[e];if("function"==typeof r||"object"==typeof r&&null!==r){let e=t.o;null===e&&(t.o=e=new WeakMap);const n=e.get(r);void 0===n?(t={s:0,v:void 0,o:null,p:null},e.set(r,t)):t=n}else{let e=t.p;null===e&&(t.p=e=new Map);const n=e.get(r);void 0===n?(t={s:0,v:void 0,o:null,p:null},e.set(r,t)):t=n}}const a=t;let s;if(1===t.s)s=t.v;else if(s=e.apply(null,arguments),l++,n){const e=o?.deref?.()??o;null!=e&&n(e,s)&&(s=e,0!==l&&l--);o="object"==typeof s&&null!==s||"function"==typeof s?new Se(s):s}return a.s=1,a.v=s,s}return i.clearCache=()=>{r={s:0,v:void 0,o:null,p:null},i.resetResultsCount()},i.resultsCount=()=>l,i.resetResultsCount=()=>{l=0},i}function Ie(e,...t){const r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,o=0,l={},i=e.pop();"object"==typeof i&&(l=i,i=e.pop()),function(e,t="expected a function, instead received "+typeof e){if("function"!=typeof e)throw new TypeError(t)}(i,`createSelector expects an output function after the inputs, but received: [${typeof i}]`);const a={...r,...l},{memoize:s,memoizeOptions:u=[],argsMemoize:c=Re,argsMemoizeOptions:d=[]}=a,p=Ce(u),f=Ce(d),g=ve(e),m=s((function(){return n++,i.apply(null,arguments)}),...p),h=c((function(){o++;const e=function(e,t){const r=[],{length:n}=e;for(let o=0;o<n;o++)r.push(e[o].apply(null,t));return r}(g,arguments);return t=m.apply(null,e),t}),...f);return Object.assign(h,{resultFunc:i,memoizedResultFunc:m,dependencies:g,dependencyRecomputations:()=>o,resetDependencyRecomputations:()=>{o=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:s,argsMemoize:c})};return Object.assign(n,{withTypes:()=>n}),n}var Me=Ie(Re),ke=Object.assign(((e,t=Me)=>{!function(e,t="expected an object, instead received "+typeof e){if("object"!=typeof e)throw new TypeError(t)}(e,"createStructuredSelector expects first argument to be an object where each property is a selector, instead received a "+typeof e);const r=Object.keys(e);return t(r.map((t=>e[t])),((...e)=>e.reduce(((e,t,n)=>(e[r[n]]=t,e)),{})))}),{withTypes:()=>ke});const Pe=Object.is;function Ee(e,t){if(e===t)return!0;if(!(e instanceof Object&&t instanceof Object))return!1;let r=0,n=0;for(const o in e){if(r+=1,!Pe(e[o],t[o]))return!1;if(!(o in t))return!1}for(const o in t)n+=1;return r===n}var Fe,He,De={exports:{}},Te={};var Oe=(He||(He=1,De.exports=function(){if(Fe)return Te;Fe=1;var e=t(),r="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},n=e.useState,o=e.useEffect,l=e.useLayoutEffect,i=e.useDebugValue;function a(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!r(e,n)}catch(o){return!0}}var s="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),s=n({inst:{value:r,getSnapshot:t}}),u=s[0].inst,c=s[1];return l((function(){u.value=r,u.getSnapshot=t,a(u)&&c({inst:u})}),[e,r,t]),o((function(){return a(u)&&c({inst:u}),e((function(){a(u)&&c({inst:u})}))}),[e]),i(r),r};return Te.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:s,Te}()),De.exports);function Le(e){return e.acceptsApiRef}function $e(e,t){return Le(t)?t(e):t(e.current.state)}function ze(e,t,r,n){return Le(t)?t(e,r):t(e.current.state,n)}const je=Object.is,Ve=Ee,Ae=(e,t)=>e===t||e.length===t.length&&e.every(((e,r)=>e===t[r])),Ne=()=>({state:null,equals:null,selector:null,args:void 0}),Ge=[],Be=()=>null,We=(t,n,o=je)=>{const l=r(Ne),i=null!==l.current.selector,[a,s]=e.useState(i?null:$e(t,n));l.current.state=a,l.current.equals=o,l.current.selector=n;const u=e.useCallback((()=>(l.current.subscription||(l.current.subscription=t.current.store.subscribe((()=>{const e=$e(t,l.current.selector);l.current.equals(l.current.state,e)||(l.current.state=e,s(e))}))),null)),Ge),c=e.useCallback((()=>()=>{l.current.subscription&&(l.current.subscription(),l.current.subscription=void 0)}),Ge);return Oe.useSyncExternalStore(c,u,Be),a},_e=(t,n,o=void 0,l=je)=>{const i=r(Ne),a=null!==i.current.selector,[s,u]=e.useState(a?null:ze(t,n,o,t.current.instanceId));i.current.state=s,i.current.equals=l,i.current.selector=n;const c=i.current.args;if(i.current.args=o,a&&!((e,t)=>{let r=Object.is;return t instanceof Array?r=Ae:t instanceof Object&&(r=Ve),r(e,t)})(c,o)){const e=ze(t,i.current.selector,i.current.args,t.current.instanceId);i.current.equals(i.current.state,e)||(i.current.state=e,u(e))}const d=e.useCallback((()=>(i.current.subscription||(i.current.subscription=t.current.store.subscribe((()=>{const e=ze(t,i.current.selector,i.current.args,t.current.instanceId);i.current.equals(i.current.state,e)||(i.current.state=e,u(e))}))),null)),Ge),p=e.useCallback((()=>()=>{i.current.subscription&&(i.current.subscription(),i.current.subscription=void 0)}),Ge);return Oe.useSyncExternalStore(p,d,Be),s},Ue=Ie({memoize:ye,memoizeOptions:{maxSize:1,equalityCheck:Object.is}}),Ke=new WeakMap;function qe(e){return"current"in e&&"instanceId"in e.current}const Xe={id:"default"},Ye=(e,t,r,n,o,l,...i)=>{if(i.length>0)throw new Error("Unsupported number of selectors");let a;if(e&&t&&r&&n&&o&&l)a=(i,a)=>{const s=qe(i),u=a??(s?i.current.instanceId:Xe),c=s?i.current.state:i,d=e(c,u),p=t(c,u),f=r(c,u),g=n(c,u),m=o(c,u);return l(d,p,f,g,m)};else if(e&&t&&r&&n&&o)a=(l,i)=>{const a=qe(l),s=i??(a?l.current.instanceId:Xe),u=a?l.current.state:l,c=e(u,s),d=t(u,s),p=r(u,s),f=n(u,s);return o(c,d,p,f)};else if(e&&t&&r&&n)a=(o,l)=>{const i=qe(o),a=l??(i?o.current.instanceId:Xe),s=i?o.current.state:o,u=e(s,a),c=t(s,a),d=r(s,a);return n(u,c,d)};else if(e&&t&&r)a=(n,o)=>{const l=qe(n),i=o??(l?n.current.instanceId:Xe),a=l?n.current.state:n,s=e(a,i),u=t(a,i);return r(s,u)};else{if(!e||!t)throw new Error("Missing arguments");a=(r,n)=>{const o=qe(r),l=n??(o?r.current.instanceId:Xe),i=o?r.current.state:r,a=e(i,l);return t(a)}}return a.acceptsApiRef=!0,a},Qe=(e,t,r,n,o,l,...i)=>{if(i.length>0)throw new Error("Unsupported number of selectors");let a;if(e&&t&&r&&n&&o&&l)a=(i,a,s)=>{const u=qe(i),c=s??(u?i.current.instanceId:Xe),d=u?i.current.state:i,p=e(d,a,c),f=t(d,a,c),g=r(d,a,c),m=n(d,a,c),h=o(d,a,c);return l(p,f,g,m,h,a)};else if(e&&t&&r&&n&&o)a=(l,i,a)=>{const s=qe(l),u=a??(s?l.current.instanceId:Xe),c=s?l.current.state:l,d=e(c,i,u),p=t(c,i,u),f=r(c,i,u),g=n(c,i,u);return o(d,p,f,g,i)};else if(e&&t&&r&&n)a=(o,l,i)=>{const a=qe(o),s=i??(a?o.current.instanceId:Xe),u=a?o.current.state:o,c=e(u,l,s),d=t(u,l,s),p=r(u,l,s);return n(c,d,p,l)};else if(e&&t&&r)a=(n,o,l)=>{const i=qe(n),a=l??(i?n.current.instanceId:Xe),s=i?n.current.state:n,u=e(s,o,a),c=t(s,o,a);return r(u,c,o)};else{if(!e||!t)throw new Error("Missing arguments");a=(r,n,o)=>{const l=qe(r),i=o??(l?r.current.instanceId:Xe),a=l?r.current.state:r,s=e(a,n,i);return t(s,n)}}return a.acceptsApiRef=!0,a},Ze=(...e)=>{const t=(t,r)=>{const n=qe(t),o=n?t.current.instanceId:r??Xe,l=n?t.current.state:t,i=Ke.get(o),a=i??new Map,s=a?.get(e);if(a&&s)return s(l,o);const u=Ue(...e);return i||Ke.set(o,a),a.set(e,u),u(l,o)};return t.acceptsApiRef=!0,t},Je=e=>e.dimensions,et=Ye(Je,(e=>e.columnsTotalWidth)),tt=e=>e.dimensions.rowHeight,rt=e=>e.dimensions.contentSize.height,nt=e=>e.dimensions.hasScrollX,ot=e=>e.dimensions.hasScrollY,lt=e=>e.dimensions.columnsTotalWidth<e.dimensions.viewportOuterSize.width,it=e=>e.dimensions.headerHeight,at=e=>e.dimensions.groupHeaderHeight,st=e=>e.dimensions.hasScrollY?e.dimensions.scrollbarSize:0,ut=e=>{const t=e.dimensions.hasScrollX?e.dimensions.scrollbarSize:0,r=e.dimensions.viewportOuterSize.height-e.dimensions.minimumSize.height>0;return!(0===t&&!r)};function ct(t){return e.memo(t,Ee)}const dt={noRowsLabel:"No rows",noResultsOverlayLabel:"No results found.",toolbarDensity:"Density",toolbarDensityLabel:"Density",toolbarDensityCompact:"Compact",toolbarDensityStandard:"Standard",toolbarDensityComfortable:"Comfortable",toolbarColumns:"Columns",toolbarColumnsLabel:"Select columns",toolbarFilters:"Filters",toolbarFiltersLabel:"Show filters",toolbarFiltersTooltipHide:"Hide filters",toolbarFiltersTooltipShow:"Show filters",toolbarFiltersTooltipActive:e=>1!==e?`${e} active filters`:`${e} active filter`,toolbarQuickFilterPlaceholder:"Search…",toolbarQuickFilterLabel:"Search",toolbarQuickFilterDeleteIconLabel:"Clear",toolbarExport:"Export",toolbarExportLabel:"Export",toolbarExportCSV:"Download as CSV",toolbarExportPrint:"Print",toolbarExportExcel:"Download as Excel",columnsManagementSearchTitle:"Search",columnsManagementNoColumns:"No columns",columnsManagementShowHideAllText:"Show/Hide All",columnsManagementReset:"Reset",columnsManagementDeleteIconLabel:"Clear",filterPanelAddFilter:"Add filter",filterPanelRemoveAll:"Remove all",filterPanelDeleteIconLabel:"Delete",filterPanelLogicOperator:"Logic operator",filterPanelOperator:"Operator",filterPanelOperatorAnd:"And",filterPanelOperatorOr:"Or",filterPanelColumns:"Columns",filterPanelInputLabel:"Value",filterPanelInputPlaceholder:"Filter value",filterOperatorContains:"contains",filterOperatorDoesNotContain:"does not contain",filterOperatorEquals:"equals",filterOperatorDoesNotEqual:"does not equal",filterOperatorStartsWith:"starts with",filterOperatorEndsWith:"ends with",filterOperatorIs:"is",filterOperatorNot:"is not",filterOperatorAfter:"is after",filterOperatorOnOrAfter:"is on or after",filterOperatorBefore:"is before",filterOperatorOnOrBefore:"is on or before",filterOperatorIsEmpty:"is empty",filterOperatorIsNotEmpty:"is not empty",filterOperatorIsAnyOf:"is any of","filterOperator=":"=","filterOperator!=":"!=","filterOperator>":">","filterOperator>=":">=","filterOperator<":"<","filterOperator<=":"<=",headerFilterOperatorContains:"Contains",headerFilterOperatorDoesNotContain:"Does not contain",headerFilterOperatorEquals:"Equals",headerFilterOperatorDoesNotEqual:"Does not equal",headerFilterOperatorStartsWith:"Starts with",headerFilterOperatorEndsWith:"Ends with",headerFilterOperatorIs:"Is",headerFilterOperatorNot:"Is not",headerFilterOperatorAfter:"Is after",headerFilterOperatorOnOrAfter:"Is on or after",headerFilterOperatorBefore:"Is before",headerFilterOperatorOnOrBefore:"Is on or before",headerFilterOperatorIsEmpty:"Is empty",headerFilterOperatorIsNotEmpty:"Is not empty",headerFilterOperatorIsAnyOf:"Is any of","headerFilterOperator=":"Equals","headerFilterOperator!=":"Not equals","headerFilterOperator>":"Greater than","headerFilterOperator>=":"Greater than or equal to","headerFilterOperator<":"Less than","headerFilterOperator<=":"Less than or equal to",filterValueAny:"any",filterValueTrue:"true",filterValueFalse:"false",columnMenuLabel:"Menu",columnMenuAriaLabel:e=>`${e} column menu`,columnMenuShowColumns:"Show columns",columnMenuManageColumns:"Manage columns",columnMenuFilter:"Filter",columnMenuHideColumn:"Hide column",columnMenuUnsort:"Unsort",columnMenuSortAsc:"Sort by ASC",columnMenuSortDesc:"Sort by DESC",columnHeaderFiltersTooltipActive:e=>1!==e?`${e} active filters`:`${e} active filter`,columnHeaderFiltersLabel:"Show filters",columnHeaderSortIconLabel:"Sort",footerRowSelected:e=>1!==e?`${e.toLocaleString()} rows selected`:`${e.toLocaleString()} row selected`,footerTotalRows:"Total Rows:",footerTotalVisibleRows:(e,t)=>`${e.toLocaleString()} of ${t.toLocaleString()}`,checkboxSelectionHeaderName:"Checkbox selection",checkboxSelectionSelectAllRows:"Select all rows",checkboxSelectionUnselectAllRows:"Unselect all rows",checkboxSelectionSelectRow:"Select row",checkboxSelectionUnselectRow:"Unselect row",booleanCellTrueLabel:"yes",booleanCellFalseLabel:"no",actionsCellMore:"more",pinToLeft:"Pin to left",pinToRight:"Pin to right",unpin:"Unpin",treeDataGroupingHeaderName:"Group",treeDataExpand:"see children",treeDataCollapse:"hide children",groupingColumnHeaderName:"Group",groupColumn:e=>`Group by ${e}`,unGroupColumn:e=>`Stop grouping by ${e}`,detailPanelToggle:"Detail panel toggle",expandDetailPanel:"Expand",collapseDetailPanel:"Collapse",MuiTablePagination:{},rowReorderingHeaderName:"Row reordering",aggregationMenuItemHeader:"Aggregation",aggregationFunctionLabelSum:"sum",aggregationFunctionLabelAvg:"avg",aggregationFunctionLabelMin:"min",aggregationFunctionLabelMax:"max",aggregationFunctionLabelSize:"size"};function pt(e){return o("MuiDataGrid",e)}const ft=n("MuiDataGrid",["actionsCell","aggregationColumnHeader","aggregationColumnHeader--alignLeft","aggregationColumnHeader--alignCenter","aggregationColumnHeader--alignRight","aggregationColumnHeaderLabel","autoHeight","autosizing","booleanCell","cell--editable","cell--editing","cell--flex","cell--textCenter","cell--textLeft","cell--textRight","cell--rangeTop","cell--rangeBottom","cell--rangeLeft","cell--rangeRight","cell--pinnedLeft","cell--pinnedRight","cell--selectionMode","cell","cellCheckbox","cellEmpty","cellSkeleton","cellOffsetLeft","checkboxInput","columnHeader","columnHeader--alignCenter","columnHeader--alignLeft","columnHeader--alignRight","columnHeader--dragging","columnHeader--moving","columnHeader--numeric","columnHeader--sortable","columnHeader--sorted","columnHeader--filtered","columnHeader--pinnedLeft","columnHeader--pinnedRight","columnHeader--last","columnHeader--lastUnpinned","columnHeader--siblingFocused","columnHeaderCheckbox","columnHeaderDraggableContainer","columnHeaderTitle","columnHeaderTitleContainer","columnHeaderTitleContainerContent","columnHeader--filledGroup","columnHeader--emptyGroup","columnHeaders","columnSeparator--resizable","columnSeparator--resizing","columnSeparator--sideLeft","columnSeparator--sideRight","columnSeparator","columnsManagement","columnsManagementRow","columnsManagementHeader","columnsManagementSearchInput","columnsManagementFooter","container--top","container--bottom","detailPanel","detailPanels","detailPanelToggleCell","detailPanelToggleCell--expanded","footerCell","panel","panelHeader","panelWrapper","panelContent","panelFooter","paper","editBooleanCell","editInputCell","filler","filler--borderBottom","filler--pinnedLeft","filler--pinnedRight","filterForm","filterFormDeleteIcon","filterFormLogicOperatorInput","filterFormColumnInput","filterFormOperatorInput","filterFormValueInput","filterIcon","footerContainer","headerFilterRow","iconButtonContainer","iconSeparator","main","main--hasPinnedRight","main--hasSkeletonLoadingOverlay","menu","menuIcon","menuIconButton","menuOpen","menuList","overlay","overlayWrapper","overlayWrapperInner","root","root--densityStandard","root--densityComfortable","root--densityCompact","root--disableUserSelection","root--noToolbar","row","row--editable","row--editing","row--firstVisible","row--lastVisible","row--dragging","row--dynamicHeight","row--detailPanelExpanded","row--borderBottom","rowReorderCellPlaceholder","rowCount","rowReorderCellContainer","rowReorderCell","rowReorderCell--draggable","rowSkeleton","scrollArea--left","scrollArea--right","scrollArea","scrollbar","scrollbar--vertical","scrollbar--horizontal","scrollbarFiller","scrollbarFiller--header","scrollbarFiller--borderTop","scrollbarFiller--borderBottom","scrollbarFiller--pinnedRight","selectedRowCount","sortIcon","toolbarContainer","toolbarFilterList","virtualScroller","virtualScroller--hasScrollX","virtualScrollerContent","virtualScrollerContent--overflowed","virtualScrollerRenderZone","pinnedColumns","withVerticalBorder","withBorderColor","cell--withRightBorder","cell--withLeftBorder","columnHeader--withRightBorder","columnHeader--withLeftBorder","treeDataGroupingCell","treeDataGroupingCellToggle","treeDataGroupingCellLoadingContainer","groupingCriteriaCell","groupingCriteriaCellToggle","groupingCriteriaCellLoadingContainer","pinnedRows","pinnedRows--top","pinnedRows--bottom","pinnedRowsRenderZone"]);class gt{constructor(e=1e3){this.timeouts=new Map,this.cleanupTimeout=1e3,this.cleanupTimeout=e}register(e,t,r){this.timeouts||(this.timeouts=new Map);const n=setTimeout((()=>{"function"==typeof t&&t(),this.timeouts.delete(r.cleanupToken)}),this.cleanupTimeout);this.timeouts.set(r.cleanupToken,n)}unregister(e){const t=this.timeouts.get(e.cleanupToken);t&&(this.timeouts.delete(e.cleanupToken),clearTimeout(t))}reset(){this.timeouts&&(this.timeouts.forEach(((e,t)=>{this.unregister({cleanupToken:t})})),this.timeouts=void 0)}}class mt{constructor(){this.registry=new FinalizationRegistry((e=>{"function"==typeof e&&e()}))}register(e,t,r){this.registry.register(e,t,r)}unregister(e){this.registry.unregister(e)}reset(){}}var ht=function(e){return e.DataGrid="DataGrid",e.DataGridPro="DataGridPro",e.DataGridPremium="DataGridPremium",e}(ht||{});class bt{}const wt=function(t){let r=0;return function(n,o,l,i){null===t.registry&&(t.registry="undefined"!=typeof FinalizationRegistry?new mt:new gt);const[a]=e.useState(new bt),s=e.useRef(null),u=e.useRef(null);u.current=l;const c=e.useRef(null);if(!s.current&&u.current){const e=(e,t,r)=>{t.defaultMuiPrevented||u.current?.(e,t,r)};s.current=n.current.subscribeEvent(o,e,i),r+=1,c.current={cleanupToken:r},t.registry.register(a,(()=>{s.current?.(),s.current=null,c.current=null}),c.current)}else!u.current&&s.current&&(s.current(),s.current=null,c.current&&(t.registry.unregister(c.current),c.current=null));e.useEffect((()=>{if(!s.current&&u.current){const e=(e,t,r)=>{t.defaultMuiPrevented||u.current?.(e,t,r)};s.current=n.current.subscribeEvent(o,e,i)}return c.current&&t.registry&&(t.registry.unregister(c.current),c.current=null),()=>{s.current?.(),s.current=null}}),[n,o,i])}}({registry:null}),Ct={isFirst:!0};function vt(e,t,r){wt(e,t,r,Ct)}const xt={compact:.7,comfortable:1.3,standard:1},yt=e=>e.density,St=Ye(yt,(e=>xt[e])),Rt=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","tabIndex","hasFocus","isValidating","debounceMs","isProcessingProps","onValueChange"],It=u(d,{name:"MuiDataGrid",slot:"EditInputCell",overridesResolver:(e,t)=>t.editInputCell})((({theme:e})=>i({},e.typography.body2,{padding:"1px 0","& input":{padding:"0 16px",height:"100%"}}))),Mt=be(((t,r)=>{const n=me(),{id:o,value:u,field:d,colDef:p,hasFocus:f,debounceMs:g=200,isProcessingProps:m,onValueChange:h}=t,b=a(t,Rt),w=fe(),C=e.useRef(null),[v,x]=e.useState(u),y=(e=>{const{classes:t}=e;return c({root:["editInputCell"]},pt,t)})(n),S=e.useCallback((async e=>{const t=e.target.value;h&&await h(e,t);const r=w.current.getColumn(d);let n=t;r.valueParser&&(n=r.valueParser(t,w.current.getRow(o),r,w)),x(n),w.current.setEditCellValue({id:o,field:d,value:n,debounceMs:g,unstable_skipValueParser:!0},e)}),[w,g,d,o,h]),R=w.current.unstable_getEditCellMeta(o,d);return e.useEffect((()=>{"debouncedSetEditCellValue"!==R?.changeReason&&x(u)}),[R,u]),s((()=>{f&&C.current.focus()}),[f]),l.jsx(It,i({inputRef:C,className:y.root,ownerState:n,fullWidth:!0,type:"number"===p.type?p.type:"text",value:v??"",onChange:S,endAdornment:m?l.jsx(n.slots.loadIcon,{fontSize:"small",color:"action"}):void 0},b,{ref:r}))})),kt=e=>e.rows,Pt=Ye(kt,(e=>e.totalRowCount)),Et=Ye(kt,(e=>e.loading)),Ft=Ye(kt,(e=>e.totalTopLevelRowCount)),Ht=Ye(kt,(e=>e.dataRowIdToModelLookup)),Dt=Ye(kt,(e=>e.dataRowIdToIdLookup)),Tt=Ye(kt,(e=>e.tree)),Ot=Ye(kt,(e=>e.groupsToFetch)),Lt=Ye(kt,(e=>e.groupingName)),$t=Ye(kt,(e=>e.treeDepths)),zt=Ze(kt,(e=>{const t=Object.entries(e.treeDepths);return 0===t.length?1:(t.filter((([,e])=>e>0)).map((([e])=>Number(e))).sort(((e,t)=>t-e))[0]??0)+1})),jt=Ye(kt,(e=>e.dataRowIds)),Vt=Ze(Ye(kt,(e=>e?.additionalRowGroups)),(e=>{const t=e?.pinnedRows;return{bottom:t?.bottom?.map((e=>({id:e.id,model:e.model??{}})))??[],top:t?.top?.map((e=>({id:e.id,model:e.model??{}})))??[]}})),At=Ye(Vt,(e=>(e?.top?.length||0)+(e?.bottom?.length||0))),Nt=(e,t)=>t&&e.length>1?[e[0]]:e,Gt=(e,t)=>r=>i({},r,{sorting:i({},r.sorting,{sortModel:Nt(e,t)})}),Bt=(e,t)=>{const r=e.map((e=>((e,t)=>{const r=t.current.getColumn(e.field);if(!r||null===e.sort)return null;let n;return n=r.getSortComparator?r.getSortComparator(e.sort):"desc"===e.sort?(...e)=>-1*r.sortComparator(...e):r.sortComparator,n?{getSortCellParams:e=>({id:e,field:r.field,rowNode:Tt(t)[e],value:t.current.getCellValue(e,r.field),api:t.current}),comparator:n}:null})(e,t))).filter((e=>!!e));return 0===r.length?null:e=>e.map((e=>({node:e,params:r.map((t=>t.getSortCellParams(e.id)))}))).sort(((e,t)=>{return n=e,o=t,r.reduce(((e,t,r)=>{if(0!==e)return e;const l=n.params[r],i=o.params[r];return t.comparator(l.value,i.value,l,i)}),0);var n,o})).map((e=>e.node.id))},Wt=(e,t)=>{const r=e.indexOf(t);return t&&-1!==r&&r+1!==e.length?e[r+1]:e[0]},_t=(e,t)=>null==e&&null!=t?-1:null==t&&null!=e?1:null==e&&null==t?0:null,Ut=new Intl.Collator,Kt=(e,t)=>{const r=_t(e,t);return null!==r?r:Number(e)-Number(t)},qt=(e,t)=>{const r=_t(e,t);return null!==r?r:e>t?1:e<t?-1:0},Xt=["item","applyValue","type","apiRef","focusElementRef","tabIndex","disabled","isFilterActive","clearButton","InputProps","variant"];function Yt(t){const{item:r,applyValue:n,type:o,apiRef:s,focusElementRef:u,tabIndex:c,disabled:d,clearButton:g,InputProps:m,variant:h="standard"}=t,b=a(t,Xt),w=p(),[C,v]=e.useState(Qt(r.value)),[x,y]=e.useState(!1),S=f(),R=me(),I=e.useCallback((e=>{const t=Qt(e.target.value);v(t),y(!0),w.start(R.filterDebounceMs,(()=>{const e=i({},r,{value:"number"!==o||Number.isNaN(Number(t))?t:Number(t),fromInput:S});n(e),y(!1)}))}),[w,R.filterDebounceMs,r,o,S,n]);return e.useEffect((()=>{r.fromInput===S&&null!=r.value||v(Qt(r.value))}),[S,r]),l.jsx(R.slots.baseTextField,i({id:S,label:s.current.getLocaleText("filterPanelInputLabel"),placeholder:s.current.getLocaleText("filterPanelInputPlaceholder"),value:C??"",onChange:I,variant:h,type:o||"text",InputProps:i({},x||g?{endAdornment:x?l.jsx(R.slots.loadIcon,{fontSize:"small",color:"action"}):g}:{},{disabled:d},m,{inputProps:i({tabIndex:c},m?.inputProps)}),InputLabelProps:{shrink:!0},inputRef:u},b,R.slotProps?.baseTextField))}function Qt(e){if(null!=e&&""!==e)return String(e)}function Zt(e){return"object"==typeof e&&null!==e}function Jt(e){return e.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")}const er=(e,t,r)=>Math.max(t,Math.min(r,e));function tr(e,t){return Array.from({length:t-e}).map(((t,r)=>e+r))}function rr(e,t){if(e===t)return!0;if(e&&t&&"object"==typeof e&&"object"==typeof t){if(e.constructor!==t.constructor)return!1;if(Array.isArray(e)){const r=e.length;if(r!==t.length)return!1;for(let n=0;n<r;n+=1)if(!rr(e[n],t[n]))return!1;return!0}if(e instanceof Map&&t instanceof Map){if(e.size!==t.size)return!1;const r=Array.from(e.entries());for(let e=0;e<r.length;e+=1)if(!t.has(r[e][0]))return!1;for(let e=0;e<r.length;e+=1){const n=r[e];if(!rr(n[1],t.get(n[0])))return!1}return!0}if(e instanceof Set&&t instanceof Set){if(e.size!==t.size)return!1;const r=Array.from(e.entries());for(let e=0;e<r.length;e+=1)if(!t.has(r[e][0]))return!1;return!0}if(ArrayBuffer.isView(e)&&ArrayBuffer.isView(t)){const r=e.length;if(r!==t.length)return!1;for(let n=0;n<r;n+=1)if(e[n]!==t[n])return!1;return!0}if(e.constructor===RegExp)return e.source===t.source&&e.flags===t.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===t.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===t.toString();const r=Object.keys(e),n=r.length;if(n!==Object.keys(t).length)return!1;for(let e=0;e<n;e+=1)if(!Object.prototype.hasOwnProperty.call(t,r[e]))return!1;for(let o=0;o<n;o+=1){const n=r[o];if(!rr(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function nr(e){return"function"==typeof structuredClone?structuredClone(e):JSON.parse(JSON.stringify(e))}const or=(e,t)=>r=>{e&&t(r)},lr=["item","applyValue","type","apiRef","focusElementRef","color","error","helperText","size","variant"],ir=["key"];function ar(t){const{item:r,applyValue:n,type:o,apiRef:s,focusElementRef:u,color:c,error:d,helperText:p,size:m,variant:h="standard"}=t,b=a(t,lr),w={color:c,error:d,helperText:p,size:m,variant:h},[C,v]=e.useState(r.value||[]),x=f(),y=me();e.useEffect((()=>{const e=r.value??[];v(e.map(String))}),[r.value]);const S=e.useCallback(((e,t)=>{v(t.map(String)),n(i({},r,{value:[...t.map((e=>"number"===o?Number(e):e))]}))}),[n,r,o]);return l.jsx(g,i({multiple:!0,freeSolo:!0,options:[],filterOptions:(e,t)=>{const{inputValue:r}=t;return null==r||""===r?[]:[r]},id:x,value:C,onChange:S,renderTags:(e,t)=>e.map(((e,r)=>{const n=t({index:r}),{key:o}=n,s=a(n,ir);return l.jsx(y.slots.baseChip,i({variant:"outlined",size:"small",label:e},s),o)})),renderInput:e=>l.jsx(y.slots.baseTextField,i({},e,{label:s.current.getLocaleText("filterPanelInputLabel"),placeholder:s.current.getLocaleText("filterPanelInputPlaceholder"),InputLabelProps:i({},e.InputLabelProps,{shrink:!0}),inputRef:u,type:o||"text"},w,y.slotProps?.baseTextField))},b))}var sr=function(e){return e.Cell="cell",e.Row="row",e}(sr||{}),ur=function(e){return e.Edit="edit",e.View="view",e}(ur||{}),cr=function(e){return e.Edit="edit",e.View="view",e}(cr||{}),dr=function(e){return e.And="and",e.Or="or",e}(dr||{}),pr=function(e){return e.enterKeyDown="enterKeyDown",e.cellDoubleClick="cellDoubleClick",e.printableKeyDown="printableKeyDown",e.deleteKeyDown="deleteKeyDown",e.pasteKeyDown="pasteKeyDown",e}(pr||{}),fr=function(e){return e.cellFocusOut="cellFocusOut",e.escapeKeyDown="escapeKeyDown",e.enterKeyDown="enterKeyDown",e.tabKeyDown="tabKeyDown",e.shiftTabKeyDown="shiftTabKeyDown",e}(fr||{}),gr=function(e){return e.enterKeyDown="enterKeyDown",e.cellDoubleClick="cellDoubleClick",e.printableKeyDown="printableKeyDown",e.deleteKeyDown="deleteKeyDown",e}(gr||{}),mr=function(e){return e.rowFocusOut="rowFocusOut",e.escapeKeyDown="escapeKeyDown",e.enterKeyDown="enterKeyDown",e.tabKeyDown="tabKeyDown",e.shiftTabKeyDown="shiftTabKeyDown",e}(mr||{});function hr(e){return void 0!==e.field}const br={filteredRowsLookup:{},filteredChildrenCountLookup:{},filteredDescendantCountLookup:{}},wr=()=>({items:[],logicOperator:dr.And,quickFilterValues:[],quickFilterLogicOperator:dr.And});function Cr(e){return{current:e.current.getPublicApi()}}let vr=function(e){return e.LEFT="left",e.RIGHT="right",e}({});const xr={left:[],right:[]},yr=e=>e.columns,Sr=Ye(yr,(e=>e.orderedFields)),Rr=Ye(yr,(e=>e.lookup)),Ir=Ze(Sr,Rr,((e,t)=>e.map((e=>t[e])))),Mr=Ye(yr,(e=>e.columnVisibilityModel)),kr=Ze(Ir,Mr,((e,t)=>e.filter((e=>!1!==t[e.field])))),Pr=Ze(kr,(e=>e.map((e=>e.field)))),Er=Ze(yr,(e=>e.pinnedColumns),Pr,(e=>e.isRtl),((e,t,r,n)=>{const o=function(e,t,r){if(!Array.isArray(e.left)&&!Array.isArray(e.right))return xr;if(0===e.left?.length&&0===e.right?.length)return xr;const n=(e,t)=>Array.isArray(e)?e.filter((e=>t.includes(e))):[],o=n(e.left,t),l=t.filter((e=>!o.includes(e))),i=n(e.right,l);if(r)return{left:i,right:o};return{left:o,right:i}}(t,r,n);return{left:o.left.map((t=>e.lookup[t])),right:o.right.map((t=>e.lookup[t]))}}));const Fr=Ze(kr,(e=>{const t=[];let r=0;for(let n=0;n<e.length;n+=1)t.push(r),r+=e[n].computedWidth;return t})),Hr=Ze(Ir,(e=>e.filter((e=>e.filterable)))),Dr=Ze(Ir,(e=>e.reduce(((e,t)=>(t.filterable&&(e[t.field]=t),e)),{}))),Tr=Ze(Ir,(e=>e.some((e=>void 0!==e.colSpan))));let Or;const Lr=(e,t)=>{const r=i({},e);if(null==r.id&&(r.id=Math.round(1e5*Math.random())),null==r.operator){const e=Rr(t)[r.field];r.operator=e&&e.filterOperators[0].value}return r},$r=(e,t,r)=>{const n=e.items.length>1;let o;o=n&&t?[e.items[0]]:e.items;const l=n&&o.some((e=>null==e.id));return o.some((e=>null==e.operator))||l?i({},e,{items:o.map((e=>Lr(e,r)))}):e.items!==o?i({},e,{items:o}):e},zr=(e,t,r)=>n=>i({},n,{filterModel:$r(e,t,r)}),jr=e=>"string"==typeof e?e.normalize("NFD").replace(/[\u0300-\u036f]/g,""):e,Vr=(e,t)=>{if(!e.field||!e.operator)return null;const r=t.current.getColumn(e.field);if(!r)return null;let n;if(r.valueParser){const o=r.valueParser;n=Array.isArray(e.value)?e.value?.map((e=>o(e,void 0,r,t))):o(e.value,void 0,r,t)}else n=e.value;const{ignoreDiacritics:o}=t.current.rootProps;o&&(n=jr(n));const l=i({},e,{value:n}),a=r.filterOperators;if(!a?.length)throw new Error(`MUI X: No filter operators found for column '${r.field}'.`);const s=a.find((e=>e.value===l.operator));if(!s)throw new Error(`MUI X: No filter operator found for column '${r.field}' and operator value '${l.operator}'.`);const u=Cr(t),c=s.getApplyFilterFn(l,r);return"function"!=typeof c?null:{item:l,fn:e=>{let n=t.current.getRowValue(e,r);return o&&(n=jr(n)),c(n,e,r,u)}}};let Ar=1;const Nr=(e,t,r)=>{const{items:n}=e,o=n.map((e=>Vr(e,t))).filter((e=>!!e));if(0===o.length)return null;if(r||!function(){if(void 0!==Or)return Or;try{Or=new Function("return true")()}catch(e){Or=!1}return Or}())return(e,t)=>{const r={};for(let n=0;n<o.length;n+=1){const l=o[n];t&&!t(l.item.field)||(r[l.item.id]=l.fn(e))}return r};const l=new Function("appliers","row","shouldApplyFilter",`"use strict";\n${o.map(((e,t)=>`const shouldApply${t} = !shouldApplyFilter || shouldApplyFilter(${JSON.stringify(e.item.field)});`)).join("\n")}\n\nconst result$$ = {\n${o.map(((e,t)=>`  ${JSON.stringify(String(e.item.id))}: !shouldApply${t} ? false : appliers[${t}].fn(row),`)).join("\n")}\n};\n\nreturn result$$;`.replaceAll("$$",String(Ar)));Ar+=1;return(e,t)=>l(o,e,t)},Gr=e=>e.quickFilterExcludeHiddenColumns??!0,Br=(e,t,r)=>{const n=Nr(e,t,r),o=((e,t)=>{const r=e.quickFilterValues?.filter(Boolean)??[];if(0===r.length)return null;const n=Gr(e)?Pr(t):Sr(t),o=[],{ignoreDiacritics:l}=t.current.rootProps,i=Cr(t);return n.forEach((e=>{const n=t.current.getColumn(e),a=n?.getApplyQuickFilterFn;a&&o.push({column:n,appliers:r.map((e=>{const t=l?jr(e):e;return{fn:a(t,n,i)}}))})})),function(e,n){const a={};e:for(let s=0;s<r.length;s+=1){const u=r[s];for(let r=0;r<o.length;r+=1){const{column:c,appliers:d}=o[r],{field:p}=c;if(n&&!n(p))continue;const f=d[s];let g=t.current.getRowValue(e,c);if(null!==f.fn&&(l&&(g=jr(g)),f.fn(g,e,c,i))){a[u]=!0;continue e}}a[u]=!1}return a}})(e,t);return function(e,t,r){r.passingFilterItems=n?.(e,t)??null,r.passingQuickFilterValues=o?.(e,t)??null}},Wr=e=>null!=e,_r=(e,t,r,n,o)=>{const l=((e,t,r)=>(e.cleanedFilterItems||(e.cleanedFilterItems=r.filter((e=>null!==Vr(e,t)))),e.cleanedFilterItems))(o,n,r.items),i=e.filter(Wr),a=t.filter(Wr);if(i.length>0){const e=e=>i.some((t=>t[e.id]));if((r.logicOperator??wr().logicOperator)===dr.And){if(!l.every(e))return!1}else{if(!l.some(e))return!1}}if(a.length>0&&null!=r.quickFilterValues){const e=e=>a.some((t=>t[e]));if((r.quickFilterLogicOperator??wr().quickFilterLogicOperator)===dr.And){if(!r.quickFilterValues.every(e))return!1}else{if(!r.quickFilterValues.some(e))return!1}}return!0},Ur=(e,t)=>r=>{if(!r.value)return null;const n=e?r.value:r.value.trim(),o=new RegExp(Jt(n),"i");return e=>{if(null==e)return t;const r=o.test(String(e));return t?!r:r}},Kr=(e,t)=>r=>{if(!r.value)return null;const n=e?r.value:r.value.trim(),o=new Intl.Collator(void 0,{sensitivity:"base",usage:"search"});return e=>{if(null==e)return t;const r=0===o.compare(n,e.toString());return t?!r:r}},qr=e=>()=>t=>{const r=""===t||null==t;return e?!r:r},Xr={width:100,minWidth:50,maxWidth:1/0,hideable:!0,sortable:!0,resizable:!0,filterable:!0,groupable:!0,pinnable:!0,aggregable:!0,editable:!1,sortComparator:(e,t)=>{const r=_t(e,t);return null!==r?r:"string"==typeof e?Ut.compare(e.toString(),t.toString()):e-t},type:"string",align:"left",filterOperators:((e=!1)=>[{value:"contains",getApplyFilterFn:Ur(e,!1),InputComponent:Yt},{value:"doesNotContain",getApplyFilterFn:Ur(e,!0),InputComponent:Yt},{value:"equals",getApplyFilterFn:Kr(e,!1),InputComponent:Yt},{value:"doesNotEqual",getApplyFilterFn:Kr(e,!0),InputComponent:Yt},{value:"startsWith",getApplyFilterFn:t=>{if(!t.value)return null;const r=e?t.value:t.value.trim(),n=new RegExp(`^${Jt(r)}.*$`,"i");return e=>null!=e&&n.test(e.toString())},InputComponent:Yt},{value:"endsWith",getApplyFilterFn:t=>{if(!t.value)return null;const r=e?t.value:t.value.trim(),n=new RegExp(`.*${Jt(r)}$`,"i");return e=>null!=e&&n.test(e.toString())},InputComponent:Yt},{value:"isEmpty",getApplyFilterFn:qr(!1),requiresFilterValue:!1},{value:"isNotEmpty",getApplyFilterFn:qr(!0),requiresFilterValue:!1},{value:"isAnyOf",getApplyFilterFn:t=>{if(!Array.isArray(t.value)||0===t.value.length)return null;const r=e?t.value:t.value.map((e=>e.trim())),n=new Intl.Collator(void 0,{sensitivity:"base",usage:"search"});return e=>null!=e&&r.some((t=>0===n.compare(t,e.toString()||"")))},InputComponent:ar}])(),renderEditCell:e=>l.jsx(Mt,i({},e)),getApplyQuickFilterFn:e=>{if(!e)return null;const t=new RegExp(Jt(e),"i");return(e,r,n,o)=>{let l=o.current.getRowFormattedValue(r,n);return o.current.ignoreDiacritics&&(l=jr(l)),null!=l&&t.test(l.toString())}}},Yr=["open","target","onClose","children","position","className","onExited"],Qr=u(C,{name:"MuiDataGrid",slot:"Menu",overridesResolver:(e,t)=>t.menu})((({theme:e})=>({zIndex:e.zIndex.modal,[`& .${ft.menuList}`]:{outline:0}}))),Zr={"bottom-start":"top left","bottom-end":"top right"};function Jr(t){const{open:r,target:n,onClose:o,children:u,position:d,className:p,onExited:f}=t,g=a(t,Yr),C=fe(),v=me(),x=(e=>{const{classes:t}=e;return c({root:["menu"]},pt,t)})(v),y=e.useRef(null);s((()=>{r?y.current=document.activeElement instanceof HTMLElement?document.activeElement:null:(y.current?.focus?.(),y.current=null)}),[r]),e.useEffect((()=>{const e=r?"menuOpen":"menuClose";C.current.publishEvent(e,{target:n})}),[C,r,n]);const S=e=>{e.target&&(n===e.target||n?.contains(e.target))||o(e)};return l.jsx(Qr,i({as:v.slots.basePopper,className:m(x.root,p),ownerState:v,open:r,anchorEl:n,transition:!0,placement:d},g,v.slotProps?.basePopper,{children:({TransitionProps:e,placement:t})=>{return l.jsx(h,{onClickAway:S,mouseEvent:"onMouseDown",children:l.jsx(b,i({},e,{style:{transformOrigin:Zr[t]},onExited:(r=e?.onExited,e=>{r&&r(),f&&f(e)}),children:l.jsx(w,{children:u})}))});var r}}))}const en=["api","colDef","id","hasFocus","isEditable","field","value","formattedValue","row","rowNode","cellMode","tabIndex","position","focusElementRef"];function tn(t){const{colDef:r,id:n,hasFocus:o,tabIndex:s,position:u="bottom-end",focusElementRef:c}=t,d=a(t,en),[p,g]=e.useState(-1),[m,h]=e.useState(!1),b=fe(),w=e.useRef(null),C=e.useRef(null),y=e.useRef(!1),S=e.useRef({}),R=v(),I=f(),M=f(),k=me();if(!(e=>"function"==typeof e.getActions)(r))throw new Error("MUI X: Missing the `getActions` property in the `GridColDef`.");const P=r.getActions(b.current.getRowParams(n)),E=P.filter((e=>!e.props.showInMenu)),F=P.filter((e=>e.props.showInMenu)),H=E.length+(F.length?1:0);e.useLayoutEffect((()=>{o||Object.entries(S.current).forEach((([e,t])=>{t?.stop({},(()=>{delete S.current[e]}))}))}),[o]),e.useEffect((()=>{if(p<0||!w.current)return;if(p>=w.current.children.length)return;w.current.children[p].focus({preventScroll:!0})}),[p]),e.useEffect((()=>{o||(g(-1),y.current=!1)}),[o]),e.useImperativeHandle(c,(()=>({focus(){if(!y.current){const e=P.findIndex((e=>!e.props.disabled));g(e)}}})),[P]),e.useEffect((()=>{p>=H&&g(H-1)}),[p,H]);const D=()=>{h(!1)},T=e=>t=>{S.current[e]=t},O=(e,t)=>r=>{g(e),y.current=!0,t&&t(r)};return l.jsxs("div",i({role:"menu",ref:w,tabIndex:-1,className:ft.actionsCell,onKeyDown:e=>{if(H<=1)return;const t=(e,r)=>{if(e<0||e>P.length)return e;const n=("left"===r?-1:1)*(R?-1:1);return P[e+n]?.props.disabled?t(e+n,r):e+n};let r=p;"ArrowRight"===e.key?r=t(p,"right"):"ArrowLeft"===e.key&&(r=t(p,"left")),r<0||r>=H||r!==p&&(e.preventDefault(),e.stopPropagation(),g(r))}},d,{children:[E.map(((t,r)=>e.cloneElement(t,{key:r,touchRippleRef:T(r),onClick:O(r,t.props.onClick),tabIndex:p===r?s:-1}))),F.length>0&&M&&l.jsx(k.slots.baseIconButton,i({ref:C,id:M,"aria-label":b.current.getLocaleText("actionsCellMore"),"aria-haspopup":"menu","aria-expanded":m,"aria-controls":m?I:void 0,role:"menuitem",size:"small",onClick:e=>{e.stopPropagation(),e.preventDefault(),m?D():(h(!0),g(H-1),y.current=!0)},touchRippleRef:T(M),tabIndex:p===E.length?s:-1},k.slotProps?.baseIconButton,{children:l.jsx(k.slots.moreActionsIcon,{fontSize:"small"})})),F.length>0&&l.jsx(Jr,{open:m,target:C.current,position:u,onClose:D,children:l.jsx(x,{id:I,className:ft.menuList,onKeyDown:e=>{"Tab"===e.key&&e.preventDefault(),["Tab","Escape"].includes(e.key)&&D()},"aria-labelledby":M,variant:"menu",autoFocusItem:!0,children:F.map(((t,r)=>e.cloneElement(t,{key:r,closeMenu:D})))})})]}))}const rn="actions",nn=i({},Xr,{sortable:!1,filterable:!1,aggregable:!1,width:100,display:"flex",align:"center",headerAlign:"center",headerName:"",disableColumnMenu:!0,disableExport:!0,renderCell:e=>l.jsx(tn,i({},e)),getApplyQuickFilterFn:void 0}),on="auto-generated-group-node-root",ln=Symbol("mui.id_autogenerated");const an=(e,t,r)=>{const n=t?t(e):e.id;return function(e,t,r="A row was provided without id in the rows prop:"){if(null==e)throw new Error(["MUI X: The Data Grid component requires all rows to have a unique `id` property.","Alternatively, you can use the `getRowId` prop to specify a custom id for each row.",r,JSON.stringify(t)].join("\n"))}(n,e,r),n},sn=({rows:e,getRowId:t,loading:r,rowCount:n})=>{const o={type:"full",rows:[]},l={},i={};for(let a=0;a<e.length;a+=1){const r=e[a],n=an(r,t);l[n]=r,i[n]=n,o.rows.push(n)}return{rowsBeforePartialUpdates:e,loadingPropBeforePartialUpdates:r,rowCountPropBeforePartialUpdates:n,updates:o,dataRowIdToIdLookup:i,dataRowIdToModelLookup:l}},un=({tree:e,rowCountProp:t=0})=>{const r=e[on];return Math.max(t,r.children.length+(null==r.footerId?0:1))},cn=({apiRef:e,rowCountProp:t=0,loadingProp:r,previousTree:n,previousTreeDepths:o,previousGroupsToFetch:l})=>{const a=e.current.caches.rows,{tree:s,treeDepths:u,dataRowIds:c,groupingName:d,groupsToFetch:p=[]}=e.current.applyStrategyProcessor("rowTreeCreation",{previousTree:n,previousTreeDepths:o,updates:a.updates,dataRowIdToIdLookup:a.dataRowIdToIdLookup,dataRowIdToModelLookup:a.dataRowIdToModelLookup,previousGroupsToFetch:l}),f=e.current.unstable_applyPipeProcessors("hydrateRows",{tree:s,treeDepths:u,dataRowIdToIdLookup:a.dataRowIdToIdLookup,dataRowIds:c,dataRowIdToModelLookup:a.dataRowIdToModelLookup});return e.current.caches.rows.updates={type:"partial",actions:{insert:[],modify:[],remove:[]},idToActionLookup:{}},i({},f,{totalRowCount:Math.max(t,f.dataRowIds.length),totalTopLevelRowCount:un({tree:f.tree,rowCountProp:t}),groupingName:d,loading:r,groupsToFetch:p})},dn=e=>"skeletonRow"===e.type||"footer"===e.type||"group"===e.type&&e.isAutoGenerated||"pinnedRow"===e.type&&e.isAutoGenerated,pn=(e,t,r)=>{const n=e[t];if("group"!==n.type)return[];const o=[];for(let l=0;l<n.children.length;l+=1){const t=n.children[l];r&&dn(e[t])||o.push(t);const i=pn(e,t,r);for(let e=0;e<i.length;e+=1)o.push(i[e])}return r||null==n.footerId||o.push(n.footerId),o},fn=({previousCache:e,getRowId:t,updates:r,groupKeys:n})=>{if("full"===e.updates.type)throw new Error("MUI X: Unable to prepare a partial update if a full update is not applied yet.");const o=new Map;r.forEach((e=>{const r=an(e,t,"A row was provided without id when calling updateRows():");o.has(r)?o.set(r,i({},o.get(r),e)):o.set(r,e)}));const l={type:"partial",actions:{insert:[...e.updates.actions.insert??[]],modify:[...e.updates.actions.modify??[]],remove:[...e.updates.actions.remove??[]]},idToActionLookup:i({},e.updates.idToActionLookup),groupKeys:n},a=i({},e.dataRowIdToModelLookup),s=i({},e.dataRowIdToIdLookup),u={insert:{},modify:{},remove:{}};o.forEach(((e,t)=>{const r=l.idToActionLookup[t];if("delete"===e._action){if("remove"===r||!a[t])return;return null!=r&&(u[r][t]=!0),l.actions.remove.push(t),delete a[t],void delete s[t]}const n=a[t];if(n)return"remove"===r?(u.remove[t]=!0,l.actions.modify.push(t)):null==r&&l.actions.modify.push(t),void(a[t]=i({},n,e));"remove"===r?(u.remove[t]=!0,l.actions.insert.push(t)):null==r&&l.actions.insert.push(t),a[t]=e,s[t]=t}));const c=Object.keys(u);for(let i=0;i<c.length;i+=1){const e=c[i],t=u[e];Object.keys(t).length>0&&(l.actions[e]=l.actions[e].filter((e=>!t[e])))}return{dataRowIdToModelLookup:a,dataRowIdToIdLookup:s,updates:l,rowsBeforePartialUpdates:e.rowsBeforePartialUpdates,loadingPropBeforePartialUpdates:e.loadingPropBeforePartialUpdates,rowCountPropBeforePartialUpdates:e.rowCountPropBeforePartialUpdates}},gn="var(--DataGrid-overlayHeight, calc(var(--height) * 2))";function mn(e,t,r){const n=[];return t.forEach((t=>{const o=an(t,r,"A row was provided without id when calling updateRows():"),l=e.current.getRowNode(o);if("pinnedRow"===l?.type){const r=e.current.caches.pinnedRows,n=r.idLookup[o];n&&(r.idLookup[o]=i({},n,t))}else n.push(t)})),n}const hn=(e,t,r)=>"number"==typeof e&&e>0?e:t,bn="__row_group_by_columns_group__",wn="__detail_panel_toggle__";let Cn=function(e){return e[e.NONE=0]="NONE",e[e.LEFT=1]="LEFT",e[e.RIGHT=2]="RIGHT",e[e.VIRTUAL=3]="VIRTUAL",e}({});const vn=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","hasFocus","tabIndex","hideDescendantCount"];function xn(t){const{value:r,rowNode:n}=t,o=a(t,vn),s=fe(),u=me(),d=(e=>{const{classes:t}=e;return c({root:["booleanCell"]},pt,t)})({classes:u.classes}),p=We(s,zt)>0&&"group"===n.type&&!1===u.treeData,f=e.useMemo((()=>r?u.slots.booleanCellTrueIcon:u.slots.booleanCellFalseIcon),[u.slots.booleanCellFalseIcon,u.slots.booleanCellTrueIcon,r]);return p&&void 0===r?null:l.jsx(f,i({fontSize:"small",className:d.root,titleAccess:s.current.getLocaleText(r?"booleanCellTrueLabel":"booleanCellFalseLabel"),"data-value":Boolean(r)},o))}const yn=e.memo(xn),Sn=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","tabIndex","className","hasFocus","isValidating","isProcessingProps","error","onValueChange"];function Rn(t){const{id:r,value:n,field:o,className:u,hasFocus:d,onValueChange:p}=t,g=a(t,Sn),h=fe(),b=e.useRef(null),w=f(),[C,v]=e.useState(n),x=me(),y=(e=>{const{classes:t}=e;return c({root:["editBooleanCell"]},pt,t)})({classes:x.classes}),S=e.useCallback((async e=>{const t=e.target.checked;p&&await p(e,t),v(t),await h.current.setEditCellValue({id:r,field:o,value:t},e)}),[h,o,r,p]);return e.useEffect((()=>{v(n)}),[n]),s((()=>{d&&b.current.focus()}),[d]),l.jsx("label",i({htmlFor:w,className:m(y.root,u)},g,{children:l.jsx(x.slots.baseCheckbox,i({id:w,inputRef:b,checked:Boolean(C),onChange:S,size:"small"},x.slotProps?.baseCheckbox))}))}const In=["item","applyValue","apiRef","focusElementRef","isFilterActive","clearButton","tabIndex","label","variant","InputLabelProps"],Mn=e=>"true"===String(e).toLowerCase()||"false"!==String(e).toLowerCase()&&void 0,kn=u("div")({display:"flex",alignItems:"center",width:"100%","& button":{margin:"auto 0px 5px 5px"}});function Pn(t){const{item:r,applyValue:n,apiRef:o,focusElementRef:s,clearButton:u,tabIndex:c,label:d,variant:p="standard"}=t,g=a(t,In),[m,h]=e.useState(Mn(r.value)),b=me(),w=f(),C=f(),v=b.slotProps?.baseSelect||{},x=v.native??!1,y=b.slotProps?.baseSelectOption||{},S=e.useCallback((e=>{const t=Mn(e.target.value);h(t),n(i({},r,{value:t}))}),[n,r]);e.useEffect((()=>{h(Mn(r.value))}),[r.value]);const R=d??o.current.getLocaleText("filterPanelInputLabel");return l.jsxs(kn,{children:[l.jsxs(b.slots.baseFormControl,{fullWidth:!0,children:[l.jsx(b.slots.baseInputLabel,i({},b.slotProps?.baseInputLabel,{id:w,shrink:!0,variant:p,children:R})),l.jsxs(b.slots.baseSelect,i({labelId:w,id:C,label:R,value:void 0===m?"":String(m),onChange:S,variant:p,notched:"outlined"===p||void 0,native:x,displayEmpty:!0,inputProps:{ref:s,tabIndex:c}},g,v,{children:[l.jsx(b.slots.baseSelectOption,i({},y,{native:x,value:"",children:o.current.getLocaleText("filterValueAny")})),l.jsx(b.slots.baseSelectOption,i({},y,{native:x,value:"true",children:o.current.getLocaleText("filterValueTrue")})),l.jsx(b.slots.baseSelectOption,i({},y,{native:x,value:"false",children:o.current.getLocaleText("filterValueFalse")}))]}))]}),u]})}const En=i({},Xr,{type:"boolean",display:"flex",align:"center",headerAlign:"center",renderCell:e=>e.field!==bn&&dn(e.rowNode)?"":l.jsx(yn,i({},e)),renderEditCell:e=>l.jsx(Rn,i({},e)),sortComparator:Kt,valueFormatter:(e,t,r,n)=>e?n.current.getLocaleText("booleanCellTrueLabel"):n.current.getLocaleText("booleanCellFalseLabel"),filterOperators:[{value:"is",getApplyFilterFn:e=>{const t=Mn(e.value);return void 0===t?null:e=>Boolean(e)===t},InputComponent:Pn}],getApplyQuickFilterFn:void 0,aggregable:!1,pastedValueParser:e=>(e=>{switch(e.toLowerCase().trim()){case"true":case"yes":case"1":return!0;case"false":case"no":case"0":case"null":case"undefined":return!1;default:return}})(e)}),Fn=e=>e.sorting,Hn=Ye(Fn,(e=>e.sortedRows)),Dn=Ze(Hn,Ht,Tt,((e,t,r)=>e.reduce(((e,n)=>{const o=t[n];if(o)e.push({id:n,model:o});else{const t=r[n];t&&dn(t)&&e.push({id:n,model:{[ln]:n}})}return e}),[]))),Tn=Ye(Fn,(e=>e.sortModel)),On=Ze(Tn,(e=>e.reduce(((t,r,n)=>(t[r.field]={sortDirection:r.sort,sortIndex:e.length>1?n+1:void 0},t)),{})));Ze(Hn,(e=>e.reduce(((e,t,r)=>(e[t]=r,e)),Object.create(null))));const Ln=e=>e.filter,$n=Ye(Ln,(e=>e.filterModel)),zn=Ye($n,(e=>e.quickFilterValues)),jn=Ye(Ln,(e=>e.filteredRowsLookup));Ye(Ln,(e=>e.filteredChildrenCountLookup)),Ye(Ln,(e=>e.filteredDescendantCountLookup));const Vn=Ze((e=>e.visibleRowsLookup),Dn,zt,$n,zn,((e,t,r,n,o)=>r<2&&!n.items.length&&!o?.length?t:t.filter((t=>!1!==e[t.id])))),An=Ze(Vn,(e=>e.map((e=>e.id)))),Nn=Ze(jn,Dn,((e,t)=>t.filter((t=>!1!==e[t.id])))),Gn=Ze(Nn,(e=>e.map((e=>e.id))));Ze(An,Tt,((e,t)=>{const r={};let n=0;return e.reduce(((e,o)=>{const l=t[o];return r[l.depth]||(r[l.depth]=0),l.depth>n&&(r[l.depth]=0),n=l.depth,r[l.depth]+=1,e[o]=r[l.depth],e}),{})}));const Bn=Ze(Vn,Tt,zt,((e,t,r)=>r<2?e:e.filter((e=>0===t[e.id]?.depth)))),Wn=Ye(Vn,(e=>e.length)),_n=Ye(Bn,(e=>e.length)),Un=Ye(Nn,(e=>e.length));Ye(Un,_n,((e,t)=>e-t));const Kn=Ze($n,Rr,((e,t)=>e.items?.filter((e=>{if(!e.field)return!1;const r=t[e.field];if(!r?.filterOperators||0===r?.filterOperators?.length)return!1;const n=r.filterOperators.find((t=>t.value===e.operator));return!!n&&(!n.InputComponent||null!=e.value&&""!==e.value?.toString())})))),qn=Ze(Kn,(e=>e.reduce(((e,t)=>(e[t.field]?e[t.field].push(t):e[t.field]=[t],e)),{}))),Xn=e=>e.rowSelection,Yn=Ye(Xn,(e=>e.length)),Qn=Ze(Xn,Ht,((e,t)=>new Map(e.map((e=>[e,t[e]]))))),Zn=Ze(Xn,(e=>e.reduce(((e,t)=>(e[t]=t,e)),{})));function Jn(e,t){const r=Tt(e),n=Hn(e),o=jn(e),l=r[t];if(!l||"group"!==l.type)return[];const i=[];for(let a=n.findIndex((e=>e===t))+1;a<n.length&&r[n[a]]?.depth>l.depth;a+=1){const t=n[a];!1!==o[t]&&e.current.isRowSelectable(t)&&i.push(t)}return i}function eo(e){return e.signature===ht.DataGrid?e.checkboxSelection&&!0!==e.disableMultipleRowSelection:!e.disableMultipleRowSelection}const to=(e,t,r,n,o,l,i=new Set(Xn(e.current.state)))=>{const a=jn(e),s=new Set([]);if(n||o){if(n){const n=t[r];if("group"===n?.type){Jn(e,r).forEach((e=>{l(e),s.add(e)}))}}if(o){const n=e=>{if(!i.has(e)&&!s.has(e))return!1;const r=t[e];return!!r&&("group"!==r.type||r.children.every(n))},o=r=>{const i=((e,t,r)=>{const n=e[r];if(!n)return[];const o=n.parent;return null==o?[]:e[o].children.filter((e=>e!==r&&!1!==t[e]))})(t,a,r);if(0===i.length||i.every(n)){const n=t[r],i=n?.parent;null!=i&&i!==on&&e.current.isRowSelectable(i)&&(l(i),s.add(i),o(i))}};o(r)}}},ro=(e,t,r,n,o,l)=>{const i=Zn(e);if(o||n){if(o){const e=((e,t)=>{const r=[];let n=t;for(;null!=n&&n!==on;){const t=e[n];if(!t)return r;r.push(n),n=t.parent}return r})(t,r);e.forEach((e=>{i[e]===e&&l(e)}))}if(n){const n=t[r];if("group"===n?.type){Jn(e,r).forEach((e=>{l(e)}))}}}},no=["field","id","formattedValue","row","rowNode","colDef","isEditable","cellMode","hasFocus","tabIndex","api"],oo=be((function(t,r){const{field:n,id:o,rowNode:s,hasFocus:u,tabIndex:d}=t,p=a(t,no),f=fe(),g=me(),m=(e=>{const{classes:t}=e;return c({root:["checkboxInput"]},pt,t)})({classes:g.classes}),h=e.useRef(null),b=e.useRef(null),w=y(h,r);e.useLayoutEffect((()=>{if(0===d){const e=f.current.getCellElement(o,n);e&&(e.tabIndex=-1)}}),[f,d,o,n]),e.useEffect((()=>{if(u){const e=h.current?.querySelector("input");e?.focus({preventScroll:!0})}else b.current&&b.current.stop({})}),[u]);const C=e.useCallback((e=>{" "===e.key&&e.stopPropagation()}),[]),v=f.current.isRowSelectable(o),x=(S=o,R=g.rowSelectionPropagation?.parents??!1,Ye(Tt,Hn,jn,Zn,((e,t,r,n)=>{const o=e[S];if(!o||"group"!==o.type)return{isIndeterminate:!1,isChecked:n[S]===S};if(n[S]===S)return{isIndeterminate:!1,isChecked:!0};let l=0,i=0;for(let a=t.findIndex((e=>e===S))+1;a<t.length&&e[t[a]]?.depth>o.depth;a+=1){const e=t[a];!1!==r[e]&&(l+=1,void 0!==n[e]&&(i+=1))}return{isIndeterminate:i>0&&(i<l||void 0===n[S]),isChecked:R?i>0:n[S]===S}})));var S,R;const{isIndeterminate:I,isChecked:M}=We(f,x,Ve);if("footer"===s.type||"pinnedRow"===s.type)return null;const k="select"===g.indeterminateCheckboxAction?M&&!I:M,P=f.current.getLocaleText(k?"checkboxSelectionUnselectRow":"checkboxSelectionSelectRow");return l.jsx(g.slots.baseCheckbox,i({tabIndex:d,checked:k,onChange:e=>{const t={value:e.target.checked,id:o};f.current.publishEvent("rowSelectionCheckboxChange",t,e)},className:m.root,inputProps:{"aria-label":P,name:"select_row"},onKeyDown:C,indeterminate:I,disabled:!v,touchRippleRef:b},g.slotProps?.baseCheckbox,p,{ref:w}))})),lo=oo,io=e=>e.focus,ao=Ye(io,(e=>e.cell)),so=Ye(io,(e=>e.columnHeader));Ye(io,(e=>e.columnHeaderFilter));const uo=Ye(io,(e=>e.columnGroupHeader)),co=e=>e.tabIndex,po=Ye(co,(e=>e.cell)),fo=Ye(co,(e=>e.columnHeader));Ye(co,(e=>e.columnHeaderFilter));const go=Ye(co,(e=>e.columnGroupHeader));function mo(t,r,n){const o=e.useRef(!0);s((()=>{o.current=!1,t.current.register(n,r)}),[t,n,r]),o.current&&t.current.register(n,r)}function ho(t,r){const n=e.useRef(null);if(n.current)return n.current;const o=t.current.getLogger(r);return n.current=o,o}const bo=(e,t,r,n,o)=>{const l=ho(e,"useNativeEventListener");vt(e,"rootMount",(()=>{const e="function"==typeof t?t():t.current;if(e&&r&&n)return l.debug(`Binding native ${r} event`),e.addEventListener(r,n,o),()=>{l.debug(`Clearing native ${r} event`),e.removeEventListener(r,n,o)}}))},wo=t=>{const r=e.useRef(!0);r.current&&(r.current=!1,t())},Co=()=>{},vo=(e,t,r)=>t>0&&e>0?Math.ceil(e/t):-1===e?r+2:0,xo=e=>({page:0,pageSize:e?0:100}),yo=(e,t)=>{if(t===ht.DataGrid&&e>100)throw new Error(["MUI X: `pageSize` cannot exceed 100 in the MIT version of the DataGrid.","You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature."].join("\n"))},So=e=>e.pagination,Ro=Ye(So,(e=>e.enabled&&"client"===e.paginationMode)),Io=Ye(So,(e=>e.paginationModel)),Mo=Ye(So,(e=>e.rowCount)),ko=Ye(So,(e=>e.meta)),Po=Ye(Io,(e=>e.page)),Eo=Ye(Io,(e=>e.pageSize)),Fo=Ye(Io,Mo,((e,t)=>vo(t,e.pageSize,e.page))),Ho=Ze(Ro,Io,Tt,zt,Vn,Bn,((e,t,r,n,o,l)=>{if(!e)return null;const i=l.length,a=Math.min(t.pageSize*t.page,i-1),s=-1===t.pageSize?i-1:Math.min(a+t.pageSize-1,i-1);if(-1===a||-1===s)return null;if(n<2)return{firstRowIndex:a,lastRowIndex:s};const u=l[a],c=s-a+1,d=o.findIndex((e=>e.id===u.id));let p=d,f=0;for(;p<o.length&&f<=c;){const e=o[p],t=r[e.id]?.depth;void 0===t?p+=1:((f<c||t>0)&&(p+=1),0===t&&(f+=1))}return{firstRowIndex:d,lastRowIndex:p-1}})),Do=Ze(Vn,Ho,((e,t)=>t?e.slice(t.firstRowIndex,t.lastRowIndex+1):[])),To=Ze(An,Ho,((e,t)=>t?e.slice(t.firstRowIndex,t.lastRowIndex+1):[])),Oo=Ze(Ro,Ho,Do,Vn,((e,t,r,n)=>e?{rows:r,range:t,rowToIndexMap:r.reduce(((e,t,r)=>(e.set(t.model,r),e)),new Map)}:{rows:n,range:0===n.length?null:{firstRowIndex:0,lastRowIndex:n.length-1},rowToIndexMap:n.reduce(((e,t,r)=>(e.set(t.model,r),e)),new Map)})),Lo=["field","colDef"],$o=be((function(t,r){const n=a(t,Lo),[,o]=e.useState(!1),s=fe(),u=me(),d=(e=>{const{classes:t}=e;return c({root:["checkboxInput"]},pt,t)})({classes:u.classes}),p=We(s,fo),f=We(s,Xn),g=We(s,An),m=We(s,To),h=e.useMemo((()=>"function"!=typeof u.isRowSelectable?f:f.filter((e=>!!u.keepNonExistentRowsSelected||!!s.current.getRow(e)&&u.isRowSelectable(s.current.getRowParams(e))))),[s,u.isRowSelectable,f,u.keepNonExistentRowsSelected]),b=e.useMemo((()=>(u.pagination&&u.checkboxSelectionVisibleOnly&&"server"!==u.paginationMode?m:g).reduce(((e,t)=>(e[t]=!0,e)),{})),[u.pagination,u.paginationMode,u.checkboxSelectionVisibleOnly,m,g]),w=e.useMemo((()=>h.filter((e=>b[e])).length),[h,b]),C=w>0&&w<Object.keys(b).length,v=w>0,x=null!==p&&p.field===t.field?0:-1;e.useLayoutEffect((()=>{const e=s.current.getColumnHeaderElement(t.field);0===x&&e&&(e.tabIndex=-1)}),[x,s,t.field]);const y=e.useCallback((e=>{" "===e.key&&s.current.publishEvent("headerSelectionCheckboxChange",{value:!v})}),[s,v]),S=e.useCallback((()=>{o((e=>!e))}),[]);e.useEffect((()=>s.current.subscribeEvent("rowSelectionChange",S)),[s,S]);const R="select"===u.indeterminateCheckboxAction?v&&!C:v,I=s.current.getLocaleText(R?"checkboxSelectionUnselectAllRows":"checkboxSelectionSelectAllRows");return l.jsx(u.slots.baseCheckbox,i({indeterminate:C,checked:R,onChange:e=>{const t={value:e.target.checked};s.current.publishEvent("headerSelectionCheckboxChange",t)},className:d.root,inputProps:{"aria-label":I,name:"select_all_rows"},tabIndex:x,onKeyDown:y,disabled:!eo(u)},u.slotProps?.baseCheckbox,n,{ref:r}))})),zo=(e,t)=>ln in t?t[ln]:e.props.getRowId?e.props.getRowId(t):t.id,jo="__check__",Vo=i({},En,{type:"custom",field:jo,width:50,resizable:!1,sortable:!1,filterable:!1,aggregable:!1,disableColumnMenu:!0,disableReorder:!0,disableExport:!0,getApplyQuickFilterFn:void 0,display:"flex",valueGetter:(e,t,r,n)=>void 0!==Zn(n)[zo(n.current.state,t)],renderHeader:e=>l.jsx($o,i({},e)),renderCell:e=>l.jsx(lo,i({},e))}),Ao=["item","applyValue","type","apiRef","focusElementRef","InputProps","isFilterActive","clearButton","tabIndex","disabled"];function No(e,t){if(null==e)return"";const r=new Date(e);return Number.isNaN(r.getTime())?"":"date"===t?r.toISOString().substring(0,10):"datetime-local"===t?(r.setMinutes(r.getMinutes()-r.getTimezoneOffset()),r.toISOString().substring(0,19)):r.toISOString().substring(0,10)}function Go(t){const{item:r,applyValue:n,type:o,apiRef:s,focusElementRef:u,InputProps:c,clearButton:d,tabIndex:g,disabled:m}=t,h=a(t,Ao),b=p(),[w,C]=e.useState((()=>No(r.value,o))),[v,x]=e.useState(!1),y=f(),S=me(),R=e.useCallback((e=>{b.clear();const t=e.target.value;C(t),x(!0),b.start(S.filterDebounceMs,(()=>{const e=new Date(t);n(i({},r,{value:Number.isNaN(e.getTime())?void 0:e})),x(!1)}))}),[n,r,S.filterDebounceMs,b]);return e.useEffect((()=>{const e=No(r.value,o);C(e)}),[r.value,o]),l.jsx(S.slots.baseTextField,i({fullWidth:!0,id:y,label:s.current.getLocaleText("filterPanelInputLabel"),placeholder:s.current.getLocaleText("filterPanelInputPlaceholder"),value:w,onChange:R,variant:"standard",type:o||"text",InputLabelProps:{shrink:!0},inputRef:u,InputProps:i({},v||d?{endAdornment:v?l.jsx(S.slots.loadIcon,{fontSize:"small",color:"action"}):d}:{},{disabled:m},c,{inputProps:i({max:"datetime-local"===o?"9999-12-31T23:59":"9999-12-31",tabIndex:g},c?.inputProps)})},h,S.slotProps?.baseTextField))}function Bo(e,t,r,n){if(!e.value)return null;const o=new Date(e.value);r?o.setSeconds(0,0):(o.setMinutes(o.getMinutes()+o.getTimezoneOffset()),o.setHours(0,0,0,0));const l=o.getTime();return e=>{if(!e)return!1;if(n)return t(e.getTime(),l);const o=new Date(e);return r?o.setSeconds(0,0):o.setHours(0,0,0,0),t(o.getTime(),l)}}const Wo=e=>[{value:"is",getApplyFilterFn:t=>Bo(t,((e,t)=>e===t),e),InputComponent:Go,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"not",getApplyFilterFn:t=>Bo(t,((e,t)=>e!==t),e),InputComponent:Go,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"after",getApplyFilterFn:t=>Bo(t,((e,t)=>e>t),e),InputComponent:Go,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"onOrAfter",getApplyFilterFn:t=>Bo(t,((e,t)=>e>=t),e),InputComponent:Go,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"before",getApplyFilterFn:t=>Bo(t,((e,t)=>e<t),e,!e),InputComponent:Go,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"onOrBefore",getApplyFilterFn:t=>Bo(t,((e,t)=>e<=t),e),InputComponent:Go,InputComponentProps:{type:e?"datetime-local":"date"}},{value:"isEmpty",getApplyFilterFn:()=>e=>null==e,requiresFilterValue:!1},{value:"isNotEmpty",getApplyFilterFn:()=>e=>null!=e,requiresFilterValue:!1}],_o=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","tabIndex","hasFocus","inputProps","isValidating","isProcessingProps","onValueChange"],Uo=u(d)({fontSize:"inherit"});function Ko(t){const{id:r,value:n,field:o,colDef:u,hasFocus:d,inputProps:p,onValueChange:f}=t,g=a(t,_o),m="dateTime"===u.type,h=fe(),b=e.useRef(null),w=e.useMemo((()=>{let e,t;if(e=null==n?null:n instanceof Date?n:new Date((n??"").toString()),null==e||Number.isNaN(e.getTime()))t="";else{t=new Date(e.getTime()-60*e.getTimezoneOffset()*1e3).toISOString().substr(0,m?16:10)}return{parsed:e,formatted:t}}),[n,m]),[C,v]=e.useState(w),x=(e=>{const{classes:t}=e;return c({root:["editInputCell"]},pt,t)})({classes:me().classes}),y=e.useCallback((e=>{if(""===e)return null;const[t,r]=e.split("T"),[n,o,l]=t.split("-"),i=new Date;if(i.setFullYear(Number(n),Number(o)-1,Number(l)),i.setHours(0,0,0,0),r){const[e,t]=r.split(":");i.setHours(Number(e),Number(t),0,0)}return i}),[]),S=e.useCallback((async e=>{const t=e.target.value,n=y(t);f&&await f(e,n),v({parsed:n,formatted:t}),h.current.setEditCellValue({id:r,field:o,value:n},e)}),[h,o,r,f,y]);return e.useEffect((()=>{v((e=>w.parsed!==e.parsed&&w.parsed?.getTime()!==e.parsed?.getTime()?w:e))}),[w]),s((()=>{d&&b.current.focus()}),[d]),l.jsx(Uo,i({inputRef:b,fullWidth:!0,className:x.root,type:m?"datetime-local":"date",inputProps:i({max:m?"9999-12-31T23:59":"9999-12-31"},p),value:C.formatted,onChange:S},g))}const qo=e=>l.jsx(Ko,i({},e));function Xo({value:e,columnType:t,rowId:r,field:n}){if(!(e instanceof Date))throw new Error([`MUI X: \`${t}\` column type only accepts \`Date\` objects as values.`,"Use `valueGetter` to transform the value into a `Date` object.",`Row ID: ${r}, field: "${n}".`].join("\n"))}const Yo=i({},Xr,{type:"date",sortComparator:qt,valueFormatter:(e,t,r,n)=>{if(!e)return"";return Xo({value:e,columnType:"date",rowId:zo(n.current.state,t),field:r.field}),e.toLocaleDateString()},filterOperators:Wo(),renderEditCell:qo,pastedValueParser:e=>new Date(e)}),Qo=i({},Xr,{type:"dateTime",sortComparator:qt,valueFormatter:(e,t,r,n)=>{if(!e)return"";return Xo({value:e,columnType:"dateTime",rowId:zo(n.current.state,t),field:r.field}),e.toLocaleString()},filterOperators:Wo(!0),renderEditCell:qo,pastedValueParser:e=>new Date(e)}),Zo=e=>null==e?null:Number(e),Jo=i({},Xr,{type:"number",align:"right",headerAlign:"right",sortComparator:Kt,valueParser:e=>""===e?null:Number(e),valueFormatter:e=>function(e){return"number"==typeof e&&!Number.isNaN(e)}(e)?e.toLocaleString():e||"",filterOperators:[{value:"=",getApplyFilterFn:e=>null==e.value||Number.isNaN(e.value)?null:t=>Zo(t)===e.value,InputComponent:Yt,InputComponentProps:{type:"number"}},{value:"!=",getApplyFilterFn:e=>null==e.value||Number.isNaN(e.value)?null:t=>Zo(t)!==e.value,InputComponent:Yt,InputComponentProps:{type:"number"}},{value:">",getApplyFilterFn:e=>null==e.value||Number.isNaN(e.value)?null:t=>null!=t&&Zo(t)>e.value,InputComponent:Yt,InputComponentProps:{type:"number"}},{value:">=",getApplyFilterFn:e=>null==e.value||Number.isNaN(e.value)?null:t=>null!=t&&Zo(t)>=e.value,InputComponent:Yt,InputComponentProps:{type:"number"}},{value:"<",getApplyFilterFn:e=>null==e.value||Number.isNaN(e.value)?null:t=>null!=t&&Zo(t)<e.value,InputComponent:Yt,InputComponentProps:{type:"number"}},{value:"<=",getApplyFilterFn:e=>null==e.value||Number.isNaN(e.value)?null:t=>null!=t&&Zo(t)<=e.value,InputComponent:Yt,InputComponentProps:{type:"number"}},{value:"isEmpty",getApplyFilterFn:()=>e=>null==e,requiresFilterValue:!1},{value:"isNotEmpty",getApplyFilterFn:()=>e=>null!=e,requiresFilterValue:!1},{value:"isAnyOf",getApplyFilterFn:e=>Array.isArray(e.value)&&0!==e.value.length?t=>null!=t&&e.value.includes(Number(t)):null,InputComponent:ar,InputComponentProps:{type:"number"}}],getApplyQuickFilterFn:e=>null==e||Number.isNaN(e)||""===e?null:t=>Zo(t)===Zo(e)});function el(e){return"singleSelect"===e?.type}function tl(e,t){if(e)return"function"==typeof e.valueOptions?e.valueOptions(i({field:e.field},t)):e.valueOptions}function rl(e,t,r){if(void 0===t)return;const n=t.find((t=>{const n=r(t);return String(n)===String(e)}));return r(n)}const nl=["id","value","formattedValue","api","field","row","rowNode","colDef","cellMode","isEditable","tabIndex","className","hasFocus","isValidating","isProcessingProps","error","onValueChange","initialOpen"],ol=["MenuProps"];function ll(t){const r=me(),{id:n,value:o,field:u,row:c,colDef:d,hasFocus:p,error:f,onValueChange:g,initialOpen:m=r.editMode===sr.Cell}=t,h=a(t,nl),b=fe(),w=e.useRef(null),C=e.useRef(null),[v,x]=e.useState(m),y=(r.slotProps?.baseSelect||{}).native??!1,S=r.slotProps?.baseSelect||{},{MenuProps:R}=S,I=a(S,ol);if(s((()=>{p&&C.current?.focus()}),[p]),!el(d))return null;const M=tl(d,{id:n,row:c});if(!M)return null;const k=d.getOptionValue,P=d.getOptionLabel;return M&&d?l.jsx(r.slots.baseSelect,i({ref:w,inputRef:C,value:o,onChange:async e=>{if(!el(d)||!M)return;x(!1);const t=rl(e.target.value,M,k);g&&await g(e,t),await b.current.setEditCellValue({id:n,field:u,value:t},e)},open:v,onOpen:e=>{(function(e){return!!e.key})(e)&&"Enter"===e.key||x(!0)},MenuProps:i({onClose:(e,t)=>{if(r.editMode!==sr.Row){if("backdropClick"===t||"Escape"===e.key){const t=b.current.getCellParams(n,u);b.current.publishEvent("cellEditStop",i({},t,{reason:"Escape"===e.key?fr.escapeKeyDown:fr.cellFocusOut}))}}else x(!1)}},R),error:f,native:y,fullWidth:!0},h,I,{children:M.map((t=>{const n=k(t);return e.createElement(r.slots.baseSelectOption,i({},r.slotProps?.baseSelectOption||{},{native:y,key:n,value:n}),P(t))}))})):null}const il=["item","applyValue","type","apiRef","focusElementRef","placeholder","tabIndex","label","variant","isFilterActive","clearButton","InputLabelProps"],al=({column:t,OptionComponent:r,getOptionLabel:n,getOptionValue:o,isSelectNative:l,baseSelectOptionProps:a})=>["",...tl(t)||[]].map((t=>{const s=o(t);let u=n(t);return""===u&&(u=" "),e.createElement(r,i({},a,{native:l,key:s,value:s}),u)})),sl=u("div")({display:"flex",alignItems:"flex-end",width:"100%","& button":{margin:"auto 0px 5px 5px"}});function ul(t){const{item:r,applyValue:n,type:o,apiRef:s,focusElementRef:u,placeholder:c,tabIndex:d,label:p,variant:g="standard",clearButton:m}=t,h=a(t,il),b=r.value??"",w=f(),C=f(),v=me(),x=v.slotProps?.baseSelect?.native??!1;let y=null;if(r.field){const e=s.current.getColumn(r.field);el(e)&&(y=e)}const S=y?.getOptionValue,R=y?.getOptionLabel,I=e.useMemo((()=>tl(y)),[y]),M=e.useCallback((e=>{let t=e.target.value;t=rl(t,I,S),n(i({},r,{value:t}))}),[I,S,n,r]);if(!el(y))return null;const k=p??s.current.getLocaleText("filterPanelInputLabel");return l.jsxs(sl,{children:[l.jsxs(v.slots.baseFormControl,{fullWidth:!0,children:[l.jsx(v.slots.baseInputLabel,i({},v.slotProps?.baseInputLabel,{id:C,htmlFor:w,shrink:!0,variant:g,children:k})),l.jsx(v.slots.baseSelect,i({id:w,label:k,labelId:C,value:b,onChange:M,variant:g,type:o||"text",inputProps:{tabIndex:d,ref:u,placeholder:c??s.current.getLocaleText("filterPanelInputPlaceholder")},native:x,notched:"outlined"===g||void 0},h,v.slotProps?.baseSelect,{children:al({column:y,OptionComponent:v.slots.baseSelectOption,getOptionLabel:R,getOptionValue:S,isSelectNative:x,baseSelectOptionProps:v.slotProps?.baseSelectOption})}))]}),m]})}const cl=["item","applyValue","type","apiRef","focusElementRef","color","error","helperText","size","variant"],dl=["key"],pl=S();function fl(t){const{item:r,applyValue:n,apiRef:o,focusElementRef:s,color:u,error:c,helperText:d,size:p,variant:m="standard"}=t,h=a(t,cl),b={color:u,error:c,helperText:d,size:p,variant:m},w=f(),C=me();let v=null;if(r.field){const e=o.current.getColumn(r.field);el(e)&&(v=e)}const x=v?.getOptionValue,y=v?.getOptionLabel,S=e.useCallback(((e,t)=>x(e)===x(t)),[x]),R=e.useMemo((()=>tl(v)||[]),[v]),I=e.useMemo((()=>Array.isArray(r.value)?r.value.reduce(((e,t)=>{const r=R.find((e=>x(e)===t));return null!=r&&e.push(r),e}),[]):[]),[x,r.value,R]),M=e.useCallback(((e,t)=>{n(i({},r,{value:t.map(x)}))}),[n,r,x]);return l.jsx(g,i({multiple:!0,options:R,isOptionEqualToValue:S,filterOptions:pl,id:w,value:I,onChange:M,getOptionLabel:y,renderTags:(e,t)=>e.map(((e,r)=>{const n=t({index:r}),{key:o}=n,s=a(n,dl);return l.jsx(C.slots.baseChip,i({variant:"outlined",size:"small",label:y(e)},s),o)})),renderInput:e=>l.jsx(C.slots.baseTextField,i({},e,{label:o.current.getLocaleText("filterPanelInputLabel"),placeholder:o.current.getLocaleText("filterPanelInputPlaceholder"),InputLabelProps:i({},e.InputLabelProps,{shrink:!0}),inputRef:s,type:"singleSelect"},b,C.slotProps?.baseTextField))},h))}const gl=e=>null!=e&&Zt(e)?e.value:e,ml=i({},Xr,{type:"singleSelect",getOptionLabel:e=>Zt(e)?e.label:String(e),getOptionValue:e=>Zt(e)?e.value:e,valueFormatter(e,t,r,n){const o=zo(n.current.state,t);if(!el(r))return"";const l=tl(r,{id:o,row:t});if(null==e)return"";if(!l)return e;if("object"!=typeof l[0])return r.getOptionLabel(e);const i=l.find((t=>r.getOptionValue(t)===e));return i?r.getOptionLabel(i):""},renderEditCell:e=>l.jsx(ll,i({},e)),filterOperators:[{value:"is",getApplyFilterFn:e=>null==e.value||""===e.value?null:t=>gl(t)===gl(e.value),InputComponent:ul},{value:"not",getApplyFilterFn:e=>null==e.value||""===e.value?null:t=>gl(t)!==gl(e.value),InputComponent:ul},{value:"isAnyOf",getApplyFilterFn:e=>{if(!Array.isArray(e.value)||0===e.value.length)return null;const t=e.value.map(gl);return e=>t.includes(gl(e))},InputComponent:fl}],pastedValueParser:(e,t,r)=>{const n=r,o=tl(n)||[],l=n.getOptionValue;if(o.find((t=>l(t)===e)))return e}}),hl=e=>e.headerFiltering,bl=Ye(hl,(e=>e?.enabled??!1)),wl=Ye(hl,(e=>e.editing)),Cl=Ye(hl,(e=>e.menuOpen)),vl=e=>e.columnGrouping,xl=Ze(vl,(e=>e?.unwrappedGroupingModel??{})),yl=Ze(vl,(e=>e?.lookup??{})),Sl=Ze(vl,(e=>e?.headerStructure??[])),Rl=Ye(vl,(e=>e?.maxDepth??0)),Il=["maxWidth","minWidth","width","flex"],Ml={string:Xr,number:Jo,date:Yo,dateTime:Qo,boolean:En,singleSelect:ml,[rn]:nn,custom:Xr};const kl=(e,t)=>{const r={};let n=0,o=0;const l=[];e.orderedFields.forEach((t=>{let a=e.lookup[t],s=0,u=!1;!1!==e.columnVisibilityModel[t]&&(a.flex&&a.flex>0?(n+=a.flex,u=!0):s=er(a.width||Xr.width,a.minWidth||Xr.minWidth,a.maxWidth||Xr.maxWidth),o+=s),a.computedWidth!==s&&(a=i({},a,{computedWidth:s})),u&&l.push(a),r[t]=a}));const a=void 0===t?0:t.viewportOuterSize.width-(t.hasScrollY?t.scrollbarSize:0),s=Math.max(a-o,0);if(n>0&&a>0){const e=function({initialFreeSpace:e,totalFlexUnits:t,flexColumns:r}){const n=new Set(r.map((e=>e.field))),o={all:{},frozenFields:[],freeze:e=>{const t=o.all[e];t&&!0!==t.frozen&&(o.all[e].frozen=!0,o.frozenFields.push(e))}};return function l(){if(o.frozenFields.length===n.size)return;const i={min:{},max:{}};let a=e,s=t,u=0;o.frozenFields.forEach((e=>{a-=o.all[e].computedWidth,s-=o.all[e].flex}));for(let e=0;e<r.length;e+=1){const t=r[e];if(o.all[t.field]&&!0===o.all[t.field].frozen)continue;let n=a/s*t.flex;n<t.minWidth?(u+=t.minWidth-n,n=t.minWidth,i.min[t.field]=!0):n>t.maxWidth&&(u+=t.maxWidth-n,n=t.maxWidth,i.max[t.field]=!0),o.all[t.field]={frozen:!1,computedWidth:n,flex:t.flex}}u<0?Object.keys(i.max).forEach((e=>{o.freeze(e)})):u>0?Object.keys(i.min).forEach((e=>{o.freeze(e)})):r.forEach((({field:e})=>{o.freeze(e)})),l()}(),o.all}({initialFreeSpace:s,totalFlexUnits:n,flexColumns:l});Object.keys(e).forEach((t=>{r[t].computedWidth=e[t].computedWidth}))}return i({},e,{lookup:r})};function Pl(e){let t=Ml.string;return e&&Ml[e]&&(t=Ml[e]),t}const El=({apiRef:e,columnsToUpsert:t,initialState:r,columnVisibilityModel:n=Mr(e),keepOnlyColumnsToUpsert:o=!1})=>{const l=!e.current.state.columns;let a;if(l)a={orderedFields:[],lookup:{},columnVisibilityModel:n};else{const t=yr(e.current.state);a={orderedFields:o?[]:[...t.orderedFields],lookup:i({},t.lookup),columnVisibilityModel:n}}let s={};o&&!l&&(s=Object.keys(a.lookup).reduce(((e,t)=>i({},e,{[t]:!1})),{})),t.forEach((e=>{const{field:t}=e;s[t]=!0;let r=a.lookup[t];null==r?(r=i({},Pl(e.type),{field:t,hasBeenResized:!1}),a.orderedFields.push(t)):o&&a.orderedFields.push(t),r&&r.type!==e.type&&(r=i({},Pl(e.type),{field:t}));let n=r.hasBeenResized;Il.forEach((t=>{void 0!==e[t]&&(n=!0,-1===e[t]&&(e[t]=1/0))})),a.lookup[t]=R(r,i({},e,{hasBeenResized:n}))})),o&&!l&&Object.keys(a.lookup).forEach((e=>{s[e]||delete a.lookup[e]}));const u=((e,t)=>{if(!t)return e;const{orderedFields:r=[],dimensions:n={}}=t,o=Object.keys(n);if(0===o.length&&0===r.length)return e;const l={},a=[];for(let i=0;i<r.length;i+=1){const t=r[i];e.lookup[t]&&(l[t]=!0,a.push(t))}const s=0===a.length?e.orderedFields:[...a,...e.orderedFields.filter((e=>!l[e]))],u=i({},e.lookup);for(let c=0;c<o.length;c+=1){const e=o[c],t=i({},u[e],{hasBeenResized:!0});Object.entries(n[e]).forEach((([e,r])=>{t[e]=-1===r?1/0:r})),u[e]=t}return i({},e,{orderedFields:s,lookup:u})})(e.current.unstable_applyPipeProcessors("hydrateColumns",a),r);return kl(u,e.current.getRootDimensions?.()??void 0)};function Fl(e,t){if(t.unstable_listView)return 0;const r=St(e),n=Rl(e),o=bl(e);return Math.floor(t.columnHeaderHeight*r)+Math.floor((t.columnGroupHeaderHeight??t.columnHeaderHeight)*r)*n+(o?Math.floor((t.headerFilterHeight??t.columnHeaderHeight)*r):0)}const Hl=M("div",{name:"MuiDataGrid",slot:"ScrollArea",overridesResolver:(e,t)=>[{[`&.${ft["scrollArea--left"]}`]:t["scrollArea--left"]},{[`&.${ft["scrollArea--right"]}`]:t["scrollArea--right"]},t.scrollArea]})((()=>({position:"absolute",top:0,zIndex:101,width:20,bottom:0,[`&.${ft["scrollArea--left"]}`]:{left:0},[`&.${ft["scrollArea--right"]}`]:{right:0}}))),Dl=Qe(Je,((e,t)=>"left"===t?e.leftPinnedWidth:"right"===t?e.rightPinnedWidth+(e.hasScrollX?e.scrollbarSize:0):0));function Tl(t){const{scrollDirection:r,scrollPosition:n}=t,o=e.useRef(null),a=fe(),s=p(),u=We(a,St),d=We(a,et),f=_e(a,Dl,r),g=()=>{const e=Je(a.current.state);if("left"===r)return n.current.left>0;if("right"===r){const t=d-e.viewportInnerSize.width;return n.current.left<t}return!1},[h,b]=e.useState(g),w=me(),C=i({},w,{scrollDirection:r}),v=(e=>{const{scrollDirection:t,classes:r}=e;return c({root:["scrollArea",`scrollArea--${t}`]},pt,r)})(C),x=Fl(a,w),y=Math.floor(w.columnHeaderHeight*u),S={height:y,top:x-y};"left"===r?S.left=f:"right"===r&&(S.right=f);const R=I((e=>{let t;if(e.preventDefault(),"left"===r)t=e.clientX-o.current.getBoundingClientRect().right;else{if("right"!==r)throw new Error("MUI X: Wrong drag direction");t=Math.max(1,e.clientX-o.current.getBoundingClientRect().left)}t=1.5*(t-1)+1,s.start(0,(()=>{a.current.scroll({left:n.current.left+t,top:n.current.top})}))}));return wt(a,"scrollPositionChange",(()=>{b(g)})),h?l.jsx(Hl,{ref:o,className:m(v.root),ownerState:C,onDragOver:R,style:S}):null}const Ol=ct((function(t){const r=fe(),[n,o]=e.useState(!1);return wt(r,"columnHeaderDragStart",(()=>o(!0))),wt(r,"columnHeaderDragEnd",(()=>o(!1))),n?l.jsx(Tl,i({},t)):null}));var Ll,$l,zl,jl={},Vl={exports:{}},Al={exports:{}};function Nl(){return Ll||(Ll=1,function(e){function t(r){return e.exports=t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},e.exports.__esModule=!0,e.exports.default=e.exports,t(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(Al)),Al.exports}function Gl(){if(zl)return jl;zl=1;var e=($l||($l=1,function(e){var t=Nl().default;function r(n,o){if("function"==typeof WeakMap)var l=new WeakMap,i=new WeakMap;return(e.exports=r=function(e,r){if(!r&&e&&e.__esModule)return e;var n,o,a={__proto__:null,default:e};if(null===e||"object"!=t(e)&&"function"!=typeof e)return a;if(n=r?i:l){if(n.has(e))return n.get(e);n.set(e,a)}for(var s in e)"default"!==s&&{}.hasOwnProperty.call(e,s)&&((o=(n=Object.defineProperty)&&Object.getOwnPropertyDescriptor(e,s))&&(o.get||o.set)?n(a,s,o):a[s]=e[s]);return a},e.exports.__esModule=!0,e.exports.default=e.exports)(n,o)}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports}(Vl)),Vl.exports).default;Object.defineProperty(jl,"__esModule",{value:!0}),jl.default=void 0;var r=e(t());return jl.default=parseInt(r.version,10),jl}const Bl=k(Gl()),Wl=e.createContext(void 0);function _l(){const t=e.useContext(Wl);if(void 0===t)throw new Error(["MUI X: Could not find the Data Grid private context.","It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.","This can also happen if you are bundling multiple versions of the Data Grid."].join("\n"));return t}const Ul=(e,t)=>Oo(e),Kl=(e,t)=>We(e,Oo),ql=("undefined"!=typeof navigator?navigator.userAgent.toLowerCase():"empty").includes("firefox"),Xl=e=>e.rowsMeta,Yl=e=>e.virtualization;Ye(Yl,(e=>e.enabled));const Ql=Ye(Yl,(e=>e.enabledForColumns)),Zl=Ye(Yl,(e=>e.enabledForRows)),Jl=Ye(Yl,(e=>e.renderContext)),ei=Ze((e=>e.virtualization.renderContext.firstColumnIndex),(e=>e.virtualization.renderContext.lastColumnIndex),((e,t)=>({firstColumnIndex:e,lastColumnIndex:t}))),ti={firstRowIndex:0,lastRowIndex:0,firstColumnIndex:0,lastColumnIndex:0},ri=(e,t)=>{const{disableVirtualization:r,autoHeight:n}=t;return i({},e,{virtualization:{enabled:!r,enabledForColumns:!r,enabledForRows:!r&&!n,renderContext:ti}})};const ni=e=>e.rowSpanning,oi=Ye(ni,(e=>e.hiddenCells)),li=Ye(ni,(e=>e.spannedCells)),ii=Ye(ni,(e=>e.hiddenCellOriginMap)),ai=e=>e.listViewColumn,si=Ze(Me(ao,Jl,Oo,kr,Ht,((e,t,r,n,o)=>{if(!e)return!1;const l=o[e.id];if(!l)return!1;const i=r.rowToIndexMap.get(l),a=n.slice(t.firstColumnIndex,t.lastColumnIndex).findIndex((t=>t.field===e.field));return!(void 0!==i&&-1!==a&&i>=t.firstRowIndex&&i<=t.lastRowIndex)})),kr,Oo,Ht,ao,((e,t,r,n,o)=>{if(!e)return null;const l=n[o.id];if(!l)return null;const a=r.rowToIndexMap.get(l);if(void 0===a)return null;const s=t.findIndex((e=>e.field===o.field));return-1===s?null:i({},o,{rowIndex:a,columnIndex:s})}));function ui(e,t){return Math.round(e*10**t)/10**t}const ci="undefined"!=typeof window&&/jsdom|HappyDOM/.test(window.navigator.userAgent);var di=function(e){return e[e.NONE=0]="NONE",e[e.UP=1]="UP",e[e.DOWN=2]="DOWN",e[e.LEFT=3]="LEFT",e[e.RIGHT=4]="RIGHT",e}(di||{});const pi={top:0,left:0},fi=Object.freeze(new Map),gi=()=>{const t=_l(),n=me(),{unstable_listView:o}=n,a=We(t,(()=>o?[ai(t.current.state)]:kr(t))),u=We(t,Zl)&&!ci,c=We(t,Ql)&&!ci,d=We(t,Vt),f=Er(t),g=o?xr:f,m=d.bottom.length>0,[h,b]=e.useState(fi),w=v(),C=We(t,Zn),x=Kl(t),y=t.current.mainElementRef,S=t.current.virtualScrollerRef,R=t.current.virtualScrollbarVerticalRef,M=t.current.virtualScrollbarHorizontalRef,k=We(t,Tr),E=e.useRef(!1),F=We(t,tt),H=We(t,rt),D=We(t,et),T=We(t,mi),O=We(t,st),L=We(t,lt),$=e.useRef(null),z=e.useCallback((e=>{if(y.current=e,!e)return;const r=e.getBoundingClientRect();let n={width:ui(r.width,1),height:ui(r.height,1)};if((!$.current||n.width!==$.current.width&&n.height!==$.current.height)&&($.current=n,t.current.publishEvent("resize",n)),"undefined"==typeof ResizeObserver)return;const o=new ResizeObserver((e=>{const r=e[0];if(!r)return;const o={width:ui(r.contentRect.width,1),height:ui(r.contentRect.height,1)};o.width===n.width&&o.height===n.height||(t.current.publishEvent("resize",o),n=o)}));return o.observe(e),Bl>=19?()=>{y.current=null,o.disconnect()}:void 0}),[t,y]),j=e.useRef(n.initialState?.scroll??pi),V=e.useRef(!1),A=e.useRef(pi),N=e.useRef(ti),G=We(t,Jl),B=We(t,si),W=p(),_=e.useRef(void 0),U=r((()=>((e,t,r,n,o)=>({direction:di.NONE,buffer:yi(e,di.NONE,t,r,n,o)}))(w,n.rowBufferPx,n.columnBufferPx,15*F,300))).current,K=e.useCallback((e=>{if(function(e,t){if(e===t)return!0;return e.firstRowIndex===t.firstRowIndex&&e.lastRowIndex===t.lastRowIndex&&e.firstColumnIndex===t.firstColumnIndex&&e.lastColumnIndex===t.lastColumnIndex}(e,t.current.state.virtualization.renderContext))return;const r=e.firstRowIndex!==N.current.firstRowIndex||e.lastRowIndex!==N.current.lastRowIndex;t.current.setState((t=>i({},t,{virtualization:i({},t.virtualization,{renderContext:e})})));Je(t.current.state).isReady&&r&&(N.current=e,t.current.publishEvent("renderedRowsIntervalChange",e)),A.current=j.current}),[t]),q=I((()=>{const e=S.current;if(!e)return;const r=Je(t.current.state),o=Math.ceil(r.minimumSize.height-r.viewportOuterSize.height),l=Math.ceil(r.minimumSize.width-r.viewportInnerSize.width),i={top:er(e.scrollTop,0,o),left:w?er(e.scrollLeft,-l,0):er(e.scrollLeft,0,l)},a=i.left-j.current.left,s=i.top-j.current.top,d=0!==a||0!==s;j.current=i;const p=d?function(e,t){if(0===e&&0===t)return di.NONE;return Math.abs(t)>=Math.abs(e)?t>0?di.DOWN:di.UP:e>0?di.RIGHT:di.LEFT}(a,s):di.NONE,f=Math.abs(j.current.top-A.current.top),g=Math.abs(j.current.left-A.current.left),m=f>=F||g>=50,h=U.direction!==p;if(!(m||h))return G;if(h)switch(p){case di.NONE:case di.LEFT:case di.RIGHT:_.current=void 0;break;default:_.current=G}U.direction=p,U.buffer=yi(w,p,n.rowBufferPx,n.columnBufferPx,15*F,300);const b=bi(hi(t,n,u,c),j.current,U);return P.flushSync((()=>{K(b)})),W.start(1e3,q),b})),X=()=>{if(!Je(t.current.state).isReady&&(u||c))return;const e=bi(hi(t,n,u,c),j.current,U);_.current=void 0,K(e)},Y=I((()=>{if(V.current)return void(V.current=!1);const e=q();t.current.publishEvent("scrollPositionChange",{top:j.current.top,left:j.current.left,renderContext:e})})),Q=I((e=>{t.current.publishEvent("virtualScrollerWheel",{},e)})),Z=I((e=>{t.current.publishEvent("virtualScrollerTouchMove",{},e)})),J=e.useMemo((()=>({overflowX:!T||o?"hidden":void 0,overflowY:n.autoHeight?"hidden":void 0})),[T,n.autoHeight,o]),ee=e.useMemo((()=>{const e={width:T?D:"auto",flexBasis:H,flexShrink:0};return 0===e.flexBasis&&(e.flexBasis=gn),e}),[D,H,T]),te=e.useCallback((e=>{e&&t.current.publishEvent("virtualScrollerContentSizeChange",{columnsTotalWidth:D,contentHeight:H})}),[t,D,H]);return s((()=>{E.current&&t.current.updateRenderContext?.()}),[t,c,u]),s((()=>{o&&(S.current.scrollLeft=0)}),[o,S]),((t,r)=>{const n=e.useRef(!1);s((()=>n.current||!t?Co:(n.current=!0,r())),[n.current||t])})(G!==ti,(()=>{if(t.current.publishEvent("scrollPositionChange",{top:j.current.top,left:j.current.left,renderContext:G}),E.current=!0,n.initialState?.scroll&&S.current){const e=S.current,{top:r,left:o}=n.initialState.scroll,l={top:!(r>0),left:!(o>0)};if(!l.left&&D&&(e.scrollLeft=o,V.current=!0,l.left=!0),!l.top&&H&&(e.scrollTop=r,V.current=!0,l.top=!0),!l.top||!l.left){const n=t.current.subscribeEvent("virtualScrollerContentSizeChange",(t=>{!l.left&&t.columnsTotalWidth&&(e.scrollLeft=o,V.current=!0,l.left=!0),!l.top&&t.contentHeight&&(e.scrollTop=r,V.current=!0,l.top=!0),l.left&&l.top&&n()}));return n}}})),t.current.register("private",{updateRenderContext:X}),vt(t,"sortedRowsSet",X),vt(t,"paginationModelChange",X),vt(t,"columnsChange",X),{renderContext:G,setPanels:b,getRows:(e={})=>{if(!e.rows&&!x.range)return[];const r=Tt(t);let o=G;e.renderContext&&(o=e.renderContext,o.firstColumnIndex=G.firstColumnIndex,o.lastColumnIndex=G.lastColumnIndex);const s=!m&&void 0===e.position||m&&"bottom"===e.position,u=void 0!==e.position;let c;switch(e.position){case"top":c=0;break;case"bottom":c=d.top.length+x.rows.length;break;case void 0:c=d.top.length}const p=e.rows??x.rows,f=o.firstRowIndex,b=Math.min(o.lastRowIndex,p.length),w=e.rows?tr(0,e.rows.length):tr(f,b);let v=-1;!u&&B&&(B.rowIndex<f&&(w.unshift(B.rowIndex),v=B.rowIndex),B.rowIndex>b&&(w.push(B.rowIndex),v=B.rowIndex));const y=[],S=n.slotProps?.row,R=Fr(t);return w.forEach((d=>{const{id:f,model:m}=p[d];if(!r[f])return;const b=(x?.range?.firstRowIndex||0)+c+d;if(k){const e=g.left.length,r=a.length-g.right.length;t.current.calculateColSpan({rowId:f,minFirstColumn:e,maxLastColumn:r,columns:a}),g.left.length>0&&t.current.calculateColSpan({rowId:f,minFirstColumn:0,maxLastColumn:g.left.length,columns:a}),g.right.length>0&&t.current.calculateColSpan({rowId:f,minFirstColumn:a.length-g.right.length,maxLastColumn:a.length,columns:a})}const w=t.current.rowHasAutoHeight(f)?"auto":t.current.unstable_getRowHeight(f);let I;I=null!=C[f]&&t.current.isRowSelectable(f);let M=!1;void 0===e.position&&(M=0===d);let P=!1;const E=d===p.length-1;if(s)if(u)P=E;else{d===x.rows.length-1&&(P=!0)}let F=o;_.current&&d>=_.current.firstRowIndex&&d<_.current.lastRowIndex&&(F=_.current);const H=d===v,T=B?.rowIndex===b,$=xi(R,F,g.left.length),z=E&&"top"===e.position,j=F.firstColumnIndex,V=F.lastColumnIndex;if(y.push(l.jsx(n.slots.row,i({row:m,rowId:f,index:b,selected:I,offsetLeft:$,columnsTotalWidth:D,rowHeight:w,pinnedColumns:g,visibleColumns:a,firstColumnIndex:j,lastColumnIndex:V,focusedColumnIndex:T?B.columnIndex:void 0,isFirstVisible:M,isLastVisible:P,isNotVisible:H,showBottomBorder:z,scrollbarWidth:O,gridHasFiller:L},S),f)),H)return;const A=h.get(f);A&&y.push(A),void 0===e.position&&E&&y.push(t.current.getInfiniteLoadingTriggerElement?.({lastRowId:f}))})),y},getContainerProps:()=>({ref:z}),getScrollerProps:()=>({ref:S,onScroll:Y,onWheel:Q,onTouchMove:Z,style:J,role:"presentation",tabIndex:ql?-1:void 0}),getContentProps:()=>({style:ee,role:"presentation",ref:te}),getRenderZoneProps:()=>({role:"rowgroup"}),getScrollbarVerticalProps:()=>({ref:R,scrollPosition:j}),getScrollbarHorizontalProps:()=>({ref:M,scrollPosition:j}),getScrollAreaProps:()=>({scrollPosition:j})}};function mi(e){return e.dimensions.viewportOuterSize.width>0&&e.dimensions.columnsTotalWidth>e.dimensions.viewportOuterSize.width}function hi(e,t,r,n){const o=Je(e.current.state),l=Ul(e),i=t.unstable_listView?[ai(e.current.state)]:kr(e),a=ii(e),s=e.current.state.rows.dataRowIds.at(-1),u=i.at(-1);return{enabledForRows:r,enabledForColumns:n,apiRef:e,autoHeight:t.autoHeight,rowBufferPx:t.rowBufferPx,columnBufferPx:t.columnBufferPx,leftPinnedWidth:o.leftPinnedWidth,columnsTotalWidth:o.columnsTotalWidth,viewportInnerWidth:o.viewportInnerSize.width,viewportInnerHeight:o.viewportInnerSize.height,lastRowHeight:void 0!==s?e.current.unstable_getRowHeight(s):0,lastColumnWidth:u?.computedWidth??0,rowsMeta:Xl(e.current.state),columnPositions:Fr(e),rows:l.rows,range:l.range,pinnedColumns:Er(e),visibleColumns:i,hiddenCellsOriginMap:a,listView:t.unstable_listView??!1,virtualizeColumnsWithAutoRowHeight:t.virtualizeColumnsWithAutoRowHeight}}function bi(e,t,r){const n={firstRowIndex:0,lastRowIndex:e.rows.length,firstColumnIndex:0,lastColumnIndex:e.visibleColumns.length},{top:o,left:l}=t,a=Math.abs(l)+e.leftPinnedWidth;if(e.enabledForRows){let t=Math.min(wi(e,o,{atStart:!0,lastPosition:e.rowsMeta.positions[e.rowsMeta.positions.length-1]+e.lastRowHeight}),e.rowsMeta.positions.length-1);const r=e.hiddenCellsOriginMap[t];if(r){const e=Math.min(...Object.values(r));t=Math.min(t,e)}const l=e.autoHeight?t+e.rows.length:wi(e,o+e.viewportInnerHeight);n.firstRowIndex=t,n.lastRowIndex=l}if(e.listView)return i({},n,{lastColumnIndex:1});if(e.enabledForColumns){let t=0,o=e.columnPositions.length,l=!1;const[i,s]=vi({firstIndex:n.firstRowIndex,lastIndex:n.lastRowIndex,minFirstIndex:0,maxLastIndex:e.rows.length,bufferBefore:r.buffer.rowBefore,bufferAfter:r.buffer.rowAfter,positions:e.rowsMeta.positions,lastSize:e.lastRowHeight});if(!e.virtualizeColumnsWithAutoRowHeight)for(let r=i;r<s&&!l;r+=1){const t=e.rows[r];l=e.apiRef.current.rowHasAutoHeight(t.id)}l&&!e.virtualizeColumnsWithAutoRowHeight||(t=Ci(a,e.columnPositions,{atStart:!0,lastPosition:e.columnsTotalWidth}),o=Ci(a+e.viewportInnerWidth,e.columnPositions)),n.firstColumnIndex=t,n.lastColumnIndex=o}const s=function(e,t,r){const[n,o]=vi({firstIndex:t.firstRowIndex,lastIndex:t.lastRowIndex,minFirstIndex:0,maxLastIndex:e.rows.length,bufferBefore:r.buffer.rowBefore,bufferAfter:r.buffer.rowAfter,positions:e.rowsMeta.positions,lastSize:e.lastRowHeight}),[l,i]=vi({firstIndex:t.firstColumnIndex,lastIndex:t.lastColumnIndex,minFirstIndex:e.pinnedColumns.left.length,maxLastIndex:e.visibleColumns.length-e.pinnedColumns.right.length,bufferBefore:r.buffer.columnBefore,bufferAfter:r.buffer.columnAfter,positions:e.columnPositions,lastSize:e.lastColumnWidth}),a=function({firstColumnToRender:e,apiRef:t,firstRowToRender:r,lastRowToRender:n,visibleRows:o}){let l=e;for(let i=r;i<n;i+=1)if(o[i]){const r=o[i].id,n=t.current.unstable_getCellColSpanInfo(r,e);n&&n.spannedByColSpan&&(l=n.leftVisibleCellIndex)}return l}({firstColumnToRender:l,apiRef:e.apiRef,firstRowToRender:n,lastRowToRender:o,visibleRows:e.rows});return{firstRowIndex:n,lastRowIndex:o,firstColumnIndex:a,lastColumnIndex:i}}(e,n,r);return s}function wi(e,t,r){const n=e.apiRef.current.getLastMeasuredRowIndex();let o=n===1/0;e.range?.lastRowIndex&&!o&&(o=n>=e.range.lastRowIndex);const l=er(n-(e.range?.firstRowIndex||0),0,e.rowsMeta.positions.length);return o||e.rowsMeta.positions[l]>=t?Ci(t,e.rowsMeta.positions,r):function(e,t,r,n){let o=1;for(;r<t.length&&Math.abs(t[r])<e;)r+=o,o*=2;return Ci(e,t,n,Math.floor(r/2),Math.min(r,t.length))}(t,e.rowsMeta.positions,l,r)}function Ci(e,t,r=void 0,n=0,o=t.length){if(t.length<=0)return-1;if(n>=o)return n;const l=n+Math.floor((o-n)/2),i=t[l];let a;if(r?.atStart){a=e-((l===t.length-1?r.lastPosition:t[l+1])-i)<i}else a=e<=i;return a?Ci(e,t,r,n,l):Ci(e,t,r,l+1,o)}function vi({firstIndex:e,lastIndex:t,bufferBefore:r,bufferAfter:n,minFirstIndex:o,maxLastIndex:l,positions:i,lastSize:a}){const s=i[e]-r,u=i[t]+n,c=Ci(s,i,{atStart:!0,lastPosition:i[i.length-1]+a}),d=Ci(u,i);return[er(c,o,l),er(d,o,l)]}function xi(e,t,r){const n=(e[t.firstColumnIndex]??0)-(e[r]??0);return Math.abs(n)}function yi(e,t,r,n,o,l){if(e)switch(t){case di.LEFT:t=di.RIGHT;break;case di.RIGHT:t=di.LEFT}switch(t){case di.NONE:return{rowAfter:r,rowBefore:r,columnAfter:n,columnBefore:n};case di.LEFT:return{rowAfter:0,rowBefore:0,columnAfter:0,columnBefore:l};case di.RIGHT:return{rowAfter:0,rowBefore:0,columnAfter:l,columnBefore:0};case di.UP:return{rowAfter:0,rowBefore:o,columnAfter:0,columnBefore:0};case di.DOWN:return{rowAfter:o,rowBefore:0,columnAfter:0,columnBefore:0};default:throw new Error("unreachable")}}const Si=M("div",{name:"MuiDataGrid",slot:"OverlayWrapper",shouldForwardProp:e=>"overlayType"!==e&&"loadingOverlayVariant"!==e,overridesResolver:(e,t)=>t.overlayWrapper})((({overlayType:e,loadingOverlayVariant:t})=>"skeleton"!==t?{position:"sticky",top:"var(--DataGrid-headersTotalHeight)",left:0,width:0,height:0,zIndex:"loadingOverlay"===e?5:4}:{})),Ri=M("div",{name:"MuiDataGrid",slot:"OverlayWrapperInner",shouldForwardProp:e=>"overlayType"!==e&&"loadingOverlayVariant"!==e,overridesResolver:(e,t)=>t.overlayWrapperInner})({});function Ii(e){const t=fe(),r=me(),n=We(t,Je);let o=Math.max(n.viewportOuterSize.height-n.topContainerHeight-n.bottomContainerHeight-(n.hasScrollX?n.scrollbarSize:0),0);0===o&&(o=gn);const a=(e=>{const{classes:t}=e;return c({root:["overlayWrapper"],inner:["overlayWrapperInner"]},pt,t)})(i({},e,{classes:r.classes}));return l.jsx(Si,i({className:m(a.root)},e,{children:l.jsx(Ri,i({className:m(a.inner),style:{height:o,width:n.viewportOuterSize.width}},e))}))}function Mi(e){const{overlayType:t}=e,r=me();if(!t)return null;const n=r.slots?.[t],o=r.slotProps?.[t];return l.jsx(Ii,i({},e,{children:l.jsx(n,i({},o))}))}const ki=e=>e.columnMenu;const Pi=ct((function(){const e=_l(),t=me(),r=We(e,kr),n=We(e,qn),o=We(e,On),a=We(e,fo),s=We(e,(()=>null===po(e))),u=We(e,go),c=We(e,so),d=We(e,uo),p=We(e,Rl),f=We(e,ki),g=We(e,Mr),m=We(e,Sl),h=!(null===u&&null===a&&s),b=e.current.columnHeadersContainerRef;return l.jsx(t.slots.columnHeaders,i({ref:b,visibleColumns:r,filterColumnLookup:n,sortColumnLookup:o,columnHeaderTabIndexState:a,columnGroupHeaderTabIndexState:u,columnHeaderFocus:c,columnGroupHeaderFocus:d,headerGroupingMaxDepth:p,columnMenuState:f,columnVisibility:g,columnGroupsHeaderStructure:m,hasOtherElementInTabSequence:h},t.slotProps?.columnHeaders))})),Ei=e.createContext(void 0),Fi=()=>{const t=e.useContext(Ei);if(void 0===t)throw new Error(["MUI X: Could not find the Data Grid configuration context.","It looks like you rendered your component outside of a DataGrid, DataGridPro or DataGridPremium parent component.","This can also happen if you are bundling multiple versions of the Data Grid."].join("\n"));return t},Hi=M("div")({position:"absolute",top:"var(--DataGrid-headersTotalHeight)",left:0,width:"calc(100% - (var(--DataGrid-hasScrollY) * var(--DataGrid-scrollbarSize)))"}),Di=M("div",{name:"MuiDataGrid",slot:"Main",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.main,r.hasPinnedRight&&t["main--hasPinnedRight"],"skeleton"===r.loadingOverlayVariant&&t["main--hasSkeletonLoadingOverlay"]]}})({flexGrow:1,position:"relative",overflow:"hidden",display:"flex",flexDirection:"column"}),Ti=be(((e,t)=>{const{ownerState:r}=e,n=me(),o=Fi().hooks.useGridAriaAttributes();return l.jsxs(Di,i({ownerState:r,className:e.className,tabIndex:-1},o,n.slotProps?.main,{ref:t,children:[l.jsx(Hi,{role:"presentation","data-id":"gridPanelAnchor"}),e.children]}))})),Oi=M("div")({position:"sticky",zIndex:40,top:0});function Li(e){const t=c({root:["topContainer"]},pt,{});return l.jsx(Oi,i({},e,{className:m(t.root,ft["container--top"]),role:"presentation"}))}const $i=M("div")({position:"sticky",zIndex:40,bottom:"calc(var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize))"});function zi(e){const t=c({root:["bottomContainer"]},pt,{});return l.jsx($i,i({},e,{className:m(t.root,ft["container--bottom"]),role:"presentation"}))}const ji=M("div",{name:"MuiDataGrid",slot:"VirtualScrollerContent",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.virtualScrollerContent,r.overflowedContent&&t["virtualScrollerContent--overflowed"]]}})({}),Vi=be((function(e,t){const r=me(),n=!r.autoHeight&&"auto"===e.style?.minHeight,o=((e,t)=>{const{classes:r}=e;return c({root:["virtualScrollerContent",t&&"virtualScrollerContent--overflowed"]},pt,r)})(r,n),a={classes:r.classes,overflowedContent:n};return l.jsx(ji,i({},e,{ownerState:a,className:m(o.root,e.className),ref:t}))})),Ai=M("div")({display:"flex",flexDirection:"row",width:"var(--DataGrid-rowWidth)",boxSizing:"border-box"}),Ni=M("div")({position:"sticky",height:"100%",boxSizing:"border-box",borderTop:"1px solid var(--rowBorderColor)",backgroundColor:"var(--DataGrid-pinnedBackground)"}),Gi=M(Ni)({left:0,borderRight:"1px solid var(--rowBorderColor)"}),Bi=M(Ni)({right:0,borderLeft:"1px solid var(--rowBorderColor)"}),Wi=M("div")({flexGrow:1,borderTop:"1px solid var(--rowBorderColor)"});const _i=ct((function({rowsLength:e}){const t=fe(),{viewportOuterSize:r,minimumSize:n,hasScrollX:o,hasScrollY:i,scrollbarSize:a,leftPinnedWidth:s,rightPinnedWidth:u}=We(t,Je),c=o?a:0,d=r.height-n.height>0;return 0!==c||d?l.jsxs(Ai,{className:ft.filler,role:"presentation",style:{height:c,"--rowBorderColor":0===e?"transparent":"var(--DataGrid-rowBorderColor)"},children:[s>0&&l.jsx(Gi,{className:ft["filler--pinnedLeft"],style:{width:s}}),l.jsx(Wi,{}),u>0&&l.jsx(Bi,{className:ft["filler--pinnedRight"],style:{width:u+(i?a:0)}})]}):null})),Ui=["className"],Ki=M("div",{name:"MuiDataGrid",slot:"VirtualScrollerRenderZone",overridesResolver:(e,t)=>t.virtualScrollerRenderZone})({position:"absolute",display:"flex",flexDirection:"column"}),qi=be((function(e,t){const{className:r}=e,n=a(e,Ui),o=fe(),s=me(),u=(e=>{const{classes:t}=e;return c({root:["virtualScrollerRenderZone"]},pt,t)})(s),d=We(o,(()=>{const e=Jl(o);return Xl(o.current.state).positions[e.firstRowIndex]??0}));return l.jsx(Ki,i({className:m(u.root,r),ownerState:s,style:{transform:`translate3d(0, ${d}px, 0)`}},n,{ref:t}))})),Xi={includeHeaders:!0,includeOutliers:!1,outliersFactor:1.5,expand:!1,disableColumnVirtualization:!0},Yi=e=>e.editRows,Qi=Qe(Yi,((e,{rowId:t,editMode:r})=>r===sr.Row&&Boolean(e[t]))),Zi=Qe(Yi,((e,{rowId:t,field:r})=>e[t]?.[r]??null)),Ji=e=>e.preferencePanel,ea=Qe(Ji,((e,t)=>!(!e.open||e.labelId!==t)));var ta=function(e){return e.filters="filters",e.columns="columns",e}(ta||{});const ra=M("div")({position:"absolute",display:"inline-block",zIndex:60,"&:hover":{zIndex:70},"--size":"calc(max(var(--DataGrid-scrollbarSize), 14px))"}),na=M(ra)({width:"var(--size)",height:"calc(var(--DataGrid-hasScrollY) * (100% - var(--DataGrid-topContainerHeight) - var(--DataGrid-bottomContainerHeight) - var(--DataGrid-hasScrollX) * var(--DataGrid-scrollbarSize)))",overflowY:"auto",overflowX:"hidden",outline:0,"& > div":{width:"var(--size)"},top:"var(--DataGrid-topContainerHeight)",right:"0px"}),oa=M(ra)({width:"100%",height:"var(--size)",overflowY:"hidden",overflowX:"auto",outline:0,"& > div":{height:"var(--size)"},bottom:"0px"}),la=be((function(t,r){const n=_l(),o=me(),i=e.useRef(!1),a=e.useRef(0),s=e.useRef(null),u=e.useRef(null),d=((e,t)=>{const{classes:r}=e;return c({root:["scrollbar",`scrollbar--${t}`],content:["scrollbarContent"]},pt,r)})(o,t.position),p=We(n,Je),f="vertical"===t.position?"height":"width",g="vertical"===t.position?"scrollTop":"scrollLeft",m="vertical"===t.position?"top":"left",h="vertical"===t.position?p.hasScrollX:p.hasScrollY,b=p.minimumSize[f]+(h?p.scrollbarSize:0),w=("vertical"===t.position?p.viewportInnerSize.height:p.viewportOuterSize.width)*(b/p.viewportOuterSize[f]),C=I((()=>{const e=s.current,r=t.scrollPosition.current;if(!e)return;if(r[m]===a.current)return;if(a.current=r[m],i.current)return void(i.current=!1);i.current=!0;const n=r[m]/b;e[g]=n*w})),v=I((()=>{const e=n.current.virtualScrollerRef.current,t=s.current;if(!t)return;if(i.current)return void(i.current=!1);i.current=!0;const r=t[g]/w;e[g]=r*b}));E((()=>{const e=n.current.virtualScrollerRef.current,t=s.current,r={passive:!0};return e.addEventListener("scroll",C,r),t.addEventListener("scroll",v,r),()=>{e.removeEventListener("scroll",C,r),t.removeEventListener("scroll",v,r)}})),e.useEffect((()=>{u.current.style.setProperty(f,`${w}px`)}),[w,f]);const x="vertical"===t.position?na:oa;return l.jsx(x,{ref:y(r,s),className:d.root,style:"vertical"===t.position&&o.unstable_listView?{height:"100%",top:0}:void 0,tabIndex:-1,"aria-hidden":"true",onFocus:e=>{e.target.blur()},children:l.jsx("div",{ref:u,className:d.content})})})),ia=M("div",{name:"MuiDataGrid",slot:"VirtualScroller",overridesResolver:(e,t)=>{const{ownerState:r}=e;return[t.virtualScroller,r.hasScrollX&&t["virtualScroller--hasScrollX"]]}})({position:"relative",height:"100%",flexGrow:1,overflow:"scroll",scrollbarWidth:"none",display:"flex",flexDirection:"column","&::-webkit-scrollbar":{display:"none"},"@media print":{overflow:"hidden"},zIndex:0}),aa=e=>e.dimensions.rightPinnedWidth>0;function sa(e){const t=fe(),r=me(),n=We(t,ot),o=We(t,nt),a=We(t,aa),s=We(t,ut),u=(()=>{const e=fe(),t=me(),r=We(e,Pt),n=We(e,Wn),o=We(e,At),l=0===r&&0===o,i=We(e,Et);let a=null,s=null;return!i&&l&&(a="noRowsOverlay"),!i&&r>0&&0===n&&(a="noResultsOverlay"),i&&(a="loadingOverlay",s=t.slotProps?.loadingOverlay?.[l?"noRowsVariant":"variant"]||null),{overlayType:a,loadingOverlayVariant:s}})(),d={classes:r.classes,hasScrollX:o,hasPinnedRight:a,loadingOverlayVariant:u.loadingOverlayVariant},p=(e=>{const{classes:t,hasScrollX:r,hasPinnedRight:n,loadingOverlayVariant:o}=e;return c({root:["main",n&&"main--hasPinnedRight","skeleton"===o&&"main--hasSkeletonLoadingOverlay"],scroller:["virtualScroller",r&&"virtualScroller--hasScrollX"]},pt,t)})(d),f=gi(),{getContainerProps:g,getScrollerProps:m,getContentProps:h,getRenderZoneProps:b,getScrollbarVerticalProps:w,getScrollbarHorizontalProps:C,getRows:v,getScrollAreaProps:x}=f,y=v();return l.jsxs(Ti,i({className:p.root},g(),{ownerState:d,children:[l.jsx(Ol,i({scrollDirection:"left"},x())),l.jsx(Ol,i({scrollDirection:"right"},x())),l.jsxs(ia,i({className:p.scroller},m(),{ownerState:d,children:[l.jsxs(Li,{children:[!r.unstable_listView&&l.jsx(Pi,{}),l.jsx(r.slots.pinnedRows,{position:"top",virtualScroller:f})]}),l.jsx(Mi,i({},u)),l.jsx(Vi,i({},h(),{children:l.jsxs(qi,i({},b(),{children:[y,l.jsx(r.slots.detailPanels,{virtualScroller:f})]}))})),s&&l.jsx(_i,{rowsLength:y.length}),l.jsx(zi,{children:l.jsx(r.slots.pinnedRows,{position:"bottom",virtualScroller:f})})]})),o&&!r.unstable_listView&&l.jsx(la,i({position:"horizontal"},C())),n&&l.jsx(la,i({position:"vertical"},w())),e.children]}))}function ua(){const e=me();return e.hideFooter?null:l.jsx(e.slots.footer,i({},e.slotProps?.footer))}let ca;function da(t){return e.useMemo((()=>function(e){if(e.slotProps?.root)return e;const t=Object.keys(e);if(!t.some((e=>e.startsWith("aria-")||e.startsWith("data-"))))return e;const r={},n=e.forwardedProps??{};for(let o=0;o<t.length;o+=1){const l=t[o];l.startsWith("aria-")||l.startsWith("data-")?n[l]=e[l]:r[l]=e[l]}return r.forwardedProps=n,r}(t)),[t])}const pa=(e,t)=>{if(e)if(t){if(e===Cn.LEFT)return"right";if(e===Cn.RIGHT)return"left"}else{if(e===Cn.LEFT)return"left";if(e===Cn.RIGHT)return"right"}};function fa(e,t,r,n){const o=pa(r,t);return o&&void 0!==n?(e[o]=n,e):e}const ga=["column","row","rowId","rowNode","align","children","colIndex","width","className","style","colSpan","disableDragEvents","isNotVisible","pinnedOffset","pinnedPosition","showRightBorder","showLeftBorder","onClick","onDoubleClick","onMouseDown","onMouseUp","onMouseOver","onKeyDown","onKeyUp","onDragEnter","onDragOver"],ma=["changeReason","unstable_updateValueOnRender"];Cn.LEFT,vr.LEFT,Cn.RIGHT,vr.RIGHT,Cn.NONE,Cn.VIRTUAL;const ha=be((function(t,r){const{column:n,row:o,rowId:s,rowNode:u,align:d,colIndex:p,width:f,className:g,style:h,colSpan:b,disableDragEvents:w,isNotVisible:C,pinnedOffset:x,pinnedPosition:S,showRightBorder:R,showLeftBorder:I,onClick:M,onDoubleClick:k,onMouseDown:P,onMouseUp:E,onMouseOver:D,onKeyDown:T,onKeyUp:O,onDragEnter:L,onDragOver:$}=t,z=a(t,ga),j=_l(),V=me(),A=v(),N=n.field,G=_e(j,Zi,{rowId:s,field:N}),B=Fi().hooks.useCellAggregationResult(s,N),W=G?ur.Edit:ur.View,_=j.current.getCellParamsForRow(s,N,o,{colDef:n,cellMode:W,rowNode:u,tabIndex:We(j,(()=>{const e=po(j);return e&&e.field===N&&e.id===s?0:-1})),hasFocus:We(j,(()=>{const e=ao(j);return e?.id===s&&e.field===N}))});_.api=j.current,B&&(_.value=B.value,_.formattedValue=n.valueFormatter?n.valueFormatter(_.value,o,n,j):_.value);const U=We(j,(()=>j.current.unstable_applyPipeProcessors("isCellSelected",!1,{id:s,field:N}))),K=We(j,oi),q=We(j,li),{hasFocus:X,isEditable:Y=!1,value:Q}=_,Z="actions"===n.type&&n.getActions?.(j.current.getRowParams(s)).some((e=>!e.props.disabled)),J="view"!==W&&Y||Z?-1:_.tabIndex,{classes:ee,getCellClassName:te}=V,re=[We(j,(()=>j.current.unstable_applyPipeProcessors("cellClassName",[],{id:s,field:N}).filter(Boolean).join(" ")))];n.cellClassName&&re.push("function"==typeof n.cellClassName?n.cellClassName(_):n.cellClassName),"flex"===n.display&&re.push(ft["cell--flex"]),te&&re.push(te(_));const ne=_.formattedValue??Q,oe=e.useRef(null),le=y(r,oe),ie=e.useRef(null),ae=V.cellSelection??!1,se=(e=>{const{align:t,showLeftBorder:r,showRightBorder:n,pinnedPosition:o,isEditable:l,isSelected:i,isSelectionMode:a,classes:s}=e,u={root:["cell",`cell--text${H(t)}`,i&&"selected",l&&"cell--editable",r&&"cell--withLeftBorder",n&&"cell--withRightBorder",o===Cn.LEFT&&"cell--pinnedLeft",o===Cn.RIGHT&&"cell--pinnedRight",a&&!l&&"cell--selectionMode"]};return c(u,pt,s)})({align:d,showLeftBorder:I,showRightBorder:R,isEditable:Y,classes:V.classes,pinnedPosition:S,isSelected:U,isSelectionMode:ae}),ue=e.useCallback((e=>t=>{const r=j.current.getCellParams(s,N||"");j.current.publishEvent(e,r,t),E&&E(t)}),[j,N,E,s]),ce=e.useCallback((e=>t=>{const r=j.current.getCellParams(s,N||"");j.current.publishEvent(e,r,t),P&&P(t)}),[j,N,P,s]),de=e.useCallback(((e,t)=>r=>{if(!j.current.getRow(s))return;const n=j.current.getCellParams(s,N||"");j.current.publishEvent(e,n,r),t&&t(r)}),[j,N,s]),pe=K[s]?.[N]??!1,fe=q[s]?.[N]??1,ge=e.useMemo((()=>{if(C)return{padding:0,opacity:0,width:0,height:0,border:0};const e=fa(i({"--width":`${f}px`},h),A,S,x),t=S===Cn.LEFT,r=S===Cn.RIGHT;return fe>1&&(e.height=`calc(var(--height) * ${fe})`,e.zIndex=10,(t||r)&&(e.zIndex=40)),e}),[f,C,h,x,S,A,fe]);if(e.useEffect((()=>{if(!X||W===ur.Edit)return;const e=F(j.current.rootElementRef.current);if(oe.current&&!oe.current.contains(e.activeElement)){const e=oe.current.querySelector('[tabindex="0"]'),t=ie.current||e||oe.current;if(void 0===ca&&document.createElement("div").focus({get preventScroll(){return ca=!0,!1}}),ca)t.focus({preventScroll:!0});else{const e=j.current.getScrollPosition();t.focus(),j.current.scroll(e)}}}),[X,W,j]),pe)return l.jsx("div",{"data-colindex":p,role:"presentation",style:i({width:"var(--width)"},ge)});let he,be,we=z.onFocus;if(null===G&&n.renderCell&&(he=n.renderCell(_)),null!==G&&n.renderEditCell){const e=j.current.getRowWithUpdatedValues(s,n.field),t=a(G,ma),r=n.valueFormatter?n.valueFormatter(G.value,e,n,j):_.formattedValue,o=i({},_,{row:e,formattedValue:r},t);he=n.renderEditCell(o),re.push(ft["cell--editing"]),re.push(ee?.["cell--editing"])}if(void 0===he){const e=ne?.toString();he=e,be=e}e.isValidElement(he)&&Z&&(he=e.cloneElement(he,{focusElementRef:ie}));const Ce=w?null:{onDragEnter:de("cellDragEnter",L),onDragOver:de("cellDragOver",$)};return l.jsx("div",i({className:m(se.root,re,g),role:"gridcell","data-field":N,"data-colindex":p,"aria-colindex":p+1,"aria-colspan":b,"aria-rowspan":fe,style:ge,title:be,tabIndex:J,onClick:de("cellClick",M),onDoubleClick:de("cellDoubleClick",k),onMouseOver:de("cellMouseOver",D),onMouseDown:ce("cellMouseDown"),onMouseUp:ue("cellMouseUp"),onKeyDown:de("cellKeyDown",T),onKeyUp:de("cellKeyUp",O)},Ce,z,{onFocus:we,ref:le,children:he}))})),ba=ct(ha),wa=["field","type","align","width","height","empty","style","className"],Ca="1.3em",va=[40,80],xa={number:[40,60],string:[40,80],date:[40,60],dateTime:[60,80],singleSelect:[40,80]},ya=function(e){const t=(r=e,()=>{let e=r+=1831565813;return e=Math.imul(e^e>>>15,1|e),e^=e+Math.imul(e^e>>>7,61|e),((e^e>>>14)>>>0)/4294967296});var r;return(e,r)=>e+(r-e)*t()}(12345);const Sa=ct((function(t){const{field:r,type:n,align:o,width:s,height:u,empty:d=!1,style:p,className:f}=t,g=a(t,wa),h=(e=>{const{align:t,classes:r,empty:n}=e,o={root:["cell","cellSkeleton",`cell--text${t?H(t):"Left"}`,n&&"cellEmpty"]};return c(o,pt,r)})({classes:me().classes,align:o,empty:d}),b=e.useMemo((()=>{if("boolean"===n||"actions"===n)return{variant:"circular",width:Ca,height:Ca};const[e,t]=n?xa[n]??va:va;return{variant:"text",width:`${Math.round(ya(e,t))}%`,height:"1.2em"}}),[n]);return l.jsx("div",i({"data-field":r,className:m(h.root,f),style:i({height:u,maxWidth:s,minWidth:s},p)},g,{children:!d&&l.jsx(D,i({},b))}))}));const Ra={[`& .${ft.iconButtonContainer}`]:{visibility:"visible",width:"auto"},[`& .${ft.menuIcon}`]:{width:"auto",visibility:"visible"}},Ia={width:3,rx:1.5,x:10.5},Ma=e=>e.dimensions.hasScrollX&&(!e.dimensions.hasScrollY||0===e.dimensions.scrollbarSize),ka=u("div",{name:"MuiDataGrid",slot:"Root",overridesResolver:(e,t)=>[t.root,{[`&.${ft.autoHeight}`]:t.autoHeight},{[`&.${ft.autosizing}`]:t.autosizing},{[`&.${ft["root--densityStandard"]}`]:t["root--densityStandard"]},{[`&.${ft["root--densityComfortable"]}`]:t["root--densityComfortable"]},{[`&.${ft["root--densityCompact"]}`]:t["root--densityCompact"]},{[`&.${ft["root--disableUserSelection"]}`]:t["root--disableUserSelection"]},{[`&.${ft["root--noToolbar"]}`]:t["root--noToolbar"]},{[`&.${ft.withVerticalBorder}`]:t.withVerticalBorder},{[`& .${ft.actionsCell}`]:t.actionsCell},{[`& .${ft.booleanCell}`]:t.booleanCell},{[`& .${ft.cell}`]:t.cell},{[`& .${ft["cell--editable"]}`]:t["cell--editable"]},{[`& .${ft["cell--editing"]}`]:t["cell--editing"]},{[`& .${ft["cell--flex"]}`]:t["cell--flex"]},{[`& .${ft["cell--pinnedLeft"]}`]:t["cell--pinnedLeft"]},{[`& .${ft["cell--pinnedRight"]}`]:t["cell--pinnedRight"]},{[`& .${ft["cell--rangeBottom"]}`]:t["cell--rangeBottom"]},{[`& .${ft["cell--rangeLeft"]}`]:t["cell--rangeLeft"]},{[`& .${ft["cell--rangeRight"]}`]:t["cell--rangeRight"]},{[`& .${ft["cell--rangeTop"]}`]:t["cell--rangeTop"]},{[`& .${ft["cell--selectionMode"]}`]:t["cell--selectionMode"]},{[`& .${ft["cell--textCenter"]}`]:t["cell--textCenter"]},{[`& .${ft["cell--textLeft"]}`]:t["cell--textLeft"]},{[`& .${ft["cell--textRight"]}`]:t["cell--textRight"]},{[`& .${ft["cell--withLeftBorder"]}`]:t["cell--withLeftBorder"]},{[`& .${ft["cell--withRightBorder"]}`]:t["cell--withRightBorder"]},{[`& .${ft.cellCheckbox}`]:t.cellCheckbox},{[`& .${ft.cellEmpty}`]:t.cellEmpty},{[`& .${ft.cellOffsetLeft}`]:t.cellOffsetLeft},{[`& .${ft.cellSkeleton}`]:t.cellSkeleton},{[`& .${ft.checkboxInput}`]:t.checkboxInput},{[`& .${ft.columnHeader}`]:t.columnHeader},{[`& .${ft["columnHeader--alignCenter"]}`]:t["columnHeader--alignCenter"]},{[`& .${ft["columnHeader--alignLeft"]}`]:t["columnHeader--alignLeft"]},{[`& .${ft["columnHeader--alignRight"]}`]:t["columnHeader--alignRight"]},{[`& .${ft["columnHeader--dragging"]}`]:t["columnHeader--dragging"]},{[`& .${ft["columnHeader--emptyGroup"]}`]:t["columnHeader--emptyGroup"]},{[`& .${ft["columnHeader--filledGroup"]}`]:t["columnHeader--filledGroup"]},{[`& .${ft["columnHeader--filtered"]}`]:t["columnHeader--filtered"]},{[`& .${ft["columnHeader--last"]}`]:t["columnHeader--last"]},{[`& .${ft["columnHeader--lastUnpinned"]}`]:t["columnHeader--lastUnpinned"]},{[`& .${ft["columnHeader--moving"]}`]:t["columnHeader--moving"]},{[`& .${ft["columnHeader--numeric"]}`]:t["columnHeader--numeric"]},{[`& .${ft["columnHeader--pinnedLeft"]}`]:t["columnHeader--pinnedLeft"]},{[`& .${ft["columnHeader--pinnedRight"]}`]:t["columnHeader--pinnedRight"]},{[`& .${ft["columnHeader--siblingFocused"]}`]:t["columnHeader--siblingFocused"]},{[`& .${ft["columnHeader--sortable"]}`]:t["columnHeader--sortable"]},{[`& .${ft["columnHeader--sorted"]}`]:t["columnHeader--sorted"]},{[`& .${ft["columnHeader--withLeftBorder"]}`]:t["columnHeader--withLeftBorder"]},{[`& .${ft["columnHeader--withRightBorder"]}`]:t["columnHeader--withRightBorder"]},{[`& .${ft.columnHeaderCheckbox}`]:t.columnHeaderCheckbox},{[`& .${ft.columnHeaderDraggableContainer}`]:t.columnHeaderDraggableContainer},{[`& .${ft.columnHeaderTitleContainer}`]:t.columnHeaderTitleContainer},{[`& .${ft.columnHeaderTitleContainerContent}`]:t.columnHeaderTitleContainerContent},{[`& .${ft.columnSeparator}`]:t.columnSeparator},{[`& .${ft["columnSeparator--resizable"]}`]:t["columnSeparator--resizable"]},{[`& .${ft["columnSeparator--resizing"]}`]:t["columnSeparator--resizing"]},{[`& .${ft["columnSeparator--sideLeft"]}`]:t["columnSeparator--sideLeft"]},{[`& .${ft["columnSeparator--sideRight"]}`]:t["columnSeparator--sideRight"]},{[`& .${ft["container--bottom"]}`]:t["container--bottom"]},{[`& .${ft["container--top"]}`]:t["container--top"]},{[`& .${ft.detailPanelToggleCell}`]:t.detailPanelToggleCell},{[`& .${ft["detailPanelToggleCell--expanded"]}`]:t["detailPanelToggleCell--expanded"]},{[`& .${ft.editBooleanCell}`]:t.editBooleanCell},{[`& .${ft.filterIcon}`]:t.filterIcon},{[`& .${ft["filler--borderBottom"]}`]:t["filler--borderBottom"]},{[`& .${ft["filler--pinnedLeft"]}`]:t["filler--pinnedLeft"]},{[`& .${ft["filler--pinnedRight"]}`]:t["filler--pinnedRight"]},{[`& .${ft.groupingCriteriaCell}`]:t.groupingCriteriaCell},{[`& .${ft.groupingCriteriaCellLoadingContainer}`]:t.groupingCriteriaCellLoadingContainer},{[`& .${ft.groupingCriteriaCellToggle}`]:t.groupingCriteriaCellToggle},{[`& .${ft.headerFilterRow}`]:t.headerFilterRow},{[`& .${ft.iconSeparator}`]:t.iconSeparator},{[`& .${ft.menuIcon}`]:t.menuIcon},{[`& .${ft.menuIconButton}`]:t.menuIconButton},{[`& .${ft.menuList}`]:t.menuList},{[`& .${ft.menuOpen}`]:t.menuOpen},{[`& .${ft.overlayWrapperInner}`]:t.overlayWrapperInner},{[`& .${ft.pinnedRows}`]:t.pinnedRows},{[`& .${ft["pinnedRows--bottom"]}`]:t["pinnedRows--bottom"]},{[`& .${ft["pinnedRows--top"]}`]:t["pinnedRows--top"]},{[`& .${ft.row}`]:t.row},{[`& .${ft["row--borderBottom"]}`]:t["row--borderBottom"]},{[`& .${ft["row--detailPanelExpanded"]}`]:t["row--detailPanelExpanded"]},{[`& .${ft["row--dragging"]}`]:t["row--dragging"]},{[`& .${ft["row--dynamicHeight"]}`]:t["row--dynamicHeight"]},{[`& .${ft["row--editable"]}`]:t["row--editable"]},{[`& .${ft["row--editing"]}`]:t["row--editing"]},{[`& .${ft["row--firstVisible"]}`]:t["row--firstVisible"]},{[`& .${ft["row--lastVisible"]}`]:t["row--lastVisible"]},{[`& .${ft.rowReorderCell}`]:t.rowReorderCell},{[`& .${ft["rowReorderCell--draggable"]}`]:t["rowReorderCell--draggable"]},{[`& .${ft.rowReorderCellContainer}`]:t.rowReorderCellContainer},{[`& .${ft.rowReorderCellPlaceholder}`]:t.rowReorderCellPlaceholder},{[`& .${ft.rowSkeleton}`]:t.rowSkeleton},{[`& .${ft.scrollbar}`]:t.scrollbar},{[`& .${ft["scrollbar--horizontal"]}`]:t["scrollbar--horizontal"]},{[`& .${ft["scrollbar--vertical"]}`]:t["scrollbar--vertical"]},{[`& .${ft.scrollbarFiller}`]:t.scrollbarFiller},{[`& .${ft["scrollbarFiller--borderBottom"]}`]:t["scrollbarFiller--borderBottom"]},{[`& .${ft["scrollbarFiller--borderTop"]}`]:t["scrollbarFiller--borderTop"]},{[`& .${ft["scrollbarFiller--header"]}`]:t["scrollbarFiller--header"]},{[`& .${ft["scrollbarFiller--pinnedRight"]}`]:t["scrollbarFiller--pinnedRight"]},{[`& .${ft.sortIcon}`]:t.sortIcon},{[`& .${ft.treeDataGroupingCell}`]:t.treeDataGroupingCell},{[`& .${ft.treeDataGroupingCellLoadingContainer}`]:t.treeDataGroupingCellLoadingContainer},{[`& .${ft.treeDataGroupingCellToggle}`]:t.treeDataGroupingCellToggle},{[`& .${ft.withBorderColor}`]:t.withBorderColor}]})((({theme:e})=>{const t=_l(),r=We(t,Ma),n=(o=e).vars?o.vars.palette.TableCell.border:"light"===o.palette.mode?O(T(o.palette.divider,1),.88):L(T(o.palette.divider,1),.68);var o;const l=e.shape.borderRadius,a=e.vars?e.vars.palette.background.default:e.mixins.MuiDataGrid?.containerBackground??e.palette.background.default,s=e.mixins.MuiDataGrid?.pinnedBackground??a,u=e.vars?`rgba(${e.vars.palette.background.defaultChannel} / ${e.vars.palette.action.disabledOpacity})`:T(e.palette.background.default,e.palette.action.disabledOpacity),c=(e.vars||e).palette.action.hoverOpacity,d=(e.vars||e).palette.action.hover,p=(e.vars||e).palette.action.selectedOpacity,f=e.vars?`calc(${c} + ${p})`:c+p,g=e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${p})`:T(e.palette.primary.main,p),m=e.vars?`rgba(${e.vars.palette.primary.mainChannel} / ${f})`:T(e.palette.primary.main,f),h=e.vars?Fa:Pa,b=e=>({[`& .${ft["cell--pinnedLeft"]}, & .${ft["cell--pinnedRight"]}`]:{backgroundColor:e,"&.Mui-selected":{backgroundColor:h(e,g,p),"&:hover":{backgroundColor:h(e,g,f)}}}}),w=b(h(s,d,c)),C=h(s,g,p),v=b(C),x=b(h(s,m,f)),y={backgroundColor:g,"&:hover":{backgroundColor:m,"@media (hover: none)":{backgroundColor:g}}};return i({"--unstable_DataGrid-radius":"number"==typeof l?`${l}px`:l,"--unstable_DataGrid-headWeight":e.typography.fontWeightMedium,"--unstable_DataGrid-overlayBackground":u,"--DataGrid-containerBackground":a,"--DataGrid-pinnedBackground":s,"--DataGrid-rowBorderColor":n,"--DataGrid-cellOffsetMultiplier":2,"--DataGrid-width":"0px","--DataGrid-hasScrollX":"0","--DataGrid-hasScrollY":"0","--DataGrid-scrollbarSize":"10px","--DataGrid-rowWidth":"0px","--DataGrid-columnsTotalWidth":"0px","--DataGrid-leftPinnedWidth":"0px","--DataGrid-rightPinnedWidth":"0px","--DataGrid-headerHeight":"0px","--DataGrid-headersTotalHeight":"0px","--DataGrid-topContainerHeight":"0px","--DataGrid-bottomContainerHeight":"0px",flex:1,boxSizing:"border-box",position:"relative",borderWidth:"1px",borderStyle:"solid",borderColor:n,borderRadius:"var(--unstable_DataGrid-radius)",color:(e.vars||e).palette.text.primary},e.typography.body2,{outline:"none",height:"100%",display:"flex",minWidth:0,minHeight:0,flexDirection:"column",overflow:"hidden",overflowAnchor:"none",transform:"translate(0, 0)",[`.${ft.main} > *:first-child/* emotion-disable-server-rendering-unsafe-selector-warning-please-do-not-use-this-the-warning-exists-for-a-reason */`]:{borderTopLeftRadius:"var(--unstable_DataGrid-radius)",borderTopRightRadius:"var(--unstable_DataGrid-radius)"},[`&.${ft.autoHeight}`]:{height:"auto"},[`&.${ft.autosizing}`]:{[`& .${ft.columnHeaderTitleContainerContent} > *`]:{overflow:"visible !important"},"@media (hover: hover)":{[`& .${ft.menuIcon}`]:{width:"0 !important",visibility:"hidden !important"}},[`& .${ft.cell}`]:{overflow:"visible !important",whiteSpace:"nowrap",minWidth:"max-content !important",maxWidth:"max-content !important"},[`& .${ft.groupingCriteriaCell}`]:{width:"unset"},[`& .${ft.treeDataGroupingCell}`]:{width:"unset"}},[`& .${ft.columnHeader}, & .${ft.cell}`]:{WebkitTapHighlightColor:"transparent",padding:"0 10px",boxSizing:"border-box"},[`& .${ft.columnHeader}:focus-within, & .${ft.cell}:focus-within`]:{outline:`solid ${e.vars?`rgba(${e.vars.palette.primary.mainChannel} / 0.5)`:T(e.palette.primary.main,.5)} 1px`,outlineOffset:-1},[`& .${ft.columnHeader}:focus, & .${ft.cell}:focus`]:{outline:`solid ${e.palette.primary.main} 1px`,outlineOffset:-1},[`& .${ft.columnHeader}:focus,\n      & .${ft["columnHeader--withLeftBorder"]},\n      & .${ft["columnHeader--withRightBorder"]},\n      & .${ft["columnHeader--siblingFocused"]},\n      & .${ft["virtualScroller--hasScrollX"]} .${ft["columnHeader--lastUnpinned"]},\n      & .${ft["virtualScroller--hasScrollX"]} .${ft["columnHeader--last"]}\n      `]:{[`& .${ft.columnSeparator}`]:{opacity:0},"@media (hover: none)":{[`& .${ft["columnSeparator--resizable"]}`]:{opacity:1}},[`& .${ft["columnSeparator--resizable"]}:hover`]:{opacity:1}},[`&.${ft["root--noToolbar"]} [aria-rowindex="1"] [aria-colindex="1"]`]:{borderTopLeftRadius:"calc(var(--unstable_DataGrid-radius) - 1px)"},[`&.${ft["root--noToolbar"]} [aria-rowindex="1"] .${ft["columnHeader--last"]}`]:{borderTopRightRadius:r?"calc(var(--unstable_DataGrid-radius) - 1px)":void 0},[`& .${ft.columnHeaderCheckbox}, & .${ft.cellCheckbox}`]:{padding:0,justifyContent:"center",alignItems:"center"},[`& .${ft.columnHeader}`]:{position:"relative",display:"flex",alignItems:"center"},[`& .${ft["virtualScroller--hasScrollX"]} .${ft["columnHeader--last"]}`]:{overflow:"hidden"},[`& .${ft["columnHeader--sorted"]} .${ft.iconButtonContainer}, & .${ft["columnHeader--filtered"]} .${ft.iconButtonContainer}`]:{visibility:"visible",width:"auto"},[`& .${ft.columnHeader}:not(.${ft["columnHeader--sorted"]}) .${ft.sortIcon}`]:{opacity:0,transition:e.transitions.create(["opacity"],{duration:e.transitions.duration.shorter})},[`& .${ft.columnHeaderTitleContainer}`]:{display:"flex",alignItems:"center",gap:e.spacing(.25),minWidth:0,flex:1,whiteSpace:"nowrap",overflow:"hidden"},[`& .${ft.columnHeaderTitleContainerContent}`]:{overflow:"hidden",display:"flex",alignItems:"center"},[`& .${ft["columnHeader--filledGroup"]} .${ft.columnHeaderTitleContainer}`]:{borderBottomWidth:"1px",borderBottomStyle:"solid",boxSizing:"border-box"},[`& .${ft.sortIcon}, & .${ft.filterIcon}`]:{fontSize:"inherit"},[`& .${ft["columnHeader--sortable"]}`]:{cursor:"pointer"},[`& .${ft["columnHeader--alignCenter"]} .${ft.columnHeaderTitleContainer}`]:{justifyContent:"center"},[`& .${ft["columnHeader--alignRight"]} .${ft.columnHeaderDraggableContainer}, & .${ft["columnHeader--alignRight"]} .${ft.columnHeaderTitleContainer}`]:{flexDirection:"row-reverse"},[`& .${ft["columnHeader--alignCenter"]} .${ft.menuIcon}`]:{marginLeft:"auto"},[`& .${ft["columnHeader--alignRight"]} .${ft.menuIcon}`]:{marginRight:"auto",marginLeft:-5},[`& .${ft["columnHeader--moving"]}`]:{backgroundColor:(e.vars||e).palette.action.hover},[`& .${ft["columnHeader--pinnedLeft"]}, & .${ft["columnHeader--pinnedRight"]}`]:{position:"sticky",zIndex:40,background:"var(--DataGrid-pinnedBackground)"},[`& .${ft.columnSeparator}`]:{position:"absolute",overflow:"hidden",zIndex:30,display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",maxWidth:10,color:n},[`& .${ft.columnHeaders}`]:{width:"var(--DataGrid-rowWidth)"},"@media (hover: hover)":{[`& .${ft.columnHeader}:hover`]:Ra,[`& .${ft.columnHeader}:not(.${ft["columnHeader--sorted"]}):hover .${ft.sortIcon}`]:{opacity:.5}},"@media (hover: none)":{[`& .${ft.columnHeader}`]:Ra,[`& .${ft.columnHeader}:focus,\n        & .${ft["columnHeader--siblingFocused"]}`]:{[`.${ft["columnSeparator--resizable"]}`]:{color:(e.vars||e).palette.primary.main}}},[`& .${ft["columnSeparator--sideLeft"]}`]:{left:-5},[`& .${ft["columnSeparator--sideRight"]}`]:{right:-5},[`& .${ft["columnHeader--withRightBorder"]} .${ft["columnSeparator--sideLeft"]}`]:{left:-5.5},[`& .${ft["columnHeader--withRightBorder"]} .${ft["columnSeparator--sideRight"]}`]:{right:-5.5},[`& .${ft["columnSeparator--resizable"]}`]:{cursor:"col-resize",touchAction:"none",[`&.${ft["columnSeparator--resizing"]}`]:{color:(e.vars||e).palette.primary.main},"@media (hover: none)":{[`& .${ft.iconSeparator} rect`]:Ia},"@media (hover: hover)":{"&:hover":{color:(e.vars||e).palette.primary.main,[`& .${ft.iconSeparator} rect`]:Ia}},"& svg":{pointerEvents:"none"}},[`& .${ft.iconSeparator}`]:{color:"inherit",transition:e.transitions.create(["color","width"],{duration:e.transitions.duration.shortest})},[`& .${ft.menuIcon}`]:{width:0,visibility:"hidden",fontSize:20,marginRight:-5,display:"flex",alignItems:"center"},[`.${ft.menuOpen}`]:{visibility:"visible",width:"auto"},[`& .${ft.headerFilterRow}`]:{[`& .${ft.columnHeader}`]:{boxSizing:"border-box",borderBottom:"1px solid var(--DataGrid-rowBorderColor)"}},[`& .${ft["row--borderBottom"]} .${ft.columnHeader},\n      & .${ft["row--borderBottom"]} .${ft.filler},\n      & .${ft["row--borderBottom"]} .${ft.scrollbarFiller}`]:{borderBottom:"1px solid var(--DataGrid-rowBorderColor)"},[`& .${ft["row--borderBottom"]} .${ft.cell}`]:{borderBottom:"1px solid var(--rowBorderColor)"},[`.${ft.row}`]:{display:"flex",width:"var(--DataGrid-rowWidth)",breakInside:"avoid","--rowBorderColor":"var(--DataGrid-rowBorderColor)",[`&.${ft["row--firstVisible"]}`]:{"--rowBorderColor":"transparent"},"&:hover":{backgroundColor:(e.vars||e).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},[`&.${ft.rowSkeleton}:hover`]:{backgroundColor:"transparent"},"&.Mui-selected":y},[`& .${ft["container--top"]}, & .${ft["container--bottom"]}`]:{"[role=row]":{background:"var(--DataGrid-containerBackground)"}},[`& .${ft.cell}`]:{flex:"0 0 auto",height:"var(--height)",width:"var(--width)",lineHeight:"calc(var(--height) - 1px)",boxSizing:"border-box",borderTop:"1px solid var(--rowBorderColor)",overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis","&.Mui-selected":y},[`& .${ft["virtualScrollerContent--overflowed"]} .${ft["row--lastVisible"]} .${ft.cell}`]:{borderTopColor:"transparent"},[`& .${ft["pinnedRows--top"]} :first-of-type`]:{[`& .${ft.cell}, .${ft.scrollbarFiller}`]:{borderTop:"none"}},[`&.${ft["root--disableUserSelection"]} .${ft.cell}`]:{userSelect:"none"},[`& .${ft["row--dynamicHeight"]} > .${ft.cell}`]:{whiteSpace:"initial",lineHeight:"inherit"},[`& .${ft.cellEmpty}`]:{flex:1,padding:0,height:"unset"},[`& .${ft.cell}.${ft["cell--selectionMode"]}`]:{cursor:"default"},[`& .${ft.cell}.${ft["cell--editing"]}`]:{padding:1,display:"flex",boxShadow:e.shadows[2],backgroundColor:(e.vars||e).palette.background.paper,"&:focus-within":{outline:`1px solid ${(e.vars||e).palette.primary.main}`,outlineOffset:-1}},[`& .${ft["row--editing"]}`]:{boxShadow:e.shadows[2]},[`& .${ft["row--editing"]} .${ft.cell}`]:{boxShadow:e.shadows[0],backgroundColor:(e.vars||e).palette.background.paper},[`& .${ft.editBooleanCell}`]:{display:"flex",height:"100%",width:"100%",alignItems:"center",justifyContent:"center"},[`& .${ft.booleanCell}[data-value="true"]`]:{color:(e.vars||e).palette.text.secondary},[`& .${ft.booleanCell}[data-value="false"]`]:{color:(e.vars||e).palette.text.disabled},[`& .${ft.actionsCell}`]:{display:"inline-flex",alignItems:"center",gridGap:e.spacing(1)},[`& .${ft.rowReorderCell}`]:{display:"inline-flex",flex:1,alignItems:"center",justifyContent:"center",opacity:(e.vars||e).palette.action.disabledOpacity},[`& .${ft["rowReorderCell--draggable"]}`]:{cursor:"move",opacity:1},[`& .${ft.rowReorderCellContainer}`]:{padding:0,display:"flex",alignItems:"stretch"},[`.${ft.withBorderColor}`]:{borderColor:n},[`& .${ft["cell--withLeftBorder"]}, & .${ft["columnHeader--withLeftBorder"]}`]:{borderLeftColor:"var(--DataGrid-rowBorderColor)",borderLeftWidth:"1px",borderLeftStyle:"solid"},[`& .${ft["cell--withRightBorder"]}, & .${ft["columnHeader--withRightBorder"]}`]:{borderRightColor:"var(--DataGrid-rowBorderColor)",borderRightWidth:"1px",borderRightStyle:"solid"},[`& .${ft["cell--flex"]}`]:{display:"flex",alignItems:"center",lineHeight:"inherit"},[`& .${ft["cell--textLeft"]}`]:{textAlign:"left",justifyContent:"flex-start"},[`& .${ft["cell--textRight"]}`]:{textAlign:"right",justifyContent:"flex-end"},[`& .${ft["cell--textCenter"]}`]:{textAlign:"center",justifyContent:"center"},[`& .${ft["cell--pinnedLeft"]}, & .${ft["cell--pinnedRight"]}`]:{position:"sticky",zIndex:30,background:"var(--DataGrid-pinnedBackground)","&.Mui-selected":{backgroundColor:C}},[`& .${ft.virtualScrollerContent} .${ft.row}`]:{"&:hover":w,"&.Mui-selected":v,"&.Mui-selected:hover":x},[`& .${ft.cellOffsetLeft}`]:{flex:"0 0 auto",display:"inline-block"},[`& .${ft.cellSkeleton}`]:{flex:"0 0 auto",height:"100%",display:"inline-flex",alignItems:"center"},[`& .${ft.columnHeaderDraggableContainer}`]:{display:"flex",width:"100%",height:"100%"},[`& .${ft.rowReorderCellPlaceholder}`]:{display:"none"},[`& .${ft["columnHeader--dragging"]}, & .${ft["row--dragging"]}`]:{background:(e.vars||e).palette.background.paper,padding:"0 12px",borderRadius:"var(--unstable_DataGrid-radius)",opacity:(e.vars||e).palette.action.disabledOpacity},[`& .${ft["row--dragging"]}`]:{background:(e.vars||e).palette.background.paper,padding:"0 12px",borderRadius:"var(--unstable_DataGrid-radius)",opacity:(e.vars||e).palette.action.disabledOpacity,[`& .${ft.rowReorderCellPlaceholder}`]:{display:"flex"}},[`& .${ft.treeDataGroupingCell}`]:{display:"flex",alignItems:"center",width:"100%"},[`& .${ft.treeDataGroupingCellToggle}`]:{flex:"0 0 28px",alignSelf:"stretch",marginRight:e.spacing(2)},[`& .${ft.treeDataGroupingCellLoadingContainer}, .${ft.groupingCriteriaCellLoadingContainer}`]:{display:"flex",alignItems:"center",justifyContent:"center",height:"100%"},[`& .${ft.groupingCriteriaCell}`]:{display:"flex",alignItems:"center",width:"100%"},[`& .${ft.groupingCriteriaCellToggle}`]:{flex:"0 0 28px",alignSelf:"stretch",marginRight:e.spacing(2)},[`.${ft.scrollbarFiller}`]:{minWidth:"calc(var(--DataGrid-hasScrollY) * var(--DataGrid-scrollbarSize))",alignSelf:"stretch",[`&.${ft["scrollbarFiller--borderTop"]}`]:{borderTop:"1px solid var(--DataGrid-rowBorderColor)"},[`&.${ft["scrollbarFiller--borderBottom"]}`]:{borderBottom:"1px solid var(--DataGrid-rowBorderColor)"},[`&.${ft["scrollbarFiller--pinnedRight"]}`]:{backgroundColor:"var(--DataGrid-pinnedBackground)",position:"sticky",right:0}},[`& .${ft.filler}`]:{flex:"1 0 auto"},[`& .${ft["filler--borderBottom"]}`]:{borderBottom:"1px solid var(--DataGrid-rowBorderColor)"},[`& .${ft["main--hasSkeletonLoadingOverlay"]}`]:{[`& .${ft.virtualScrollerContent}`]:{position:"fixed",visibility:"hidden"},[`& .${ft["scrollbar--vertical"]}, & .${ft.pinnedRows}, & .${ft.virtualScroller} > .${ft.filler}`]:{display:"none"}}})}));function Pa(e,t,r,n=1){const o=(e,t)=>Math.round((e**(1/n)*(1-r)+t**(1/n)*r)**n),l=$(e),i=$(t),a=[o(l.values[0],i.values[0]),o(l.values[1],i.values[1]),o(l.values[2],i.values[2])];return z({type:"rgb",values:a})}const Ea=e=>`rgb(from ${e} r g b / 1)`;function Fa(e,t,r){return`color-mix(in srgb,${e}, ${Ea(t)} calc(${r} * 100%))`}const Ha=()=>()=>{},Da=()=>!1,Ta=()=>!0;function Oa(){const e=fe(),t=We(e,Ir),r=me(),n=We(e,Ji),o=e.current.unstable_applyPipeProcessors("preferencePanel",null,n.openedPanelValue??ta.filters);return l.jsx(r.slots.panel,i({as:r.slots.basePopper,open:t.length>0&&n.open,id:n.panelId,"aria-labelledby":n.labelId},r.slotProps?.panel,r.slotProps?.basePopper,{children:o}))}function La(){const t=me();return l.jsxs(e.Fragment,{children:[l.jsx(Oa,{}),t.slots.toolbar&&l.jsx(t.slots.toolbar,i({},t.slotProps?.toolbar))]})}const $a=["className","children"],za=be((function(t,r){const n=me(),{className:o,children:s}=t,u=a(t,$a),d=_l(),p=We(d,yt),f=d.current.rootElementRef,g=e.useCallback((e=>{null!==e&&d.current.publishEvent("rootMount",e)}),[d]),h=y(f,r,g),b=n,w=((e,t)=>{const{autoHeight:r,classes:n,showCellVerticalBorder:o}=e,l={root:["root",r&&"autoHeight",`root--density${H(t)}`,null===e.slots.toolbar&&"root--noToolbar","withBorderColor",o&&"withVerticalBorder"]};return c(l,pt,n)})(b,p);return Oe.useSyncExternalStore(Ha,Da,Ta)?null:l.jsxs(ka,i({className:m(w.root,o),ownerState:b},u,{ref:h,children:[l.jsx(La,{}),l.jsx(sa,{children:s}),l.jsx(ua,{})]}))})),ja=ct(za),Va=["className"],Aa=M("div",{name:"MuiDataGrid",slot:"FooterContainer",overridesResolver:(e,t)=>t.footerContainer})({display:"flex",justifyContent:"space-between",alignItems:"center",minHeight:52,borderTop:"1px solid"}),Na=be((function(e,t){const{className:r}=e,n=a(e,Va),o=me(),s=(e=>{const{classes:t}=e;return c({root:["footerContainer","withBorderColor"]},pt,t)})(o);return l.jsx(Aa,i({className:m(s.root,r),ownerState:o},n,{ref:t}))})),Ga=["className"],Ba=M("div",{name:"MuiDataGrid",slot:"Overlay",overridesResolver:(e,t)=>t.overlay})({width:"100%",height:"100%",display:"flex",alignSelf:"center",alignItems:"center",justifyContent:"center",backgroundColor:"var(--unstable_DataGrid-overlayBackground)"}),Wa=be((function(e,t){const{className:r}=e,n=a(e,Ga),o=me(),s=(e=>{const{classes:t}=e;return c({root:["overlay"]},pt,t)})(o);return l.jsx(Ba,i({className:m(s.root,r),ownerState:o},n,{ref:t}))})),_a=e.memo((t=>{const{colDef:r,open:n,columnMenuId:o,columnMenuButtonId:a,iconButtonRef:s}=t,u=fe(),d=me(),p=(e=>{const{classes:t,open:r}=e;return c({root:["menuIcon",r&&"menuOpen"],button:["menuIconButton"]},pt,t)})(i({},t,{classes:d.classes})),f=e.useCallback((e=>{e.preventDefault(),e.stopPropagation(),u.current.toggleColumnMenu(r.field)}),[u,r.field]),g=r.headerName??r.field;return l.jsx("div",{className:p.root,children:l.jsx(d.slots.baseTooltip,i({title:u.current.getLocaleText("columnMenuLabel"),enterDelay:1e3},d.slotProps?.baseTooltip,{children:l.jsx(d.slots.baseIconButton,i({ref:s,tabIndex:-1,className:p.button,"aria-label":u.current.getLocaleText("columnMenuAriaLabel")(g),size:"small",onClick:f,"aria-haspopup":"menu","aria-expanded":n,"aria-controls":n?o:void 0,id:a},d.slotProps?.baseIconButton,{children:l.jsx(d.slots.columnMenuIcon,{fontSize:"inherit"})}))}))})}));function Ua({columnMenuId:e,columnMenuButtonId:t,ContentComponent:r,contentComponentProps:n,field:o,open:a,target:s,onExited:u}){const c=fe(),d=c.current.getColumn(o),p=I((e=>{e&&(e.stopPropagation(),s?.contains(e.target))||c.current.hideColumnMenu()}));return s&&d?l.jsx(Jr,{placement:"bottom-"+("right"===d.align?"start":"end"),open:a,target:s,onClose:p,onExited:u,children:l.jsx(r,i({colDef:d,hideMenu:p,open:a,id:e,labelledby:t},n))}):null}function Ka(e,t){return e.closest(`.${t}`)}function qa(e){return e.replace(/["\\]/g,"\\$&")}function Xa(e){return`.${ft.row}[data-id="${qa(String(e))}"]`}function Ya(e){return 1===e.target.nodeType&&!e.currentTarget.contains(e.target)}function Qa(e,t){return e.rootElementRef.current.querySelector(`.${ft[t]}`)}const Za=({api:e,colIndex:t,position:r,filterFn:n})=>{if(null===t)return[];const o=[];return es(e).forEach((e=>{e.getAttribute("data-id")&&e.querySelectorAll(`.${ft["left"===r?"cell--pinnedLeft":"cell--pinnedRight"]}`).forEach((e=>{const t=ts(e);null!==t&&n(t)&&o.push(e)}))})),o};const Ja=({api:e,colIndex:t,position:r,filterFn:n})=>{if(!e.columnHeadersContainerRef?.current)return[];if(null===t)return[];const o=[];return e.columnHeadersContainerRef.current.querySelectorAll(`.${ft["left"===r?"columnHeader--pinnedLeft":"columnHeader--pinnedRight"]}`).forEach((e=>{const t=ts(e);null!==t&&n(t,e)&&o.push(e)})),o};function es(e){return e.virtualScrollerRef.current.querySelectorAll(`:scope > div > div > .${ft.row}`)}function ts(e){const t=e.getAttribute("aria-colindex");return t?Number(t)-1:null}const rs=["className","aria-label"],ns=M("div",{name:"MuiDataGrid",slot:"ColumnHeaderTitle",overridesResolver:(e,t)=>t.columnHeaderTitle})({textOverflow:"ellipsis",overflow:"hidden",whiteSpace:"nowrap",fontWeight:"var(--unstable_DataGrid-headWeight)",lineHeight:"normal"}),os=be((function(e,t){const{className:r}=e,n=a(e,rs),o=me(),s=(e=>{const{classes:t}=e;return c({root:["columnHeaderTitle"]},pt,t)})(o);return l.jsx(ns,i({className:m(s.root,r),ownerState:o},n,{ref:t}))}));function ls(t){const{label:r,description:n}=t,o=me(),a=e.useRef(null),[s,u]=e.useState(""),c=e.useCallback((()=>{if(!n&&a?.current){const t=(e=a.current).scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth;u(t?r:"")}var e}),[n,r]);return l.jsx(o.slots.baseTooltip,i({title:n||s},o.slotProps?.baseTooltip,{children:l.jsx(os,{onMouseOver:c,ref:a,children:r})}))}const is=["resizable","resizing","height","side"];var as=function(e){return e.Left="left",e.Right="right",e}(as||{});function ss(t){const{height:r,side:n=as.Right}=t,o=a(t,is),s=me(),u=(e=>{const{resizable:t,resizing:r,classes:n,side:o}=e,l={root:["columnSeparator",t&&"columnSeparator--resizable",r&&"columnSeparator--resizing",o&&`columnSeparator--side${H(o)}`],icon:["iconSeparator"]};return c(l,pt,n)})(i({},t,{side:n,classes:s.classes})),d=e.useCallback((e=>{e.preventDefault(),e.stopPropagation()}),[]);return l.jsx("div",i({className:u.root,style:{minHeight:r}},o,{onClick:d,children:l.jsx(s.slots.columnResizeIcon,{className:u.icon})}))}const us=e.memo(ss),cs=["classes","columnMenuOpen","colIndex","height","isResizing","sortDirection","hasFocus","tabIndex","separatorSide","isDraggable","headerComponent","description","elementId","width","columnMenuIconButton","columnMenu","columnTitleIconButtons","headerClassName","label","resizable","draggableContainerProps","columnHeaderSeparatorProps","style"],ds=be((function(t,r){const{classes:n,colIndex:o,height:s,isResizing:u,sortDirection:c,hasFocus:d,tabIndex:p,separatorSide:f,isDraggable:g,headerComponent:h,description:b,width:w,columnMenuIconButton:C=null,columnMenu:v=null,columnTitleIconButtons:x=null,headerClassName:S,label:R,resizable:I,draggableContainerProps:M,columnHeaderSeparatorProps:k,style:P}=t,E=a(t,cs),F=_l(),H=me(),D=e.useRef(null),T=y(D,r);let O="none";return null!=c&&(O="asc"===c?"ascending":"descending"),e.useLayoutEffect((()=>{const e=F.current.state.columnMenu;if(d&&!e.open){const e=D.current.querySelector('[tabindex="0"]')||D.current;e?.focus(),F.current.columnHeadersContainerRef?.current&&(F.current.columnHeadersContainerRef.current.scrollLeft=0)}}),[F,d]),l.jsxs("div",i({className:m(n.root,S),style:i({},P,{height:s,width:w}),role:"columnheader",tabIndex:p,"aria-colindex":o+1,"aria-sort":O},E,{ref:T,children:[l.jsxs("div",i({className:n.draggableContainer,draggable:g,role:"presentation"},M,{children:[l.jsxs("div",{className:n.titleContainer,role:"presentation",children:[l.jsx("div",{className:n.titleContainerContent,children:void 0!==h?h:l.jsx(ls,{label:R,description:b,columnWidth:w})}),x]}),C]})),l.jsx(us,i({resizable:!H.disableColumnResize&&!!I,resizing:u,height:s,side:f},k)),v]}))}));const ps=ct((function(t){const{colDef:r,columnMenuOpen:n,colIndex:o,headerHeight:a,isResizing:s,isLast:u,sortDirection:d,sortIndex:p,filterItemsCounter:g,hasFocus:h,tabIndex:b,disableReorder:w,separatorSide:C,showLeftBorder:x,showRightBorder:y,pinnedPosition:S,pinnedOffset:R}=t,I=_l(),M=me(),k=v(),P=e.useRef(null),E=f(),F=f(),H=e.useRef(null),[D,T]=e.useState(n),O=e.useMemo((()=>!M.disableColumnReorder&&!w&&!r.disableReorder),[M.disableColumnReorder,w,r.disableReorder]);let L;r.renderHeader&&(L=r.renderHeader(I.current.getColumnHeaderParams(r.field)));const $=(e=>{const{colDef:t,classes:r,isDragging:n,sortDirection:o,showRightBorder:l,showLeftBorder:i,filterItemsCounter:a,pinnedPosition:s,isLastUnpinned:u,isSiblingFocused:d}=e,p=null!=o,f=null!=a&&a>0,g="number"===t.type,m={root:["columnHeader","left"===t.headerAlign&&"columnHeader--alignLeft","center"===t.headerAlign&&"columnHeader--alignCenter","right"===t.headerAlign&&"columnHeader--alignRight",t.sortable&&"columnHeader--sortable",n&&"columnHeader--moving",p&&"columnHeader--sorted",f&&"columnHeader--filtered",g&&"columnHeader--numeric","withBorderColor",l&&"columnHeader--withRightBorder",i&&"columnHeader--withLeftBorder",s===Cn.LEFT&&"columnHeader--pinnedLeft",s===Cn.RIGHT&&"columnHeader--pinnedRight",u&&"columnHeader--lastUnpinned",d&&"columnHeader--siblingFocused"],draggableContainer:["columnHeaderDraggableContainer"],titleContainer:["columnHeaderTitleContainer"],titleContainerContent:["columnHeaderTitleContainerContent"]};return c(m,pt,r)})(i({},t,{classes:M.classes,showRightBorder:y,showLeftBorder:x})),z=e.useCallback((e=>t=>{Ya(t)||I.current.publishEvent(e,I.current.getColumnHeaderParams(r.field),t)}),[I,r.field]),j=e.useMemo((()=>({onClick:z("columnHeaderClick"),onContextMenu:z("columnHeaderContextMenu"),onDoubleClick:z("columnHeaderDoubleClick"),onMouseOver:z("columnHeaderOver"),onMouseOut:z("columnHeaderOut"),onMouseEnter:z("columnHeaderEnter"),onMouseLeave:z("columnHeaderLeave"),onKeyDown:z("columnHeaderKeyDown"),onFocus:z("columnHeaderFocus"),onBlur:z("columnHeaderBlur")})),[z]),V=e.useMemo((()=>O?{onDragStart:z("columnHeaderDragStart"),onDragEnter:z("columnHeaderDragEnter"),onDragOver:z("columnHeaderDragOver"),onDragEnd:z("columnHeaderDragEnd")}:{}),[O,z]),A=e.useMemo((()=>({onMouseDown:z("columnSeparatorMouseDown"),onDoubleClick:z("columnSeparatorDoubleClick")})),[z]);e.useEffect((()=>{D||T(n)}),[D,n]);const N=e.useCallback((()=>{T(!1)}),[]),G=!M.disableColumnMenu&&!r.disableColumnMenu&&l.jsx(_a,{colDef:r,columnMenuId:E,columnMenuButtonId:F,open:D,iconButtonRef:H}),B=l.jsx(Ua,{columnMenuId:E,columnMenuButtonId:F,field:r.field,open:n,target:H.current,ContentComponent:M.slots.columnMenu,contentComponentProps:M.slotProps?.columnMenu,onExited:N}),W=r.sortingOrder??M.sortingOrder,_=(r.sortable||null!=d)&&!r.hideSortIcons&&!M.disableColumnSorting,U=l.jsxs(e.Fragment,{children:[!M.disableColumnFilter&&l.jsx(M.slots.columnHeaderFilterIconButton,i({field:r.field,counter:g},M.slotProps?.columnHeaderFilterIconButton)),_&&l.jsx(M.slots.columnHeaderSortIcon,i({field:r.field,direction:d,index:p,sortingOrder:W,disabled:!r.sortable},M.slotProps?.columnHeaderSortIcon))]});e.useLayoutEffect((()=>{const e=I.current.state.columnMenu;if(h&&!e.open){const e=P.current.querySelector('[tabindex="0"]')||P.current;e?.focus(),I.current.columnHeadersContainerRef?.current&&(I.current.columnHeadersContainerRef.current.scrollLeft=0)}}),[I,h]);const K="function"==typeof r.headerClassName?r.headerClassName({field:r.field,colDef:r}):r.headerClassName,q=r.headerName??r.field,X=e.useMemo((()=>fa(i({},t.style),k,S,R)),[S,R,t.style,k]);return l.jsx(ds,i({ref:P,classes:$,columnMenuOpen:n,colIndex:o,height:a,isResizing:s,sortDirection:d,hasFocus:h,tabIndex:b,separatorSide:C,isDraggable:O,headerComponent:L,description:r.description,elementId:r.field,width:r.computedWidth,columnMenuIconButton:G,columnTitleIconButtons:U,headerClassName:m(K,u&&ft["columnHeader--last"]),label:q,resizable:!M.disableColumnResize&&!!r.resizable,"data-field":r.field,columnMenu:B,draggableContainerProps:V,columnHeaderSeparatorProps:A,style:X},j))})),fs=["className"],gs=M("div",{name:"MuiDataGrid",slot:"IconButtonContainer",overridesResolver:(e,t)=>t.iconButtonContainer})((()=>({display:"flex",visibility:"hidden",width:0}))),ms=be((function(e,t){const{className:r}=e,n=a(e,fs),o=me(),s=(e=>{const{classes:t}=e;return c({root:["iconButtonContainer"]},pt,t)})(o);return l.jsx(gs,i({className:m(s.root,r),ownerState:o},n,{ref:t}))})),hs=["direction","index","sortingOrder","disabled"];function bs(e){const{direction:t,index:r,sortingOrder:n,disabled:o}=e,s=a(e,hs),u=fe(),d=me(),p=(e=>{const{classes:t}=e;return c({icon:["sortIcon"]},pt,t)})(i({},e,{classes:d.classes})),f=function(e,t,r,n){let o;const a={};return"asc"===t?o=e.columnSortedAscendingIcon:"desc"===t?o=e.columnSortedDescendingIcon:(o=e.columnUnsortedIcon,a.sortingOrder=n),o?l.jsx(o,i({fontSize:"small",className:r},a)):null}(d.slots,t,p.icon,n);if(!f)return null;const g=l.jsx(d.slots.baseIconButton,i({tabIndex:-1,"aria-label":u.current.getLocaleText("columnHeaderSortIconLabel"),title:u.current.getLocaleText("columnHeaderSortIconLabel"),size:"small",disabled:o},d.slotProps?.baseIconButton,s,{children:f}));return l.jsxs(ms,{children:[null!=r&&l.jsx(d.slots.baseBadge,{badgeContent:r,color:"default",overlap:"circular",children:g}),null==r&&g]})}const ws=e.memo(bs);function Cs(t){const{counter:r,field:n,onClick:o}=t,a=fe(),s=me(),u=(e=>{const{classes:t}=e;return c({icon:["filterIcon"]},pt,t)})(i({},t,{classes:s.classes})),d=f(),p=_e(a,ea,d),g=f(),m=e.useCallback((e=>{e.preventDefault(),e.stopPropagation();const{open:t,openedPanelValue:r}=Ji(a.current.state);t&&r===ta.filters?a.current.hideFilterPanel():a.current.showFilterPanel(void 0,g,d),o&&o(a.current.getColumnHeaderParams(n),e)}),[a,n,o,g,d]);if(!r)return null;const h=l.jsx(s.slots.baseIconButton,i({id:d,onClick:m,color:"default","aria-label":a.current.getLocaleText("columnHeaderFiltersLabel"),size:"small",tabIndex:-1,"aria-haspopup":"menu","aria-expanded":p,"aria-controls":p?g:void 0},s.slotProps?.baseIconButton,{children:l.jsx(s.slots.columnFilteredIcon,{className:u.icon,fontSize:"small"})}));return l.jsx(s.slots.baseTooltip,i({title:a.current.getLocaleText("columnHeaderFiltersTooltipActive")(r),enterDelay:1e3},s.slotProps?.baseTooltip,{children:l.jsxs(ms,{children:[r>1&&l.jsx(s.slots.baseBadge,{badgeContent:r,color:"default",children:h}),1===r&&h]})}))}const vs=j(l.jsx("path",{d:"M4 12l1.41 1.41L11 7.83V20h2V7.83l5.58 5.59L20 12l-8-8-8 8z"})),xs=j(l.jsx("path",{d:"M20 12l-1.41-1.41L13 16.17V4h-2v12.17l-5.58-5.59L4 12l8 8 8-8z"})),ys=j(l.jsx("path",{d:"M8.59 16.59 13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z"})),Ss=j(l.jsx("path",{d:"M16.59 8.59 12 13.17 7.41 8.59 6 10l6 6 6-6z"})),Rs=j(l.jsx("path",{d:"M10 18h4v-2h-4v2zM3 6v2h18V6H3zm3 7h12v-2H6v2z"})),Is=j(l.jsx("path",{d:"M4.25 5.61C6.27 8.2 10 13 10 13v6c0 .55.45 1 1 1h2c.55 0 1-.45 1-1v-6s3.72-4.8 5.74-7.39c.51-.66.04-1.61-.79-1.61H5.04c-.83 0-1.3.95-.79 1.61z"})),Ms=j(l.jsx("path",{d:"M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"}));j(l.jsx("path",{d:"M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"})),j(l.jsx("path",{d:"M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"}));const ks=j(l.jsx("path",{d:"M6 5H3c-.55 0-1 .45-1 1v12c0 .55.45 1 1 1h3c.55 0 1-.45 1-1V6c0-.55-.45-1-1-1zm14 0h-3c-.55 0-1 .45-1 1v12c0 .55.45 1 1 1h3c.55 0 1-.45 1-1V6c0-.55-.45-1-1-1zm-7 0h-3c-.55 0-1 .45-1 1v12c0 .55.45 1 1 1h3c.55 0 1-.45 1-1V6c0-.55-.45-1-1-1z"})),Ps=j(l.jsx("rect",{width:"1",height:"24",x:"11.5",rx:"0.5"})),Es=j(l.jsx("path",{d:"M4 15h16v-2H4v2zm0 4h16v-2H4v2zm0-8h16V9H4v2zm0-6v2h16V5H4z"})),Fs=j(l.jsx("path",{d:"M21,8H3V4h18V8z M21,10H3v4h18V10z M21,16H3v4h18V16z"})),Hs=j(l.jsx("path",{d:"M4 18h17v-6H4v6zM4 5v6h17V5H4z"})),Ds=j(l.jsx("path",{d:"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})),Ts=j(l.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"})),Os=j(l.jsx("path",{d:"M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"})),Ls=j(l.jsx("path",{d:"M19 13H5v-2h14v2z"})),$s=j(l.jsx("path",{d:"M12 4V1L8 5l4 4V6c3.31 0 6 2.69 6 6 0 1.01-.25 1.97-.7 2.8l1.46 1.46C19.54 15.03 20 13.57 20 12c0-4.42-3.58-8-8-8zm0 14c-3.31 0-6-2.69-6-6 0-1.01.25-1.97.7-2.8L5.24 7.74C4.46 8.97 4 10.43 4 12c0 4.42 3.58 8 8 8v3l4-4-4-4v3z"})),zs=j(l.jsx("path",{d:"M11 18c0 1.1-.9 2-2 2s-2-.9-2-2 .9-2 2-2 2 .9 2 2zm-2-8c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm6 4c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})),js=j(l.jsx("path",{d:"M19 12v7H5v-7H3v7c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-7h-2zm-6 .67l2.59-2.58L17 11.5l-5 5-5-5 1.41-1.41L11 12.67V3h2z"})),Vs=j(l.jsx("path",{d:"M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"})),As=j(l.jsx("path",{d:"M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})),Ns=j(l.jsx("path",{d:"M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"})),Gs=j(l.jsx("g",{children:l.jsx("path",{d:"M14.67,5v14H9.33V5H14.67z M15.67,19H21V5h-5.33V19z M8.33,19V5H3v14H8.33z"})})),Bs=j(l.jsx("path",{d:"M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"}));j(l.jsx("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"}));const Ws=j(l.jsx("path",{d:"M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zm2.46-7.12l1.41-1.41L12 12.59l2.12-2.12 1.41 1.41L13.41 14l2.12 2.12-1.41 1.41L12 15.41l-2.12 2.12-1.41-1.41L10.59 14l-2.13-2.12zM15.5 4l-1-1h-5l-1 1H5v2h14V4z"}));function _s(e){return 1===e.key.length&&!e.ctrlKey&&!e.metaKey}const Us=e=>0===e.indexOf("Arrow")||0===e.indexOf("Page")||" "===e||"Home"===e||"End"===e,Ks=e=>"Tab"===e||"Escape"===e;function qs(e){return(e.ctrlKey||e.metaKey)&&"V"===String.fromCharCode(e.keyCode)&&!e.shiftKey&&!e.altKey}const Xs=["hideMenu","colDef","id","labelledby","className","children","open"],Ys=u(x)((()=>({minWidth:248}))),Qs=be((function(t,r){const{hideMenu:n,id:o,labelledby:s,className:u,children:c,open:d}=t,p=a(t,Xs),f=e.useCallback((e=>{"Tab"===e.key&&e.preventDefault(),Ks(e.key)&&n(e)}),[n]);return l.jsx(Ys,i({id:o,className:m(ft.menuList,u),"aria-labelledby":s,onKeyDown:f,autoFocus:d},p,{ref:r,children:c}))})),Zs=["displayOrder"];function Js(t){const{colDef:r,onClick:n}=t,o=fe(),i=me(),a=1===kr(o).filter((e=>!0!==e.disableColumnMenu)).length,s=e.useCallback((e=>{a||(o.current.setColumnVisibility(r.field,!1),n(e))}),[o,r.field,n,a]);return i.disableColumnSelector||!1===r.hideable?null:l.jsxs(V,{onClick:s,disabled:a,children:[l.jsx(A,{children:l.jsx(i.slots.columnMenuHideIcon,{fontSize:"small"})}),l.jsx(N,{children:o.current.getLocaleText("columnMenuHideColumn")})]})}function eu(t){const{onClick:r}=t,n=fe(),o=me(),i=e.useCallback((e=>{r(e),n.current.showPreferences(ta.columns)}),[n,r]);return o.disableColumnSelector?null:l.jsxs(V,{onClick:i,children:[l.jsx(A,{children:l.jsx(o.slots.columnMenuManageColumnsIcon,{fontSize:"small"})}),l.jsx(N,{children:n.current.getLocaleText("columnMenuManageColumns")})]})}const tu=["defaultSlots","defaultSlotProps","slots","slotProps"],ru={columnMenuSortItem:function(t){const{colDef:r,onClick:n}=t,o=fe(),i=We(o,Tn),a=me(),s=e.useMemo((()=>{if(!r)return null;const e=i.find((e=>e.field===r.field));return e?.sort}),[r,i]),u=r.sortingOrder??a.sortingOrder,c=e.useCallback((e=>{n(e);const t=e.currentTarget.getAttribute("data-value")||null;o.current.sortColumn(r.field,t===s?null:t)}),[o,r,n,s]);if(a.disableColumnSorting||!r||!r.sortable||!u.some((e=>!!e)))return null;const d=e=>{const t=o.current.getLocaleText(e);return"function"==typeof t?t(r):t};return l.jsxs(e.Fragment,{children:[u.includes("asc")&&"asc"!==s?l.jsxs(V,{onClick:c,"data-value":"asc",children:[l.jsx(A,{children:l.jsx(a.slots.columnMenuSortAscendingIcon,{fontSize:"small"})}),l.jsx(N,{children:d("columnMenuSortAsc")})]}):null,u.includes("desc")&&"desc"!==s?l.jsxs(V,{onClick:c,"data-value":"desc",children:[l.jsx(A,{children:l.jsx(a.slots.columnMenuSortDescendingIcon,{fontSize:"small"})}),l.jsx(N,{children:d("columnMenuSortDesc")})]}):null,u.includes(null)&&null!=s?l.jsxs(V,{onClick:c,children:[l.jsx(A,{}),l.jsx(N,{children:o.current.getLocaleText("columnMenuUnsort")})]}):null]})},columnMenuFilterItem:function(t){const{colDef:r,onClick:n}=t,o=fe(),i=me(),a=e.useCallback((e=>{n(e),o.current.showFilterPanel(r.field)}),[o,r.field,n]);return i.disableColumnFilter||!r.filterable?null:l.jsxs(V,{onClick:a,children:[l.jsx(A,{children:l.jsx(i.slots.columnMenuFilterIcon,{fontSize:"small"})}),l.jsx(N,{children:o.current.getLocaleText("columnMenuFilter")})]})},columnMenuColumnsItem:function(t){return l.jsxs(e.Fragment,{children:[l.jsx(Js,i({},t)),l.jsx(eu,i({},t))]})}},nu={columnMenuSortItem:{displayOrder:10},columnMenuFilterItem:{displayOrder:20},columnMenuColumnsItem:{displayOrder:30}},ou=be((function(t,r){const{defaultSlots:n,defaultSlotProps:o,slots:s,slotProps:u}=t,c=a(t,tu),d=(t=>{const r=_l(),n=me(),{defaultSlots:o,defaultSlotProps:l,slots:s={},slotProps:u={},hideMenu:c,colDef:d,addDividers:p=!0}=t,f=e.useMemo((()=>i({},o,s)),[o,s]),g=e.useMemo((()=>{if(!u||0===Object.keys(u).length)return l;const e=i({},u);return Object.entries(l).forEach((([t,r])=>{e[t]=i({},r,u[t]||{})})),e}),[l,u]),m=r.current.unstable_applyPipeProcessors("columnMenu",[],t.colDef),h=e.useMemo((()=>{const e=Object.keys(o);return Object.keys(s).filter((t=>!e.includes(t)))}),[s,o]);return e.useMemo((()=>{const e=Array.from(new Set([...m,...h])).filter((e=>null!=f[e])).sort(((e,t)=>{const r=g[e],n=g[t];return(Number.isFinite(r?.displayOrder)?r.displayOrder:100)-(Number.isFinite(n?.displayOrder)?n.displayOrder:100)}));return e.reduce(((t,r,o)=>{let l={colDef:d,onClick:c};const s=g[r];if(s){const e=a(s,Zs);l=i({},l,e)}return p&&o!==e.length-1?[...t,[f[r],l],[n.slots.baseDivider,{}]]:[...t,[f[r],l]]}),[])}),[p,d,m,c,f,g,h,n.slots.baseDivider])})(i({},c,{defaultSlots:n,defaultSlotProps:o,slots:s,slotProps:u}));return l.jsx(Qs,i({},c,{ref:r,children:d.map((([e,t],r)=>l.jsx(e,i({},t),r)))}))})),lu=be((function(e,t){return l.jsx(ou,i({},e,{ref:t,defaultSlots:ru,defaultSlotProps:nu}))})),iu=["className","slotProps"],au=u("div",{name:"MuiDataGrid",slot:"PanelWrapper",overridesResolver:(e,t)=>t.panelWrapper})({display:"flex",flexDirection:"column",flex:1,"&:focus":{outline:0}}),su=()=>!0,uu=be((function(e,t){const{className:r,slotProps:n={}}=e,o=a(e,iu),s=me(),u=(e=>{const{classes:t}=e;return c({root:["panelWrapper"]},pt,t)})(s);return l.jsx(G,i({open:!0,disableEnforceFocus:!0,isEnabled:su},n.TrapFocus,{children:l.jsx(au,i({tabIndex:-1,className:m(u.root,r),ownerState:s},o,{ref:t}))}))}));const cu=["children","className","classes"],du=n("MuiDataGrid",["panel","paper"]),pu=u(C,{name:"MuiDataGrid",slot:"Panel",overridesResolver:(e,t)=>t.panel})((({theme:e})=>({zIndex:e.zIndex.modal}))),fu=u(w,{name:"MuiDataGrid",slot:"Paper",overridesResolver:(e,t)=>t.paper})((({theme:e})=>({backgroundColor:(e.vars||e).palette.background.paper,minWidth:300,maxHeight:450,display:"flex",maxWidth:`calc(100vw - ${e.spacing(.5)})`,overflow:"auto"}))),gu=be(((t,r)=>{const{children:n,className:o}=t,s=a(t,cu),u=fe(),c=me(),d=du,[p,f]=e.useState(!1),g=e.useCallback((()=>{u.current.hidePreferences()}),[u]),b=e.useCallback((e=>{"Escape"===e.key&&u.current.hidePreferences()}),[u]),w=e.useMemo((()=>[{name:"flip",enabled:!0,options:{rootBoundary:"document"}},{name:"isPlaced",enabled:!0,phase:"main",fn:()=>{f(!0)},effect:()=>()=>{f(!1)}}]),[]),[C,v]=e.useState(null);return e.useEffect((()=>{const e=u.current.rootElementRef?.current?.querySelector('[data-id="gridPanelAnchor"]');e&&v(e)}),[u]),C?l.jsx(pu,i({placement:"bottom-start",className:m(d.panel,o),ownerState:c,anchorEl:C,modifiers:w},s,{ref:r,children:l.jsx(h,{mouseEvent:"onPointerUp",touchEvent:!1,onClickAway:g,children:l.jsx(fu,{className:d.paper,ownerState:c,elevation:8,onKeyDown:b,children:p&&n})})})):null})),mu=["className"],hu=M("div",{name:"MuiDataGrid",slot:"PanelContent",overridesResolver:(e,t)=>t.panelContent})({display:"flex",flexDirection:"column",overflow:"auto",flex:"1 1",maxHeight:400});function bu(e){const{className:t}=e,r=a(e,mu),n=me(),o=(e=>{const{classes:t}=e;return c({root:["panelContent"]},pt,t)})(n);return l.jsx(hu,i({className:m(o.root,t),ownerState:n},r))}const wu=["className"],Cu=M("div",{name:"MuiDataGrid",slot:"PanelFooter",overridesResolver:(e,t)=>t.panelFooter})((({theme:e})=>({padding:e.spacing(.5),display:"flex",justifyContent:"space-between"})));function vu(e){const{className:t}=e,r=a(e,wu),n=me(),o=(e=>{const{classes:t}=e;return c({root:["panelFooter"]},pt,t)})(n);return l.jsx(Cu,i({className:m(o.root,t),ownerState:n},r))}const xu=["item","hasMultipleFilters","deleteFilter","applyFilterChanges","showMultiFilterOperators","disableMultiFilterOperator","applyMultiFilterOperatorChanges","focusElementRef","logicOperators","columnsSort","filterColumns","deleteIconProps","logicOperatorInputProps","operatorInputProps","columnInputProps","valueInputProps","readOnly","children"],yu=["InputComponentProps"],Su=u("div",{name:"MuiDataGrid",slot:"FilterForm",overridesResolver:(e,t)=>t.filterForm})((({theme:e})=>({display:"flex",padding:e.spacing(1)}))),Ru=u("div",{name:"MuiDataGrid",slot:"FilterFormDeleteIcon",overridesResolver:(e,t)=>t.filterFormDeleteIcon})((({theme:e})=>({flexShrink:0,justifyContent:"flex-end",marginRight:e.spacing(.5),marginBottom:e.spacing(.2)}))),Iu=u("div",{name:"MuiDataGrid",slot:"FilterFormLogicOperatorInput",overridesResolver:(e,t)=>t.filterFormLogicOperatorInput})({minWidth:55,marginRight:5,justifyContent:"end"}),Mu=u("div",{name:"MuiDataGrid",slot:"FilterFormColumnInput",overridesResolver:(e,t)=>t.filterFormColumnInput})({width:150}),ku=u("div",{name:"MuiDataGrid",slot:"FilterFormOperatorInput",overridesResolver:(e,t)=>t.filterFormOperatorInput})({width:150}),Pu=u("div",{name:"MuiDataGrid",slot:"FilterFormValueInput",overridesResolver:(e,t)=>t.filterFormValueInput})({width:190}),Eu=e=>e.headerName||e.field,Fu=new Intl.Collator,Hu=be((function(t,r){const{item:n,hasMultipleFilters:o,deleteFilter:s,applyFilterChanges:u,showMultiFilterOperators:d,disableMultiFilterOperator:p,applyMultiFilterOperatorChanges:g,focusElementRef:h,logicOperators:b=[dr.And,dr.Or],columnsSort:w,filterColumns:C,deleteIconProps:v={},logicOperatorInputProps:x={},operatorInputProps:y={},columnInputProps:S={},valueInputProps:R={},readOnly:I}=t,M=a(t,xu),k=fe(),P=We(k,Rr),E=We(k,Hr),F=We(k,$n),D=f(),T=f(),O=f(),L=f(),$=me(),z=(e=>{const{classes:t}=e;return c({root:["filterForm"],deleteIcon:["filterFormDeleteIcon"],logicOperatorInput:["filterFormLogicOperatorInput"],columnInput:["filterFormColumnInput"],operatorInput:["filterFormOperatorInput"],valueInput:["filterFormValueInput"]},pt,t)})($),j=e.useRef(null),V=e.useRef(null),A=F.logicOperator??dr.And,N=o&&b.length>0,G=$.slotProps?.baseFormControl||{},B=($.slotProps?.baseSelect||{}).native??!1,W=$.slotProps?.baseInputLabel||{},_=$.slotProps?.baseSelectOption||{},{InputComponentProps:U}=R,K=a(R,yu),{filteredColumns:q,selectedField:X}=e.useMemo((()=>{let e=n.field;const t=!1===P[n.field].filterable?P[n.field]:null;if(t)return{filteredColumns:[t],selectedField:e};if(void 0===C||"function"!=typeof C)return{filteredColumns:E,selectedField:e};const r=C({field:n.field,columns:E,currentFilters:F?.items||[]});return{filteredColumns:E.filter((t=>{const o=r.includes(t.field);return t.field!==n.field||o||(e=void 0),o})),selectedField:e}}),[C,F?.items,E,n.field,P]),Y=e.useMemo((()=>{switch(w){case"asc":return q.sort(((e,t)=>Fu.compare(Eu(e),Eu(t))));case"desc":return q.sort(((e,t)=>-Fu.compare(Eu(e),Eu(t))));default:return q}}),[q,w]),Q=n.field?k.current.getColumn(n.field):null,Z=e.useMemo((()=>n.operator&&Q?Q.filterOperators?.find((e=>e.value===n.operator)):null),[n,Q]),J=e.useCallback((e=>{const t=e.target.value,r=k.current.getColumn(t);if(r.field===Q.field)return;const o=r.filterOperators.find((e=>e.value===n.operator))||r.filterOperators[0];let l=!o.InputComponent||o.InputComponent!==Z?.InputComponent||r.type!==Q.type?void 0:n.value;if("singleSelect"===r.type&&void 0!==l){const e=r,t=tl(e);Array.isArray(l)?l=l.filter((r=>void 0!==rl(r,t,e?.getOptionValue))):void 0===rl(n.value,t,e?.getOptionValue)&&(l=void 0)}u(i({},n,{field:t,operator:o.value,value:l}))}),[k,u,n,Q,Z]),ee=e.useCallback((e=>{const t=e.target.value,r=Q?.filterOperators.find((e=>e.value===t));u(i({},n,{operator:t,value:!r?.InputComponent||r?.InputComponent!==Z?.InputComponent?void 0:n.value}))}),[u,n,Q,Z]),te=e.useCallback((e=>{const t=e.target.value===dr.And.toString()?dr.And:dr.Or;g(t)}),[g]);return e.useImperativeHandle(h,(()=>({focus:()=>{Z?.InputComponent?j?.current?.focus():V.current.focus()}})),[Z]),l.jsxs(Su,i({className:z.root,"data-id":n.id,ownerState:$},M,{ref:r,children:[l.jsx(Ru,i({variant:"standard",as:$.slots.baseFormControl},G,v,{className:m(z.deleteIcon,G.className,v.className),ownerState:$,children:l.jsx($.slots.baseIconButton,i({"aria-label":k.current.getLocaleText("filterPanelDeleteIconLabel"),title:k.current.getLocaleText("filterPanelDeleteIconLabel"),onClick:()=>{s(n)},size:"small",disabled:I},$.slotProps?.baseIconButton,{children:l.jsx($.slots.filterPanelDeleteIcon,{fontSize:"small"})}))})),l.jsx(Iu,i({variant:"standard",as:$.slots.baseFormControl},G,x,{sx:[N?{display:"flex"}:{display:"none"},d?{visibility:"visible"}:{visibility:"hidden"},G.sx,x.sx],className:m(z.logicOperatorInput,G.className,x.className),ownerState:$,children:l.jsx($.slots.baseSelect,i({inputProps:{"aria-label":k.current.getLocaleText("filterPanelLogicOperator")},value:A??"",onChange:te,disabled:!!p||1===b.length,native:B},$.slotProps?.baseSelect,{children:b.map((t=>e.createElement($.slots.baseSelectOption,i({},_,{native:B,key:t.toString(),value:t.toString()}),k.current.getLocaleText((e=>{switch(e){case dr.And:return"filterPanelOperatorAnd";case dr.Or:return"filterPanelOperatorOr";default:throw new Error("MUI X: Invalid `logicOperator` property in the `GridFilterPanel`.")}})(t)))))}))})),l.jsxs(Mu,i({variant:"standard",as:$.slots.baseFormControl},G,S,{className:m(z.columnInput,G.className,S.className),ownerState:$,children:[l.jsx($.slots.baseInputLabel,i({},W,{htmlFor:D,id:T,children:k.current.getLocaleText("filterPanelColumns")})),l.jsx($.slots.baseSelect,i({labelId:T,id:D,label:k.current.getLocaleText("filterPanelColumns"),value:X??"",onChange:J,native:B,disabled:I},$.slotProps?.baseSelect,{children:Y.map((t=>e.createElement($.slots.baseSelectOption,i({},_,{native:B,key:t.field,value:t.field}),Eu(t))))}))]})),l.jsxs(ku,i({variant:"standard",as:$.slots.baseFormControl},G,y,{className:m(z.operatorInput,G.className,y.className),ownerState:$,children:[l.jsx($.slots.baseInputLabel,i({},W,{htmlFor:O,id:L,children:k.current.getLocaleText("filterPanelOperator")})),l.jsx($.slots.baseSelect,i({labelId:L,label:k.current.getLocaleText("filterPanelOperator"),id:O,value:n.operator,onChange:ee,native:B,inputRef:V,disabled:I},$.slotProps?.baseSelect,{children:Q?.filterOperators?.map((t=>e.createElement($.slots.baseSelectOption,i({},_,{native:B,key:t.value,value:t.value}),t.label||k.current.getLocaleText(`filterOperator${H(t.value)}`))))}))]})),l.jsx(Pu,i({variant:"standard",as:$.slots.baseFormControl},G,K,{className:m(z.valueInput,G.className,K.className),ownerState:$,children:Z?.InputComponent?l.jsx(Z.InputComponent,i({apiRef:k,item:n,applyValue:u,focusElementRef:j,disabled:I},Z.InputComponentProps,U),n.field):null}))]}))})),Du=["logicOperators","columnsSort","filterFormProps","getColumnForNewFilter","children","disableAddFilterButton","disableRemoveAllButton"],Tu=e=>({field:e.field,operator:e.filterOperators[0].value,id:Math.round(1e5*Math.random())}),Ou=be((function(t,r){const n=fe(),o=me(),s=We(n,$n),u=We(n,Hr),c=We(n,Dr),d=e.useRef(null),p=e.useRef(null),{logicOperators:f=[dr.And,dr.Or],columnsSort:g,filterFormProps:m,getColumnForNewFilter:h,disableAddFilterButton:b=!1,disableRemoveAllButton:w=!1}=t,C=a(t,Du),v=n.current.upsertFilterItem,x=e.useCallback((e=>{n.current.setFilterLogicOperator(e)}),[n]),y=e.useCallback((()=>{let e;if(h&&"function"==typeof h){const t=h({currentFilters:s?.items||[],columns:u});if(null===t)return null;e=u.find((({field:e})=>e===t))}else e=u.find((e=>e.filterOperators?.length));return e?Tu(e):null}),[s?.items,u,h]),S=e.useCallback((()=>{if(void 0===h||"function"!=typeof h)return y();const e=s.items.length?s.items:[y()].filter(Boolean),t=h({currentFilters:e,columns:u});if(null===t)return null;const r=u.find((({field:e})=>e===t));return r?Tu(r):null}),[s.items,u,h,y]),R=e.useMemo((()=>s.items.length?s.items:(p.current||(p.current=y()),p.current?[p.current]:[])),[s.items,y]),I=R.length>1,{readOnlyFilters:M,validFilters:k}=e.useMemo((()=>R.reduce(((e,t)=>(c[t.field]?e.validFilters.push(t):e.readOnlyFilters.push(t),e)),{readOnlyFilters:[],validFilters:[]})),[R,c]),P=e.useCallback((()=>{const e=S();e&&n.current.upsertFilterItems([...R,e])}),[n,S,R]),E=e.useCallback((e=>{const t=1===k.length;n.current.deleteFilterItem(e),t&&n.current.hideFilterPanel()}),[n,k.length]),F=e.useCallback((()=>1===k.length&&void 0===k[0].value?(n.current.deleteFilterItem(k[0]),n.current.hideFilterPanel()):n.current.setFilterModel(i({},s,{items:M}),"removeAllFilterItems")),[n,M,s,k]);return e.useEffect((()=>{f.length>0&&s.logicOperator&&!f.includes(s.logicOperator)&&x(f[0])}),[f,x,s.logicOperator]),e.useEffect((()=>{k.length>0&&d.current.focus()}),[k.length]),l.jsxs(uu,i({},C,{ref:r,children:[l.jsxs(bu,{children:[M.map(((e,t)=>l.jsx(Hu,i({item:e,applyFilterChanges:v,deleteFilter:E,hasMultipleFilters:I,showMultiFilterOperators:t>0,disableMultiFilterOperator:1!==t,applyMultiFilterOperatorChanges:x,focusElementRef:null,readOnly:!0,logicOperators:f,columnsSort:g},m),null==e.id?t:e.id))),k.map(((e,t)=>l.jsx(Hu,i({item:e,applyFilterChanges:v,deleteFilter:E,hasMultipleFilters:I,showMultiFilterOperators:M.length+t>0,disableMultiFilterOperator:M.length+t!==1,applyMultiFilterOperatorChanges:x,focusElementRef:t===k.length-1?d:null,logicOperators:f,columnsSort:g},m),null==e.id?t+M.length:e.id)))]}),o.disableMultipleColumnsFiltering||b&&w?null:l.jsxs(vu,{children:[b?l.jsx("span",{}):l.jsx(o.slots.baseButton,i({onClick:P,startIcon:l.jsx(o.slots.filterPanelAddIcon,{})},o.slotProps?.baseButton,{children:n.current.getLocaleText("filterPanelAddFilter")})),!w&&k.length>0?l.jsx(o.slots.baseButton,i({onClick:F,startIcon:l.jsx(o.slots.filterPanelRemoveAllIcon,{})},o.slotProps?.baseButton,{children:n.current.getLocaleText("filterPanelRemoveAll")})):null]})]}))})),Lu=(e,t)=>(e.headerName||e.field).toLowerCase().indexOf(t)>-1,$u=new Intl.Collator;const zu=u("div",{name:"MuiDataGrid",slot:"ColumnsManagement",overridesResolver:(e,t)=>t.columnsManagement})((({theme:e})=>({padding:e.spacing(0,3,1.5),display:"flex",flexDirection:"column",overflow:"auto",flex:"1 1",maxHeight:400,alignItems:"flex-start"}))),ju=u("div",{name:"MuiDataGrid",slot:"ColumnsManagementHeader",overridesResolver:(e,t)=>t.columnsManagementHeader})((({theme:e})=>({padding:e.spacing(1.5,3)}))),Vu=u(W,{name:"MuiDataGrid",slot:"ColumnsManagementSearchInput",overridesResolver:(e,t)=>t.columnsManagementSearchInput})((({theme:e})=>({[`& .${_.root}`]:{padding:e.spacing(0,1.5,0,1.5)},[`& .${_.input}::-webkit-search-decoration,\n  & .${_.input}::-webkit-search-cancel-button,\n  & .${_.input}::-webkit-search-results-button,\n  & .${_.input}::-webkit-search-results-decoration`]:{display:"none"}}))),Au=u("div",{name:"MuiDataGrid",slot:"ColumnsManagementFooter",overridesResolver:(e,t)=>t.columnsManagementFooter})((({theme:e})=>({padding:e.spacing(.5,1,.5,3),display:"flex",justifyContent:"space-between",borderTop:`1px solid ${e.palette.divider}`}))),Nu=u("div")((({theme:e})=>({padding:e.spacing(.5,0),color:e.palette.grey[500]}))),Gu=be((function(t,r){const{children:n,slotProps:o={}}=t,a=o.button||{},s=o.tooltip||{},u=fe(),c=me(),d=f(),p=f(),[g,m]=e.useState(!1),h=e.useRef(null),b=y(r,h),w=()=>m(!1);return null==n?null:l.jsxs(e.Fragment,{children:[l.jsx(c.slots.baseTooltip,i({title:u.current.getLocaleText("toolbarExportLabel"),enterDelay:1e3},c.slotProps?.baseTooltip,s,{children:l.jsx(c.slots.baseButton,i({size:"small",startIcon:l.jsx(c.slots.exportIcon,{}),"aria-expanded":g,"aria-label":u.current.getLocaleText("toolbarExportLabel"),"aria-haspopup":"menu","aria-controls":g?p:void 0,id:d},c.slotProps?.baseButton,a,{onClick:e=>{m((e=>!e)),a.onClick?.(e)},ref:b,children:u.current.getLocaleText("toolbarExport")}))})),l.jsx(Jr,{open:g,target:h.current,onClose:w,position:"bottom-start",children:l.jsx(x,{id:p,className:ft.menuList,"aria-labelledby":d,onKeyDown:e=>{"Tab"===e.key&&e.preventDefault(),Ks(e.key)&&w()},autoFocusItem:g,children:e.Children.map(n,(t=>e.isValidElement(t)?e.cloneElement(t,{hideMenu:w}):t))})})]})})),Bu=["hideMenu","options"],Wu=["hideMenu","options"],_u=["csvOptions","printOptions","excelOptions"];function Uu(e){const t=fe(),{hideMenu:r,options:n}=e,o=a(e,Bu);return l.jsx(V,i({onClick:()=>{t.current.exportDataAsCsv(n),r?.()}},o,{children:t.current.getLocaleText("toolbarExportCSV")}))}function Ku(e){const t=fe(),{hideMenu:r,options:n}=e,o=a(e,Wu);return l.jsx(V,i({onClick:()=>{t.current.exportDataAsPrint(n),r?.()}},o,{children:t.current.getLocaleText("toolbarExportPrint")}))}be((function(t,r){const n=t,{csvOptions:o={},printOptions:s={},excelOptions:u}=n,c=a(n,_u),d=fe().current.unstable_applyPipeProcessors("exportMenu",[],{excelOptions:u,csvOptions:o,printOptions:s}).sort(((e,t)=>e.componentName>t.componentName?1:-1));return 0===d.length?null:l.jsx(Gu,i({},c,{ref:r,children:d.map(((t,r)=>e.cloneElement(t.component,{key:r})))}))}));const qu=["className","selectedRowCount"],Xu=M("div",{name:"MuiDataGrid",slot:"SelectedRowCount",overridesResolver:(e,t)=>t.selectedRowCount})((({theme:e})=>({alignItems:"center",display:"flex",margin:e.spacing(0,2),visibility:"hidden",width:0,height:0,[e.breakpoints.up("sm")]:{visibility:"visible",width:"auto",height:"auto"}}))),Yu=be((function(e,t){const{className:r,selectedRowCount:n}=e,o=a(e,qu),s=fe(),u=me(),d=(e=>{const{classes:t}=e;return c({root:["selectedRowCount"]},pt,t)})(u),p=s.current.getLocaleText("footerRowSelected")(n);return l.jsx(Xu,i({className:m(d.root,r),ownerState:u},o,{ref:t,children:p}))})),Qu=be((function(e,t){const r=fe(),n=me(),o=We(r,Ft),a=We(r,Yn),s=We(r,_n),u=!n.hideFooterSelectedRowCount&&a>0?l.jsx(Yu,{selectedRowCount:a}):l.jsx("div",{}),c=n.hideFooterRowCount||n.pagination?null:l.jsx(n.slots.footerRowCount,i({},n.slotProps?.footerRowCount,{rowCount:o,visibleRowCount:s})),d=n.pagination&&!n.hideFooterPagination&&n.slots.pagination&&l.jsx(n.slots.pagination,i({},n.slotProps?.pagination));return l.jsxs(Na,i({},e,{ref:t,children:[u,c,d]}))})),Zu=(e,t,r,n,o,l)=>{let i;switch(e){case Cn.LEFT:i=n[r];break;case Cn.RIGHT:i=o-n[r]-t+l;break;default:i=void 0}return i},Ju=(e,t,r,n,o)=>{const l=t===r-1;return!(e!==Cn.LEFT||!l)||!!n&&(e===Cn.LEFT||(e===Cn.RIGHT?!l:!l||o))},ec=(e,t)=>e===Cn.RIGHT&&0===t,tc={root:ft.scrollbarFiller,header:ft["scrollbarFiller--header"],borderTop:ft["scrollbarFiller--borderTop"],borderBottom:ft["scrollbarFiller--borderBottom"],pinnedRight:ft["scrollbarFiller--pinnedRight"]};function rc({header:e,borderTop:t=!0,borderBottom:r,pinnedRight:n}){return l.jsx("div",{role:"presentation",className:m(tc.root,e&&tc.header,t&&tc.borderTop,r&&tc.borderBottom,n&&tc.pinnedRight)})}const nc=M("div",{name:"MuiDataGrid",slot:"SkeletonLoadingOverlay",overridesResolver:(e,t)=>t.skeletonLoadingOverlay})({minWidth:"100%",width:"max-content",height:"100%",overflow:"clip"}),oc=e=>parseInt(e.getAttribute("data-colindex"),10),lc=be((function(t,r){const n=me(),{slots:o}=n,a=v(),s=(e=>{const{classes:t}=e;return c({root:["skeletonLoadingOverlay"]},pt,t)})({classes:n.classes}),u=e.useRef(null),d=y(u,r),p=fe(),f=We(p,Je),g=f?.viewportInnerSize.height??0,h=Math.ceil(g/f.rowHeight),b=We(p,et),w=We(p,Fr),C=e.useMemo((()=>w.filter((e=>e<=b)).length),[b,w]),x=We(p,kr),S=e.useMemo((()=>x.slice(0,C)),[x,C]),R=We(p,Er),I=e.useCallback((e=>-1!==R.left.findIndex((t=>t.field===e))?Cn.LEFT:-1!==R.right.findIndex((t=>t.field===e))?Cn.RIGHT:void 0),[R.left,R.right]),M=e.useMemo((()=>{const e=[];for(let t=0;t<h;t+=1){const r=[];for(let e=0;e<S.length;e+=1){const s=S[e],u=I(s.field),c=u===Cn.LEFT,d=u===Cn.RIGHT,p=pa(u,a),g=p?R[p].length:S.length-R.left.length-R.right.length,h=p?R[p].findIndex((e=>e.field===s.field)):e-R.left.length,b=f.hasScrollY?f.scrollbarSize:0,C=fa({},a,u,Zu(u,s.computedWidth,e,w,f.columnsTotalWidth,b)),v=f.columnsTotalWidth<f.viewportOuterSize.width,x=Ju(u,h,g,n.showCellVerticalBorder,v),y=ec(u,h),M=e===S.length-1,k=d&&0===h,P=k&&v,E=M&&!k&&v,F=f.viewportOuterSize.width-f.columnsTotalWidth,H=Math.max(0,F),D=l.jsx(o.skeletonCell,{width:H,empty:!0},`skeleton-filler-column-${t}`),T=M&&0!==b;P&&r.push(D),r.push(l.jsx(o.skeletonCell,{field:s.field,type:s.type,align:s.align,width:"var(--width)",height:f.rowHeight,"data-colindex":e,className:m(c&&ft["cell--pinnedLeft"],d&&ft["cell--pinnedRight"],x&&ft["cell--withRightBorder"],y&&ft["cell--withLeftBorder"]),style:i({"--width":`${s.computedWidth}px`},C)},`skeleton-column-${t}-${s.field}`)),E&&r.push(D),T&&r.push(l.jsx(rc,{pinnedRight:R.right.length>0},`skeleton-scrollbar-filler-${t}`))}e.push(l.jsx("div",{className:m(ft.row,ft.rowSkeleton,0===t&&ft["row--firstVisible"]),children:r},`skeleton-row-${t}`))}return e}),[o,S,R,h,n.showCellVerticalBorder,f,w,I,a]);return wt(p,"columnResize",(e=>{const{colDef:t,width:r}=e,n=u.current?.querySelectorAll(`[data-field="${qa(t.field)}"]`);if(!n)throw new Error("MUI X: Expected skeleton cells to be defined with `data-field` attribute.");const o=S.findIndex((e=>e.field===t.field)),l=I(t.field),i=l===Cn.LEFT,a=l===Cn.RIGHT,s=getComputedStyle(n[0]).getPropertyValue("--width"),c=parseInt(s,10)-r;if(n&&n.forEach((e=>{e.style.setProperty("--width",`${r}px`)})),i){const e=u.current?.querySelectorAll(`.${ft["cell--pinnedLeft"]}`);e?.forEach((e=>{oc(e)>o&&(e.style.left=parseInt(getComputedStyle(e).left,10)-c+"px")}))}if(a){const e=u.current?.querySelectorAll(`.${ft["cell--pinnedRight"]}`);e?.forEach((e=>{oc(e)<o&&(e.style.right=`${parseInt(getComputedStyle(e).right,10)+c}px`)}))}})),l.jsx(nc,i({className:s.root},t,{ref:d,children:M}))})),ic=["variant","noRowsVariant","style"],ac={"circular-progress":{component:K,style:{}},"linear-progress":{component:U,style:{display:"block"}},skeleton:{component:lc,style:{display:"block"}}},sc=be((function(e,t){const{variant:r="circular-progress",noRowsVariant:n="circular-progress",style:o}=e,s=a(e,ic),u=fe(),c=We(u,Pt),d=ac[0===c?n:r];return l.jsx(Wa,i({style:i({},d.style,o)},s,{ref:t,children:l.jsx(d.component,{})}))})),uc=be((function(e,t){const r=fe().current.getLocaleText("noRowsLabel");return l.jsx(Wa,i({},e,{ref:t,children:r}))})),cc=u(q)((({theme:e})=>({maxHeight:"calc(100% + 1px)",flexGrow:1,[`& .${X.selectLabel}`]:{display:"none",[e.breakpoints.up("sm")]:{display:"block"}},[`& .${X.input}`]:{display:"none",[e.breakpoints.up("sm")]:{display:"inline-flex"}}}))),dc=({from:e,to:t,count:r,estimated:n})=>n?`${e}–${t} of ${-1!==r?r:`more than ${n>t?n:t}`}`:`${e}–${t} of ${-1!==r?r:`more than ${t}`}`,pc=be((function(t,r){const n=fe(),o=me(),a=We(n,Io),s=We(n,Mo),u=We(n,Fo),{paginationMode:c,loading:d,estimatedRowCount:p}=o,f=e.useMemo((()=>-1===s&&"server"===c&&d?{backIconButtonProps:{disabled:!0},nextIconButtonProps:{disabled:!0}}:{}),[d,c,s]),g=e.useMemo((()=>Math.max(0,u-1)),[u]),m=e.useMemo((()=>-1===s||a.page<=g?a.page:g),[g,a.page,s]),h=e.useCallback((e=>{const t=Number(e.target.value);n.current.setPageSize(t)}),[n]),b=e.useCallback(((e,t)=>{n.current.setPage(t)}),[n]),w=(e=>{for(let t=0;t<o.pageSizeOptions.length;t+=1){const r=o.pageSizeOptions[t];if("number"==typeof r){if(r===e)return!0}else if(r.value===e)return!0}return!1})(a.pageSize)?o.pageSizeOptions:[],C=n.current.getLocaleText("MuiTablePagination"),v=(x=C.labelDisplayedRows||dc,y=p,({from:e,to:t,count:r,page:n})=>x({from:e,to:t,count:r,page:n,estimated:y}));var x,y;return l.jsx(cc,i({component:"div",count:s,page:m,rowsPerPageOptions:w,rowsPerPage:a.pageSize,onPageChange:b,onRowsPerPageChange:h},f,C,{labelDisplayedRows:v},t,{ref:r}))})),fc=["className","rowCount","visibleRowCount"],gc=M("div",{name:"MuiDataGrid",slot:"RowCount",overridesResolver:(e,t)=>t.rowCount})((({theme:e})=>({alignItems:"center",display:"flex",margin:e.spacing(0,2)}))),mc=be((function(e,t){const{className:r,rowCount:n,visibleRowCount:o}=e,s=a(e,fc),u=fe(),d=me(),p=(e=>{const{classes:t}=e;return c({root:["rowCount"]},pt,t)})(d);if(0===n)return null;const f=o<n?u.current.getLocaleText("footerTotalVisibleRows")(o,n):n.toLocaleString();return l.jsxs(gc,i({className:m(p.root,r),ownerState:d},s,{ref:t,children:[u.current.getLocaleText("footerTotalRows")," ",f]}))}));const hc=["selected","rowId","row","index","style","rowHeight","className","visibleColumns","pinnedColumns","offsetLeft","columnsTotalWidth","firstColumnIndex","lastColumnIndex","focusedColumnIndex","isFirstVisible","isLastVisible","isNotVisible","showBottomBorder","scrollbarWidth","gridHasFiller","onClick","onDoubleClick","onMouseEnter","onMouseLeave","onMouseOut","onMouseOver"],bc=Qe(Yi,((e,t)=>{if(!t)return!1;return!!function(e){for(const t in e)return!1;return!0}(e)})),wc=be((function(t,r){const{selected:n,rowId:o,row:s,index:u,style:d,rowHeight:p,className:f,visibleColumns:g,pinnedColumns:h,offsetLeft:b,columnsTotalWidth:w,firstColumnIndex:C,lastColumnIndex:v,focusedColumnIndex:x,isFirstVisible:S,isLastVisible:R,isNotVisible:I,showBottomBorder:M,scrollbarWidth:k,gridHasFiller:P,onClick:E,onDoubleClick:F,onMouseEnter:H,onMouseLeave:D,onMouseOut:T,onMouseOver:O}=t,L=a(t,hc),$=_l(),z=Fi(),j=e.useRef(null),V=me(),A=Kl($),N=We($,Tn),G=We($,zt),B=We($,Fr),W=V.rowReordering,_=_e($,bc,W),U=y(j,r),K=$.current.getRowNode(o),q=_e($,Qi,{rowId:o,editMode:V.editMode}),X=V.editMode===sr.Row,Y=void 0!==x,Q=Y&&x>=h.left.length&&x<C,Z=Y&&x<g.length-h.right.length&&x>=v,J=function(e,t){return c(t,pt,e)}(V.classes,{root:["row",n&&"selected",X&&"row--editable",q&&"row--editing",S&&"row--firstVisible",R&&"row--lastVisible",M&&"row--borderBottom","auto"===p&&"row--dynamicHeight"]}),ee=z.hooks.useGridRowAriaAttributes();e.useLayoutEffect((()=>{if(A.range){const e=$.current.getRowIndexRelativeToVisibleRows(o);void 0!==e&&$.current.unstable_setLastMeasuredRowIndex(e)}if(j.current&&"auto"===p)return $.current.observeRowHeight(j.current,o)}),[$,A.range,p,o]);const te=e.useCallback(((e,t)=>r=>{Ya(r)||$.current.getRow(o)&&($.current.publishEvent(e,$.current.getRowParams(o),r),t&&t(r))}),[$,o]),re=e.useCallback((e=>{const t=Ka(e.target,ft.cell),r=t?.getAttribute("data-field");if(r){if(r===Vo.field)return;if(r===wn)return;if("__reorder__"===r)return;if($.current.getCellMode(o,r)===ur.Edit)return;const e=$.current.getColumn(r);if(e?.type===rn)return}te("rowClick",E)(e)}),[$,E,te,o]),{slots:ne,slotProps:oe,disableColumnReorder:le}=V,ie=We($,(()=>i({},$.current.getRowHeightEntry(o))),Ve),ae=e.useMemo((()=>{if(I)return{opacity:0,width:0,height:0};const e=i({},d,{maxHeight:"auto"===p?"none":p,minHeight:p,"--height":"number"==typeof p?`${p}px`:p});if(ie.spacingTop){e["border"===V.rowSpacingType?"borderTopWidth":"marginTop"]=ie.spacingTop}if(ie.spacingBottom){const t="border"===V.rowSpacingType?"borderBottomWidth":"marginBottom";let r=e[t];"number"!=typeof r&&(r=parseInt(r||"0",10)),r+=ie.spacingBottom,e[t]=r}return e}),[I,p,d,ie,V.rowSpacingType]),se=$.current.unstable_applyPipeProcessors("rowClassName",[],o),ue=ee(K,u);if("function"==typeof V.getRowClassName){const e=u-(A.range?.firstRowIndex||0),t=i({},$.current.getRowParams(o),{isFirstVisible:0===e,isLastVisible:e===A.rows.length-1,indexRelativeToCurrentPage:e});se.push(V.getRowClassName(t))}const ce=(e,t,r,n,a=Cn.NONE)=>{const u=$.current.unstable_getCellColSpanInfo(o,r);if(u?.spannedByColSpan)return null;const c=u?.cellProps.width??e.computedWidth,d=u?.cellProps.colSpan??1,f=Zu(a,e.computedWidth,r,B,w,k);if("skeletonRow"===K.type)return l.jsx(ne.skeletonCell,{type:e.type,width:c,height:p,field:e.field,align:e.align},e.field);const g="__reorder__"===e.field,m=!(le||e.disableReorder),h=_&&!N.length&&G<=1,b=!(m||g&&h),C=a===Cn.VIRTUAL,v=ec(a,t),x=Ju(a,t,n,V.showCellVerticalBorder,P);return l.jsx(ne.cell,i({column:e,width:c,rowId:o,align:e.align||"left",colIndex:r,colSpan:d,disableDragEvents:b,isNotVisible:C,pinnedOffset:f,pinnedPosition:a,showLeftBorder:v,showRightBorder:x,row:s,rowNode:K},oe?.cell),e.field)},de=h.left.map(((e,t)=>ce(e,t,t,h.left.length,Cn.LEFT))),pe=h.right.map(((e,t)=>{const r=g.length-h.right.length+t;return ce(e,t,r,h.right.length,Cn.RIGHT)})),fe=g.length-h.left.length-h.right.length,ge=[];Q&&ge.push(ce(g[x],x-h.left.length,x,fe,Cn.VIRTUAL));for(let e=C;e<v;e+=1){const t=g[e],r=e-h.left.length;t&&ge.push(ce(t,r,e,fe))}Z&&ge.push(ce(g[x],x-h.left.length,x,fe,Cn.VIRTUAL));const he=s?{onClick:re,onDoubleClick:te("rowDoubleClick",F),onMouseEnter:te("rowMouseEnter",H),onMouseLeave:te("rowMouseLeave",D),onMouseOut:te("rowMouseOut",T),onMouseOver:te("rowMouseOver",O)}:null;return l.jsxs("div",i({"data-id":o,"data-rowindex":u,role:"row",className:m(...se,J.root,f),style:ae},ue,he,L,{ref:U,children:[de,l.jsx("div",{role:"presentation",className:ft.cellOffsetLeft,style:{width:b}}),ge,l.jsx("div",{role:"presentation",className:m(ft.cell,ft.cellEmpty)}),pe,0!==k&&l.jsx(rc,{pinnedRight:h.right.length>0,borderTop:!S})]}))})),Cc=ct(wc);function vc({privateApiRef:t,configuration:r,props:n,children:o}){const i=e.useRef(t.current.getPublicApi());return l.jsx(Ei.Provider,{value:r,children:l.jsx(ge.Provider,{value:n,children:l.jsx(Wl.Provider,{value:t,children:l.jsx(pe.Provider,{value:i,children:o})})})})}const xc=function(){try{const e="__some_random_key_you_are_not_going_to_use__";return window.localStorage.setItem(e,e),window.localStorage.removeItem(e),!0}catch(e){return!1}}()&&null!=window.localStorage.getItem("DEBUG"),yc=()=>{},Sc={debug:yc,info:yc,warn:yc,error:yc},Rc=["debug","info","warn","error"];function Ic(e,t,r=console){const n=Rc.indexOf(t);if(-1===n)throw new Error(`MUI X: Log level ${t} not recognized.`);return Rc.reduce(((t,o,l)=>(t[o]=l>=n?(...t)=>{const[n,...l]=t;r[o](`MUI X: ${e} - ${n}`,...l)}:yc,t)),{})}class Mc{constructor(){this.maxListeners=20,this.warnOnce=!1,this.events={}}on(e,t,r={}){let n=this.events[e];n||(n={highPriority:new Map,regular:new Map},this.events[e]=n),r.isFirst?n.highPriority.set(t,!0):n.regular.set(t,!0)}removeListener(e,t){this.events[e]&&(this.events[e].regular.delete(t),this.events[e].highPriority.delete(t))}removeAllListeners(){this.events={}}emit(e,...t){const r=this.events[e];if(!r)return;const n=Array.from(r.highPriority.keys()),o=Array.from(r.regular.keys());for(let l=n.length-1;l>=0;l-=1){const e=n[l];r.highPriority.has(e)&&e.apply(this,t)}for(let l=0;l<o.length;l+=1){const e=o[l];r.regular.has(e)&&e.apply(this,t)}}once(e,t){const r=this;this.on(e,(function n(...o){r.removeListener(e,n),t.apply(r,o)}))}}class kc{static create(e){return new kc(e)}constructor(e){this.value=void 0,this.listeners=void 0,this.subscribe=e=>(this.listeners.add(e),()=>{this.listeners.delete(e)}),this.getSnapshot=()=>this.value,this.update=e=>{this.value=e,this.listeners.forEach((t=>t(e)))},this.value=e,this.listeners=new Set}}const Pc=Symbol("mui.api_private");let Ec=0;function Fc(t,r){const n=e.useRef(null),o=e.useRef(null);o.current||(o.current=function(e){const t=e.current?.[Pc];if(t)return t;const r={},n={state:r,store:kc.create(r),instanceId:{id:Ec}};return Ec+=1,n.getPublicApi=()=>e.current,n.register=(t,r)=>{Object.keys(r).forEach((o=>{const l=r[o],i=n[o];if(!0===i?.spying?i.target=l:n[o]=l,"public"===t){const t=e.current,r=t[o];!0===r?.spying?r.target=l:t[o]=l}}))},n.register("private",{caches:{},eventManager:new Mc}),n}(n)),n.current||(n.current=function(e){return{get state(){return e.current.state},get store(){return e.current.store},get instanceId(){return e.current.instanceId},[Pc]:e.current}}(o));const l=e.useCallback(((...e)=>{const[t,n,l={}]=e;if(l.defaultMuiPrevented=!1,(e=>void 0!==e.isPropagationStopped)(l)&&l.isPropagationStopped())return;const i=r.signature===ht.DataGridPro||r.signature===ht.DataGridPremium?{api:o.current.getPublicApi()}:{};o.current.eventManager.emit(t,n,l,i)}),[o,r.signature]),i=e.useCallback(((e,t,r)=>{o.current.eventManager.on(e,t,r);const n=o.current;return()=>{n.eventManager.removeListener(e,t)}}),[o]);return mo(o,{subscribeEvent:i,publishEvent:l},"public"),t&&!t.current?.state&&(t.current=n.current),e.useImperativeHandle(t,(()=>n.current),[n]),e.useEffect((()=>{const e=o.current;return()=>{e.publishEvent("unmount")}}),[o]),o}const Hc=(t,r,n,o=!0)=>{const l=e.useRef(null),i=e.useRef(`mui-${Math.round(1e9*Math.random())}`),a=e.useCallback((()=>{l.current=t.current.registerPipeProcessor(r,i.current,n)}),[t,n,r]);wo((()=>{o&&a()}));const s=e.useRef(!0);e.useEffect((()=>(s.current?s.current=!1:o&&a(),()=>{l.current&&(l.current(),l.current=null)})),[a,o])},Dc=(t,r,n)=>{const o=e.useRef(null),l=e.useRef(`mui-${Math.round(1e9*Math.random())}`),i=e.useCallback((()=>{o.current=t.current.registerPipeApplier(r,l.current,n)}),[t,n,r]);wo((()=>{i()}));const a=e.useRef(!0);e.useEffect((()=>(a.current?a.current=!1:i(),()=>{o.current&&(o.current(),o.current=null)})),[i])},Tc=(t,r,n,o)=>{const l=e.useCallback((()=>{t.current.registerStrategyProcessor(r,n,o)}),[t,o,n,r]);wo((()=>{l()}));const i=e.useRef(!0);e.useEffect((()=>{i.current?i.current=!1:l()}),[l])},Oc="none",Lc={rowTreeCreation:"rowTree",filtering:"rowTree",sorting:"rowTree",visibleRowsLookupCreation:"rowTree"},$c=(e,t)=>i({},e,{props:{getRowId:t.getRowId}}),zc=(t,r)=>{const n=Fc(t,r);return(t=>{const r=e.useRef(null),n=e.useRef(null),o=e.useRef(null),l=e.useRef(null),i=e.useRef(null),a=e.useRef(null);t.current.register("public",{rootElementRef:r}),t.current.register("private",{mainElementRef:n,virtualScrollerRef:o,virtualScrollbarVerticalRef:l,virtualScrollbarHorizontalRef:i,columnHeadersContainerRef:a})})(n),((t,r)=>{e.useEffect((()=>{t.current.setState((e=>i({},e,{props:{getRowId:r.getRowId}})))}),[t,r.getRowId])})(n,r),(t=>{const r=v();void 0===t.current.state.isRtl&&(t.current.state.isRtl=r);const n=e.useRef(!0);e.useEffect((()=>{n.current?n.current=!1:t.current.setState((e=>i({},e,{isRtl:r})))}),[t,r])})(n),((t,r)=>{mo(t,{getLogger:e.useCallback((e=>xc?Ic(e,"debug",r.logger):r.logLevel?Ic(e,r.logLevel.toString(),r.logger):Sc),[r.logLevel,r.logger])},"private")})(n,r),(t=>{const r=e.useRef({}),n=e.useCallback((e=>{r.current[e.stateId]=e}),[]),o=e.useCallback(((e,n)=>{let o;if(o="function"==typeof e?e(t.current.state):e,t.current.state===o)return!1;let l=!1;const i=[];if(Object.keys(r.current).forEach((e=>{const n=r.current[e],a=n.stateSelector(t.current.state,t.current.instanceId),s=n.stateSelector(o,t.current.instanceId);s!==a&&(i.push({stateId:n.stateId,hasPropChanged:s!==n.propModel}),void 0!==n.propModel&&s!==n.propModel&&(l=!0))})),i.length>1)throw new Error(`You're not allowed to update several sub-state in one transaction. You already updated ${i[0].stateId}, therefore, you're not allowed to update ${i.map((e=>e.stateId)).join(", ")} in the same transaction.`);if(l||(t.current.state=o,t.current.publishEvent("stateChange",o),t.current.store.update(o)),1===i.length){const{stateId:e,hasPropChanged:a}=i[0],s=r.current[e],u=s.stateSelector(o,t.current.instanceId);s.propOnChange&&a&&s.propOnChange(u,{reason:n,api:t.current}),l||t.current.publishEvent(s.changeEvent,u,{reason:n})}return!l}),[t]),l=e.useCallback(((e,r,n)=>t.current.setState((t=>i({},t,{[e]:r(t[e])})),n)),[t]),a=e.useCallback((()=>{}),[]),s={updateControlState:l,registerControlState:n};mo(t,{setState:o,forceUpdate:a},"public"),mo(t,s,"private")})(n),(t=>{const r=e.useRef({}),n=e.useRef(!1),o=e.useCallback((e=>{!n.current&&e&&(n.current=!0,Object.values(e.appliers).forEach((e=>{e()})),n.current=!1)}),[]),l=e.useCallback(((e,t,n)=>{r.current[e]||(r.current[e]={processors:new Map,processorsAsArray:[],appliers:{}});const l=r.current[e];return l.processors.get(t)!==n&&(l.processors.set(t,n),l.processorsAsArray=Array.from(r.current[e].processors.values()),o(l)),()=>{r.current[e].processors.delete(t),r.current[e].processorsAsArray=Array.from(r.current[e].processors.values())}}),[o]),i=e.useCallback(((e,t,n)=>(r.current[e]||(r.current[e]={processors:new Map,processorsAsArray:[],appliers:{}}),r.current[e].appliers[t]=n,()=>{const n=r.current[e].appliers,o=a(n,[t].map(de));r.current[e].appliers=o})),[]),s=e.useCallback((e=>{o(r.current[e])}),[o]),u={unstable_applyPipeProcessors:e.useCallback(((...e)=>{const[t,n,o]=e;if(!r.current[t])return n;const l=r.current[t].processorsAsArray;let i=n;for(let r=0;r<l.length;r+=1)i=l[r](i,o);return i}),[])};mo(t,{registerPipeProcessor:l,registerPipeApplier:i,requestPipeProcessorsApplication:s},"private"),mo(t,u,"public")})(n),(t=>{const r=e.useRef(new Map),n=e.useRef({}),o=e.useCallback(((e,r,o)=>{const l=()=>{const t=n.current[r],o=a(t,[e].map(de));n.current[r]=o};n.current[r]||(n.current[r]={});const i=n.current[r],s=i[e];return i[e]=o,s&&s!==o?(e===t.current.getActiveStrategy(Lc[r])&&t.current.publishEvent("activeStrategyProcessorChange",r),l):l}),[t]),l=e.useCallback(((e,r)=>{const o=t.current.getActiveStrategy(Lc[e]);if(null==o)throw new Error("Can't apply a strategy processor before defining an active strategy");const l=n.current[e];if(!l||!l[o])throw new Error(`No processor found for processor "${e}" on strategy "${o}"`);return(0,l[o])(r)}),[t]),i=e.useCallback((e=>{const t=Array.from(r.current.entries()).find((([,t])=>t.group===e&&t.isAvailable()));return t?.[0]??Oc}),[]),s=e.useCallback(((e,n,o)=>{r.current.set(n,{group:e,isAvailable:o}),t.current.publishEvent("strategyAvailabilityChange")}),[t]);mo(t,{registerStrategyProcessor:o,applyStrategyProcessor:l,getActiveStrategy:i,setStrategyAvailability:s},"private")})(n),((t,r)=>{const n=e.useCallback((e=>{if(null==r.localeText[e])throw new Error(`Missing translation for key ${e}.`);return r.localeText[e]}),[r.localeText]);t.current.register("public",{getLocaleText:n})})(n,r),n.current.register("private",{rootProps:r}),n},jc=(t,r,n)=>{const o=e.useRef(!1);o.current||(r.current.state=t(r.current.state,n,r),o.current=!0)};function Vc(e,t){if(null==e)return"";const r="string"==typeof e?e:`${e}`;if(t.shouldAppendQuotes||t.escapeFormulas){const e=r.replace(/"/g,'""');return t.escapeFormulas&&["=","+","-","@","\t","\r"].includes(e[0])?`"'${e}"`:[t.delimiter,"\n","\r",'"'].some((e=>r.includes(e)))?`"${e}"`:e}return r}const Ac=(e,t)=>{const{csvOptions:r,ignoreValueFormatter:n}=t;let o;if(n){const t=e.colDef.type;o="number"===t?String(e.value):"date"===t||"dateTime"===t?e.value?.toISOString():"function"==typeof e.value?.toString?e.value.toString():e.value}else o=e.formattedValue;return Vc(o,r)};class Nc{constructor(e){this.options=void 0,this.rowString="",this.isEmpty=!0,this.options=e}addValue(e){this.isEmpty||(this.rowString+=this.options.csvOptions.delimiter),"function"==typeof this.options.sanitizeCellValue?this.rowString+=this.options.sanitizeCellValue(e,this.options.csvOptions):this.rowString+=e,this.isEmpty=!1}getRowString(){return this.rowString}}function Gc(e){const{columns:t,rowIds:r,csvOptions:n,ignoreValueFormatter:o,apiRef:l}=e,i=r.reduce(((e,r)=>`${e}${(({id:e,columns:t,getCellParams:r,csvOptions:n,ignoreValueFormatter:o})=>{const l=new Nc({csvOptions:n});return t.forEach((t=>{const i=r(e,t.field);l.addValue(Ac(i,{ignoreValueFormatter:o,csvOptions:n}))})),l.getRowString()})({id:r,columns:t,getCellParams:l.current.getCellParams,ignoreValueFormatter:o,csvOptions:n})}\r\n`),"").trim();if(!n.includeHeaders)return i;const a=t.filter((e=>e.field!==Vo.field)),s=[];if(n.includeColumnGroupsHeaders){const e=l.current.getAllGroupDetails();let t=0;const r=a.reduce(((e,r)=>{const n=l.current.getColumnGroupPath(r.field);return e[r.field]=n,t=Math.max(t,n.length),e}),{});for(let o=0;o<t;o+=1){const t=new Nc({csvOptions:n,sanitizeCellValue:Vc});s.push(t),a.forEach((n=>{const l=(r[n.field]||[])[o],i=e[l];t.addValue(i?i.headerName||i.groupId:"")}))}}const u=new Nc({csvOptions:n,sanitizeCellValue:Vc});a.forEach((e=>{u.addValue(e.headerName||e.field)})),s.push(u);return`${`${s.map((e=>e.getRowString())).join("\r\n")}\r\n`}${i}`.trim()}function Bc(e){const t=document.createElement("span");t.style.whiteSpace="pre",t.style.userSelect="all",t.style.opacity="0px",t.textContent=e,document.body.appendChild(t);const r=document.createRange();r.selectNode(t);const n=window.getSelection();n.removeAllRanges(),n.addRange(r);try{document.execCommand("copy")}finally{document.body.removeChild(t)}}const Wc=(t,r)=>{const n=r.ignoreValueFormatterDuringExport,o=("object"==typeof n?n?.clipboardExport:n)||!1,l=r.clipboardCopyCellDelimiter,i=e.useCallback((e=>{if(!function(e){return(e.ctrlKey||e.metaKey)&&"C"===String.fromCharCode(e.keyCode)&&!e.shiftKey&&!e.altKey}(e))return;if(r=e.target,window.getSelection()?.toString()||r&&(r.selectionEnd||0)-(r.selectionStart||0)>0)return;var r;let n="";if(t.current.getSelectedRows().size>0)n=t.current.getDataAsCsv({includeHeaders:!1,delimiter:l,shouldAppendQuotes:!1,escapeFormulas:!1});else{const e=ao(t);if(e){const r=t.current.getCellParams(e.id,e.field);n=Ac(r,{csvOptions:{delimiter:l,shouldAppendQuotes:!1,escapeFormulas:!1},ignoreValueFormatter:o})}}var i;n=t.current.unstable_applyPipeProcessors("clipboardCopy",n),n&&(i=n,navigator.clipboard?navigator.clipboard.writeText(i).catch((()=>{Bc(i)})):Bc(i),t.current.publishEvent("clipboardCopy",n))}),[t,o,l]);bo(t,(()=>t.current.rootElementRef.current),"keydown",i),vt(t,"clipboardCopy",r.onClipboardCopy)},_c=e=>i({},e,{columnMenu:{open:!1}}),Uc=(e,t,r)=>{const n=El({apiRef:r,columnsToUpsert:t.columns,initialState:t.initialState?.columns,columnVisibilityModel:t.columnVisibilityModel??t.initialState?.columns?.columnVisibilityModel??{},keepOnlyColumnsToUpsert:!0});return i({},e,{columns:n,pinnedColumns:e.pinnedColumns??xr})};function Kc(e){return t=>i({},t,{columns:e})}const qc=(e,t)=>i({},e,{density:t.initialState?.density??t.density??"standard"});const Xc=({apiRef:e,options:t})=>{const r=Ir(e);if(t.fields)return t.fields.reduce(((e,t)=>{const n=r.find((e=>e.field===t));return n&&e.push(n),e}),[]);return(t.allColumns?r:kr(e)).filter((e=>!e.disableExport))},Yc=({apiRef:e})=>{const t=Gn(e),r=Tt(e),n=e.current.getSelectedRows(),o=t.filter((e=>"footer"!==r[e].type)),l=Vt(e),i=l?.top?.map((e=>e.id))||[],a=l?.bottom?.map((e=>e.id))||[];return o.unshift(...i),o.push(...a),n.size>0?o.filter((e=>n.has(e))):o},Qc=(t,r)=>{const n=ho(t,"useGridCsvExport"),o=r.ignoreValueFormatterDuringExport,i=("object"==typeof o?o?.csvExport:o)||!1,a=e.useCallback(((e={})=>{n.debug("Get data as CSV");return Gc({columns:Xc({apiRef:t,options:e}),rowIds:(e.getRowsToExport??Yc)({apiRef:t}),csvOptions:{delimiter:e.delimiter||",",shouldAppendQuotes:e.shouldAppendQuotes??!0,includeHeaders:e.includeHeaders??!0,includeColumnGroupsHeaders:e.includeColumnGroupsHeaders??!0,escapeFormulas:e.escapeFormulas??!0},ignoreValueFormatter:i,apiRef:t})}),[n,t,i]),s=e.useCallback((e=>{n.debug("Export data as CSV");const t=a(e);!function(e,t="csv",r=document.title||"untitled"){const n=`${r}.${t}`;if("download"in HTMLAnchorElement.prototype){const t=URL.createObjectURL(e),r=document.createElement("a");return r.href=t,r.download=n,r.click(),void setTimeout((()=>{URL.revokeObjectURL(t)}))}throw new Error("MUI X: exportAs not supported.")}(new Blob([e?.utf8WithBom?new Uint8Array([239,187,191]):"",t],{type:"text/csv"}),"csv",e?.fileName)}),[n,a]);mo(t,{getDataAsCsv:a,exportDataAsCsv:s},"public");const u=e.useCallback(((e,t)=>t.csvOptions?.disableToolbarButton?e:[...e,{component:l.jsx(Uu,{options:t.csvOptions}),componentName:"csvExport"}]),[]);Hc(t,"exportMenu",u)},Zc=(e,t,r)=>{let n=e.paginationModel;const o=e.rowCount,l=r?.pageSize??n.pageSize,a=r?.page??n.page,s=vo(o,l,a);!r||r?.page===n.page&&r?.pageSize===n.pageSize||(n=r);const u=-1===l?0:((e,t=0)=>0===t?e:Math.max(Math.min(e,t-1),0))(n.page,s);return u!==n.page&&(n=i({},n,{page:u})),yo(n.pageSize,t),n};const Jc=(t,r)=>{const n=null!==t.current.rootElementRef.current,o=ho(t,"useGridPrintExport"),a=e.useRef(null),s=e.useRef(null),u=e.useRef({}),c=e.useRef([]),d=e.useRef(null);e.useEffect((()=>{a.current=F(t.current.rootElementRef.current)}),[t,n]);const p=e.useCallback(((e,r,n)=>new Promise((o=>{const l=Xc({apiRef:t,options:{fields:e,allColumns:r}}).map((e=>e.field)),i=Ir(t),a={};i.forEach((e=>{a[e.field]=l.includes(e.field)})),n&&(a[Vo.field]=!0),t.current.setColumnVisibilityModel(a),o()}))),[t]),f=e.useCallback((e=>{const r=e({apiRef:t}).reduce(((e,r)=>{const n=t.current.getRow(r);return n[ln]||e.push(n),e}),[]);t.current.setRows(r)}),[t]),g=e.useCallback(((e,n)=>{const o=i({copyStyles:!0,hideToolbar:!1,hideFooter:!1,includeCheckboxes:!1},n),l=e.contentDocument;if(!l)return;const s=Xl(t.current.state),u=t.current.rootElementRef.current,c=u.cloneNode(!0);c.querySelector(`.${ft.main}`).style.overflow="visible",c.style.contain="size";let d=u.querySelector(`.${ft.toolbarContainer}`)?.offsetHeight||0,p=u.querySelector(`.${ft.footerContainer}`)?.offsetHeight||0;const f=c.querySelector(`.${ft.footerContainer}`);o.hideToolbar&&(c.querySelector(`.${ft.toolbarContainer}`)?.remove(),d=0),o.hideFooter&&f&&(f.remove(),p=0);const g=s.currentPageTotalHeight+Fl(t,r)+d+p;c.style.height=`${g}px`,c.style.boxSizing="content-box",!o.hideFooter&&f&&(f.style.position="absolute",f.style.width="100%",f.style.top=g-p+"px");const m=document.createElement("div");m.appendChild(c),l.body.style.marginTop="0px",l.body.innerHTML=m.innerHTML;const h="function"==typeof o.pageStyle?o.pageStyle():o.pageStyle;if("string"==typeof h){const e=l.createElement("style");e.appendChild(l.createTextNode(h)),l.head.appendChild(e)}o.bodyClassName&&l.body.classList.add(...o.bodyClassName.split(" "));const b=[];if(o.copyStyles){const e=u.getRootNode(),t=("ShadowRoot"===e.constructor.name?e:a.current).querySelectorAll("style, link[rel='stylesheet']");for(let r=0;r<t.length;r+=1){const e=t[r];if("STYLE"===e.tagName){const t=l.createElement(e.tagName),r=e.sheet;if(r){let e="";for(let t=0;t<r.cssRules.length;t+=1)"string"==typeof r.cssRules[t].cssText&&(e+=`${r.cssRules[t].cssText}\r\n`);t.appendChild(l.createTextNode(e)),l.head.appendChild(t)}}else if(e.getAttribute("href")){const t=l.createElement(e.tagName);for(let r=0;r<e.attributes.length;r+=1){const n=e.attributes[r];n&&t.setAttribute(n.nodeName,n.nodeValue||"")}b.push(new Promise((e=>{t.addEventListener("load",(()=>e()))}))),l.head.appendChild(t)}}}Promise.all(b).then((()=>{e.contentWindow.print()}))}),[t,a,r]),m=e.useCallback((e=>{a.current.body.removeChild(e),t.current.restoreState(s.current||{}),s.current?.columns?.columnVisibilityModel||t.current.setColumnVisibilityModel(u.current),t.current.setState((e=>i({},e,{virtualization:d.current}))),t.current.setRows(c.current),s.current=null,u.current={},c.current=[]}),[t]),h=e.useCallback((async e=>{if(o.debug("Export data as Print"),!t.current.rootElementRef.current)throw new Error("MUI X: No grid root element available.");if(s.current=t.current.exportState(),u.current=Mr(t),c.current=t.current.getSortedRows().filter((e=>!e[ln])),r.pagination){const e={page:0,pageSize:Wn(t)};t.current.setState((t=>i({},t,{pagination:i({},t.pagination,{paginationModel:Zc(t.pagination,"DataGridPro",e)})})))}d.current=t.current.state.virtualization,t.current.setState((e=>i({},e,{virtualization:i({},e.virtualization,{enabled:!1,enabledForColumns:!1})}))),await p(e?.fields,e?.allColumns,e?.includeCheckboxes),f(e?.getRowsToExport??Yc),await new Promise((e=>{requestAnimationFrame((()=>{e()}))}));const n=function(e){const t=document.createElement("iframe");return t.style.position="absolute",t.style.width="0px",t.style.height="0px",t.title=e||document.title,t}(e?.fileName);n.onload=()=>{g(n,e);n.contentWindow.matchMedia("print").addEventListener("change",(e=>{!1===e.matches&&m(n)}))},a.current.body.appendChild(n)}),[r,o,t,g,m,p,f]);mo(t,{exportDataAsPrint:h},"public");const b=e.useCallback(((e,t)=>t.printOptions?.disableToolbarButton?e:[...e,{component:l.jsx(Ku,{options:t.printOptions}),componentName:"printExport"}]),[]);Hc(t,"exportMenu",b)},ed=(e,t,r)=>{const n=t.filterModel??t.initialState?.filter?.filterModel??wr();return i({},e,{filter:i({filterModel:$r(n,t.disableMultipleColumnsFiltering,r)},br),visibleRowsLookup:{}})},td=e=>e.filteredRowsLookup;function rd(e,t){return e.current.applyStrategyProcessor("visibleRowsLookupCreation",{tree:t.rows.tree,filteredRowsLookup:t.filter.filteredRowsLookup})}function nd(){return ye(Object.values)}const od=e=>i({},e,{focus:{cell:null,columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null},tabIndex:{cell:null,columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}}),ld=({currentColIndex:e,firstColIndex:t,lastColIndex:r,isRtl:n})=>{if(n){if(e<r)return e+1}else if(!n&&e>t)return e-1;return null},id=({currentColIndex:e,firstColIndex:t,lastColIndex:r,isRtl:n})=>{if(n){if(e>t)return e-1}else if(!n&&e<r)return e+1;return null};const ad=Ze(Oo,Vt,((e,t)=>(t.top||[]).concat(e.rows,t.bottom||[]))),sd=(t,r)=>{const n=ho(t,"useGridKeyboardNavigation"),o=v(),l=r.unstable_listView,i=e.useCallback((()=>ad(t)),[t]),a="DataGrid"!==r.signature&&r.headerFilters,s=e.useCallback(((e,r,o="left",i="up")=>{const a=Vn(t),s=t.current.unstable_getCellColSpanInfo(r,e);s&&s.spannedByColSpan&&("left"===o?e=s.leftVisibleCellIndex:"right"===o&&(e=s.rightVisibleCellIndex));const u=l?ai(t.current.state).field:Pr(t)[e],c=function(e,t,r,n){const o=oi(e);if(!o[t]?.[r])return t;const l=Gn(e);let i=l.indexOf(t)+("down"===n?1:-1);for(;i>=0&&i<l.length;){const e=l[i];if(!o[e]?.[r])return e;i+="down"===n?1:-1}return t}(t,r,u,i),d=a.findIndex((e=>e.id===c));n.debug(`Navigating to cell row ${d}, col ${e}`),t.current.scrollToIndexes({colIndex:e,rowIndex:d}),t.current.setCellFocus(c,u)}),[t,n,l]),u=e.useCallback(((e,r)=>{n.debug(`Navigating to header col ${e}`),t.current.scrollToIndexes({colIndex:e});const o=t.current.getVisibleColumns()[e].field;t.current.setColumnHeaderFocus(o,r)}),[t,n]),c=e.useCallback(((e,r)=>{n.debug(`Navigating to header filter col ${e}`),t.current.scrollToIndexes({colIndex:e});const o=t.current.getVisibleColumns()[e].field;t.current.setColumnHeaderFilterFocus(o,r)}),[t,n]),d=e.useCallback(((e,r,o)=>{n.debug(`Navigating to header col ${e}`),t.current.scrollToIndexes({colIndex:e});const{field:l}=t.current.getVisibleColumns()[e];t.current.setColumnGroupHeaderFocus(l,r,o)}),[t,n]),p=e.useCallback((e=>i()[e]?.id),[i]),f=e.useCallback(((e,r)=>{const n=r.currentTarget.querySelector(`.${ft.columnHeaderTitleContainerContent}`);if(!!n&&n.contains(r.target)&&e.field!==Vo.field)return;const l=i(),f=t.current.getViewportPageSize(),g=e.field?t.current.getColumnIndex(e.field):0,m=l.length>0?0:null,h=l.length-1,b=kr(t).length-1,w=Rl(t);let C=!0;switch(r.key){case"ArrowDown":a?c(g,r):null!==m&&s(g,p(m));break;case"ArrowRight":{const e=id({currentColIndex:g,firstColIndex:0,lastColIndex:b,isRtl:o});null!==e&&u(e,r);break}case"ArrowLeft":{const e=ld({currentColIndex:g,firstColIndex:0,lastColIndex:b,isRtl:o});null!==e&&u(e,r);break}case"ArrowUp":w>0&&d(g,w-1,r);break;case"PageDown":null!==m&&null!==h&&s(g,p(Math.min(m+f,h)));break;case"Home":u(0,r);break;case"End":u(b,r);break;case"Enter":(r.ctrlKey||r.metaKey)&&t.current.toggleColumnMenu(e.field);break;case" ":break;default:C=!1}C&&r.preventDefault()}),[t,i,a,c,s,p,o,u,d]),g=e.useCallback(((e,r)=>{const n=wl(t)===e.field,l=Cl(t)===e.field;if(n||l||!Us(r.key))return;const a=i(),d=t.current.getViewportPageSize(),f=e.field?t.current.getColumnIndex(e.field):0,g=a.length-1,m=kr(t).length-1;let h=!0;switch(r.key){case"ArrowDown":{const e=p(0);null!=e&&s(f,e);break}case"ArrowRight":{const e=id({currentColIndex:f,firstColIndex:0,lastColIndex:m,isRtl:o});null!==e&&c(e,r);break}case"ArrowLeft":{const n=ld({currentColIndex:f,firstColIndex:0,lastColIndex:m,isRtl:o});null!==n?c(n,r):t.current.setColumnHeaderFilterFocus(e.field,r);break}case"ArrowUp":u(f,r);break;case"PageDown":null!==g&&s(f,p(Math.min(0+d,g)));break;case"Home":c(0,r);break;case"End":c(m,r);break;case" ":break;default:h=!1}h&&r.preventDefault()}),[t,i,c,o,u,s,p]),m=e.useCallback(((e,r)=>{const n=uo(t);if(null===n)return;const{field:o,depth:l}=n,{fields:a,depth:c,maxDepth:f}=e,g=i(),m=t.current.getViewportPageSize(),h=t.current.getColumnIndex(o),b=o?t.current.getColumnIndex(o):0,w=g.length-1,C=kr(t).length-1;let v=!0;switch(r.key){case"ArrowDown":c===f-1?u(h,r):d(h,l+1,r);break;case"ArrowUp":c>0&&d(h,l-1,r);break;case"ArrowRight":{const e=a.length-a.indexOf(o)-1;h+e+1<=C&&d(h+e+1,l,r);break}case"ArrowLeft":{const e=a.indexOf(o);h-e-1>=0&&d(h-e-1,l,r);break}case"PageDown":null!==w&&s(b,p(Math.min(0+m,w)));break;case"Home":d(0,l,r);break;case"End":d(C,l,r);break;case" ":break;default:v=!1}v&&r.preventDefault()}),[t,i,u,d,s,p]),h=e.useCallback(((e,r)=>{if(Ya(r))return;const n=t.current.getCellParams(e.id,e.field);if(n.cellMode===ur.Edit||!Us(r.key))return;if(!t.current.unstable_applyPipeProcessors("canUpdateFocus",!0,{event:r,cell:n}))return;const d=i();if(0===d.length)return;const f=t.current.getViewportPageSize(),g=l?()=>0:t.current.getColumnIndex,m=e.field?g(e.field):0,h=d.findIndex((t=>t.id===e.id)),b=d.length-1,w=(l?[ai(t.current.state)]:kr(t)).length-1;let C=!0;switch(r.key){case"ArrowDown":h<b&&s(m,p(h+1),o?"right":"left","down");break;case"ArrowUp":h>0?s(m,p(h-1)):a?c(m,r):u(m,r);break;case"ArrowRight":{const e=id({currentColIndex:m,firstColIndex:0,lastColIndex:w,isRtl:o});null!==e&&s(e,p(h),o?"left":"right");break}case"ArrowLeft":{const e=ld({currentColIndex:m,firstColIndex:0,lastColIndex:w,isRtl:o});null!==e&&s(e,p(h),o?"right":"left");break}case"Tab":r.shiftKey&&m>0?s(m-1,p(h),"left"):!r.shiftKey&&m<w&&s(m+1,p(h),"right");break;case" ":{if(e.field===wn)break;const t=e.colDef;if(t&&("__tree_data_group__"===t.field||(e=>e===bn||null!==(e=>{const t=e.match(/^__row_group_by_columns_group_(.*)__$/);return t?t[1]:null})(e))(t.field)))break;!r.shiftKey&&h<b&&s(m,p(Math.min(h+f,b)));break}case"PageDown":h<b&&s(m,p(Math.min(h+f,b)));break;case"PageUp":{const e=Math.max(h-f,0);e!==h&&e>=0?s(m,p(e)):u(m,r);break}case"Home":r.ctrlKey||r.metaKey||r.shiftKey?s(0,p(0)):s(0,p(h));break;case"End":r.ctrlKey||r.metaKey||r.shiftKey?s(w,p(b)):s(w,p(h));break;default:C=!1}C&&r.preventDefault()}),[t,i,o,s,p,a,c,u,l]),b=e.useCallback(((e,{event:t})=>" "!==t.key&&e),[]);Hc(t,"canStartEditing",b),wt(t,"columnHeaderKeyDown",f),wt(t,"headerFilterKeyDown",g),wt(t,"columnGroupHeaderKeyDown",m),wt(t,"cellKeyDown",h)},ud=(e,t)=>{const r=i({},xo(t.autoPageSize),t.paginationModel??t.initialState?.pagination?.paginationModel);yo(r.pageSize,t.signature);const n=t.rowCount??t.initialState?.pagination?.rowCount??("client"===t.paginationMode?e.rows?.totalRowCount:void 0),o=t.paginationMeta??t.initialState?.pagination?.meta??{};return i({},e,{pagination:i({},e.pagination,{paginationModel:r,rowCount:n,meta:o,enabled:!0===t.pagination,paginationMode:t.paginationMode})})},cd=(t,n)=>{((t,r)=>{const n=ho(t,"useGridPaginationMeta"),o=We(t,ko);t.current.registerControlState({stateId:"paginationMeta",propModel:r.paginationMeta,propOnChange:r.onPaginationMetaChange,stateSelector:ko,changeEvent:"paginationMetaChange"});const l=e.useCallback((e=>{o!==e&&(n.debug("Setting 'paginationMeta' to",e),t.current.setState((t=>i({},t,{pagination:i({},t.pagination,{meta:e})}))))}),[t,n,o]);mo(t,{setPaginationMeta:l},"public");const a=e.useCallback(((e,n)=>{const o=ko(t);return n.exportOnlyDirtyModels&&null==r.paginationMeta&&null==r.initialState?.pagination?.meta?e:i({},e,{pagination:i({},e.pagination,{meta:o})})}),[t,r.paginationMeta,r.initialState?.pagination?.meta]),s=e.useCallback(((e,r)=>{const n=r.stateToRestore.pagination?.meta?r.stateToRestore.pagination.meta:ko(t);return t.current.setState((e=>i({},e,{pagination:i({},e.pagination,{meta:n})}))),e}),[t]);Hc(t,"exportState",a),Hc(t,"restoreState",s),e.useEffect((()=>{r.paginationMeta&&t.current.setPaginationMeta(r.paginationMeta)}),[t,r.paginationMeta])})(t,n),((t,r)=>{const n=ho(t,"useGridPaginationModel"),o=We(t,St),l=e.useRef($n(t)),a=Math.floor(r.rowHeight*o);t.current.registerControlState({stateId:"paginationModel",propModel:r.paginationModel,propOnChange:r.onPaginationModelChange,stateSelector:Io,changeEvent:"paginationModelChange"});const s=e.useCallback((e=>{const r=Io(t);e!==r.page&&(n.debug(`Setting page to ${e}`),t.current.setPaginationModel({page:e,pageSize:r.pageSize}))}),[t,n]),u=e.useCallback((e=>{const r=Io(t);e!==r.pageSize&&(n.debug(`Setting page size to ${e}`),t.current.setPaginationModel({pageSize:e,page:r.page}))}),[t,n]),c=e.useCallback((e=>{const o=Io(t);e!==o&&(n.debug("Setting 'paginationModel' to",e),t.current.setState((t=>i({},t,{pagination:i({},t.pagination,{paginationModel:Zc(t.pagination,r.signature,e)})})),"setPaginationModel"))}),[t,n,r.signature]);mo(t,{setPage:s,setPageSize:u,setPaginationModel:c},"public");const d=e.useCallback(((e,n)=>{const o=Io(t);return!n.exportOnlyDirtyModels||null!=r.paginationModel||null!=r.initialState?.pagination?.paginationModel||0!==o.page&&o.pageSize!==(r.autoPageSize?0:100)?i({},e,{pagination:i({},e.pagination,{paginationModel:o})}):e}),[t,r.paginationModel,r.initialState?.pagination?.paginationModel,r.autoPageSize]),p=e.useCallback(((e,n)=>{const o=n.stateToRestore.pagination?.paginationModel?i({},xo(r.autoPageSize),n.stateToRestore.pagination?.paginationModel):Io(t);return t.current.setState((e=>i({},e,{pagination:i({},e.pagination,{paginationModel:Zc(e.pagination,r.signature,o)})})),"stateRestorePreProcessing"),e}),[t,r.autoPageSize,r.signature]);Hc(t,"exportState",d),Hc(t,"restoreState",p);const f=e.useCallback((()=>{if(!r.autoPageSize)return;const e=t.current.getRootDimensions(),n=Math.floor(e.viewportInnerSize.height/a);t.current.setPageSize(n)}),[t,r.autoPageSize,a]),g=e.useCallback((e=>{if(null==e)return;const r=Io(t);if(0===r.page)return;const n=Fo(t);r.page>n-1&&t.current.setPage(Math.max(0,n-1))}),[t]),m=e.useCallback((()=>{0!==Io(t).page&&t.current.setPage(0),0!==t.current.getScrollPosition().top&&t.current.scroll({top:0})}),[t]),h=e.useCallback((e=>{const r=i({},e,{items:Kn(t)});rr(r,l.current)||(l.current=r,m())}),[t,m]);wt(t,"viewportInnerSizeChange",f),wt(t,"paginationModelChange",(()=>{const e=Io(t);t.current.virtualScrollerRef?.current&&t.current.scrollToIndexes({rowIndex:e.page*e.pageSize})})),wt(t,"rowCountChange",g),wt(t,"sortModelChange",or(r.resetPageOnSortFilter,m)),wt(t,"filterModelChange",or(r.resetPageOnSortFilter,h));const b=e.useRef(!0);e.useEffect((()=>{b.current?b.current=!1:r.pagination&&t.current.setState((e=>i({},e,{pagination:i({},e.pagination,{paginationModel:Zc(e.pagination,r.signature,r.paginationModel)})})))}),[t,r.paginationModel,r.signature,r.pagination]),e.useEffect((()=>{t.current.setState((e=>{const t=!0===r.pagination;return e.pagination.paginationMode===r.paginationMode||e.pagination.enabled===t?e:i({},e,{pagination:i({},e.pagination,{paginationMode:r.paginationMode,enabled:!0===r.pagination})})}))}),[t,r.paginationMode,r.pagination]),e.useEffect(f,[f])})(t,n),((t,n)=>{const o=ho(t,"useGridRowCount"),l=We(t,_n),a=We(t,Mo),s=We(t,ko),u=We(t,Io),c=r((()=>Io(t).pageSize));t.current.registerControlState({stateId:"paginationRowCount",propModel:n.rowCount,propOnChange:n.onRowCountChange,stateSelector:Mo,changeEvent:"rowCountChange"});const d=e.useCallback((e=>{a!==e&&(o.debug("Setting 'rowCount' to",e),t.current.setState((t=>i({},t,{pagination:i({},t.pagination,{rowCount:e})}))))}),[t,o,a]);mo(t,{setRowCount:d},"public");const p=e.useCallback(((e,r)=>{const o=Mo(t);return r.exportOnlyDirtyModels&&null==n.rowCount&&null==n.initialState?.pagination?.rowCount?e:i({},e,{pagination:i({},e.pagination,{rowCount:o})})}),[t,n.rowCount,n.initialState?.pagination?.rowCount]),f=e.useCallback(((e,r)=>{const n=r.stateToRestore.pagination?.rowCount?r.stateToRestore.pagination.rowCount:Mo(t);return t.current.setState((e=>i({},e,{pagination:i({},e.pagination,{rowCount:n})}))),e}),[t]);Hc(t,"exportState",p),Hc(t,"restoreState",f);const g=e.useCallback((e=>{"client"!==n.paginationMode&&c.current&&e.pageSize!==c.current&&(c.current=e.pageSize,-1===a&&t.current.setPage(0))}),[n.paginationMode,c,a,t]);wt(t,"paginationModelChange",g),e.useEffect((()=>{"client"===n.paginationMode?t.current.setRowCount(l):null!=n.rowCount&&t.current.setRowCount(n.rowCount)}),[t,n.paginationMode,l,n.rowCount]);const m=!1===s.hasNextPage;e.useEffect((()=>{m&&-1===a&&t.current.setRowCount(u.pageSize*u.page+l)}),[t,l,m,a,u])})(t,n)},dd=(e,t)=>i({},e,{preferencePanel:t.initialState?.preferencePanel??{open:!1}}),pd=e=>{switch(e.type){case"boolean":return!1;case"date":case"dateTime":case"number":return;case"singleSelect":return null;default:return""}},fd=["id","field"],gd=["id","field"],md=["id"],hd=["id"],bd=e=>i({},e,{editRows:{}}),wd=(t,r)=>{((t,r)=>{const[n,o]=e.useState({}),l=e.useRef(n),u=e.useRef({}),{processRowUpdate:c,onProcessRowUpdateError:d,cellModesModel:p,onCellModesModelChange:f}=r,g=e=>(...t)=>{r.editMode===sr.Cell&&e(...t)},m=e.useCallback(((e,r)=>{const n=t.current.getCellParams(e,r);if(!t.current.isCellEditable(n))throw new Error(`MUI X: The cell with id=${e} and field=${r} is not editable.`)}),[t]),h=e.useCallback(((e,r,n)=>{if(t.current.getCellMode(e,r)!==n)throw new Error(`MUI X: The cell with id=${e} and field=${r} is not in ${n} mode.`)}),[t]),b=e.useCallback(((e,r)=>{if(!e.isEditable)return;if(e.cellMode===ur.Edit)return;const n=i({},e,{reason:pr.cellDoubleClick});t.current.publishEvent("cellEditStart",n,r)}),[t]),w=e.useCallback(((e,r)=>{if(e.cellMode===ur.View)return;if(t.current.getCellMode(e.id,e.field)===ur.View)return;const n=i({},e,{reason:fr.cellFocusOut});t.current.publishEvent("cellEditStop",n,r)}),[t]),C=e.useCallback(((e,r)=>{if(e.cellMode===ur.Edit){if(229===r.which)return;let n;if("Escape"===r.key?n=fr.escapeKeyDown:"Enter"===r.key?n=fr.enterKeyDown:"Tab"===r.key&&(n=r.shiftKey?fr.shiftTabKeyDown:fr.tabKeyDown,r.preventDefault()),n){const o=i({},e,{reason:n});t.current.publishEvent("cellEditStop",o,r)}}else if(e.isEditable){let n;if(!t.current.unstable_applyPipeProcessors("canStartEditing",!0,{event:r,cellParams:e,editMode:"cell"}))return;if(_s(r)?n=pr.printableKeyDown:qs(r)?n=pr.pasteKeyDown:"Enter"===r.key?n=pr.enterKeyDown:"Backspace"!==r.key&&"Delete"!==r.key||(n=pr.deleteKeyDown),n){const o=i({},e,{reason:n,key:r.key});t.current.publishEvent("cellEditStart",o,r)}}}),[t]),v=e.useCallback((e=>{const{id:r,field:n,reason:o}=e,l={id:r,field:n};o!==pr.printableKeyDown&&o!==pr.deleteKeyDown&&o!==pr.pasteKeyDown||(l.deleteValue=!0),t.current.startCellEditMode(l)}),[t]),x=e.useCallback((e=>{const{id:r,field:n,reason:o}=e;let l;t.current.runPendingEditCellValueMutation(r,n),o===fr.enterKeyDown?l="below":o===fr.tabKeyDown?l="right":o===fr.shiftTabKeyDown&&(l="left");const i="escapeKeyDown"===o;t.current.stopCellEditMode({id:r,field:n,ignoreModifications:i,cellToFocusAfter:l})}),[t]);var y;wt(t,"cellDoubleClick",g(b)),wt(t,"cellFocusOut",g(w)),wt(t,"cellKeyDown",g(C)),wt(t,"cellEditStart",g(v)),wt(t,"cellEditStop",g(x)),vt(t,"cellEditStart",r.onCellEditStart),vt(t,"cellEditStop",(y=r.onCellEditStop,async(...e)=>{if(y){const{id:r,field:n}=e[0],o=t.current.state.editRows,l=o[r][n]?.error;l||y(...e)}}));const S=e.useCallback(((e,r)=>{const n=Yi(t.current.state);return n[e]&&n[e][r]?ur.Edit:ur.View}),[t]),R=I((e=>{const n=e!==r.cellModesModel;f&&n&&f(e,{api:t.current}),r.cellModesModel&&n||(o(e),l.current=e,t.current.publishEvent("cellModesModelChange",e))})),M=e.useCallback(((e,t,r)=>{const n=i({},l.current);if(null!==r)n[e]=i({},n[e],{[t]:i({},r)});else{const r=n[e],o=a(r,[t].map(de));n[e]=o,0===Object.keys(n[e]).length&&delete n[e]}R(n)}),[R]),k=e.useCallback(((e,r,n)=>{t.current.setState((t=>{const o=i({},t.editRows);return null!==n?o[e]=i({},o[e],{[r]:i({},n)}):(delete o[e][r],0===Object.keys(o[e]).length&&delete o[e]),i({},t,{editRows:o})})),t.current.forceUpdate()}),[t]),P=e.useCallback((e=>{const{id:t,field:r}=e,n=a(e,fd);m(t,r),h(t,r,ur.View),M(t,r,i({mode:ur.Edit},n))}),[m,h,M]),E=I((async e=>{const{id:r,field:n,deleteValue:o,initialValue:l}=e,a=t.current.getCellValue(r,n);let s=a;o?s=pd(t.current.getColumn(n)):l&&(s=l);const u=t.current.getColumn(n),c=!!u.preProcessEditCellProps&&o;let d={value:s,error:!1,isProcessingProps:c};if(k(r,n,d),t.current.setCellFocus(r,n),c&&(d=await Promise.resolve(u.preProcessEditCellProps({id:r,row:t.current.getRow(r),props:d,hasChanged:s!==a})),t.current.getCellMode(r,n)===ur.Edit)){const e=Yi(t.current.state);k(r,n,i({},d,{value:e[r][n].value,isProcessingProps:!1}))}})),F=e.useCallback((e=>{const{id:t,field:r}=e,n=a(e,gd);h(t,r,ur.Edit),M(t,r,i({mode:ur.View},n))}),[h,M]),H=I((async e=>{const{id:r,field:n,ignoreModifications:o,cellToFocusAfter:l="none"}=e;h(r,n,ur.Edit),t.current.runPendingEditCellValueMutation(r,n);const i=()=>{k(r,n,null),M(r,n,null),"none"!==l&&t.current.moveFocusToRelativeCell(r,n,l)};if(o)return void i();const a=Yi(t.current.state),{error:s,isProcessingProps:p}=a[r][n];if(s||p)return u.current[r][n].mode=ur.Edit,void M(r,n,{mode:ur.Edit});const f=t.current.getRowWithUpdatedValuesFromCellEditing(r,n);if(c){const e=e=>{u.current[r][n].mode=ur.Edit,M(r,n,{mode:ur.Edit}),d&&d(e)};try{const n=t.current.getRow(r);Promise.resolve(c(f,n,{rowId:r})).then((e=>{t.current.updateRows([e]),i()})).catch(e)}catch(g){e(g)}}else t.current.updateRows([f]),i()})),D={setCellEditingEditCellValue:e.useCallback((async e=>{const{id:r,field:n,value:o,debounceMs:l,unstable_skipValueParser:a}=e;m(r,n),h(r,n,ur.Edit);const s=t.current.getColumn(n),u=t.current.getRow(r);let c=o;s.valueParser&&!a&&(c=s.valueParser(o,u,s,t));let d=Yi(t.current.state),p=i({},d[r][n],{value:c,changeReason:l?"debouncedSetEditCellValue":"setEditCellValue"});if(s.preProcessEditCellProps){const e=o!==d[r][n].value;p=i({},p,{isProcessingProps:!0}),k(r,n,p),p=await Promise.resolve(s.preProcessEditCellProps({id:r,row:u,props:p,hasChanged:e}))}return t.current.getCellMode(r,n)!==ur.View&&(d=Yi(t.current.state),p=i({},p,{isProcessingProps:!1}),p.value=s.preProcessEditCellProps?d[r][n].value:c,k(r,n,p),d=Yi(t.current.state),!d[r]?.[n]?.error)}),[t,m,h,k]),getRowWithUpdatedValuesFromCellEditing:e.useCallback(((e,r)=>{const n=t.current.getColumn(r),o=Yi(t.current.state),l=t.current.getRow(e);if(!o[e]||!o[e][r])return t.current.getRow(e);const{value:a}=o[e][r];return n.valueSetter?n.valueSetter(a,l,n,t):i({},l,{[r]:a})}),[t])};mo(t,{getCellMode:S,startCellEditMode:P,stopCellEditMode:F},"public"),mo(t,D,"private"),e.useEffect((()=>{p&&R(p)}),[p,R]),s((()=>{const e=Ht(t),r=u.current;u.current=nr(n),Object.entries(n).forEach((([n,o])=>{Object.entries(o).forEach((([o,l])=>{const a=r[n]?.[o]?.mode||ur.View,s=e[n]?t.current.getRowId(e[n]):n;l.mode===ur.Edit&&a===ur.View?E(i({id:s,field:o},l)):l.mode===ur.View&&a===ur.Edit&&H(i({id:s,field:o},l))}))}))}),[t,n,E,H])})(t,r),((t,r)=>{const[n,o]=e.useState({}),l=e.useRef(n),u=e.useRef({}),c=e.useRef({}),d=e.useRef(void 0),p=e.useRef(null),{processRowUpdate:f,onProcessRowUpdateError:g,rowModesModel:m,onRowModesModelChange:h}=r,b=e=>(...t)=>{r.editMode===sr.Row&&e(...t)},w=e.useCallback(((e,r)=>{const n=t.current.getCellParams(e,r);if(!t.current.isCellEditable(n))throw new Error(`MUI X: The cell with id=${e} and field=${r} is not editable.`)}),[t]),C=e.useCallback(((e,r)=>{if(t.current.getRowMode(e)!==r)throw new Error(`MUI X: The row with id=${e} is not in ${r} mode.`)}),[t]),v=e.useCallback((e=>{const r=Yi(t.current.state);return Object.values(r[e]).some((e=>e.error))}),[t]),x=e.useCallback(((e,r)=>{if(!e.isEditable)return;if(t.current.getRowMode(e.id)===cr.Edit)return;const n=t.current.getRowParams(e.id),o=i({},n,{field:e.field,reason:gr.cellDoubleClick});t.current.publishEvent("rowEditStart",o,r)}),[t]),y=e.useCallback((e=>{p.current=e}),[]),S=e.useCallback(((e,r)=>{e.isEditable&&t.current.getRowMode(e.id)!==cr.View&&(p.current=null,d.current=setTimeout((()=>{if(p.current?.id!==e.id){if(!t.current.getRow(e.id))return;if(t.current.getRowMode(e.id)===cr.View)return;if(v(e.id))return;const n=t.current.getRowParams(e.id),o=i({},n,{field:e.field,reason:mr.rowFocusOut});t.current.publishEvent("rowEditStop",o,r)}})))}),[t,v]);e.useEffect((()=>()=>{clearTimeout(d.current)}),[]);const R=e.useCallback(((e,r)=>{if(e.cellMode===cr.Edit){if(229===r.which)return;let n;if("Escape"===r.key)n=mr.escapeKeyDown;else if("Enter"===r.key)n=mr.enterKeyDown;else if("Tab"===r.key){const o=Pr(t).filter((r=>t.current.getColumn(r).type===rn||t.current.isCellEditable(t.current.getCellParams(e.id,r))));if(r.shiftKey?e.field===o[0]&&(n=mr.shiftTabKeyDown):e.field===o[o.length-1]&&(n=mr.tabKeyDown),r.preventDefault(),!n){const n=o.findIndex((t=>t===e.field)),l=o[r.shiftKey?n-1:n+1];t.current.setCellFocus(e.id,l)}}if(n){if(n!==mr.escapeKeyDown&&v(e.id))return;const o=i({},t.current.getRowParams(e.id),{reason:n,field:e.field});t.current.publishEvent("rowEditStop",o,r)}}else if(e.isEditable){let n;if(!t.current.unstable_applyPipeProcessors("canStartEditing",!0,{event:r,cellParams:e,editMode:"row"}))return;if(_s(r)||qs(r)?n=gr.printableKeyDown:"Enter"===r.key?n=gr.enterKeyDown:"Backspace"!==r.key&&"Delete"!==r.key||(n=gr.deleteKeyDown),n){const o=t.current.getRowParams(e.id),l=i({},o,{field:e.field,reason:n});t.current.publishEvent("rowEditStart",l,r)}}}),[t,v]),M=e.useCallback((e=>{const{id:r,field:n,reason:o}=e,l={id:r,fieldToFocus:n};o!==gr.printableKeyDown&&o!==gr.deleteKeyDown||(l.deleteValue=!!n),t.current.startRowEditMode(l)}),[t]),k=e.useCallback((e=>{const{id:r,reason:n,field:o}=e;let l;t.current.runPendingEditCellValueMutation(r),n===mr.enterKeyDown?l="below":n===mr.tabKeyDown?l="right":n===mr.shiftTabKeyDown&&(l="left");const i="escapeKeyDown"===n;t.current.stopRowEditMode({id:r,ignoreModifications:i,field:o,cellToFocusAfter:l})}),[t]);wt(t,"cellDoubleClick",b(x)),wt(t,"cellFocusIn",b(y)),wt(t,"cellFocusOut",b(S)),wt(t,"cellKeyDown",b(R)),wt(t,"rowEditStart",b(M)),wt(t,"rowEditStop",b(k)),vt(t,"rowEditStart",r.onRowEditStart),vt(t,"rowEditStop",r.onRowEditStop);const P=e.useCallback((e=>Qi(t,{rowId:e,editMode:r.editMode})?cr.Edit:cr.View),[t,r.editMode]),E=I((e=>{const n=e!==r.rowModesModel;h&&n&&h(e,{api:t.current}),r.rowModesModel&&n||(o(e),l.current=e,t.current.publishEvent("rowModesModelChange",e))})),F=e.useCallback(((e,t)=>{const r=i({},l.current);null!==t?r[e]=i({},t):delete r[e],E(r)}),[E]),H=e.useCallback(((e,r)=>{t.current.setState((t=>{const n=i({},t.editRows);return null!==r?n[e]=r:delete n[e],i({},t,{editRows:n})})),t.current.forceUpdate()}),[t]),D=e.useCallback(((e,r,n)=>{t.current.setState((t=>{const o=i({},t.editRows);return null!==n?o[e]=i({},o[e],{[r]:i({},n)}):(delete o[e][r],0===Object.keys(o[e]).length&&delete o[e]),i({},t,{editRows:o})})),t.current.forceUpdate()}),[t]),T=e.useCallback((e=>{const{id:t}=e,r=a(e,md);C(t,cr.View),F(t,i({mode:cr.Edit},r))}),[C,F]),O=I((e=>{const{id:r,fieldToFocus:n,deleteValue:o,initialValue:l}=e,a=t.current.getRow(r),s=Sr(t),u=s.reduce(((e,i)=>{if(!t.current.getCellParams(r,i).isEditable)return e;const a=t.current.getColumn(i);let s=t.current.getCellValue(r,i);return n===i&&(o||l)&&(o?s=pd(a):l&&(s=l)),e[i]={value:s,error:!1,isProcessingProps:!!a.preProcessEditCellProps&&o},e}),{});c.current[r]=a,H(r,u),n&&t.current.setCellFocus(r,n),s.filter((e=>!!t.current.getColumn(e).preProcessEditCellProps&&o)).forEach((e=>{const n=t.current.getColumn(e),s=t.current.getCellValue(r,e),c=o?pd(n):l??s;Promise.resolve(n.preProcessEditCellProps({id:r,row:a,props:u[e],hasChanged:c!==s})).then((n=>{if(t.current.getRowMode(r)===cr.Edit){const o=Yi(t.current.state);D(r,e,i({},n,{value:o[r][e].value,isProcessingProps:!1}))}}))}))})),L=e.useCallback((e=>{const{id:t}=e,r=a(e,hd);C(t,cr.Edit),F(t,i({mode:cr.View},r))}),[C,F]),$=I((e=>{const{id:r,ignoreModifications:n,field:o,cellToFocusAfter:l="none"}=e;t.current.runPendingEditCellValueMutation(r);const i=()=>{"none"!==l&&o&&t.current.moveFocusToRelativeCell(r,o,l),H(r,null),F(r,null),delete c.current[r]};if(n)return void i();const a=Yi(t.current.state),s=c.current[r];if(Object.values(a[r]).some((e=>e.isProcessingProps)))return void(u.current[r].mode=cr.Edit);if(v(r))return u.current[r].mode=cr.Edit,void F(r,{mode:cr.Edit});const d=t.current.getRowWithUpdatedValuesFromRowEditing(r);if(f){const e=e=>{u.current[r]&&(u.current[r].mode=cr.Edit,F(r,{mode:cr.Edit})),g&&g(e)};try{Promise.resolve(f(d,s,{rowId:r})).then((e=>{t.current.updateRows([e]),i()})).catch(e)}catch(p){e(p)}}else t.current.updateRows([d]),i()})),z={setRowEditingEditCellValue:e.useCallback((e=>{const{id:r,field:n,value:o,debounceMs:l,unstable_skipValueParser:s}=e;w(r,n);const u=t.current.getColumn(n),c=t.current.getRow(r);let d=o;u.valueParser&&!s&&(d=u.valueParser(o,c,u,t));let p=Yi(t.current.state),f=i({},p[r][n],{value:d,changeReason:l?"debouncedSetEditCellValue":"setEditCellValue"});return u.preProcessEditCellProps||D(r,n,f),new Promise((e=>{const o=[];if(u.preProcessEditCellProps){const l=f.value!==p[r][n].value;f=i({},f,{isProcessingProps:!0}),D(r,n,f);const s=p[r],g=a(s,[n].map(de)),m=Promise.resolve(u.preProcessEditCellProps({id:r,row:c,props:f,hasChanged:l,otherFieldsProps:g})).then((o=>{t.current.getRowMode(r)!==cr.View?(p=Yi(t.current.state),(o=i({},o,{isProcessingProps:!1})).value=u.preProcessEditCellProps?p[r][n].value:d,D(r,n,o)):e(!1)}));o.push(m)}Object.entries(p[r]).forEach((([l,s])=>{if(l===n)return;const u=t.current.getColumn(l);if(!u.preProcessEditCellProps)return;s=i({},s,{isProcessingProps:!0}),D(r,l,s),p=Yi(t.current.state);const d=p[r],f=a(d,[l].map(de)),g=Promise.resolve(u.preProcessEditCellProps({id:r,row:c,props:s,hasChanged:!1,otherFieldsProps:f})).then((n=>{t.current.getRowMode(r)!==cr.View?(n=i({},n,{isProcessingProps:!1}),D(r,l,n)):e(!1)}));o.push(g)})),Promise.all(o).then((()=>{t.current.getRowMode(r)===cr.Edit?(p=Yi(t.current.state),e(!p[r][n].error)):e(!1)}))}))}),[t,w,D]),getRowWithUpdatedValuesFromRowEditing:e.useCallback((e=>{const r=Yi(t.current.state),n=t.current.getRow(e);if(!r[e])return t.current.getRow(e);let o=i({},c.current[e],n);return Object.entries(r[e]).forEach((([e,r])=>{const n=t.current.getColumn(e);n?.valueSetter?o=n.valueSetter(r.value,o,n,t):o[e]=r.value})),o}),[t])};mo(t,{getRowMode:P,startRowEditMode:T,stopRowEditMode:L},"public"),mo(t,z,"private"),e.useEffect((()=>{m&&E(m)}),[m,E]),s((()=>{const e=Ht(t),r=u.current;u.current=nr(n);const o=new Set([...Object.keys(n),...Object.keys(r)]);Array.from(o).forEach((o=>{const l=n[o]??{mode:cr.View},a=r[o]?.mode||cr.View,s=e[o]?t.current.getRowId(e[o]):o;l.mode===cr.Edit&&a===cr.View?O(i({id:s},l)):l.mode===cr.View&&a===cr.Edit&&$(i({id:s},l))}))}),[t,n,O,$])})(t,r);const n=e.useRef({}),{isCellEditable:o}=r,l=e.useCallback((e=>!dn(e.rowNode)&&(!!e.colDef.editable&&(!!e.colDef.renderEditCell&&(!o||o(e))))),[o]);e.useEffect((()=>{const e=n.current;return()=>{Object.entries(e).forEach((([t,r])=>{Object.keys(r).forEach((r=>{const[n]=e[t][r];clearTimeout(n),delete e[t][r]}))}))}}),[]);const u=e.useCallback(((e,t)=>{if(n.current[e])if(t){if(n.current[e][t]){const[,r]=n.current[e][t];r()}}else Object.keys(n.current[e]).forEach((t=>{const[,r]=n.current[e][t];r()}))}),[]),c=e.useCallback((e=>{const{id:o,field:l,debounceMs:i}=e;return new Promise((a=>{((e,t,r,o)=>{if(!r)return void o();if(n.current[e]||(n.current[e]={}),n.current[e][t]){const[r]=n.current[e][t];clearTimeout(r)}const l=setTimeout((()=>{o(),delete n.current[e][t]}),r);n.current[e][t]=[l,()=>{const[r]=n.current[e][t];clearTimeout(r),o(),delete n.current[e][t]}]})(o,l,i,(async()=>{const n=r.editMode===sr.Row?t.current.setRowEditingEditCellValue:t.current.setCellEditingEditCellValue;if(t.current.getCellMode(o,l)===ur.Edit){const t=await n(e);a(t)}}))}))}),[t,r.editMode]),d=e.useCallback(((e,n)=>r.editMode===sr.Cell?t.current.getRowWithUpdatedValuesFromCellEditing(e,n):t.current.getRowWithUpdatedValuesFromRowEditing(e)),[t,r.editMode]),p=e.useCallback(((e,r)=>{const n=Yi(t.current.state);return n[e]?.[r]??null}),[t]),f={runPendingEditCellValueMutation:u};mo(t,{isCellEditable:l,setEditCellValue:c,getRowWithUpdatedValues:d,unstable_getEditCellMeta:p},"public"),mo(t,f,"private")},Cd=(e,t,r)=>{const n=!!t.unstable_dataSource;return r.current.caches.rows=sn({rows:n?[]:t.rows,getRowId:t.getRowId,loading:t.loading,rowCount:t.rowCount}),i({},e,{rows:cn({apiRef:r,rowCountProp:t.rowCount,loadingProp:!!n||t.loading,previousTree:null,previousTreeDepths:null})})},vd=e=>"full"===e.updates.type?(e=>{const t={[on]:i({},{type:"group",id:on,depth:-1,groupingField:null,groupingKey:null,isAutoGenerated:!0,children:[],childrenFromPath:{},childrenExpanded:!0,parent:null},{children:e})};for(let r=0;r<e.length;r+=1){const n=e[r];t[n]={id:n,depth:0,parent:on,type:"leaf",groupingKey:null}}return{groupingName:Oc,tree:t,treeDepths:{0:e.length},dataRowIds:e}})(e.updates.rows):(({previousTree:e,actions:t})=>{const r=i({},e),n={};for(let i=0;i<t.remove.length;i+=1){const e=t.remove[i];n[e]=!0,delete r[e]}for(let i=0;i<t.insert.length;i+=1){const e=t.insert[i];r[e]={id:e,depth:0,parent:on,type:"leaf",groupingKey:null}}const o=r[on];let l=[...o.children,...t.insert];return Object.values(n).length&&(l=l.filter((e=>!n[e]))),r[on]=i({},o,{children:l}),{groupingName:Oc,tree:r,treeDepths:{0:l.length},dataRowIds:l}})({previousTree:e.previousTree,actions:e.updates.actions});class xd extends Error{}function yd(t,r){const n=e.useCallback((e=>({field:e,colDef:t.current.getColumn(e)})),[t]),o=e.useCallback((e=>{const r=t.current.getRow(e);if(!r)throw new xd(`No row with id #${e} found`);return{id:e,columns:t.current.getAllColumns(),row:r}}),[t]),l=e.useCallback(((e,r,n,{cellMode:o,colDef:l,hasFocus:i,rowNode:a,tabIndex:s})=>{const u=n[r],c=l?.valueGetter?l.valueGetter(u,n,l,t):u,d={id:e,field:r,row:n,rowNode:a,colDef:l,cellMode:o,hasFocus:i,tabIndex:s,value:c,formattedValue:c,isEditable:!1,api:null};return l&&l.valueFormatter&&(d.formattedValue=l.valueFormatter(c,n,l,t)),d.isEditable=l&&t.current.isCellEditable(d),d}),[t]),i=e.useCallback(((e,n)=>{const o=t.current.getRow(e),l=t.current.getRowNode(e);if(!o||!l)throw new xd(`No row with id #${e} found`);const i=ao(t),a=po(t),s=t.current.getCellMode(e,n);return t.current.getCellParamsForRow(e,n,o,{colDef:r.unstable_listView&&r.unstable_listColumn?.field===n?ai(t.current.state):t.current.getColumn(n),rowNode:l,hasFocus:null!==i&&i.field===n&&i.id===e,tabIndex:a&&a.field===n&&a.id===e?0:-1,cellMode:s})}),[t,r.unstable_listView,r.unstable_listColumn?.field]),a=e.useCallback(((e,r)=>{const n=t.current.getColumn(r),o=t.current.getRow(e);if(!o)throw new xd(`No row with id #${e} found`);return n&&n.valueGetter?n.valueGetter(o[n.field],o,n,t):o[r]}),[t]),s=e.useCallback(((e,r)=>{const n=r.field;if(!r||!r.valueGetter)return e[n];const o=e[r.field];return r.valueGetter(o,e,r,t)}),[t]),u=e.useCallback(((e,r)=>{const n=s(e,r);return r&&r.valueFormatter?r.valueFormatter(n,e,r,t):n}),[t,s]),c=e.useCallback((e=>t.current.rootElementRef.current?function(e,t){return e.querySelector(`[role="columnheader"][data-field="${qa(t)}"]`)}(t.current.rootElementRef.current,e):null),[t]),d=e.useCallback((e=>t.current.rootElementRef.current?function(e,t){return e.querySelector(Xa(t))}(t.current.rootElementRef.current,e):null),[t]),p=e.useCallback(((e,r)=>t.current.rootElementRef.current?function(e,{id:t,field:r}){const n=`${Xa(t)} .${ft.cell}[data-field="${qa(r)}"]`;return e.querySelector(n)}(t.current.rootElementRef.current,{id:e,field:r}):null),[t]),f={getCellParamsForRow:l};mo(t,{getCellValue:a,getCellParams:i,getCellElement:p,getRowValue:s,getRowFormattedValue:u,getRowParams:o,getRowElement:d,getColumnHeaderParams:n,getColumnHeaderElement:c},"public"),mo(t,f,"private")}const Sd=(e,t)=>null==e||Array.isArray(e)?e:t&&t[0]===e?t:[e],Rd=(e,t)=>i({},e,{rowSelection:t.rowSelection?Sd(t.rowSelectionModel)??[]:[]}),Id=(t,r)=>{const n=ho(t,"useGridSelection"),o=e.useCallback((e=>(...t)=>{r.rowSelection&&e(...t)}),[r.rowSelection]),l=r.signature!==ht.DataGrid&&(r.rowSelectionPropagation?.parents||r.rowSelectionPropagation?.descendants),a=e.useMemo((()=>Sd(r.rowSelectionModel,Xn(t.current.state))),[t,r.rowSelectionModel]),s=e.useRef(null);t.current.registerControlState({stateId:"rowSelection",propModel:a,propOnChange:r.onRowSelectionModelChange,stateSelector:Xn,changeEvent:"rowSelectionChange"});const{checkboxSelection:u,disableRowSelectionOnClick:c,isRowSelectable:d}=r,p=eo(r),f=We(t,Tt),g=We(t,zt)>1,m=e.useCallback((e=>{let r=e;const n=s.current??e,o=t.current.isRowSelected(e);if(o){const e=An(t),o=e.findIndex((e=>e===n)),l=e.findIndex((e=>e===r));if(o===l)return;r=o>l?e[l+1]:e[l-1]}s.current=e,t.current.selectRowRange({startId:n,endId:r},!o)}),[t]),h=e.useCallback((e=>{if(r.signature===ht.DataGrid&&!p&&Array.isArray(e)&&e.length>1)throw new Error(["MUI X: `rowSelectionModel` can only contain 1 item in DataGrid.","You need to upgrade to DataGridPro or DataGridPremium component to unlock multiple selection."].join("\n"));Xn(t.current.state)!==e&&(n.debug("Setting selection model"),t.current.setState((t=>i({},t,{rowSelection:r.rowSelection?e:[]}))),t.current.forceUpdate())}),[t,n,r.rowSelection,r.signature,p]),b=e.useCallback((e=>Xn(t.current.state).includes(e)),[t]),w=e.useCallback((e=>{if(!1===r.rowSelection)return!1;if(d&&!d(t.current.getRowParams(e)))return!1;const n=Tt(t)[e];return"footer"!==n?.type&&"pinnedRow"!==n?.type}),[t,r.rowSelection,d]),C=e.useCallback((()=>Qn(t)),[t]),v=e.useCallback(((e,o=!0,i=!1)=>{if(t.current.isRowSelectable(e))if(s.current=e,i){n.debug(`Setting selection for row ${e}`);const i=[],a=e=>{i.push(e)};o&&(a(e),l&&to(t,f,e,r.rowSelectionPropagation?.descendants??!1,r.rowSelectionPropagation?.parents??!1,a)),t.current.setRowSelectionModel(i)}else{n.debug(`Toggling selection for row ${e}`);const i=Xn(t.current.state),a=new Set(i);a.delete(e);const s=e=>{a.add(e)},u=e=>{a.delete(e)};o?(s(e),l&&to(t,f,e,r.rowSelectionPropagation?.descendants??!1,r.rowSelectionPropagation?.parents??!1,s)):l&&ro(t,f,e,r.rowSelectionPropagation?.descendants??!1,r.rowSelectionPropagation?.parents??!1,u);(a.size<2||p)&&t.current.setRowSelectionModel(Array.from(a))}}),[t,n,l,f,r.rowSelectionPropagation?.descendants,r.rowSelectionPropagation?.parents,p]),x=e.useCallback(((e,o=!0,i=!1)=>{n.debug("Setting selection for several rows");const a=e.filter((e=>t.current.isRowSelectable(e)));let s;if(i){if(o){if(s=new Set(a),l){const e=e=>{s.add(e)};a.forEach((n=>{to(t,f,n,r.rowSelectionPropagation?.descendants??!1,r.rowSelectionPropagation?.parents??!1,e)}))}}else s=new Set;const e=Zn(t);if(s.size===Object.keys(e).length&&Array.from(s).every((t=>e[t]===t)))return}else{s=new Set(Object.values(Zn(t)));const e=e=>{s.add(e)},n=e=>{s.delete(e)};a.forEach((i=>{o?(s.add(i),l&&to(t,f,i,r.rowSelectionPropagation?.descendants??!1,r.rowSelectionPropagation?.parents??!1,e)):(n(i),l&&ro(t,f,i,r.rowSelectionPropagation?.descendants??!1,r.rowSelectionPropagation?.parents??!1,n))}))}(s.size<2||p)&&t.current.setRowSelectionModel(Array.from(s))}),[n,l,p,t,f,r.rowSelectionPropagation?.descendants,r.rowSelectionPropagation?.parents]),y=e.useCallback((e=>{if(!g||!l||0===e.length)return e;const n=new Set(e),o=e=>{n.add(e)};for(const l of e)to(t,f,l,r.rowSelectionPropagation?.descendants??!1,r.rowSelectionPropagation?.parents??!1,o,n);return Array.from(n)}),[t,f,r.rowSelectionPropagation?.descendants,r.rowSelectionPropagation?.parents,g,l]),S=e.useCallback((({startId:e,endId:r},o=!0,l=!1)=>{if(!t.current.getRow(e)||!t.current.getRow(r))return;n.debug(`Expanding selection from row ${e} to row ${r}`);const i=An(t),a=i.indexOf(e),s=i.indexOf(r),[u,c]=a>s?[s,a]:[a,s],d=i.slice(u,c+1);t.current.selectRows(d,o,l)}),[t,n]),R={selectRows:x,selectRowRange:S,getPropagatedRowSelectionModel:y};mo(t,{selectRow:v,setRowSelectionModel:h,getSelectedRows:C,isRowSelected:b,isRowSelectable:w},"public"),mo(t,R,r.signature===ht.DataGrid?"private":"public");const M=e.useRef(!0),k=e.useCallback(((e=!1)=>{if(M.current)return;const n=Xn(t.current.state),o=Ht(t),l=jn(t),a=i({},Zn(t));let s=!1;n.forEach((e=>{if((e=>"server"===r.filterMode?!o[e]:!o[e]||!1===l[e])(e)){if(r.keepNonExistentRowsSelected)return;return delete a[e],void(s=!0)}if(!r.rowSelectionPropagation?.parents)return;const t=f[e];if("group"===t.type){if(t.isAutoGenerated)return delete a[e],void(s=!0);t.children.every((e=>!1===l[e]))||(delete a[e],s=!0)}}));const u=g&&r.rowSelectionPropagation?.parents&&Object.keys(a).length>0;if(s||u&&!e){const e=Object.values(a);u?t.current.selectRows(e,!0,!0):t.current.setRowSelectionModel(e)}}),[t,g,r.rowSelectionPropagation?.parents,r.keepNonExistentRowsSelected,r.filterMode,f]),P=e.useCallback(((e,r)=>{const n=r.metaKey||r.ctrlKey,o=!u&&!n&&!(e=>!!e.key)(r),l=!p||o,i=t.current.isRowSelected(e);l?t.current.selectRow(e,!!o||!i,!0):t.current.selectRow(e,!i,!1)}),[t,p,u]),E=e.useCallback(((e,r)=>{if(c)return;const n=r.target.closest(`.${ft.cell}`)?.getAttribute("data-field");if(n===Vo.field)return;if(n===wn)return;if(n){const e=t.current.getColumn(n);if(e?.type===rn)return}"pinnedRow"!==Tt(t)[e.id].type&&(r.shiftKey&&p?m(e.id):P(e.id,r))}),[c,p,t,m,P]),F=e.useCallback(((e,t)=>{p&&t.shiftKey&&window.getSelection()?.removeAllRanges()}),[p]),H=e.useCallback(((e,r)=>{p&&r.nativeEvent.shiftKey?m(e.id):t.current.selectRow(e.id,e.value,!p)}),[t,m,p]),D=e.useCallback((e=>{const n=r.pagination&&r.checkboxSelectionVisibleOnly&&"client"===r.paginationMode?To(t):An(t);t.current.selectRows(n,e.value)}),[t,r.checkboxSelectionVisibleOnly,r.pagination,r.paginationMode]),T=e.useCallback(((e,r)=>{if(t.current.getCellMode(e.id,e.field)!==ur.Edit&&!Ya(r)){if(Us(r.key)&&r.shiftKey){const n=ao(t);if(n&&n.id!==e.id){r.preventDefault();const o=t.current.isRowSelected(n.id);if(!p)return void t.current.selectRow(n.id,!o,!0);const l=t.current.getRowIndexRelativeToVisibleRows(n.id),i=t.current.getRowIndexRelativeToVisibleRows(e.id);let a,s;l>i?o?(a=i,s=l-1):(a=i,s=l):o?(a=l+1,s=i):(a=l,s=i);const u=Ul(t).rows.slice(a,s+1).map((e=>e.id));return void t.current.selectRows(u,!o)}}if(" "===r.key&&r.shiftKey)return r.preventDefault(),void P(e.id,r);"A"===String.fromCharCode(r.keyCode)&&(r.ctrlKey||r.metaKey)&&(r.preventDefault(),x(t.current.getAllRowIds(),!0))}}),[t,P,x,p]),O=I((()=>{if(!r.rowSelection)return void t.current.setRowSelectionModel([]);if(void 0===a)return;if(!l||!g||0===a.length)return void t.current.setRowSelectionModel(a);const e=t.current.getPropagatedRowSelectionModel(a);e.length===a.length&&e.every((e=>a.includes(e)))?t.current.setRowSelectionModel(a):t.current.setRowSelectionModel(e)}));wt(t,"sortedRowsSet",o((()=>k(!0)))),wt(t,"filteredRowsSet",o((()=>k()))),wt(t,"rowClick",o(E)),wt(t,"rowSelectionCheckboxChange",o(H)),wt(t,"headerSelectionCheckboxChange",D),wt(t,"cellMouseDown",o(F)),wt(t,"cellKeyDown",o(T)),e.useEffect((()=>{O()}),[t,a,r.rowSelection,O]);const L=null!=a;e.useEffect((()=>{if(L||!r.rowSelection)return;const e=Xn(t.current.state);if(w){const r=e.filter((e=>w(e)));r.length<e.length&&t.current.setRowSelectionModel(r)}}),[t,w,L,r.rowSelection]),e.useEffect((()=>{if(!r.rowSelection||L)return;const e=Xn(t.current.state);!p&&e.length>1&&t.current.setRowSelectionModel([])}),[t,p,u,L,r.rowSelection]),e.useEffect((()=>{o(k)}),[k,o]),e.useEffect((()=>{M.current&&(M.current=!1)}),[])},Md=(t,r)=>{const n=(t=>{const{classes:r}=t;return e.useMemo((()=>c({cellCheckbox:["cellCheckbox"],columnHeaderCheckbox:["columnHeaderCheckbox"]},pt,r)),[r])})({classes:r.classes}),o=e.useCallback((e=>{const o=i({},Vo,{cellClassName:n.cellCheckbox,headerClassName:n.columnHeaderCheckbox,headerName:t.current.getLocaleText("checkboxSelectionHeaderName")}),l=r.checkboxSelection,a=null!=e.lookup[jo];return l&&!a?(e.lookup[jo]=o,e.orderedFields=[jo,...e.orderedFields]):!l&&a?(delete e.lookup[jo],e.orderedFields=e.orderedFields.filter((e=>e!==jo))):l&&a&&(e.lookup[jo]=i({},o,e.lookup[jo])),e}),[t,n,r.checkboxSelection]);Hc(t,"hydrateColumns",o)},kd=(e,t)=>{const r=t.sortModel??t.initialState?.sorting?.sortModel??[];return i({},e,{sorting:{sortModel:Nt(r,t.disableMultipleColumnsSorting),sortedRows:[]}})};function Pd(e){const{containerSize:t,scrollPosition:r,elementSize:n,elementOffset:o}=e,l=o+n;return n>t?o:l-t>r?l-t:o<r?o:void 0}const Ed={autoHeight:!1,autoPageSize:!1,autosizeOnMount:!1,checkboxSelection:!1,checkboxSelectionVisibleOnly:!1,clipboardCopyCellDelimiter:"\t",columnBufferPx:150,columnHeaderHeight:56,disableAutosize:!1,disableColumnFilter:!1,disableColumnMenu:!1,disableColumnReorder:!1,disableColumnResize:!1,disableColumnSelector:!1,disableColumnSorting:!1,disableDensitySelector:!1,disableEval:!1,disableMultipleColumnsFiltering:!1,disableMultipleColumnsSorting:!1,disableMultipleRowSelection:!1,disableRowSelectionOnClick:!1,disableVirtualization:!1,editMode:sr.Cell,filterDebounceMs:150,filterMode:"client",hideFooter:!1,hideFooterPagination:!1,hideFooterRowCount:!1,hideFooterSelectedRowCount:!1,ignoreDiacritics:!1,ignoreValueFormatterDuringExport:!1,indeterminateCheckboxAction:"deselect",keepColumnPositionIfDraggedOutside:!1,keepNonExistentRowsSelected:!1,loading:!1,logger:console,logLevel:"error",pageSizeOptions:[25,50,100],pagination:!1,paginationMode:"client",resetPageOnSortFilter:!1,resizeThrottleMs:60,rowBufferPx:150,rowHeight:52,rowPositionsDebounceMs:166,rows:[],rowSelection:!0,rowSpacingType:"margin",showCellVerticalBorder:!1,showColumnVerticalBorder:!1,sortingMode:"client",sortingOrder:["asc","desc",null],throttleRowsMs:0,unstable_rowSpanning:!1,virtualizeColumnsWithAutoRowHeight:!1},Fd={width:0,height:0},Hd={isReady:!1,root:Fd,viewportOuterSize:Fd,viewportInnerSize:Fd,contentSize:Fd,minimumSize:Fd,hasScrollX:!1,hasScrollY:!1,scrollbarSize:0,headerHeight:0,groupHeaderHeight:0,headerFilterHeight:0,rowWidth:0,rowHeight:0,columnsTotalWidth:0,leftPinnedWidth:0,rightPinnedWidth:0,headersTotalHeight:0,topContainerHeight:0,bottomContainerHeight:0},Dd=(e,t,r)=>{const n=Hd,o=St(r);return i({},e,{dimensions:i({},n,$d(t,r,o,Er(r)))})},Td=Ye(kr,Fr,((e,t)=>{const r=e.length;return 0===r?0:ui(t[r-1]+e[r-1].computedWidth,1)}));function Od(t,r){const n=ho(t,"useResizeContainer"),o=e.useRef(!1),l=e.useRef(Fd),a=We(t,Er),u=We(t,St),c=We(t,Td),d=e.useRef(!0),{rowHeight:p,headerHeight:f,groupHeaderHeight:g,headerFilterHeight:m,headersTotalHeight:h,leftPinnedWidth:b,rightPinnedWidth:w}=$d(r,t,u,a),C=e.useRef(void 0),v=e.useCallback((()=>Je(t.current.state)),[t]),x=e.useCallback((e=>{t.current.setState((t=>i({},t,{dimensions:e}))),t.current.rootElementRef.current&&Ld(t.current.rootElementRef.current,Je(t.current.state))}),[t]),y=e.useCallback((()=>{const e=t.current.mainElementRef.current;if(!e)return;const r=Y(e).getComputedStyle(e),n={width:parseFloat(r.width)||0,height:parseFloat(r.height)||0};C.current&&jd(C.current,n)||(t.current.publishEvent("resize",n),C.current=n)}),[t]),S=e.useCallback((()=>{const e=Je(t.current.state);if(!e.isReady)return 0;const n=Ul(t);if(r.getRowHeight){const e=Jl(t),r=e.lastRowIndex-e.firstRowIndex;return Math.min(r-1,n.rows.length)}const o=Math.floor(e.viewportInnerSize.height/p);return Math.min(o,n.rows.length)}),[t,r.getRowHeight,p]),R=e.useCallback((()=>{if(d.current)return;const e=function(e,t){if(void 0!==t)return t;if(null===e)return 0;const r=zd.get(e);if(void 0!==r)return r;const n=F(e).createElement("div");n.style.width="99px",n.style.height="99px",n.style.position="absolute",n.style.overflow="scroll",n.className="scrollDiv",e.appendChild(n);const o=n.offsetWidth-n.clientWidth;return e.removeChild(n),zd.set(e,o),o}(t.current.mainElementRef.current,r.scrollbarSize),n=Xl(t.current.state),o=h+n.pinnedTopRowsTotalHeight,i=n.pinnedBottomRowsTotalHeight,a={width:c-b-w,height:ui(n.currentPageTotalHeight,1)};let s,u,C=!1,v=!1;if(r.autoHeight)v=!1,C=Math.round(c)>Math.round(l.current.width),s={width:l.current.width,height:o+i+a.height},u={width:Math.max(0,s.width-(v?e:0)),height:Math.max(0,s.height-(C?e:0))};else{s={width:l.current.width,height:l.current.height},u={width:Math.max(0,s.width-b-w),height:Math.max(0,s.height-o-i)};const t=a,r=u,n=t.width>r.width,c=t.height>r.height;(n||c)&&(v=c,C=t.width+(v?e:0)>r.width,C&&(v=t.height+e>r.height)),v&&(u.width-=e),C&&(u.height-=e)}const y=Math.max(s.width,c+(v?e:0)),S={width:c,height:o+a.height+i},R={isReady:!0,root:l.current,viewportOuterSize:s,viewportInnerSize:u,contentSize:a,minimumSize:S,hasScrollX:C,hasScrollY:v,scrollbarSize:e,headerHeight:f,groupHeaderHeight:g,headerFilterHeight:m,rowWidth:y,rowHeight:p,columnsTotalWidth:c,leftPinnedWidth:b,rightPinnedWidth:w,headersTotalHeight:h,topContainerHeight:o,bottomContainerHeight:i},I=t.current.state.dimensions;rr(I,R)||(x(R),jd(R.viewportInnerSize,I.viewportInnerSize)||t.current.publishEvent("viewportInnerSizeChange",R.viewportInnerSize),t.current.updateRenderContext?.())}),[t,x,r.scrollbarSize,r.autoHeight,p,f,g,m,c,h,b,w]),M=I(R),k=e.useMemo((()=>r.resizeThrottleMs>0?function(e,t=166){let r,n;const o=()=>{r=void 0,e(...n)};function l(...e){n=e,void 0===r&&(r=setTimeout(o,t))}return l.clear=()=>{clearTimeout(r),r=void 0},l}((()=>{M(),t.current.publishEvent("debouncedResize",l.current)}),r.resizeThrottleMs):void 0),[t,r.resizeThrottleMs,M]);e.useEffect((()=>k?.clear),[k]);const P={resize:y,getRootDimensions:v},E={updateDimensions:R,getViewportPageSize:S};s(R,[R]),mo(t,P,"public"),mo(t,E,"private");const H=e.useCallback((e=>{Ld(e,Je(t.current.state))}),[t]),D=e.useCallback((e=>{if(l.current=e,0!==e.height||o.current||r.autoHeight||ci||(n.error(["The parent DOM element of the Data Grid has an empty height.","Please make sure that this element has an intrinsic height.","The grid displays with a height of 0px.","","More details: https://mui.com/r/x-data-grid-no-dimensions."].join("\n")),o.current=!0),0!==e.width||o.current||ci||(n.error(["The parent DOM element of the Data Grid has an empty width.","Please make sure that this element has an intrinsic width.","The grid displays with a width of 0px.","","More details: https://mui.com/r/x-data-grid-no-dimensions."].join("\n")),o.current=!0),d.current||!k)return d.current=!1,void R();k()}),[R,r.autoHeight,k,n]);vt(t,"rootMount",H),vt(t,"resize",D),vt(t,"debouncedResize",r.onResize)}function Ld(e,t){const r=(t,r)=>e.style.setProperty(t,r);r("--DataGrid-hasScrollX",`${Number(t.hasScrollX)}`),r("--DataGrid-hasScrollY",`${Number(t.hasScrollY)}`),r("--DataGrid-scrollbarSize",`${t.scrollbarSize}px`),r("--DataGrid-rowWidth",`${t.rowWidth}px`),r("--DataGrid-columnsTotalWidth",`${t.columnsTotalWidth}px`),r("--DataGrid-leftPinnedWidth",`${t.leftPinnedWidth}px`),r("--DataGrid-rightPinnedWidth",`${t.rightPinnedWidth}px`),r("--DataGrid-headerHeight",`${t.headerHeight}px`),r("--DataGrid-headersTotalHeight",`${t.headersTotalHeight}px`),r("--DataGrid-topContainerHeight",`${t.topContainerHeight}px`),r("--DataGrid-bottomContainerHeight",`${t.bottomContainerHeight}px`),r("--height",`${t.rowHeight}px`)}function $d(e,t,r,n){const o=hn(e.rowHeight,Ed.rowHeight);return{rowHeight:Math.floor(o*r),headerHeight:Math.floor(e.columnHeaderHeight*r),groupHeaderHeight:Math.floor((e.columnGroupHeaderHeight??e.columnHeaderHeight)*r),headerFilterHeight:Math.floor((e.headerFilterHeight??e.columnHeaderHeight)*r),columnsTotalWidth:Td(t),headersTotalHeight:Fl(t,e),leftPinnedWidth:n.left.reduce(((e,t)=>e+t.computedWidth),0),rightPinnedWidth:n.right.reduce(((e,t)=>e+t.computedWidth),0)}}const zd=new WeakMap;function jd(e,t){return e.width===t.width&&e.height===t.height}const Vd=void 0!==globalThis.ResizeObserver?globalThis.ResizeObserver:class{observe(){}unobserve(){}disconnect(){}},Ad=(e,t,r)=>{r.current.caches.rowsMeta={heights:new Map};const n=tt(r.current.state),o=Pt(r),l=So(r.current.state),a=Math.min(l.enabled?l.paginationModel.pageSize:o,o);return i({},e,{rowsMeta:{currentPageTotalHeight:a*n,positions:Array.from({length:a},((e,t)=>t*n)),pinnedTopRowsTotalHeight:0,pinnedBottomRowsTotalHeight:0}})};function Nd(e){const{apiRef:t,lookup:r,columnIndex:n,rowId:o,minFirstColumnIndex:l,maxLastColumnIndex:i,columns:a}=e,s=a.length,u=a[n],c=t.current.getRow(o),d=t.current.getRowValue(c,u),p="function"==typeof u.colSpan?u.colSpan(d,c,u,t):u.colSpan;if(!p||1===p)return Gd(r,o,n,{spannedByColSpan:!1,cellProps:{colSpan:1,width:u.computedWidth}}),{colSpan:1};let f=u.computedWidth;for(let g=1;g<p;g+=1){const e=n+g;if(e>=l&&e<i){f+=a[e].computedWidth,Gd(r,o,n+g,{spannedByColSpan:!0,rightVisibleCellIndex:Math.min(n+p,s-1),leftVisibleCellIndex:n})}Gd(r,o,n,{spannedByColSpan:!1,cellProps:{colSpan:p,width:f}})}return{colSpan:p}}function Gd(e,t,r,n){e[t]||(e[t]={}),e[t][r]=n}const Bd=(e,t,r)=>{if(hr(e)){if(void 0!==r[e.field])throw new Error(["MUI X: columnGroupingModel contains duplicated field",`column field ${e.field} occurs two times in the grouping model:`,`- ${r[e.field].join(" > ")}`,`- ${t.join(" > ")}`].join("\n"));return void(r[e.field]=t)}const{groupId:n,children:o}=e;o.forEach((e=>{Bd(e,[...t,n],r)}))},Wd=e=>{if(!e)return{};const t={};return e.forEach((e=>{Bd(e,[],t)})),t},_d=(e,t,r)=>{const n=e=>t[e]??[],o=[],l=Math.max(...e.map((e=>n(e).length))),i=(e,t,r)=>rr(n(e).slice(0,r+1),n(t).slice(0,r+1));for(let a=0;a<l;a+=1){const t=e.reduce(((e,t)=>{const o=n(t)[a]??null;if(0===e.length)return[{columnFields:[t],groupId:o}];const l=e[e.length-1],s=l.columnFields[l.columnFields.length-1];return l.groupId!==o||!i(s,t,a)||(u=s,c=t,r?.left&&r.left.includes(u)&&!r.left.includes(c)||r?.right&&!r.right.includes(u)&&r.right.includes(c))?[...e,{columnFields:[t],groupId:o}]:[...e.slice(0,e.length-1),{columnFields:[...l.columnFields,t],groupId:o}];var u,c}),[]);o.push(t)}return o},Ud=["groupId","children"],Kd=e=>{let t={};return e.forEach((e=>{if(hr(e))return;const{groupId:r,children:n}=e,o=a(e,Ud);if(!r)throw new Error("MUI X: An element of the columnGroupingModel does not have either `field` or `groupId`.");const l=i({},o,{groupId:r}),s=Kd(n);if(void 0!==s[r]||void 0!==t[r])throw new Error(`MUI X: The groupId ${r} is used multiple times in the columnGroupingModel.`);t=i({},t,s,{[r]:l})})),i({},t)},qd=(e,t,r)=>{if(!t.columnGroupingModel)return e;const n=Sr(r),o=Pr(r),l=Kd(t.columnGroupingModel??[]),a=Wd(t.columnGroupingModel??[]),s=_d(n,a,r.current.state.pinnedColumns??{}),u=0===o.length?0:Math.max(...o.map((e=>a[e]?.length??0)));return i({},e,{columnGrouping:{lookup:l,unwrappedGroupingModel:a,headerStructure:s,maxDepth:u}})};function Xd(e,t){if(void 0!==t&&e.changedTouches){for(let r=0;r<e.changedTouches.length;r+=1){const n=e.changedTouches[r];if(n.identifier===t)return{x:n.clientX,y:n.clientY}}return!1}return{x:e.clientX,y:e.clientY}}function Yd(e,t,r,n){let o=e;return o+="Right"===n?t-r.left:r.right-t,o}function Qd(e){e.preventDefault(),e.stopImmediatePropagation()}function Zd(t){const r=e.useRef(void 0),n=()=>Ql(t),o=We(t,n);e.useEffect((()=>{r.current&&!1===o&&(r.current.resolve(),r.current=void 0)}));return()=>{if(!r.current){if(!1===n())return Promise.resolve();r.current=function(){let e,t;const r=new Promise(((r,n)=>{e=r,t=n}));return r.resolve=e,r.reject=t,r}()}return r.current}}function Jd(e,t,r){const n={},o=e.current.rootElementRef.current;return o.classList.add(ft.autosizing),r.forEach((r=>{const o=function(e,t){const r=e.virtualScrollerRef.current;return Array.from(r.querySelectorAll(`:scope > div > div > div > [data-field="${qa(t)}"][role="gridcell"]`))}(e.current,r.field),l=o.map((e=>e.getBoundingClientRect().width??0)),i=t.includeOutliers?l:function(e,t){if(e.length<4)return e;const r=e.slice();r.sort(((e,t)=>e-t));const n=r[Math.floor(.25*r.length)],o=r[Math.floor(.75*r.length)-1],l=o-n,i=l<5?5:l*t;return r.filter((e=>e>n-i&&e<o+i))}(l,t.outliersFactor);if(t.includeHeaders){const t=(a=e.current,s=r.field,a.columnHeadersContainerRef.current.querySelector(`:scope > div > [data-field="${qa(s)}"][role="columnheader"]`));if(t){const e=t.querySelector(`.${ft.columnHeaderTitle}`),r=t.querySelector(`.${ft.columnHeaderTitleContainerContent}`),n=t.querySelector(`.${ft.iconButtonContainer}`),o=t.querySelector(`.${ft.menuIcon}`),l=e??r,a=window.getComputedStyle(t,null),s=parseInt(a.paddingLeft,10)+parseInt(a.paddingRight,10),u=l.scrollWidth+1+s+(n?.clientWidth??0)+(o?.clientWidth??0);i.push(u)}}var a,s;const u=r.minWidth!==-1/0&&void 0!==r.minWidth,c=r.maxWidth!==1/0&&void 0!==r.maxWidth,d=u?r.minWidth:0,p=c?r.maxWidth:1/0,f=0===i.length?0:Math.max(...i);n[r.field]=er(f,d,p)})),o.classList.remove(ft.autosizing),n}const ep=e=>i({},e,{columnResize:{resizingColumnField:""}});function tp(){return{colDef:void 0,initialColWidth:0,initialTotalWidth:0,previousMouseClickEvent:void 0,columnHeaderElement:void 0,headerFilterElement:void 0,groupHeaderElements:[],cellElements:[],leftPinnedCellsAfter:[],rightPinnedCellsBefore:[],fillerLeft:void 0,fillerRight:void 0,leftPinnedHeadersAfter:[],rightPinnedHeadersBefore:[]}}const rp=(t,n)=>{const o=v(),l=ho(t,"useGridColumnResize"),a=r(tp).current,s=e.useRef(null),u=e.useRef(null),c=p(),d=e.useRef(void 0),f=e=>{l.debug(`Updating width to ${e} for col ${a.colDef.field}`);const r=a.columnHeaderElement.offsetWidth,n=e-r,o=e-a.initialColWidth;if(o>0){const e=a.initialTotalWidth+o;t.current.rootElementRef?.current?.style.setProperty("--DataGrid-rowWidth",`${e}px`)}a.colDef.computedWidth=e,a.colDef.width=e,a.colDef.flex=0,a.columnHeaderElement.style.width=`${e}px`;const i=a.headerFilterElement;i&&(i.style.width=`${e}px`),a.groupHeaderElements.forEach((t=>{const r=t;let o;o="1"===r.getAttribute("aria-colspan")?`${e}px`:`${r.offsetWidth+n}px`,r.style.width=o})),a.cellElements.forEach((t=>{const r=t;let o;o="1"===r.getAttribute("aria-colspan")?`${e}px`:`${r.offsetWidth+n}px`,r.style.setProperty("--width",o)}));const s=t.current.unstable_applyPipeProcessors("isColumnPinned",!1,a.colDef.field);s===vr.LEFT&&(np(a.fillerLeft,"width",n),a.leftPinnedCellsAfter.forEach((e=>{np(e,"left",n)})),a.leftPinnedHeadersAfter.forEach((e=>{np(e,"left",n)}))),s===vr.RIGHT&&(np(a.fillerRight,"width",n),a.rightPinnedCellsBefore.forEach((e=>{np(e,"right",n)})),a.rightPinnedHeadersBefore.forEach((e=>{np(e,"right",n)})))},g=e=>{if(y(),a.previousMouseClickEvent){const r=a.previousMouseClickEvent,n=r.timeStamp,o=r.clientX,l=r.clientY;if(e.timeStamp-n<300&&e.clientX===o&&e.clientY===l)return a.previousMouseClickEvent=void 0,void t.current.publishEvent("columnResizeStop",null,e)}if(a.colDef){t.current.setColumnWidth(a.colDef.field,a.colDef.width),l.debug(`Updating col ${a.colDef.field} with new width: ${a.colDef.width}`);const e=yr(t.current.state);a.groupHeaderElements.forEach((t=>{const r=t,n=`${t.getAttribute("data-fields").slice(2,-2).split("-|-").reduce(((t,r)=>!1!==e.columnVisibilityModel[r]?t+e.lookup[r].computedWidth:t),0)}px`;r.style.width=n}))}c.start(0,(()=>{t.current.publishEvent("columnResizeStop",null,e)}))},m=(e,r,n)=>{const l=t.current.rootElementRef.current;var i,c;a.initialColWidth=e.computedWidth,a.initialTotalWidth=t.current.getRootDimensions().rowWidth,a.colDef=e,a.columnHeaderElement=(i=t.current.columnHeadersContainerRef.current,c=e.field,i.querySelector(`[data-field="${qa(c)}"]`));const d=l.querySelector(`.${ft.headerFilterRow} [data-field="${qa(e.field)}"]`);d&&(a.headerFilterElement=d),a.groupHeaderElements=function(e,t){return Array.from(e.querySelectorAll(`[data-fields*="|-${qa(t)}-|"]`)??[])}(t.current.columnHeadersContainerRef?.current,e.field),a.cellElements=function(e,t){if(!Ka(e,ft.root))throw new Error("MUI X: The root element is not found.");const r=e.getAttribute("aria-colindex");if(!r)return[];const n=Number(r)-1,o=[];return t.virtualScrollerRef?.current?(es(t).forEach((e=>{const r=e.getAttribute("data-id");if(!r)return;let l=n;const i=t.unstable_getCellColSpanInfo(r,n);i&&i.spannedByColSpan&&(l=i.leftVisibleCellIndex);const a=e.querySelector(`[data-colindex="${l}"]`);a&&o.push(a)})),o):[]}(a.columnHeaderElement,t.current),a.fillerLeft=Qa(t.current,o?"filler--pinnedRight":"filler--pinnedLeft"),a.fillerRight=Qa(t.current,o?"filler--pinnedLeft":"filler--pinnedRight");const p=t.current.unstable_applyPipeProcessors("isColumnPinned",!1,a.colDef.field);a.leftPinnedCellsAfter=p!==vr.LEFT?[]:function(e,t,r){const n=ts(t);return Za({api:e,colIndex:n,position:r?"right":"left",filterFn:e=>r?e<n:e>n})}(t.current,a.columnHeaderElement,o),a.rightPinnedCellsBefore=p!==vr.RIGHT?[]:function(e,t,r){const n=ts(t);return Za({api:e,colIndex:n,position:r?"left":"right",filterFn:e=>r?e>n:e<n})}(t.current,a.columnHeaderElement,o),a.leftPinnedHeadersAfter=p!==vr.LEFT?[]:function(e,t,r){const n=ts(t);return Ja({api:e,position:r?"right":"left",colIndex:n,filterFn:e=>r?e<n:e>n})}(t.current,a.columnHeaderElement,o),a.rightPinnedHeadersBefore=p!==vr.RIGHT?[]:function(e,t,r){const n=ts(t);return Ja({api:e,position:r?"left":"right",colIndex:n,filterFn:(e,t)=>!t.classList.contains(ft["columnHeader--last"])&&(r?e>n:e<n)})}(t.current,a.columnHeaderElement,o),u.current=function(e,t){const r=e.classList.contains(ft["columnSeparator--sideRight"])?"Right":"Left";return t?function(e){return"Right"===e?"Left":"Right"}(r):r}(r,o),s.current=function(e,t,r){return"Left"===r?e-t.left:t.right-e}(n,a.columnHeaderElement.getBoundingClientRect(),u.current)},h=I(g),b=I((e=>{if(0===e.buttons)return void h(e);let r=Yd(s.current,e.clientX,a.columnHeaderElement.getBoundingClientRect(),u.current);r=er(r,a.colDef.minWidth,a.colDef.maxWidth),f(r);const n={element:a.columnHeaderElement,colDef:a.colDef,width:r};t.current.publishEvent("columnResize",n,e)})),w=I((e=>{Xd(e,d.current)&&g(e)})),C=I((e=>{const r=Xd(e,d.current);if(!r)return;if("mousemove"===e.type&&0===e.buttons)return void w(e);let n=Yd(s.current,r.x,a.columnHeaderElement.getBoundingClientRect(),u.current);n=er(n,a.colDef.minWidth,a.colDef.maxWidth),f(n);const o={element:a.columnHeaderElement,colDef:a.colDef,width:n};t.current.publishEvent("columnResize",o,e)})),x=I((e=>{const r=Ka(e.target,ft["columnSeparator--resizable"]);if(!r)return;const n=e.changedTouches[0];null!=n&&(d.current=n.identifier);const o=Ka(e.target,ft.columnHeader),i=o.getAttribute("data-field");const a=t.current.getColumn(i);l.debug(`Start Resize on col ${a.field}`),t.current.publishEvent("columnResizeStart",{field:i},e),m(a,r,n.clientX);const s=F(e.currentTarget);s.addEventListener("touchmove",C),s.addEventListener("touchend",w)})),y=e.useCallback((()=>{const e=F(t.current.rootElementRef.current);e.body.style.removeProperty("cursor"),e.removeEventListener("mousemove",b),e.removeEventListener("mouseup",h),e.removeEventListener("touchmove",C),e.removeEventListener("touchend",w),setTimeout((()=>{e.removeEventListener("click",Qd,!0)}),100),a.columnHeaderElement&&(a.columnHeaderElement.style.pointerEvents="unset")}),[t,a,b,h,C,w]),S=e.useCallback((({field:e})=>{t.current.setState((t=>i({},t,{columnResize:i({},t.columnResize,{resizingColumnField:e})}))),t.current.forceUpdate()}),[t]),R=e.useCallback((()=>{t.current.setState((e=>i({},e,{columnResize:i({},e.columnResize,{resizingColumnField:""})}))),t.current.forceUpdate()}),[t]),M=I((({colDef:e},r)=>{if(0!==r.button)return;if(!r.currentTarget.classList.contains(ft["columnSeparator--resizable"]))return;r.preventDefault(),l.debug(`Start Resize on col ${e.field}`),t.current.publishEvent("columnResizeStart",{field:e.field},r),m(e,r.currentTarget,r.clientX);const n=F(t.current.rootElementRef.current);n.body.style.cursor="col-resize",a.previousMouseClickEvent=r.nativeEvent,n.addEventListener("mousemove",b),n.addEventListener("mouseup",h),n.addEventListener("click",Qd,!0)})),k=I(((e,r)=>{if(n.disableAutosize)return;if(0!==r.button)return;const o=t.current.state.columns.lookup[e.field];!1!==o.resizable&&t.current.autosizeColumns(i({},n.autosizeOptions,{disableColumnVirtualization:!1,columns:[o.field]}))})),P=Zd(t),H=e.useRef(!1),D=e.useCallback((async e=>{const r=t.current.rootElementRef?.current;if(!r)return;if(H.current)return;H.current=!0;const o=yr(t.current.state),l=i({},Xi,e,{columns:e?.columns??o.orderedFields});l.columns=l.columns.filter((e=>!1!==o.columnVisibilityModel[e]));const a=l.columns.map((e=>t.current.state.columns.lookup[e]));try{!n.disableVirtualization&&l.disableColumnVirtualization&&(t.current.unstable_setColumnVirtualization(!1),await P());const e=Jd(t,l,a),r=a.map((t=>i({},t,{width:e[t.field],computedWidth:e[t.field],flex:0})));if(l.expand){const n=o.orderedFields.map((e=>o.lookup[e])).filter((e=>!1!==o.columnVisibilityModel[e.field])).reduce(((t,r)=>t+(e[r.field]??r.computedWidth??r.width)),0),l=t.current.getRootDimensions(),i=l.viewportInnerSize.width-n;if(i>0){const e=i/(r.length||1);r.forEach((t=>{t.width+=e,t.computedWidth+=e}))}}t.current.updateColumns(r),r.forEach(((e,r)=>{if(e.width!==a[r].width){const r=e.width;t.current.publishEvent("columnWidthChange",{element:t.current.getColumnHeaderElement(e.field),colDef:e,width:r})}}))}finally{n.disableVirtualization||t.current.unstable_setColumnVirtualization(!0),H.current=!1}}),[t,P,n.disableVirtualization]);e.useEffect((()=>y),[y]),E((()=>{n.autosizeOnMount&&Promise.resolve().then((()=>{t.current.autosizeColumns(n.autosizeOptions)}))})),bo(t,(()=>t.current.columnHeadersContainerRef?.current),"touchstart",x,{passive:!0}),mo(t,{autosizeColumns:D},"public"),wt(t,"columnResizeStop",R),wt(t,"columnResizeStart",S),wt(t,"columnSeparatorMouseDown",M),wt(t,"columnSeparatorDoubleClick",k),vt(t,"columnResize",n.onColumnResize),vt(t,"columnWidthChange",n.onColumnWidthChange)};function np(e,t,r){e&&(e.style[t]=`${parseInt(e.style[t],10)+r}px`)}function op(e){return 0!==e.firstRowIndex||0!==e.lastRowIndex}const lp=(e,t,r)=>{if(!e)return null;let n=e[t.field];const o=t.rowSpanValueGetter??t.valueGetter;return o&&(n=o(n,e,t,r)),n},ip={spannedCells:{},hiddenCells:{},hiddenCellOriginMap:{}},ap={firstRowIndex:0,lastRowIndex:0},sp=new Set([jo,"__reorder__",wn]),up=(e,t,r,n,o,l,a)=>{const s=l?{}:i({},e.current.state.rowSpanning.spannedCells),u=l?{}:i({},e.current.state.rowSpanning.hiddenCells),c=l?{}:i({},e.current.state.rowSpanning.hiddenCellOriginMap);return l&&(a=ap),t.forEach((t=>{if(!sp.has(t.field)){for(let l=o.firstRowIndex;l<o.lastRowIndex;l+=1){const i=r[l];if(u[i.id]?.[t.field])continue;const a=lp(i.model,t,e);if(null==a)continue;let d=i.id,p=l,f=0;const g=[];if(l===o.firstRowIndex){let o=l-1,i=r[o];for(;o>=n.firstRowIndex&&i&&lp(i.model,t,e)===a;){const e=r[o+1];u[e.id]?u[e.id][t.field]=!0:u[e.id]={[t.field]:!0},g.push(l),f+=1,d=i.id,p=o,o-=1,i=r[o]}}g.forEach((e=>{c[e]?c[e][t.field]=p:c[e]={[t.field]:p}}));let m=l+1;for(;m<=n.lastRowIndex&&r[m]&&lp(r[m].model,t,e)===a;){const e=r[m];u[e.id]?u[e.id][t.field]=!0:u[e.id]={[t.field]:!0},c[m]?c[m][t.field]=p:c[m]={[t.field]:p},m+=1,f+=1}f>0&&(s[d]?s[d][t.field]=f+1:s[d]={[t.field]:f+1})}a={firstRowIndex:Math.min(a.firstRowIndex,o.firstRowIndex),lastRowIndex:Math.max(a.lastRowIndex,o.lastRowIndex)}}})),{spannedCells:s,hiddenCells:u,hiddenCellOriginMap:c,processedRange:a}},cp=(e,t)=>{const r=jt(t).length;if(e.pagination){const e=Eo(t);let n=20;return e>0&&(n=e-1),{firstRowIndex:0,lastRowIndex:Math.min(n,r)}}return{firstRowIndex:0,lastRowIndex:Math.min(20,r)}},dp=(e,t,r)=>{if(!t.unstable_rowSpanning)return i({},e,{rowSpanning:ip});const n=e.rows.dataRowIds||[],o=e.columns.orderedFields||[],l=e.rows.dataRowIdToModelLookup,a=e.columns.lookup,s=Boolean(e.filter.filterModel.items.length)||Boolean(e.filter.filterModel.quickFilterValues?.length);if(!n.length||!o.length||!l||!a||s)return i({},e,{rowSpanning:ip});const u=cp(t,r),c=n.map((e=>({id:e,model:l[e]}))),d=o.map((e=>a[e])),{spannedCells:p,hiddenCells:f,hiddenCellOriginMap:g}=up(r,d,c,u,u,!0,ap);return i({},e,{rowSpanning:{spannedCells:p,hiddenCells:f,hiddenCellOriginMap:g}})},pp=(e,t,r)=>i({},e,{listViewColumn:t.unstable_listColumn?i({},t.unstable_listColumn,{computedWidth:fp(r)}):void 0});function fp(e){return Je(e.current.state).viewportInnerSize.width}const gp=(t,n)=>{const o=zc(t,n);return Md(o,n),(e=>{Tc(e,Oc,"rowTreeCreation",vd)})(o),jc($c,o,n),jc(Rd,o,n),jc(Uc,o,n),jc(Cd,o,n),jc(ud,o,n),jc(bd,o,n),jc(od,o,n),jc(kd,o,n),jc(dd,o,n),jc(ed,o,n),jc(dp,o,n),jc(qc,o,n),jc(ep,o,n),jc(_c,o,n),jc(qd,o,n),jc(ri,o,n),jc(Dd,o,n),jc(Ad,o,n),jc(pp,o,n),sd(o,n),Id(o,n),function(t,r){const n=ho(t,"useGridColumns"),o=e.useRef(r.columns);t.current.registerControlState({stateId:"visibleColumns",propModel:r.columnVisibilityModel,propOnChange:r.onColumnVisibilityModelChange,stateSelector:Mr,changeEvent:"columnVisibilityModelChange"});const a=e.useCallback((e=>{n.debug("Updating columns state."),t.current.setState(Kc(e)),t.current.publishEvent("columnsChange",e.orderedFields)}),[n,t]),s=e.useCallback((e=>Rr(t)[e]),[t]),u=e.useCallback((()=>Ir(t)),[t]),c=e.useCallback((()=>kr(t)),[t]),d=e.useCallback(((e,r=!0)=>(r?kr(t):Ir(t)).findIndex((t=>t.field===e))),[t]),p=e.useCallback((e=>{const r=d(e);return Fr(t)[r]}),[t,d]),f=e.useCallback((e=>{Mr(t)!==e&&(t.current.setState((r=>i({},r,{columns:El({apiRef:t,columnsToUpsert:[],initialState:void 0,columnVisibilityModel:e,keepOnlyColumnsToUpsert:!1})}))),t.current.updateRenderContext?.(),t.current.forceUpdate())}),[t]),g=e.useCallback((e=>{const r=El({apiRef:t,columnsToUpsert:e,initialState:void 0,keepOnlyColumnsToUpsert:!1});a(r)}),[t,a]),m=e.useCallback(((e,r)=>{const n=Mr(t);if(r!==(n[e]??!0)){const o=i({},n,{[e]:r});t.current.setColumnVisibilityModel(o)}}),[t]),h=e.useCallback((e=>Sr(t).findIndex((t=>t===e))),[t]),b=e.useCallback(((e,r)=>{const o=Sr(t),l=h(e);if(l===r)return;n.debug(`Moving column ${e} to index ${r}`);const s=[...o],u=s.splice(l,1)[0];s.splice(r,0,u),a(i({},yr(t.current.state),{orderedFields:s}));const c={column:t.current.getColumn(e),targetIndex:t.current.getColumnIndexRelativeToVisibleColumns(e),oldIndex:l};t.current.publishEvent("columnIndexChange",c)}),[t,n,a,h]),w=e.useCallback(((e,r)=>{n.debug(`Updating column ${e} width to ${r}`);const o=yr(t.current.state),l=o.lookup[e],s=i({},l,{width:r,hasBeenResized:!0});a(kl(i({},o,{lookup:i({},o.lookup,{[e]:s})}),t.current.getRootDimensions())),t.current.publishEvent("columnWidthChange",{element:t.current.getColumnHeaderElement(e),colDef:s,width:r})}),[t,n,a]),C={setColumnIndex:b};mo(t,{getColumn:s,getAllColumns:u,getColumnIndex:d,getColumnPosition:p,getVisibleColumns:c,getColumnIndexRelativeToVisibleColumns:h,updateColumns:g,setColumnVisibilityModel:f,setColumnVisibility:m,setColumnWidth:w},"public"),mo(t,C,r.signature===ht.DataGrid?"private":"public");const v=e.useCallback(((e,n)=>{const o={},l=Mr(t);(!n.exportOnlyDirtyModels||null!=r.columnVisibilityModel||Object.keys(r.initialState?.columns?.columnVisibilityModel??{}).length>0||Object.keys(l).length>0)&&(o.columnVisibilityModel=l),o.orderedFields=Sr(t);const a=Ir(t),s={};return a.forEach((e=>{if(e.hasBeenResized){const t={};Il.forEach((r=>{let n=e[r];n===1/0&&(n=-1),t[r]=n})),s[e.field]=t}})),Object.keys(s).length>0&&(o.dimensions=s),i({},e,{columns:o})}),[t,r.columnVisibilityModel,r.initialState?.columns]),x=e.useCallback(((e,r)=>{const n=r.stateToRestore.columns?.columnVisibilityModel,o=r.stateToRestore.columns;if(null==n&&null==o)return e;const l=El({apiRef:t,columnsToUpsert:[],initialState:o,columnVisibilityModel:n,keepOnlyColumnsToUpsert:!1});return t.current.setState(Kc(l)),null!=o&&t.current.publishEvent("columnsChange",l.orderedFields),e}),[t]),y=e.useCallback(((e,t)=>{if(t===ta.columns){const e=r.slots.columnsPanel;return l.jsx(e,i({},r.slotProps?.columnsPanel))}return e}),[r.slots.columnsPanel,r.slotProps?.columnsPanel]),S=e.useCallback((e=>r.disableColumnSelector?e:[...e,"columnMenuColumnsItem"]),[r.disableColumnSelector]);Hc(t,"columnMenu",S),Hc(t,"exportState",v),Hc(t,"restoreState",x),Hc(t,"preferencePanel",y);const R=e.useRef(null);wt(t,"viewportInnerSizeChange",(e=>{if(R.current!==e.width){if(R.current=e.width,!kr(t).some((e=>e.flex&&e.flex>0)))return;a(kl(yr(t.current.state),t.current.getRootDimensions()))}}));const I=e.useCallback((()=>{n.info("Columns pipe processing have changed, regenerating the columns");const e=El({apiRef:t,columnsToUpsert:[],initialState:void 0,keepOnlyColumnsToUpsert:!1});a(e)}),[t,n,a]);Dc(t,"hydrateColumns",I);const M=e.useRef(!0);e.useEffect((()=>{if(M.current)return void(M.current=!1);if(n.info(`GridColumns have changed, new length ${r.columns.length}`),o.current===r.columns)return;const e=El({apiRef:t,initialState:void 0,columnsToUpsert:r.columns,keepOnlyColumnsToUpsert:!0});o.current=r.columns,a(e)}),[n,t,a,r.columns]),e.useEffect((()=>{void 0!==r.columnVisibilityModel&&t.current.setColumnVisibilityModel(r.columnVisibilityModel)}),[t,n,r.columnVisibilityModel])}(o,n),((t,n)=>{const o=ho(t,"useGridRows"),l=e.useRef(Date.now()),a=e.useRef(n.rowCount),s=p(),u=e.useCallback((e=>{const r=Ht(t)[e];if(r)return r;const n=t.current.getRowNode(e);return n&&dn(n)?{[ln]:e}:null}),[t]),c=e.useCallback((e=>zo(t.current.state,e)),[t]),d=e.useCallback((({cache:e,throttle:r})=>{const o=()=>{l.current=Date.now(),t.current.setState((e=>i({},e,{rows:cn({apiRef:t,rowCountProp:n.rowCount,loadingProp:n.loading,previousTree:Tt(t),previousTreeDepths:$t(t),previousGroupsToFetch:Ot(t)})}))),t.current.publishEvent("rowsSet"),t.current.forceUpdate()};if(s.clear(),t.current.caches.rows=e,!r)return void o();const a=n.throttleRowsMs-(Date.now()-l.current);a>0?s.start(a,o):o()}),[n.throttleRowsMs,n.rowCount,n.loading,t,s]),f=e.useCallback((e=>{o.debug(`Updating all rows, new length ${e.length}`);const r=sn({rows:e,getRowId:n.getRowId,loading:n.loading,rowCount:n.rowCount}),l=t.current.caches.rows;r.rowsBeforePartialUpdates=l.rowsBeforePartialUpdates,d({cache:r,throttle:!0})}),[o,n.getRowId,n.loading,n.rowCount,d,t]),g=e.useCallback((e=>{if(n.signature===ht.DataGrid&&e.length>1)throw new Error(["MUI X: You cannot update several rows at once in `apiRef.current.updateRows` on the DataGrid.","You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature."].join("\n"));const r=mn(t,e,n.getRowId),o=fn({updates:r,getRowId:n.getRowId,previousCache:t.current.caches.rows});d({cache:o,throttle:!0})}),[n.signature,n.getRowId,d,t]),m=e.useCallback(((e,r)=>{const o=mn(t,e,n.getRowId),l=fn({updates:o,getRowId:n.getRowId,previousCache:t.current.caches.rows,groupKeys:r??[]});d({cache:l,throttle:!1})}),[n.getRowId,d,t]),h=e.useCallback((e=>{e!==n.loading&&(o.debug(`Setting loading to ${e}`),t.current.setState((t=>i({},t,{rows:i({},t.rows,{loading:e})}))),t.current.caches.rows.loadingPropBeforePartialUpdates=e)}),[n.loading,t,o]),b=e.useCallback((()=>{const e=jt(t),r=Ht(t);return new Map(e.map((e=>[e,r[e]??{}])))}),[t]),w=e.useCallback((()=>Pt(t)),[t]),C=e.useCallback((()=>jt(t)),[t]),v=e.useCallback((e=>{const r=t.current.getRow(e),{rowToIndexMap:n}=Ul(t);return n.get(r)}),[t]),x=e.useCallback(((e,r)=>{const n=t.current.getRowNode(e);if(!n)throw new Error(`MUI X: No row with id #${e} found.`);if("group"!==n.type)throw new Error("MUI X: Only group nodes can be expanded or collapsed.");const o=i({},n,{childrenExpanded:r});t.current.setState((t=>i({},t,{rows:i({},t.rows,{tree:i({},t.rows.tree,{[e]:o})})}))),t.current.forceUpdate(),t.current.publishEvent("rowExpansionChange",o)}),[t]),y=e.useCallback((e=>Tt(t)[e]??null),[t]),S=e.useCallback((({skipAutoGeneratedRows:e=!0,groupId:r,applySorting:n,applyFiltering:o})=>{const l=Tt(t);let i;if(n){const n=l[r];if(!n)return[];const o=Hn(t);i=[];for(let t=o.findIndex((e=>e===r))+1;t<o.length&&l[o[t]].depth>n.depth;t+=1){const r=o[t];e&&dn(l[r])||i.push(r)}}else i=pn(l,r,e);if(o){const e=jn(t);i=i.filter((t=>!1!==e[t]))}return i}),[t]),R=e.useCallback(((e,r)=>{const n=t.current.getRowNode(e);if(!n)throw new Error(`MUI X: No row with id #${e} found.`);if(n.parent!==on)throw new Error("MUI X: The row reordering do not support reordering of grouped rows yet.");if("leaf"!==n.type)throw new Error("MUI X: The row reordering do not support reordering of footer or grouping rows.");t.current.setState((n=>{const l=Tt(n,t.current.instanceId)[on],a=l.children,s=a.findIndex((t=>t===e));if(-1===s||s===r)return n;o.debug(`Moving row ${e} to index ${r}`);const u=[...a];return u.splice(r,0,u.splice(s,1)[0]),i({},n,{rows:i({},n.rows,{tree:i({},n.rows.tree,{[on]:i({},l,{children:u})})})})})),t.current.publishEvent("rowsSet")}),[t,o]),I={getRow:u,setLoading:h,getRowId:c,getRowModels:b,getRowsCount:w,getAllRowIds:C,setRows:f,updateRows:g,getRowNode:y,getRowIndexRelativeToVisibleRows:v,unstable_replaceRows:e.useCallback(((e,r)=>{if(n.signature===ht.DataGrid&&r.length>1)throw new Error(["MUI X: You cannot replace rows using `apiRef.current.unstable_replaceRows` on the DataGrid.","You need to upgrade to DataGridPro or DataGridPremium component to unlock this feature."].join("\n"));if(0===r.length)return;if(zt(t)>1)throw new Error("`apiRef.current.unstable_replaceRows` is not compatible with tree data and row grouping");const o=i({},Tt(t)),l=i({},Ht(t)),a=i({},Dt(t)),s=o[on],u=[...s.children],c=new Set;for(let t=0;t<r.length;t+=1){const i=r[t],s=an(i,n.getRowId,"A row was provided without id when calling replaceRows()."),[d]=u.splice(e+t,1,s);c.has(d)||(delete l[d],delete a[d],delete o[d]);const p={id:s,depth:0,parent:on,type:"leaf",groupingKey:null};l[s]=i,a[s]=s,o[s]=p,c.add(s)}o[on]=i({},s,{children:u});const d=u.filter((e=>"leaf"===o[e]?.type));t.current.caches.rows.dataRowIdToModelLookup=l,t.current.caches.rows.dataRowIdToIdLookup=a,t.current.setState((e=>i({},e,{rows:i({},e.rows,{dataRowIdToModelLookup:l,dataRowIdToIdLookup:a,dataRowIds:d,tree:o})}))),t.current.publishEvent("rowsSet")}),[t,n.signature,n.getRowId])},M={setRowIndex:R,setRowChildrenExpansion:x,getRowGroupChildren:S},k={updateServerRows:m},P=e.useCallback((()=>{let e;o.info("Row grouping pre-processing have changed, regenerating the row tree"),e=t.current.caches.rows.rowsBeforePartialUpdates===n.rows?i({},t.current.caches.rows,{updates:{type:"full",rows:jt(t)}}):sn({rows:n.rows,getRowId:n.getRowId,loading:n.loading,rowCount:n.rowCount}),d({cache:e,throttle:!1})}),[o,t,n.rows,n.getRowId,n.loading,n.rowCount,d]),E=r((()=>n.unstable_dataSource)),F=e.useCallback((e=>{n.unstable_dataSource&&n.unstable_dataSource!==E.current?E.current=n.unstable_dataSource:"rowTreeCreation"===e&&P()}),[P,E,n.unstable_dataSource]),H=e.useCallback((()=>{t.current.getActiveStrategy("rowTree")!==Lt(t)&&P()}),[t,P]);wt(t,"activeStrategyProcessorChange",F),wt(t,"strategyAvailabilityChange",H);const D=e.useCallback((()=>{t.current.setState((e=>{const r=t.current.unstable_applyPipeProcessors("hydrateRows",{tree:Tt(e,t.current.instanceId),treeDepths:$t(e,t.current.instanceId),dataRowIds:jt(e,t.current.instanceId),dataRowIdToModelLookup:Ht(e,t.current.instanceId),dataRowIdToIdLookup:Dt(e,t.current.instanceId)});return i({},e,{rows:i({},e.rows,r,{totalTopLevelRowCount:un({tree:r.tree,rowCountProp:n.rowCount})})})})),t.current.publishEvent("rowsSet"),t.current.forceUpdate()}),[t,n.rowCount]);Dc(t,"hydrateRows",D),mo(t,I,"public"),mo(t,M,n.signature===ht.DataGrid?"private":"public"),mo(t,k,"private");const T=e.useRef(!0);e.useEffect((()=>{if(T.current)return void(T.current=!1);let e=!1;n.rowCount!==a.current&&(e=!0,a.current=n.rowCount);const r=t.current.caches.rows.rowsBeforePartialUpdates===n.rows,l=t.current.caches.rows.loadingPropBeforePartialUpdates===n.loading,s=t.current.caches.rows.rowCountPropBeforePartialUpdates===n.rowCount;r&&(l||(t.current.setState((e=>i({},e,{rows:i({},e.rows,{loading:n.loading})}))),t.current.caches.rows.loadingPropBeforePartialUpdates=n.loading,t.current.forceUpdate()),s||(t.current.setState((e=>i({},e,{rows:i({},e.rows,{totalRowCount:Math.max(n.rowCount||0,e.rows.totalRowCount),totalTopLevelRowCount:Math.max(n.rowCount||0,e.rows.totalTopLevelRowCount)})}))),t.current.caches.rows.rowCountPropBeforePartialUpdates=n.rowCount,t.current.forceUpdate()),!e)||(o.debug(`Updating all rows, new length ${n.rows?.length}`),d({cache:sn({rows:n.rows,getRowId:n.getRowId,loading:n.loading,rowCount:n.rowCount}),throttle:!1}))}),[n.rows,n.rowCount,n.getRowId,n.loading,o,d,t])})(o,n),((t,n)=>{const o=r((()=>t.current.state.rowSpanning!==ip?cp(n,t):ap)),l=e.useCallback(((e,r=!1)=>{const{range:l,rows:a}=Ul(t,(n.pagination,n.paginationMode));if(null===l||!op(e))return;r&&(o.current=ap);const s=function(e,t){return e.firstRowIndex>=t.firstRowIndex&&e.lastRowIndex<=t.lastRowIndex?null:e.firstRowIndex>=t.firstRowIndex&&e.lastRowIndex>t.lastRowIndex?{firstRowIndex:t.lastRowIndex,lastRowIndex:e.lastRowIndex}:e.firstRowIndex<t.firstRowIndex&&e.lastRowIndex<=t.lastRowIndex?{firstRowIndex:e.firstRowIndex,lastRowIndex:t.firstRowIndex-1}:e}({firstRowIndex:e.firstRowIndex,lastRowIndex:Math.min(e.lastRowIndex,l.lastRowIndex+1)},o.current);if(null===s)return;const u=kr(t),{spannedCells:c,hiddenCells:d,hiddenCellOriginMap:p,processedRange:f}=up(t,u,a,l,s,r,o.current);o.current=f;const g=Object.keys(c).length,m=Object.keys(d).length,h=Object.keys(t.current.state.rowSpanning.spannedCells).length,b=Object.keys(t.current.state.rowSpanning.hiddenCells).length;(r||g!==h||m!==b)&&(0!==g||0!==h)&&t.current.setState((e=>i({},e,{rowSpanning:{spannedCells:c,hiddenCells:d,hiddenCellOriginMap:p}})))}),[t,o,n.pagination,n.paginationMode]),a=e.useCallback((()=>{const e=Jl(t);op(e)&&l(e,!0)}),[t,l]);wt(t,"renderedRowsIntervalChange",or(n.unstable_rowSpanning,l)),wt(t,"sortedRowsSet",or(n.unstable_rowSpanning,a)),wt(t,"paginationModelChange",or(n.unstable_rowSpanning,a)),wt(t,"filteredRowsSet",or(n.unstable_rowSpanning,a)),wt(t,"columnsChange",or(n.unstable_rowSpanning,a)),e.useEffect((()=>{n.unstable_rowSpanning?t.current.state.rowSpanning===ip&&a():t.current.state.rowSpanning!==ip&&t.current.setState((e=>i({},e,{rowSpanning:ip})))}),[t,a,n.unstable_rowSpanning])})(o,n),yd(o,n),(t=>{const r=e.useRef({}),n=()=>{r.current={}},o={resetColSpan:n,calculateColSpan:e.useCallback((({rowId:e,minFirstColumn:n,maxLastColumn:o,columns:l})=>{for(let i=n;i<o;i+=1){const a=Nd({apiRef:t,lookup:r.current,columnIndex:i,rowId:e,minFirstColumnIndex:n,maxLastColumnIndex:o,columns:l});a.colSpan>1&&(i+=a.colSpan-1)}}),[t])};mo(t,{unstable_getCellColSpanInfo:(e,t)=>r.current[e]?.[t]},"public"),mo(t,o,"private"),wt(t,"columnOrderChange",n)})(o),((t,r)=>{const n=e.useCallback((e=>xl(t)[e]??[]),[t]),o=e.useCallback((()=>yl(t)),[t]);mo(t,{getColumnGroupPath:n,getAllGroupDetails:o},"public");const l=e.useCallback((()=>{const e=Wd(r.columnGroupingModel??[]);t.current.setState((t=>{const r=t.columns?.orderedFields??[],n=t.pinnedColumns??{},o=_d(r,e,n);return i({},t,{columnGrouping:i({},t.columnGrouping,{headerStructure:o})})}))}),[t,r.columnGroupingModel]),a=e.useCallback((e=>{const r=t.current.getPinnedColumns?.()??{},n=Sr(t),o=Pr(t),l=Kd(e??[]),a=Wd(e??[]),s=_d(n,a,r),u=0===o.length?0:Math.max(...o.map((e=>a[e]?.length??0)));t.current.setState((e=>i({},e,{columnGrouping:{lookup:l,unwrappedGroupingModel:a,headerStructure:s,maxDepth:u}})))}),[t]);wt(t,"columnIndexChange",l),wt(t,"columnsChange",(()=>{a(r.columnGroupingModel)})),wt(t,"columnVisibilityModelChange",(()=>{a(r.columnGroupingModel)})),e.useEffect((()=>{a(r.columnGroupingModel)}),[a,r.columnGroupingModel])})(o,n),wd(o,n),((t,r)=>{const n=ho(t,"useGridFocus"),o=e.useRef(null),l=null!==t.current.rootElementRef.current,a=e.useCallback(((e,r)=>{e&&t.current.getRow(e.id)&&t.current.publishEvent("cellFocusOut",t.current.getCellParams(e.id,e.field),r)}),[t]),s=e.useCallback(((e,r)=>{const o=ao(t);o?.id===e&&o?.field===r||(t.current.setState((t=>(n.debug(`Focusing on cell with id=${e} and field=${r}`),i({},t,{tabIndex:{cell:{id:e,field:r},columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null},focus:{cell:{id:e,field:r},columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}})))),t.current.forceUpdate(),t.current.getRow(e)&&(o&&a(o,{}),t.current.publishEvent("cellFocusIn",t.current.getCellParams(e,r))))}),[t,n,a]),u=e.useCallback(((e,r={})=>{const o=ao(t);a(o,r),t.current.setState((t=>(n.debug(`Focusing on column header with colIndex=${e}`),i({},t,{tabIndex:{columnHeader:{field:e},columnHeaderFilter:null,cell:null,columnGroupHeader:null},focus:{columnHeader:{field:e},columnHeaderFilter:null,cell:null,columnGroupHeader:null}})))),t.current.forceUpdate()}),[t,n,a]),c=e.useCallback(((e,r={})=>{const o=ao(t);a(o,r),t.current.setState((t=>(n.debug(`Focusing on column header filter with colIndex=${e}`),i({},t,{tabIndex:{columnHeader:null,columnHeaderFilter:{field:e},cell:null,columnGroupHeader:null},focus:{columnHeader:null,columnHeaderFilter:{field:e},cell:null,columnGroupHeader:null}})))),t.current.forceUpdate()}),[t,n,a]),d=e.useCallback(((e,r,n={})=>{const o=ao(t);o&&t.current.publishEvent("cellFocusOut",t.current.getCellParams(o.id,o.field),n),t.current.setState((t=>i({},t,{tabIndex:{columnGroupHeader:{field:e,depth:r},columnHeader:null,columnHeaderFilter:null,cell:null},focus:{columnGroupHeader:{field:e,depth:r},columnHeader:null,columnHeaderFilter:null,cell:null}}))),t.current.forceUpdate()}),[t]),p=e.useCallback((()=>uo(t)),[t]),f=e.useCallback(((e,n,o)=>{let l=t.current.getColumnIndex(n);const i=kr(t),a=Ul(t,(r.pagination,r.paginationMode)),s=Vt(t),u=[].concat(s.top||[],a.rows,s.bottom||[]);let c=u.findIndex((t=>t.id===e));"right"===o?l+=1:"left"===o?l-=1:c+=1,l>=i.length?(c+=1,c<u.length&&(l=0)):l<0&&(c-=1,c>=0&&(l=i.length-1)),c=er(c,0,u.length-1);const d=u[c];if(!d)return;const p=t.current.unstable_getCellColSpanInfo(d.id,l);p&&p.spannedByColSpan&&("left"===o||"below"===o?l=p.leftVisibleCellIndex:"right"===o&&(l=p.rightVisibleCellIndex)),l=er(l,0,i.length-1);const f=i[l];t.current.setCellFocus(d.id,f.field)}),[t,r.pagination,r.paginationMode]),g=e.useCallback((({id:e,field:r})=>{t.current.setCellFocus(e,r)}),[t]),m=e.useCallback(((e,r)=>{"Enter"===r.key||"Tab"===r.key||"Shift"===r.key||Us(r.key)||t.current.setCellFocus(e.id,e.field)}),[t]),h=e.useCallback((({field:e},r)=>{r.target===r.currentTarget&&t.current.setColumnHeaderFocus(e,r)}),[t]),b=e.useCallback((({fields:e,depth:r},n)=>{if(n.target!==n.currentTarget)return;const o=uo(t);null!==o&&o.depth===r&&e.includes(o.field)||t.current.setColumnGroupHeaderFocus(e[0],r,n)}),[t]),w=e.useCallback(((e,r)=>{r.relatedTarget?.getAttribute("class")?.includes(ft.columnHeader)||(n.debug("Clearing focus"),t.current.setState((e=>i({},e,{focus:{cell:null,columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}}))))}),[n,t]),C=e.useCallback((e=>{o.current=e}),[]),v=e.useCallback((e=>{const r=o.current;o.current=null;const n=ao(t);if(!t.current.unstable_applyPipeProcessors("canUpdateFocus",!0,{event:e,cell:r}))return;if(!n)return void(r&&t.current.setCellFocus(r.id,r.field));if(r?.id===n.id&&r?.field===n.field)return;const l=t.current.getCellElement(n.id,n.field);l?.contains(e.target)||(r?t.current.setCellFocus(r.id,r.field):(t.current.setState((e=>i({},e,{focus:{cell:null,columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}}))),t.current.forceUpdate(),a(n,e)))}),[t,a]),x=e.useCallback((e=>{if("view"===e.cellMode)return;const r=ao(t);r?.id===e.id&&r?.field===e.field||t.current.setCellFocus(e.id,e.field)}),[t]),y=e.useCallback((()=>{const e=ao(t);if(e&&!t.current.getRow(e.id)){const n=e.id;let o=null;if(void 0!==n){const e=t.current.getRowElement(n),l=e?.dataset.rowindex?Number(e?.dataset.rowindex):0,i=Ul(t,(r.pagination,r.paginationMode)),a=i.rows[er(l,0,i.rows.length-1)];o=a?.id??null}t.current.setState((t=>i({},t,{focus:{cell:null===o?null:{id:o,field:e.field},columnHeader:null,columnHeaderFilter:null,columnGroupHeader:null}})))}}),[t,r.pagination,r.paginationMode]),S=I((()=>{const e=ao(t);if(!e)return;const n=Ul(t,(r.pagination,r.paginationMode));if(n.rows.find((t=>t.id===e.id)))return;const o=kr(t);t.current.setState((e=>i({},e,{tabIndex:{cell:{id:n.rows[0].id,field:o[0].field},columnGroupHeader:null,columnHeader:null,columnHeaderFilter:null}})))})),R={moveFocusToRelativeCell:f,setColumnGroupHeaderFocus:d,getColumnGroupHeaderFocus:p};mo(t,{setCellFocus:s,setColumnHeaderFocus:u,setColumnHeaderFilterFocus:c},"public"),mo(t,R,"private"),e.useEffect((()=>{const e=F(t.current.rootElementRef.current);return e.addEventListener("mouseup",v),()=>{e.removeEventListener("mouseup",v)}}),[t,l,v]),wt(t,"columnHeaderBlur",w),wt(t,"cellDoubleClick",g),wt(t,"cellMouseDown",C),wt(t,"cellKeyDown",m),wt(t,"cellModeChange",x),wt(t,"columnHeaderFocus",h),wt(t,"columnGroupHeaderFocus",b),wt(t,"rowsSet",y),wt(t,"paginationModelChange",S)})(o,n),((t,r)=>{const n=ho(t,"useGridPreferencesPanel"),o=e.useCallback((()=>{t.current.setState((e=>{if(!e.preferencePanel.open)return e;n.debug("Hiding Preferences Panel");const r=Ji(e);return t.current.publishEvent("preferencePanelClose",{openedPanelValue:r.openedPanelValue}),i({},e,{preferencePanel:{open:!1}})}))}),[t,n]),l=e.useCallback(((e,r,o)=>{n.debug("Opening Preferences Panel"),t.current.setState((t=>i({},t,{preferencePanel:i({},t.preferencePanel,{open:!0,openedPanelValue:e,panelId:r,labelId:o})}))),t.current.publishEvent("preferencePanelOpen",{openedPanelValue:e})}),[n,t]);mo(t,{showPreferences:l,hidePreferences:o},"public");const a=e.useCallback(((e,n)=>{const o=Ji(t.current.state);return!n.exportOnlyDirtyModels||null!=r.initialState?.preferencePanel||o.open?i({},e,{preferencePanel:o}):e}),[t,r.initialState?.preferencePanel]),s=e.useCallback(((e,r)=>{const n=r.stateToRestore.preferencePanel;return null!=n&&t.current.setState((e=>i({},e,{preferencePanel:n}))),e}),[t]);Hc(t,"exportState",a),Hc(t,"restoreState",s)})(o,n),((t,n)=>{const o=ho(t,"useGridFilter");t.current.registerControlState({stateId:"filter",propModel:n.filterModel,propOnChange:n.onFilterModelChange,stateSelector:$n,changeEvent:"filterModelChange"});const a=e.useCallback((()=>{t.current.setState((e=>{const r=$n(e,t.current.instanceId),n=t.current.getFilterState(r),o=i({},e,{filter:i({},e.filter,n)}),l=rd(t,o);return i({},o,{visibleRowsLookup:l})})),t.current.publishEvent("filteredRowsSet")}),[t]),u=e.useCallback(((e,t)=>null==t||!1===t.filterable||n.disableColumnFilter?e:[...e,"columnMenuFilterItem"]),[n.disableColumnFilter]),c=e.useCallback((()=>{a(),t.current.forceUpdate()}),[t,a]),d=e.useCallback((e=>{const r=$n(t),n=[...r.items],o=n.findIndex((t=>t.id===e.id));-1===o?n.push(e):n[o]=e,t.current.setFilterModel(i({},r,{items:n}),"upsertFilterItem")}),[t]),p=e.useCallback((e=>{const r=$n(t),n=[...r.items];e.forEach((e=>{const t=n.findIndex((t=>t.id===e.id));-1===t?n.push(e):n[t]=e})),t.current.setFilterModel(i({},r,{items:n}),"upsertFilterItems")}),[t]),f=e.useCallback((e=>{const r=$n(t),n=r.items.filter((t=>t.id!==e.id));n.length!==r.items.length&&t.current.setFilterModel(i({},r,{items:n}),"deleteFilterItem")}),[t]),g=e.useCallback(((e,r,l)=>{if(o.debug("Displaying filter panel"),e){const r=$n(t),o=r.items.filter((e=>{if(void 0!==e.value)return!Array.isArray(e.value)||0!==e.value.length;const r=t.current.getColumn(e.field),n=r.filterOperators?.find((t=>t.value===e.operator));return!(void 0===n?.requiresFilterValue||n?.requiresFilterValue)}));let l;const a=o.find((t=>t.field===e)),s=t.current.getColumn(e);l=a?o:n.disableMultipleColumnsFiltering?[Lr({field:e,operator:s.filterOperators[0].value},t)]:[...o,Lr({field:e,operator:s.filterOperators[0].value},t)],t.current.setFilterModel(i({},r,{items:l}))}t.current.showPreferences(ta.filters,r,l)}),[t,o,n.disableMultipleColumnsFiltering]),m=e.useCallback((()=>{o.debug("Hiding filter panel"),t.current.hidePreferences()}),[t,o]),h=e.useCallback((e=>{const r=$n(t);r.logicOperator!==e&&t.current.setFilterModel(i({},r,{logicOperator:e}),"changeLogicOperator")}),[t]),b=e.useCallback((e=>{const r=$n(t);rr(r.quickFilterValues,e)||t.current.setFilterModel(i({},r,{quickFilterValues:[...e]}))}),[t]),w=e.useCallback(((e,r)=>{$n(t)!==e&&(o.debug("Setting filter model"),t.current.updateControlState("filter",zr(e,n.disableMultipleColumnsFiltering,t),r),t.current.unstable_applyFilters())}),[t,o,n.disableMultipleColumnsFiltering]),C=e.useCallback((e=>{const r=$r(e,n.disableMultipleColumnsFiltering,t),o="client"===n.filterMode?Br(r,t,n.disableEval):null,l=t.current.applyStrategyProcessor("filtering",{isRowMatchingFilters:o,filterModel:r??wr()});return i({},l,{filterModel:r})}),[n.disableMultipleColumnsFiltering,n.filterMode,n.disableEval,t]),v={setFilterLogicOperator:h,unstable_applyFilters:c,deleteFilterItem:f,upsertFilterItem:d,upsertFilterItems:p,setFilterModel:w,showFilterPanel:g,hideFilterPanel:m,setQuickFilterValues:b,ignoreDiacritics:n.ignoreDiacritics,getFilterState:C};mo(t,v,"public");const x=e.useCallback(((e,r)=>{const o=$n(t);return o.items.forEach((e=>{delete e.fromInput})),r.exportOnlyDirtyModels&&null==n.filterModel&&null==n.initialState?.filter?.filterModel&&rr(o,wr())?e:i({},e,{filter:{filterModel:o}})}),[t,n.filterModel,n.initialState?.filter?.filterModel]),y=e.useCallback(((e,r)=>{const o=r.stateToRestore.filter?.filterModel;return null==o?e:(t.current.updateControlState("filter",zr(o,n.disableMultipleColumnsFiltering,t),"restoreState"),i({},e,{callbacks:[...e.callbacks,t.current.unstable_applyFilters]}))}),[t,n.disableMultipleColumnsFiltering]),S=e.useCallback(((e,t)=>{if(t===ta.filters){const e=n.slots.filterPanel;return l.jsx(e,i({},n.slotProps?.filterPanel))}return e}),[n.slots.filterPanel,n.slotProps?.filterPanel]),{getRowId:R}=n,I=r(nd),M=e.useCallback((e=>{if("client"!==n.filterMode||!e.isRowMatchingFilters||!e.filterModel.items.length&&!e.filterModel.quickFilterValues?.length)return br;const r=Ht(t),o={},{isRowMatchingFilters:l}=e,i={},a={passingFilterItems:null,passingQuickFilterValues:null},s=I.current(t.current.state.rows.dataRowIdToModelLookup);for(let n=0;n<s.length;n+=1){const r=s[n],u=R?R(r):r.id;l(r,void 0,a);const c=_r([a.passingFilterItems],[a.passingQuickFilterValues],e.filterModel,t,i);o[u]=c}const u="auto-generated-group-footer-root";return r[u]&&(o[u]=!0),{filteredRowsLookup:o,filteredChildrenCountLookup:{},filteredDescendantCountLookup:{}}}),[t,n.filterMode,R,I]);Hc(t,"columnMenu",u),Hc(t,"exportState",x),Hc(t,"restoreState",y),Hc(t,"preferencePanel",S),Tc(t,Oc,"filtering",M),Tc(t,Oc,"visibleRowsLookupCreation",td);const k=e.useCallback((()=>{o.debug("onColUpdated - GridColumns changed, applying filters");const e=$n(t),r=Rr(t),n=e.items.filter((e=>e.field&&r[e.field]));n.length<e.items.length&&t.current.setFilterModel(i({},e,{items:n}))}),[t,o]),P=e.useCallback((e=>{"filtering"===e&&t.current.unstable_applyFilters()}),[t]),E=e.useCallback((()=>{t.current.setState((e=>i({},e,{visibleRowsLookup:rd(t,e)}))),t.current.forceUpdate()}),[t]);wt(t,"rowsSet",a),wt(t,"columnsChange",k),wt(t,"activeStrategyProcessorChange",P),wt(t,"rowExpansionChange",E),wt(t,"columnVisibilityModelChange",(()=>{const e=$n(t);e.quickFilterValues&&Gr(e)&&t.current.unstable_applyFilters()})),wo((()=>{t.current.unstable_applyFilters()})),s((()=>{void 0!==n.filterModel&&t.current.setFilterModel(n.filterModel)}),[t,o,n.filterModel])})(o,n),((t,r)=>{const n=ho(t,"useGridSorting");t.current.registerControlState({stateId:"sortModel",propModel:r.sortModel,propOnChange:r.onSortModelChange,stateSelector:Tn,changeEvent:"sortModelChange"});const o=e.useCallback(((e,r)=>{const n=Tn(t),o=n.findIndex((t=>t.field===e));let l=[...n];return o>-1?null==r?.sort?l.splice(o,1):l.splice(o,1,r):l=[...n,r],l}),[t]),l=e.useCallback(((e,n)=>{const o=Tn(t).find((t=>t.field===e.field));if(o){const t=void 0===n?Wt(e.sortingOrder??r.sortingOrder,o.sort):n;return void 0===t?void 0:i({},o,{sort:t})}return{field:e.field,sort:void 0===n?Wt(e.sortingOrder??r.sortingOrder):n}}),[t,r.sortingOrder]),a=e.useCallback(((e,t)=>null==t||!1===t.sortable||r.disableColumnSorting?e:(t.sortingOrder||r.sortingOrder).some((e=>!!e))?[...e,"columnMenuSortItem"]:e),[r.sortingOrder,r.disableColumnSorting]),u=e.useCallback((()=>{t.current.setState((e=>{if("server"===r.sortingMode)return n.debug("Skipping sorting rows as sortingMode = server"),i({},e,{sorting:i({},e.sorting,{sortedRows:pn(Tt(t),on,!1)})});const o=Tn(e,t.current.instanceId),l=Bt(o,t),a=t.current.applyStrategyProcessor("sorting",{sortRowList:l});return i({},e,{sorting:i({},e.sorting,{sortedRows:a})})})),t.current.publishEvent("sortedRowsSet"),t.current.forceUpdate()}),[t,n,r.sortingMode]),c=e.useCallback((e=>{Tn(t)!==e&&(n.debug("Setting sort model"),t.current.setState(Gt(e,r.disableMultipleColumnsSorting)),t.current.forceUpdate(),t.current.applySorting())}),[t,n,r.disableMultipleColumnsSorting]),d=e.useCallback(((e,n,i)=>{const a=t.current.getColumn(e),s=l(a,n);let u;u=!i||r.disableMultipleColumnsSorting?null==s?.sort?[]:[s]:o(a.field,s),t.current.setSortModel(u)}),[t,o,l,r.disableMultipleColumnsSorting]),p=e.useCallback((()=>Tn(t)),[t]),f=e.useCallback((()=>Dn(t).map((e=>e.model))),[t]),g=e.useCallback((()=>Hn(t)),[t]),m=e.useCallback((e=>t.current.getSortedRowIds()[e]),[t]);mo(t,{getSortModel:p,getSortedRows:f,getSortedRowIds:g,getRowIdFromRowIndex:m,setSortModel:c,sortColumn:d,applySorting:u},"public");const h=e.useCallback(((e,n)=>{const o=Tn(t);return!n.exportOnlyDirtyModels||null!=r.sortModel||null!=r.initialState?.sorting?.sortModel||o.length>0?i({},e,{sorting:{sortModel:o}}):e}),[t,r.sortModel,r.initialState?.sorting?.sortModel]),b=e.useCallback(((e,n)=>{const o=n.stateToRestore.sorting?.sortModel;return null==o?e:(t.current.setState(Gt(o,r.disableMultipleColumnsSorting)),i({},e,{callbacks:[...e.callbacks,t.current.applySorting]}))}),[t,r.disableMultipleColumnsSorting]),w=e.useCallback((e=>{const r=Tt(t),n=r[on],o=e.sortRowList?e.sortRowList(n.children.map((e=>r[e]))):[...n.children];return null!=n.footerId&&o.push(n.footerId),o}),[t]);Hc(t,"exportState",h),Hc(t,"restoreState",b),Tc(t,Oc,"sorting",w);const C=e.useCallback((({field:e,colDef:t},n)=>{if(!t.sortable||r.disableColumnSorting)return;const o=n.shiftKey||n.metaKey||n.ctrlKey;d(e,void 0,o)}),[d,r.disableColumnSorting]),v=e.useCallback((({field:e,colDef:t},n)=>{t.sortable&&!r.disableColumnSorting&&("Enter"!==n.key||n.ctrlKey||n.metaKey||d(e,void 0,n.shiftKey))}),[d,r.disableColumnSorting]),x=e.useCallback((()=>{const e=Tn(t),r=Rr(t);if(e.length>0){const n=e.filter((e=>r[e.field]));n.length<e.length&&t.current.setSortModel(n)}}),[t]),y=e.useCallback((e=>{"sorting"===e&&t.current.applySorting()}),[t]);Hc(t,"columnMenu",a),wt(t,"columnHeaderClick",C),wt(t,"columnHeaderKeyDown",v),wt(t,"rowsSet",t.current.applySorting),wt(t,"columnsChange",x),wt(t,"activeStrategyProcessorChange",y),wo((()=>{t.current.applySorting()})),s((()=>{void 0!==r.sortModel&&t.current.setSortModel(r.sortModel)}),[t,r.sortModel])})(o,n),((t,r)=>{const n=ho(t,"useDensity");t.current.registerControlState({stateId:"density",propModel:r.density,propOnChange:r.onDensityChange,stateSelector:yt,changeEvent:"densityChange"});const o=I((e=>{yt(t.current.state)!==e&&(n.debug(`Set grid density to ${e}`),t.current.setState((t=>i({},t,{density:e}))))}));mo(t,{setDensity:o},"public");const l=e.useCallback(((e,n)=>{const o=yt(t.current.state);return n.exportOnlyDirtyModels&&null==r.density&&null==r.initialState?.density?e:i({},e,{density:o})}),[t,r.density,r.initialState?.density]),a=e.useCallback(((e,r)=>{const n=r.stateToRestore?.density?r.stateToRestore.density:yt(t.current.state);return t.current.setState((e=>i({},e,{density:n}))),e}),[t]);Hc(t,"exportState",l),Hc(t,"restoreState",a),e.useEffect((()=>{r.density&&t.current.setDensity(r.density)}),[t,r.density])})(o,n),rp(o,n),cd(o,n),((t,n)=>{const{getRowHeight:o,getRowSpacing:l,getEstimatedRowHeight:a}=n,u=t.current.caches.rowsMeta.heights,c=e.useRef(-1),d=e.useRef(!1),p=e.useRef(!1),f=We(t,St),g=Kl(t),m=We(t,Vt),h=We(t,tt),b=e.useCallback((e=>{const r=Je(t.current.state).rowHeight,n=t.current.getRowHeightEntry(e.id);if(o){const t=o(i({},e,{densityFactor:f}));if("auto"===t){if(n.needsFirstMeasurement){const t=a?a(i({},e,{densityFactor:f})):r;n.content=t??r}d.current=!0,n.autoHeight=!0}else n.content=hn(t,r),n.needsFirstMeasurement=!1,n.autoHeight=!1}else n.content=r,n.needsFirstMeasurement=!1;if(l){const r=t.current.getRowIndexRelativeToVisibleRows(e.id),o=l(i({},e,{isFirstVisible:0===r,isLastVisible:r===g.rows.length-1,indexRelativeToCurrentPage:r}));n.spacingTop=o.top??0,n.spacingBottom=o.bottom??0}else n.spacingTop=0,n.spacingBottom=0;return t.current.unstable_applyPipeProcessors("rowHeight",n,e),n}),[t,g.rows,o,a,h,l,f]),w=e.useCallback((()=>{d.current=!1;const e=m.top.reduce(((e,t)=>{const r=b(t);return e+r.content+r.spacingTop+r.spacingBottom+r.detail}),0),r=m.bottom.reduce(((e,t)=>{const r=b(t);return e+r.content+r.spacingTop+r.spacingBottom+r.detail}),0),n=[],o=g.rows.reduce(((e,t)=>{n.push(e);const r=b(t);return e+(r.content+r.spacingTop+r.spacingBottom+r.detail)}),0);d.current||(c.current=1/0);const l=e!==t.current.state.rowsMeta.pinnedTopRowsTotalHeight||r!==t.current.state.rowsMeta.pinnedBottomRowsTotalHeight||o!==t.current.state.rowsMeta.currentPageTotalHeight,a={currentPageTotalHeight:o,positions:n,pinnedTopRowsTotalHeight:e,pinnedBottomRowsTotalHeight:r};t.current.setState((e=>i({},e,{rowsMeta:a}))),l&&t.current.updateDimensions(),p.current=!0}),[t,m,g.rows,b]),C=r((()=>new Vd((e=>{for(let r=0;r<e.length;r+=1){const n=e[r],o=n.borderBoxSize&&n.borderBoxSize.length>0?n.borderBoxSize[0].blockSize:n.contentRect.height,l=n.target.__mui_id,i=si(t)?.id;if(i===l&&0===o)return;t.current.unstable_storeRowHeightMeasurement(l,o)}p.current||t.current.requestPipeProcessorsApplication("rowHeight")})))).current;Dc(t,"rowHeight",w),s((()=>{w()}),[w]);const v={unstable_getRowHeight:e=>u.get(e)?.content??h,unstable_setLastMeasuredRowIndex:e=>{d.current&&e>c.current&&(c.current=e)},unstable_storeRowHeightMeasurement:(e,r)=>{const n=t.current.getRowHeightEntry(e),o=n.content!==r;n.needsFirstMeasurement=!1,n.content=r,p.current&&=!o},resetRowHeights:()=>{u.clear(),w()}},x={hydrateRowsMeta:w,observeRowHeight:(e,t)=>(e.__mui_id=t,C.observe(e),()=>C.unobserve(e)),rowHasAutoHeight:e=>u.get(e)?.autoHeight??!1,getRowHeightEntry:e=>{let t=u.get(e);return void 0===t&&(t={content:h,spacingTop:0,spacingBottom:0,detail:0,autoHeight:!1,needsFirstMeasurement:!0},u.set(e,t)),t},getLastMeasuredRowIndex:()=>c.current};mo(t,v,"public"),mo(t,x,"private")})(o,n),((t,r)=>{const n=v(),o=ho(t,"useGridScroll"),l=t.current.columnHeadersContainerRef,i=t.current.virtualScrollerRef,a=We(t,Vn),s=e.useCallback((e=>{const n=Je(t.current.state),l=Pt(t),s=r.unstable_listView?[ai(t.current.state)]:kr(t);if(null!=e.rowIndex&&0===l||0===s.length)return!1;o.debug(`Scrolling to cell at row ${e.rowIndex}, col: ${e.colIndex} `);let u={};if(void 0!==e.colIndex){const r=Fr(t);let o;if(void 0!==e.rowIndex){const r=a[e.rowIndex]?.id,n=t.current.unstable_getCellColSpanInfo(r,e.colIndex);n&&!n.spannedByColSpan&&(o=n.cellProps.width)}void 0===o&&(o=s[e.colIndex].computedWidth),u.left=Pd({containerSize:n.viewportOuterSize.width,scrollPosition:Math.abs(i.current.scrollLeft),elementSize:o,elementOffset:r[e.colIndex]})}if(void 0!==e.rowIndex){const o=Xl(t.current.state),l=Po(t),a=Eo(t),s=r.pagination?e.rowIndex-l*a:e.rowIndex,c=o.positions[s+1]?o.positions[s+1]-o.positions[s]:o.currentPageTotalHeight-o.positions[s];u.top=Pd({containerSize:n.viewportInnerSize.height,scrollPosition:i.current.scrollTop,elementSize:c,elementOffset:o.positions[s]})}return u=t.current.unstable_applyPipeProcessors("scrollToIndexes",u,e),(void 0!==typeof u.left||void 0!==typeof u.top)&&(t.current.scroll(u),!0)}),[o,t,i,r.pagination,a,r.unstable_listView]),u=e.useCallback((e=>{if(i.current&&void 0!==e.left&&l.current){const t=n?-1:1;l.current.scrollLeft=e.left,i.current.scrollLeft=t*e.left,o.debug(`Scrolling left: ${e.left}`)}i.current&&void 0!==e.top&&(i.current.scrollTop=e.top,o.debug(`Scrolling top: ${e.top}`)),o.debug("Scrolling, updating container, and viewport")}),[i,n,l,o]),c=e.useCallback((()=>i?.current?{top:i.current.scrollTop,left:i.current.scrollLeft}:{top:0,left:0}),[i]);mo(t,{scroll:u,scrollToIndexes:s,getScrollPosition:c},"public")})(o,n),(t=>{const r=ho(t,"useGridColumnMenu"),n=e.useCallback((e=>{const n=ki(t.current.state),o=e;(!0!==n.open||o!==n.field)&&(t.current.setState((t=>t.columnMenu.open&&t.columnMenu.field===e?t:(r.debug("Opening Column Menu"),i({},t,{columnMenu:{open:!0,field:e}})))),t.current.hidePreferences())}),[t,r]),o=e.useCallback((()=>{const e=ki(t.current.state);if(e.field){const r=Rr(t),n=Mr(t),o=Sr(t);let l=e.field;if(r[l]||(l=o[0]),!1===n[l]){const e=o.filter((e=>e===l||!1!==n[e])),t=e.indexOf(l);l=e[t+1]||e[t-1]}t.current.setColumnHeaderFocus(l)}const n={open:!1,field:void 0};(n.open!==e.open||n.field!==e.field)&&t.current.setState((e=>(r.debug("Hiding Column Menu"),i({},e,{columnMenu:n}))))}),[t,r]),l=e.useCallback((e=>{r.debug("Toggle Column Menu");const l=ki(t.current.state);l.open&&l.field===e?o():n(e)}),[t,r,n,o]);mo(t,{showColumnMenu:n,hideColumnMenu:o,toggleColumnMenu:l},"public"),wt(t,"columnResizeStart",o),wt(t,"virtualScrollerWheel",t.current.hideColumnMenu),wt(t,"virtualScrollerTouchMove",t.current.hideColumnMenu)})(o),Qc(o,n),Jc(o,n),Wc(o,n),Od(o,n),function(e,t){vt(e,"columnHeaderClick",t.onColumnHeaderClick),vt(e,"columnHeaderContextMenu",t.onColumnHeaderContextMenu),vt(e,"columnHeaderDoubleClick",t.onColumnHeaderDoubleClick),vt(e,"columnHeaderOver",t.onColumnHeaderOver),vt(e,"columnHeaderOut",t.onColumnHeaderOut),vt(e,"columnHeaderEnter",t.onColumnHeaderEnter),vt(e,"columnHeaderLeave",t.onColumnHeaderLeave),vt(e,"cellClick",t.onCellClick),vt(e,"cellDoubleClick",t.onCellDoubleClick),vt(e,"cellKeyDown",t.onCellKeyDown),vt(e,"preferencePanelClose",t.onPreferencePanelClose),vt(e,"preferencePanelOpen",t.onPreferencePanelOpen),vt(e,"menuOpen",t.onMenuOpen),vt(e,"menuClose",t.onMenuClose),vt(e,"rowDoubleClick",t.onRowDoubleClick),vt(e,"rowClick",t.onRowClick),vt(e,"stateChange",t.onStateChange)}(o,n),(t=>{const r=e.useCallback(((e={})=>t.current.unstable_applyPipeProcessors("exportState",{},e)),[t]),n=e.useCallback((e=>{t.current.unstable_applyPipeProcessors("restoreState",{callbacks:[]},{stateToRestore:e}).callbacks.forEach((e=>{e()})),t.current.forceUpdate()}),[t]);mo(t,{exportState:r,restoreState:n},"public")})(o),function(t,r){const n=e=>{t.current.setState((t=>i({},t,{virtualization:i({},t.virtualization,{enabled:e,enabledForColumns:e,enabledForRows:e&&!r.autoHeight})})))},o={unstable_setVirtualization:n,unstable_setColumnVirtualization:e=>{t.current.setState((t=>i({},t,{virtualization:i({},t.virtualization,{enabledForColumns:e})})))}};mo(t,o,"public"),e.useEffect((()=>{n(!r.disableVirtualization)}),[r.disableVirtualization,r.autoHeight])}(o,n),function(t,r){const n=()=>{t.current.setState((e=>e.listViewColumn?i({},e,{listViewColumn:i({},e.listViewColumn,{computedWidth:fp(t)})}):e))},o=e.useRef(null);wt(t,"viewportInnerSizeChange",(e=>{o.current!==e.width&&(o.current=e.width,n())})),wt(t,"columnVisibilityModelChange",n),s((()=>{const e=r.unstable_listColumn;e&&t.current.setState((r=>i({},r,{listViewColumn:i({},e,{computedWidth:fp(t)})})))}),[t,r.unstable_listColumn]),e.useEffect((()=>{r.unstable_listView&&r.unstable_listColumn}),[r.unstable_listView,r.unstable_listColumn])}(o,n),o};function mp(t){const{groupId:r,width:n,depth:o,maxDepth:a,fields:s,height:u,colIndex:d,hasFocus:p,tabIndex:g,isLastColumn:m,pinnedPosition:h,pinnedOffset:b}=t,w=me(),C=v(),x=e.useRef(null),y=fe(),S=We(y,yl),R=r?S[r]:{},{headerName:I=r??"",description:M="",headerAlign:k}=R;let P;const E=r&&S[r]?.renderHeaderGroup,F=e.useMemo((()=>({groupId:r,headerName:I,description:M,depth:o,maxDepth:a,fields:s,colIndex:d,isLastColumn:m})),[r,I,M,o,a,s,d,m]);r&&E&&(P=E(F));const H=i({},t,{classes:w.classes,headerAlign:k,depth:o,isDragging:!1}),D=I??r,T=f(),O=null===r?`empty-group-cell-${T}`:r,L=(e=>{const{classes:t,headerAlign:r,isDragging:n,isLastColumn:o,showLeftBorder:l,showRightBorder:i,groupId:a,pinnedPosition:s}=e,u={root:["columnHeader","left"===r&&"columnHeader--alignLeft","center"===r&&"columnHeader--alignCenter","right"===r&&"columnHeader--alignRight",n&&"columnHeader--moving",i&&"columnHeader--withRightBorder",l&&"columnHeader--withLeftBorder","withBorderColor",null===a?"columnHeader--emptyGroup":"columnHeader--filledGroup",s===Cn.LEFT&&"columnHeader--pinnedLeft",s===Cn.RIGHT&&"columnHeader--pinnedRight",o&&"columnHeader--last"],draggableContainer:["columnHeaderDraggableContainer"],titleContainer:["columnHeaderTitleContainer","withBorderColor"],titleContainerContent:["columnHeaderTitleContainerContent"]};return c(u,pt,t)})(H);e.useLayoutEffect((()=>{if(p){const e=x.current.querySelector('[tabindex="0"]')||x.current;e?.focus()}}),[y,p]);const $=e.useCallback((e=>t=>{Ya(t)||y.current.publishEvent(e,F,t)}),[y,F]),z=e.useMemo((()=>({onKeyDown:$("columnGroupHeaderKeyDown"),onFocus:$("columnGroupHeaderFocus"),onBlur:$("columnGroupHeaderBlur")})),[$]),j="function"==typeof R.headerClassName?R.headerClassName(F):R.headerClassName,V=e.useMemo((()=>fa(i({},t.style),C,h,b)),[h,b,t.style,C]);return l.jsx(ds,i({ref:x,classes:L,columnMenuOpen:!1,colIndex:d,height:u,isResizing:!1,sortDirection:null,hasFocus:!1,tabIndex:g,isDraggable:!1,headerComponent:P,headerClassName:j,description:M,elementId:O,width:n,columnMenuIconButton:null,columnTitleIconButtons:null,resizable:!1,label:D,"aria-colspan":s.length,"data-fields":`|-${s.join("-|-")}-|`,style:V},z))}const hp=u("div",{name:"MuiDataGrid",slot:"ColumnHeaderRow",overridesResolver:(e,t)=>t.columnHeaderRow})({display:"flex"}),bp=["className"],wp=M("div",{name:"MuiDataGrid",slot:"ColumnHeaders",overridesResolver:(e,t)=>t.columnHeaders})({display:"flex",flexDirection:"column",borderTopLeftRadius:"var(--unstable_DataGrid-radius)",borderTopRightRadius:"var(--unstable_DataGrid-radius)"}),Cp=be((function(e,t){const{className:r}=e,n=a(e,bp),o=me(),s=(e=>{const{classes:t}=e;return c({root:["columnHeaders"]},pt,t)})(o);return l.jsx(wp,i({className:m(s.root,r),ownerState:o},n,{role:"presentation",ref:t}))})),vp=["className","visibleColumns","sortColumnLookup","filterColumnLookup","columnHeaderTabIndexState","columnGroupHeaderTabIndexState","columnHeaderFocus","columnGroupHeaderFocus","headerGroupingMaxDepth","columnMenuState","columnVisibility","columnGroupsHeaderStructure","hasOtherElementInTabSequence"],xp=ct(be((function(t,r){const{visibleColumns:n,sortColumnLookup:o,filterColumnLookup:s,columnHeaderTabIndexState:u,columnGroupHeaderTabIndexState:c,columnHeaderFocus:d,columnGroupHeaderFocus:p,headerGroupingMaxDepth:f,columnMenuState:g,columnVisibility:h,columnGroupsHeaderStructure:b,hasOtherElementInTabSequence:w}=t,C=a(t,vp),{getInnerProps:v,getColumnHeadersRow:x,getColumnGroupHeadersRows:y}=(t=>{const{visibleColumns:r,sortColumnLookup:n,filterColumnLookup:o,columnHeaderTabIndexState:a,columnGroupHeaderTabIndexState:s,columnHeaderFocus:u,columnGroupHeaderFocus:c,headerGroupingMaxDepth:d,columnMenuState:p,columnVisibility:f,columnGroupsHeaderStructure:g,hasOtherElementInTabSequence:h}=t,[b,w]=e.useState(""),[C,v]=e.useState(""),x=_l(),y=me(),S=We(x,xl),R=We(x,Fr),I=We(x,ei),M=We(x,Er),k=We(x,Rr),P=xi(R,I,M.left.length),E=We(x,et),F=We(x,lt),H=We(x,it),D=We(x,at),T=We(x,st),O=e.useCallback((e=>v(e.field)),[]),L=e.useCallback((()=>v("")),[]),$=e.useCallback((e=>w(e.field)),[]),z=e.useCallback((()=>w("")),[]),j=e.useMemo((()=>M.left.length?{firstColumnIndex:0,lastColumnIndex:M.left.length}:null),[M.left.length]),V=e.useMemo((()=>M.right.length?{firstColumnIndex:r.length-M.right.length,lastColumnIndex:r.length}:null),[M.right.length,r.length]);wt(x,"columnResizeStart",O),wt(x,"columnResizeStop",L),wt(x,"columnHeaderDragStart",$),wt(x,"columnHeaderDragEnd",z);const A=e=>{const{renderContext:t=I}=e||{},n=t.firstColumnIndex,o=t.lastColumnIndex;return{renderedColumns:r.slice(n,o),firstColumnToRender:n,lastColumnToRender:o}},N=(t,r,n,o=!1)=>{const i=t?.position===Cn.RIGHT,a=void 0===t?.position,s=M.right.length>0&&i||0===M.right.length&&a,u=P-n;return l.jsxs(e.Fragment,{children:[a&&l.jsx("div",{role:"presentation",style:{width:u}}),r,a&&l.jsx("div",{role:"presentation",className:m(ft.filler,o&&ft["filler--borderBottom"])}),s&&l.jsx(rc,{header:!0,pinnedRight:i,borderBottom:o,borderTop:!1})]})},G=(e,t={})=>{const{renderedColumns:r,firstColumnToRender:s}=A(e),c=[];for(let d=0;d<r.length;d+=1){const f=r[d],g=s+d,m=0===g,w=null!==a&&a.field===f.field||m&&!h?0:-1,v=null!==u&&u.field===f.field,x=p.open&&p.field===f.field,S=e?.position,I=Zu(S,f.computedWidth,g,R,E,T),k=S===Cn.RIGHT?r[d-1]:r[d+1],P=!!k&&null!==u&&u.field===k.field,D=g+1===R.length-M.right.length,O=d,L=r.length,$=ec(S,O),z=Ju(S,O,L,y.showColumnVerticalBorder,F);c.push(l.jsx(ps,i({},n[f.field],{columnMenuOpen:x,filterItemsCounter:o[f.field]&&o[f.field].length,headerHeight:H,isDragging:f.field===b,colDef:f,colIndex:g,isResizing:C===f.field,isLast:g===R.length-1,hasFocus:v,tabIndex:w,pinnedPosition:S,pinnedOffset:I,isLastUnpinned:D,isSiblingFocused:P,showLeftBorder:$,showRightBorder:z},t),f.field))}return N(e,c,0)},B=({depth:e,params:t})=>{const n=A(t);if(0===n.renderedColumns.length)return null;const{firstColumnToRender:o,lastColumnToRender:a}=n,u=g[e],p=r[o].field,m=S[p]?.[e]??null,h=u.findIndex((({groupId:e,columnFields:t})=>e===m&&t.includes(p))),b=r[a-1].field,w=S[b]?.[e]??null,C=u.findIndex((({groupId:e,columnFields:t})=>e===w&&t.includes(b))),v=u.slice(h,C+1).map((e=>i({},e,{columnFields:e.columnFields.filter((e=>!1!==f[e]))}))).filter((e=>e.columnFields.length>0)),x=v[0].columnFields.indexOf(p),I=v[0].columnFields.slice(0,x).reduce(((e,t)=>e+(k[t].computedWidth??0)),0);let M=o;const P=v.map((({groupId:r,columnFields:n},o)=>{const i=null!==c&&c.depth===e&&n.includes(c.field),a=null!==s&&s.depth===e&&n.includes(s.field)?0:-1,u={width:n.reduce(((e,t)=>e+k[t].computedWidth),0),fields:n,colIndex:M},p=t.position,f=Zu(p,u.width,M,R,E,T);M+=n.length;let g=o;return p===Cn.LEFT&&(g=M-1),l.jsx(mp,{groupId:r,width:u.width,fields:u.fields,colIndex:u.colIndex,depth:e,isLastColumn:o===v.length-1,maxDepth:d,height:D,hasFocus:i,tabIndex:a,pinnedPosition:p,pinnedOffset:f,showLeftBorder:ec(p,g),showRightBorder:Ju(p,g,v.length,y.showColumnVerticalBorder,F)},o)}));return N(t,P,I)};return{renderContext:I,leftRenderContext:j,rightRenderContext:V,pinnedColumns:M,visibleColumns:r,columnPositions:R,getFillers:N,getColumnHeadersRow:()=>l.jsxs(hp,{role:"row","aria-rowindex":d+1,ownerState:y,className:ft["row--borderBottom"],children:[j&&G({position:Cn.LEFT,renderContext:j},{disableReorder:!0}),G({renderContext:I}),V&&G({position:Cn.RIGHT,renderContext:V},{disableReorder:!0,separatorSide:as.Left})]}),getColumnsToRender:A,getColumnGroupHeadersRows:()=>{if(0===d)return null;const e=[];for(let t=0;t<d;t+=1)e.push(l.jsxs(hp,{role:"row","aria-rowindex":t+1,ownerState:y,children:[j&&B({depth:t,params:{position:Cn.LEFT,renderContext:j,maxLastColumn:j.lastColumnIndex}}),B({depth:t,params:{renderContext:I}}),V&&B({depth:t,params:{position:Cn.RIGHT,renderContext:V,maxLastColumn:V.lastColumnIndex}})]},t));return e},getPinnedCellOffset:Zu,isDragging:!!b,getInnerProps:()=>({role:"rowgroup"})}})({visibleColumns:n,sortColumnLookup:o,filterColumnLookup:s,columnHeaderTabIndexState:u,columnGroupHeaderTabIndexState:c,columnHeaderFocus:d,columnGroupHeaderFocus:p,headerGroupingMaxDepth:f,columnMenuState:g,columnVisibility:h,columnGroupsHeaderStructure:b,hasOtherElementInTabSequence:w});return l.jsxs(Cp,i({},C,v(),{ref:r,children:[y(),x()]}))})));const yp=be((function(e,t){const r=fe().current.getLocaleText("noResultsOverlayLabel");return l.jsx(Wa,i({},e,{ref:t,children:r}))})),Sp=["sortingOrder"],Rp=e.memo((function(e){const{sortingOrder:t}=e,r=a(e,Sp),n=me(),[o]=t,s="asc"===o?n.slots.columnSortedAscendingIcon:n.slots.columnSortedDescendingIcon;return s?l.jsx(s,i({},r)):null})),Ip=["native"];const Mp=i({},{booleanCellTrueIcon:Vs,booleanCellFalseIcon:Ts,columnMenuIcon:Ds,openFilterButtonIcon:Rs,filterPanelDeleteIcon:Ts,columnFilteredIcon:Is,columnSelectorIcon:ks,columnUnsortedIcon:Rp,columnSortedAscendingIcon:vs,columnSortedDescendingIcon:xs,columnResizeIcon:Ps,densityCompactIcon:Es,densityStandardIcon:Fs,densityComfortableIcon:Hs,exportIcon:js,moreActionsIcon:As,treeDataCollapseIcon:Ss,treeDataExpandIcon:ys,groupingCriteriaCollapseIcon:Ss,groupingCriteriaExpandIcon:ys,detailPanelExpandIcon:Os,detailPanelCollapseIcon:Ls,rowReorderIcon:zs,quickFilterIcon:Ms,quickFilterClearIcon:Ts,columnMenuHideIcon:Ns,columnMenuSortAscendingIcon:vs,columnMenuSortDescendingIcon:xs,columnMenuFilterIcon:Is,columnMenuManageColumnsIcon:Gs,columnMenuClearIcon:Bs,loadIcon:$s,filterPanelAddIcon:Os,filterPanelRemoveAllIcon:Ws,columnReorderIcon:zs},{baseBadge:ae,baseCheckbox:ie,baseDivider:le,baseTextField:W,baseFormControl:oe,baseSelect:ne,baseButton:re,baseIconButton:te,baseInputAdornment:ee,baseTooltip:J,basePopper:C,baseInputLabel:Z,baseSelectOption:function(e){let{native:t}=e,r=a(e,Ip);return t?l.jsx("option",i({},r)):l.jsx(V,i({},r))},baseChip:Q}),kp=i({},Mp,{cell:ba,skeletonCell:Sa,columnHeaderFilterIconButton:function(e){return e.counter?l.jsx(Cs,i({},e)):null},columnHeaderSortIcon:ws,columnMenu:lu,columnHeaders:xp,detailPanels:function(e){return null},footer:Qu,footerRowCount:mc,toolbar:null,pinnedRows:function(e){return null},loadingOverlay:sc,noResultsOverlay:yp,noRowsOverlay:uc,pagination:pc,filterPanel:Ou,columnsPanel:function(e){const t=me();return l.jsx(uu,i({},e,{children:l.jsx(t.slots.columnsManagement,i({},t.slotProps?.columnsManagement))}))},columnsManagement:function(t){const n=fe(),o=e.useRef(null),a=We(n,Ir),s=r((()=>Mr(n))).current,u=We(n,Mr),d=me(),[p,f]=e.useState(""),g=(e=>{const{classes:t}=e;return c({root:["columnsManagement"],header:["columnsManagementHeader"],searchInput:["columnsManagementSearchInput"],footer:["columnsManagementFooter"],row:["columnsManagementRow"]},pt,t)})(d),{sort:m,searchPredicate:h=Lu,autoFocusSearchField:b=!0,disableShowHideToggle:w=!1,disableResetButton:C=!1,toggleAllMode:v="all",getTogglableColumns:x,searchInputProps:y}=t,S=e.useMemo((()=>((e,t)=>{const r=new Set(Object.keys(e).filter((t=>!1===e[t]))),n=new Set(Object.keys(t).filter((e=>!1===t[e])));if(r.size!==n.size)return!1;let o=!0;return r.forEach((e=>{n.has(e)||(o=!1)})),o})(u,s)),[u,s]),R=e.useMemo((()=>{switch(m){case"asc":return[...a].sort(((e,t)=>$u.compare(e.headerName||e.field,t.headerName||t.field)));case"desc":return[...a].sort(((e,t)=>-$u.compare(e.headerName||e.field,t.headerName||t.field)));default:return a}}),[a,m]),I=e=>{const{name:t}=e.target;n.current.setColumnVisibility(t,!1===u[t])},M=e.useMemo((()=>{const e=x?x(R):null,t=e?R.filter((({field:t})=>e.includes(t))):R;return p?t.filter((e=>h(e,p.toLowerCase()))):t}),[R,p,h,x]),k=e.useCallback((e=>{const t=Mr(n),r=i({},t),o=x?x(a):null;return("filteredOnly"===v?M:a).forEach((t=>{t.hideable&&(null==o||o.includes(t.field))&&(e?delete r[t.field]:r[t.field]=!1)})),n.current.setColumnVisibilityModel(r)}),[n,a,x,v,M]),P=e.useCallback((e=>{f(e.target.value)}),[]),E=e.useMemo((()=>M.filter((e=>e.hideable))),[M]),F=e.useMemo((()=>E.every((e=>null==u[e.field]||!1!==u[e.field]))),[u,E]),H=e.useMemo((()=>E.every((e=>!1===u[e.field]))),[u,E]),D=e.useRef(null);e.useEffect((()=>{b?o.current.focus():D.current&&"function"==typeof D.current.focus&&D.current.focus()}),[b]);let T=!1;const O=e=>!1===T&&!1!==e.hideable&&(T=!0,!0),L=e.useCallback((()=>{f(""),o.current.focus()}),[]);return l.jsxs(e.Fragment,{children:[l.jsx(ju,{className:g.header,ownerState:d,children:l.jsx(Vu,i({as:d.slots.baseTextField,ownerState:d,placeholder:n.current.getLocaleText("columnsManagementSearchTitle"),inputRef:o,className:g.searchInput,value:p,onChange:P,variant:"outlined",size:"small",type:"search",InputProps:{startAdornment:l.jsx(d.slots.baseInputAdornment,{position:"start",children:l.jsx(d.slots.quickFilterIcon,{})}),endAdornment:l.jsx(d.slots.baseIconButton,i({"aria-label":n.current.getLocaleText("columnsManagementDeleteIconLabel"),size:"small",sx:[p?{visibility:"visible"}:{visibility:"hidden"}],tabIndex:-1,onClick:L},d.slotProps?.baseIconButton,{children:l.jsx(d.slots.quickFilterClearIcon,{fontSize:"small"})}))},inputProps:{"aria-label":n.current.getLocaleText("columnsManagementSearchTitle")},autoComplete:"off",fullWidth:!0},d.slotProps?.baseTextField,y))}),l.jsxs(zu,{className:g.root,ownerState:d,children:[M.map((e=>l.jsx(B,{className:g.row,control:l.jsx(d.slots.baseCheckbox,i({disabled:!1===e.hideable,checked:!1!==u[e.field],onClick:I,name:e.field,sx:{p:.5},inputRef:O(e)?D:void 0},d.slotProps?.baseCheckbox)),label:e.headerName||e.field},e.field))),0===M.length&&l.jsx(Nu,{ownerState:d,children:n.current.getLocaleText("columnsManagementNoColumns")})]}),w&&C||!(M.length>0)?null:l.jsxs(Au,{ownerState:d,className:g.footer,children:[w?l.jsx("span",{}):l.jsx(B,{control:l.jsx(d.slots.baseCheckbox,i({disabled:0===E.length,checked:F,indeterminate:!F&&!H,onClick:()=>k(!F),name:n.current.getLocaleText("columnsManagementShowHideAllText"),sx:{p:.5}},d.slotProps?.baseCheckbox)),label:n.current.getLocaleText("columnsManagementShowHideAllText")}),C?null:l.jsx(d.slots.baseButton,i({onClick:()=>n.current.setColumnVisibilityModel(s),disabled:S},d.slotProps?.baseButton,{children:n.current.getLocaleText("columnsManagementReset")}))]})]})},panel:gu,row:Cc}),Pp={disableMultipleColumnsFiltering:!0,disableMultipleColumnsSorting:!0,throttleRowsMs:void 0,hideFooterRowCount:!1,pagination:!0,checkboxSelectionVisibleOnly:!1,disableColumnReorder:!0,keepColumnPositionIfDraggedOutside:!1,signature:"DataGrid",unstable_listView:!1},Ep=kp,Fp=t=>{const r=da(se({props:t,name:"MuiDataGrid"})),n=e.useMemo((()=>i({},dt,r.localeText)),[r.localeText]),o=e.useMemo((()=>function({defaultSlots:e,slots:t}){const r=t;if(!r||0===Object.keys(r).length)return e;const n=i({},e);return Object.keys(r).forEach((e=>{const t=e;void 0!==r[t]&&(n[t]=r[t])})),n}({defaultSlots:Ep,slots:r.slots})),[r.slots]),l=e.useMemo((()=>Object.keys(Ed).reduce(((e,t)=>(e[t]=r[t]??Ed[t],e)),{})),[r]);return e.useMemo((()=>i({},r,l,{localeText:n,slots:o},Pp)),[r,n,o,l])},Hp={hooks:{useGridAriaAttributes:()=>{const e=_l(),t=me(),r=We(e,kr),n=We(e,Wn),o=We(e,Rl),l=We(e,At);return{role:"grid","aria-colcount":r.length,"aria-rowcount":o+1+l+n,"aria-multiselectable":eo(t)}},useGridRowAriaAttributes:()=>{const t=_l(),r=We(t,Zn),n=We(t,Rl);return e.useCallback(((e,o)=>{const l={},i=o+n+2;return l["aria-rowindex"]=i,t.current.isRowSelectable(e.id)&&(l["aria-selected"]=void 0!==r[e.id]),l}),[t,r,n])},useCellAggregationResult:()=>null}},Dp=be((function(e,t){const r=Fp(e),n=gp(r.apiRef,r);return l.jsx(vc,{privateApiRef:n,configuration:Hp,props:r,children:l.jsx(ja,i({className:r.className,style:r.style,sx:r.sx},r.forwardedProps,r.slotProps?.root,{ref:t}))})})),Tp=e.memo(Dp);Dp.propTypes={apiRef:ue.shape({current:ue.object.isRequired}),"aria-label":ue.string,"aria-labelledby":ue.string,autoHeight:ue.bool,autoPageSize:ue.bool,autosizeOnMount:ue.bool,autosizeOptions:ue.shape({columns:ue.arrayOf(ue.string),disableColumnVirtualization:ue.bool,expand:ue.bool,includeHeaders:ue.bool,includeOutliers:ue.bool,outliersFactor:ue.number}),cellModesModel:ue.object,checkboxSelection:ue.bool,classes:ue.object,clipboardCopyCellDelimiter:ue.string,columnBufferPx:ue.number,columnGroupHeaderHeight:ue.number,columnGroupingModel:ue.arrayOf(ue.object),columnHeaderHeight:ue.number,columns:ue.arrayOf(ue.object).isRequired,columnVisibilityModel:ue.object,density:ue.oneOf(["comfortable","compact","standard"]),disableAutosize:ue.bool,disableColumnFilter:ue.bool,disableColumnMenu:ue.bool,disableColumnResize:ue.bool,disableColumnSelector:ue.bool,disableColumnSorting:ue.bool,disableDensitySelector:ue.bool,disableEval:ue.bool,disableMultipleRowSelection:ue.bool,disableRowSelectionOnClick:ue.bool,disableVirtualization:ue.bool,editMode:ue.oneOf(["cell","row"]),estimatedRowCount:ue.number,experimentalFeatures:ue.shape({warnIfFocusStateIsNotSynced:ue.bool}),filterDebounceMs:ue.number,filterMode:ue.oneOf(["client","server"]),filterModel:ue.shape({items:ue.arrayOf(ue.shape({field:ue.string.isRequired,id:ue.oneOfType([ue.number,ue.string]),operator:ue.string.isRequired,value:ue.any})).isRequired,logicOperator:ue.oneOf(["and","or"]),quickFilterExcludeHiddenColumns:ue.bool,quickFilterLogicOperator:ue.oneOf(["and","or"]),quickFilterValues:ue.array}),forwardedProps:ue.object,getCellClassName:ue.func,getDetailPanelContent:ue.func,getEstimatedRowHeight:ue.func,getRowClassName:ue.func,getRowHeight:ue.func,getRowId:ue.func,getRowSpacing:ue.func,hideFooter:ue.bool,hideFooterPagination:ue.bool,hideFooterSelectedRowCount:ue.bool,ignoreDiacritics:ue.bool,ignoreValueFormatterDuringExport:ue.oneOfType([ue.shape({clipboardExport:ue.bool,csvExport:ue.bool}),ue.bool]),indeterminateCheckboxAction:ue.oneOf(["deselect","select"]),initialState:ue.object,isCellEditable:ue.func,isRowSelectable:ue.func,keepNonExistentRowsSelected:ue.bool,loading:ue.bool,localeText:ue.object,logger:ue.shape({debug:ue.func.isRequired,error:ue.func.isRequired,info:ue.func.isRequired,warn:ue.func.isRequired}),logLevel:ue.oneOf(["debug","error","info","warn",!1]),nonce:ue.string,onCellClick:ue.func,onCellDoubleClick:ue.func,onCellEditStart:ue.func,onCellEditStop:ue.func,onCellKeyDown:ue.func,onCellModesModelChange:ue.func,onClipboardCopy:ue.func,onColumnHeaderClick:ue.func,onColumnHeaderContextMenu:ue.func,onColumnHeaderDoubleClick:ue.func,onColumnHeaderEnter:ue.func,onColumnHeaderLeave:ue.func,onColumnHeaderOut:ue.func,onColumnHeaderOver:ue.func,onColumnOrderChange:ue.func,onColumnResize:ue.func,onColumnVisibilityModelChange:ue.func,onColumnWidthChange:ue.func,onDensityChange:ue.func,onFilterModelChange:ue.func,onMenuClose:ue.func,onMenuOpen:ue.func,onPaginationMetaChange:ue.func,onPaginationModelChange:ue.func,onPreferencePanelClose:ue.func,onPreferencePanelOpen:ue.func,onProcessRowUpdateError:ue.func,onResize:ue.func,onRowClick:ue.func,onRowCountChange:ue.func,onRowDoubleClick:ue.func,onRowEditStart:ue.func,onRowEditStop:ue.func,onRowModesModelChange:ue.func,onRowSelectionModelChange:ue.func,onSortModelChange:ue.func,onStateChange:ue.func,pageSizeOptions:ue.arrayOf(ue.oneOfType([ue.number,ue.shape({label:ue.string.isRequired,value:ue.number.isRequired})]).isRequired),pagination:ue.oneOf([!0]),paginationMeta:ue.shape({hasNextPage:ue.bool}),paginationMode:ue.oneOf(["client","server"]),paginationModel:ue.shape({page:ue.number.isRequired,pageSize:ue.number.isRequired}),processRowUpdate:ue.func,resetPageOnSortFilter:ue.bool,resizeThrottleMs:ue.number,rowBufferPx:ue.number,rowCount:ue.number,rowHeight:ue.number,rowModesModel:ue.object,rowPositionsDebounceMs:ue.number,rows:ue.arrayOf(ue.object),rowSelection:ue.bool,rowSelectionModel:ue.oneOfType([ue.arrayOf(ue.oneOfType([ue.number,ue.string]).isRequired),ue.number,ue.string]),rowSpacingType:ue.oneOf(["border","margin"]),scrollbarSize:ue.number,showCellVerticalBorder:ue.bool,showColumnVerticalBorder:ue.bool,slotProps:ue.object,slots:ue.object,sortingMode:ue.oneOf(["client","server"]),sortingOrder:ue.arrayOf(ue.oneOf(["asc","desc"])),sortModel:ue.arrayOf(ue.shape({field:ue.string.isRequired,sort:ue.oneOf(["asc","desc"])})),sx:ue.oneOfType([ue.arrayOf(ue.oneOfType([ue.func,ue.object,ue.bool])),ue.func,ue.object]),unstable_rowSpanning:ue.bool,virtualizeColumnsWithAutoRowHeight:ue.bool};export{Tp as D};

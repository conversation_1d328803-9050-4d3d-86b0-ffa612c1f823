import axiosInstance from "../axios";

class ArtifactController {
    async getArtifactTimeSeries(startTimestamp, endTimestamp, interval) {
        try {
            const res = await axiosInstance.get(
                `/artifacts/hoursAggregatedCount?startTimestamp=${startTimestamp}&endTimestamp=${endTimestamp}&interval=${interval}`,
            );
            return res;
        } catch (error) {
            console.log("Error Artifact record for scrub bar notches " + error);
            return error.response.data;
        }
    }

    async getArtifactDetail(artifactId, signal = null) {
        try {
            const config = {
                meta: { showSnackbar: false },
                ...(signal && { signal }),
            };
            const res = await axiosInstance.get(`/artifacts/detail/${artifactId}`, config);
            return res.data;
        } catch (error) {
            // Check for various abort/cancel error types
            if (
                error.name === "AbortError" ||
                error.name === "CanceledError" ||
                error.code === "ERR_CANCELED" ||
                (error.message && error.message.includes("canceled"))
            ) {
                console.log("Request was aborted for artifact:", artifactId);
                throw error;
            }
            console.log("Error fetching artifact detail: " + error);
            throw error;
        }
    }
}

const artifactController = new ArtifactController();

export default artifactController;

<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Smooth Auto Slider</title>
  <style>
    body {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      background: #111;
      color: #fff;
      font-family: sans-serif;
    }

    .slider {
      position: relative;
      width: 400px;
      height: 8px;
      background: #333;
      border-radius: 4px;
      margin-top: 20px;
    }

    .fill {
      position: absolute;
      height: 100%;
      width: 0;
      background: #4cafef;
      border-radius: 4px;
    }

    .thumb {
      position: absolute;
      top: 50%;
      width: 18px;
      height: 18px;
      background: #4cafef;
      border-radius: 50%;
      transform: translate(-50%, -50%);
    }
  </style>
</head>
<body>

  <div>
    <h2 id="label">Value: 0</h2>
    <div class="slider" id="slider">
      <div class="fill" id="fill"></div>
      <div class="thumb" id="thumb"></div>
    </div>
  </div>

  <script>
    const slider = document.getElementById("slider");
    const thumb = document.getElementById("thumb");
    const fill = document.getElementById("fill");
    const label = document.getElementById("label");

    const sliderWidth = slider.offsetWidth;
    const maxValue = 20;
    const duration = 20000; // 20 sec

    const startTime = Date.now();

    let interval = setInterval(() => {
      let elapsed = Date.now() - startTime;
      let progress = Math.min(elapsed / duration, 1); // 0 → 1
      let value = (progress * maxValue).toFixed(2);

      let pos = sliderWidth * progress;

      thumb.style.left = pos + "px";
      fill.style.width = pos + "px";
      label.textContent = "Value: " + value;

      if (progress >= 1) {
        clearInterval(interval);
      }
    }, 16); // update ~60fps
  </script>

</body>
</html>
